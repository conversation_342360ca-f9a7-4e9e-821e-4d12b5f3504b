package cz.kpsys.portaro.erp.assets;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.IOException;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@RequestMapping(ErpAssetsController.ERP_ASSETS_PATH)
public class ErpAssetsController {

    public static final String ERP_ASSETS_PATH = "/erp/assets";

    @NonNull Provider<Department> rootDepartmentProvider;
    @NonNull ContextualProvider<Department, @NonNull Resource> logoNoTextResourceResolver;
    @NonNull ContextualProvider<Department, @NonNull Resource> logoResourceResolver;
    @NonNull ContextualProvider<Department, @NonNull Resource> textLogoResourceResolver;

    @RequestMapping("logo-no-text.svg")
    public ResponseEntity<byte[]> getLogoNoText() throws IOException {
        return createSvgResponse(logoNoTextResourceResolver.getOn(rootDepartmentProvider.get()));
    }

    @RequestMapping("logo.svg")
    public ResponseEntity<byte[]> getLogo() throws IOException {
        return createSvgResponse(logoResourceResolver.getOn(rootDepartmentProvider.get()));
    }

    @RequestMapping("text-logo.svg")
    public ResponseEntity<byte[]> getTextualLogo() throws IOException {
        return createSvgResponse(textLogoResourceResolver.getOn(rootDepartmentProvider.get()));
    }

    private ResponseEntity<byte[]> createSvgResponse(Resource resource) throws IOException {
        byte[] imageData = resource.getContentAsByteArray();

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "image/svg+xml");
        headers.setContentLength(imageData.length);

        return new ResponseEntity<>(imageData, headers, HttpStatus.OK);
    }
}