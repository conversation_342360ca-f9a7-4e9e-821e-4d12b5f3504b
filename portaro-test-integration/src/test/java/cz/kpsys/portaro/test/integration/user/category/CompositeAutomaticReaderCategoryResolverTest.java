package cz.kpsys.portaro.test.integration.user.category;

import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.alive.AliveServiceUserVerifier;
import cz.kpsys.portaro.ext.alive.AnyCategoryToVerifiedStudentOrTeacherAutomaticReaderCategoryResolver;
import cz.kpsys.portaro.ext.alive.datatypes.NotValidAliveUser;
import cz.kpsys.portaro.ext.alive.datatypes.VerifiedStudent;
import cz.kpsys.portaro.ext.alive.datatypes.VerifiedTeacher;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.TrustedUserAndSourcePredicate;
import cz.kpsys.portaro.user.category.*;
import cz.kpsys.portaro.user.category.resolvers.*;
import cz.kpsys.portaro.user.contact.SourceOfData;
import cz.kpsys.portaro.user.discount.DiscountApprovalUserServicePropertiesConstants;
import cz.kpsys.portaro.user.prop.UserServiceProperty;
import cz.kpsys.portaro.user.prop.UserServicePropertyHelper;
import cz.kpsys.portaro.user.prop.UserServicePropertyLoader;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;
import org.springframework.core.convert.support.DefaultConversionService;

import java.time.Instant;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;

import static cz.kpsys.portaro.user.discount.DiscountApprovalUserServicePropertiesConstants.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Tag("ci")
@Tag("unit")
class CompositeAutomaticReaderCategoryResolverTest {

    private final ContextualProvider<Department, @Nullable ReaderCategory> defaultByReaderFullRegistrationReaderCategory = ContextIgnoringContextualProvider.<Department, String>of("FULL").andThen(ReaderCategory::testing);
    private final ContextualProvider<Department, @Nullable ReaderCategory> defaultReaderCategoryProvider = ContextIgnoringContextualProvider.<Department, String>of("DEFAULT").andThen(ReaderCategory::testing);
    private final ContextualProvider<Department, @Nullable ReaderCategory> retireeReaderCategoryProvider = ContextIgnoringContextualProvider.<Department, String>of("RETIREE").andThen(ReaderCategory::testing);
    private final ContextualProvider<Department, @Nullable Integer> retireeAgeLimitProvider = _ -> 65;
    private final ContextualProvider<Department, @Nullable Integer> childAgeLimitProvider = _ -> 15;
    private final ContextualProvider<Department, @Nullable Integer> studentNotVerifiedAgeLimitProvider = _ -> 18;
    private final ContextualProvider<Department, @Nullable Integer> studentAgeLimitProvider = _ -> 26;
    private final ContextualProvider<Department, @Nullable ReaderCategory> childReaderCategoryProvider = ContextIgnoringContextualProvider.<Department, String>of("CHILD").andThenFastReturningNull(ReaderCategory::testing);
    private final ContextualProvider<Department, @NonNull ReaderCategory> studentReaderCategoryProvider = ContextIgnoringContextualProvider.<Department, String>of("STUDENT").andThen(ReaderCategory::testing);
    private final ContextualProvider<Department, @NonNull ReaderCategory> teacherReaderCategoryProvider = ContextIgnoringContextualProvider.<Department, String>of("TEACHER").andThen(ReaderCategory::testing);
    private final ContextualProvider<Department, @NonNull ReaderCategory> ztpReaderCategoryProvider = ContextIgnoringContextualProvider.<Department, String>of("ZTP").andThen(ReaderCategory::testing);
    private final ContextualProvider<Department, @NonNull ReaderCategory> childZtpReaderCategoryProvider = ContextIgnoringContextualProvider.<Department, String>of("CHILDZTP").andThen(ReaderCategory::testing);
    private final ContextualProvider<Department, @NonNull ReaderCategory> blindReaderCategoryProvider = ContextIgnoringContextualProvider.<Department, String>of("BLIND").andThen(ReaderCategory::testing);

    private final TrustedUserAndSourcePredicate trustedUserAndSourcePredicate = new TrustedUserAndSourcePredicate(department -> true);
    private final UserServicePropertyHelper userServicePropertyHelper = new UserServicePropertyHelper(Mockito.mock(UserServicePropertyLoader.class), Mockito.mock(Saver.class), new DefaultConversionService());
    private final AliveServiceUserVerifier aliveServiceUserVerifier = Mockito.mock(AliveServiceUserVerifier.class);

    private final CompositeAutomaticReaderCategoryResolver defaultResolver = new CompositeAutomaticReaderCategoryResolver()
            .add(new EnabledAutomaticReaderCategoryResolver(_ -> true), 10)
            .add(new UnchangeableCategoryAutomaticReaderCategoryResolver(_ -> List.of()), 20)
            .add(new PersonHasValidRegistrationAutomaticReaderCategoryResolver(), 30)
            .add(new FullRegistrationToDefaultAutomaticReaderCategoryResolver(defaultByReaderFullRegistrationReaderCategory, defaultReaderCategoryProvider, trustedUserAndSourcePredicate), 40)
            .add(new NotVerifiedStudentToAdultAutomaticReaderCategoryResolver(studentNotVerifiedAgeLimitProvider, defaultReaderCategoryProvider), 50)
            .add(new ChildrenToStudentAutomaticReaderCategoryResolver(childAgeLimitProvider, studentNotVerifiedAgeLimitProvider, childReaderCategoryProvider, studentReaderCategoryProvider), 60)
            .add(new StudentToDefaultAutomaticReaderCategoryResolver(studentAgeLimitProvider, studentReaderCategoryProvider, defaultReaderCategoryProvider), 70)
            .add(new DiscountServicePropsAutomaticReaderCategoryResolver(studentReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.STUDENT), 80)
            .add(new AnyCategoryToVerifiedStudentOrTeacherAutomaticReaderCategoryResolver(studentReaderCategoryProvider, studentAgeLimitProvider, teacherReaderCategoryProvider, aliveServiceUserVerifier), 85)
            .add(new DiscountServicePropsAutomaticReaderCategoryResolver(teacherReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.TEACHER), 90)
            .add(new DiscountServicePropsAutomaticReaderCategoryResolver(teacherReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.RETIREE), 100)
            .add(new RetireeAutomaticReaderCategoryResolver(retireeAgeLimitProvider, retireeReaderCategoryProvider, trustedUserAndSourcePredicate), 110)
            .add(new ChildAutomaticReaderCategoryResolver(childAgeLimitProvider, childReaderCategoryProvider, trustedUserAndSourcePredicate), 120)
            .add(new DiscountServicePropsAutomaticReaderCategoryResolver(blindReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.BLIND), 120)
            .add(new ZtpAutomaticReaderCategoryResolver(childAgeLimitProvider,
                    childReaderCategoryProvider,
                    childZtpReaderCategoryProvider,
                    ztpReaderCategoryProvider,
                    new DiscountServicePropsAutomaticReaderCategoryResolver(ztpReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.ZTP)), 130);

    @BeforeEach
    void beforeEach() {
        when(aliveServiceUserVerifier.verify(any(), any())).thenReturn(new NotValidAliveUser());
    }

    @Test
    void shouldReturnSameReaderCategory_WhenPersonHasDefaultCategory() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(40));
        person.setLifeDateSource(SourceOfData.EXTERNAL_BANKID);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("DEFAULT"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(CurrentReaderCategory.class, result);
    }

    @Test
    void shouldAssignChildCategory_WhenPersonIsYoungerThanChildAgeLimit() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(12));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("DEFAULT"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(ReaderCategory.testing("CHILD"), ((NewReaderCategory) result).newCategory());
    }

    @Test
    void shouldAssignChildCategory_WhenPersonIsYoungerThanChildAgeLimitAndIsAlreadyChild() {
        when(aliveServiceUserVerifier.verify(any(), any())).thenReturn(new VerifiedStudent(1324));
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(12));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("CHILD"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(CurrentReaderCategory.class, result);
        assertEquals(ReaderCategory.testing("CHILD"), ((CurrentReaderCategory) result).category());
    }

    @Test
    void shouldAssignChildCategory_WhenPersonIsYoungerThanChildAgeLimitAndValidAliveStudent() {
        when(aliveServiceUserVerifier.verify(any(), any())).thenReturn(new VerifiedStudent(1324));
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(12));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("DEFAULT"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(ReaderCategory.testing("CHILD"), ((NewReaderCategory) result).newCategory());
    }

    @Test
    void shouldAssignRetireeCategory_WhenPersonIsOlderThanRetireeAgeLimit() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(66));
        person.setLifeDateSource(SourceOfData.EXTERNAL_BANKID);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("DEFAULT"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("RETIREE"));
    }

    @Test
    void shouldAssignRetireeCategory_WhenPersonIsOlderThanRetireeAgeLimitAndAlreadyHasRetireeCategory() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(66));
        person.setLifeDateSource(SourceOfData.EXTERNAL_BANKID);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("RETIREE"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(CurrentReaderCategory.class, result);
        assertEquals(ReaderCategory.testing("RETIREE"), ((CurrentReaderCategory) result).category());
    }

    @Test
    void shouldKeepSameCategory_WhenTrustedSourceNotSatisfiedForRetiree() {
        TrustedUserAndSourcePredicate trustedUserAndSourcePredicate = new TrustedUserAndSourcePredicate(department -> false);
        CompositeAutomaticReaderCategoryResolver customResolver = new CompositeAutomaticReaderCategoryResolver()
                .add(new EnabledAutomaticReaderCategoryResolver(_ -> true), 10)
                .add(new UnchangeableCategoryAutomaticReaderCategoryResolver(_ -> List.of()), 20)
                .add(new PersonHasValidRegistrationAutomaticReaderCategoryResolver(), 30)
                .add(new FullRegistrationToDefaultAutomaticReaderCategoryResolver(defaultByReaderFullRegistrationReaderCategory, defaultReaderCategoryProvider, trustedUserAndSourcePredicate), 40)
                .add(new ChildrenToStudentAutomaticReaderCategoryResolver(childAgeLimitProvider, studentNotVerifiedAgeLimitProvider, childReaderCategoryProvider, studentReaderCategoryProvider), 50)
                .add(new NotVerifiedStudentToAdultAutomaticReaderCategoryResolver(studentNotVerifiedAgeLimitProvider, defaultReaderCategoryProvider), 60)
                .add(new StudentToDefaultAutomaticReaderCategoryResolver(studentAgeLimitProvider, studentReaderCategoryProvider, defaultReaderCategoryProvider), 70)
                .add(new DiscountServicePropsAutomaticReaderCategoryResolver(studentReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.STUDENT), 80)
                .add(new AnyCategoryToVerifiedStudentOrTeacherAutomaticReaderCategoryResolver(studentReaderCategoryProvider, studentAgeLimitProvider, teacherReaderCategoryProvider, aliveServiceUserVerifier), 85)
                .add(new DiscountServicePropsAutomaticReaderCategoryResolver(teacherReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.TEACHER), 90)
                .add(new DiscountServicePropsAutomaticReaderCategoryResolver(teacherReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.RETIREE), 100)
                .add(new RetireeAutomaticReaderCategoryResolver(retireeAgeLimitProvider, retireeReaderCategoryProvider, trustedUserAndSourcePredicate), 110)
                .add(new DiscountServicePropsAutomaticReaderCategoryResolver(blindReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.BLIND), 120)
                .add(new ZtpAutomaticReaderCategoryResolver(childAgeLimitProvider,
                        childReaderCategoryProvider,
                        childZtpReaderCategoryProvider,
                        ztpReaderCategoryProvider,
                        new DiscountServicePropsAutomaticReaderCategoryResolver(ztpReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.ZTP)), 130);

        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(66));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("DEFAULT"));

        ResolvedReaderCategory result = customResolver.resolve(ctx, person);

        assertInstanceOf(CurrentReaderCategory.class, result);
    }

    @Test
    void shouldAssignRetireeCategory_WhenPersonIsOlderThanRetireeAgeLimitAndInternalSourceIsTrusted() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(66));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("DEFAULT"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("RETIREE"));
    }

    @Test
    void shouldTerminateResolving_WhenResolverIsDisabled() {
        CompositeAutomaticReaderCategoryResolver customResolver = new CompositeAutomaticReaderCategoryResolver()
                .add(new EnabledAutomaticReaderCategoryResolver(_ -> false), 10)
                .add(new UnchangeableCategoryAutomaticReaderCategoryResolver(_ -> List.of()), 20)
                .add(new PersonHasValidRegistrationAutomaticReaderCategoryResolver(), 30)
                .add(new FullRegistrationToDefaultAutomaticReaderCategoryResolver(defaultByReaderFullRegistrationReaderCategory, defaultReaderCategoryProvider, trustedUserAndSourcePredicate), 40)
                .add(new ChildrenToStudentAutomaticReaderCategoryResolver(childAgeLimitProvider, studentNotVerifiedAgeLimitProvider, childReaderCategoryProvider, studentReaderCategoryProvider), 50)
                .add(new NotVerifiedStudentToAdultAutomaticReaderCategoryResolver(studentNotVerifiedAgeLimitProvider, defaultReaderCategoryProvider), 60)
                .add(new StudentToDefaultAutomaticReaderCategoryResolver(studentAgeLimitProvider, studentReaderCategoryProvider, defaultReaderCategoryProvider), 70)
                .add(new DiscountServicePropsAutomaticReaderCategoryResolver(studentReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.STUDENT), 80)
                .add(new AnyCategoryToVerifiedStudentOrTeacherAutomaticReaderCategoryResolver(studentReaderCategoryProvider, studentAgeLimitProvider, teacherReaderCategoryProvider, aliveServiceUserVerifier), 85)
                .add(new DiscountServicePropsAutomaticReaderCategoryResolver(teacherReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.TEACHER), 90)
                .add(new DiscountServicePropsAutomaticReaderCategoryResolver(teacherReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.RETIREE), 100)
                .add(new RetireeAutomaticReaderCategoryResolver(retireeAgeLimitProvider, retireeReaderCategoryProvider, trustedUserAndSourcePredicate), 110)
                .add(new DiscountServicePropsAutomaticReaderCategoryResolver(blindReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.BLIND), 120)
                .add(new ZtpAutomaticReaderCategoryResolver(childAgeLimitProvider,
                        childReaderCategoryProvider,
                        childZtpReaderCategoryProvider,
                        ztpReaderCategoryProvider,
                        new DiscountServicePropsAutomaticReaderCategoryResolver(ztpReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.ZTP)), 130);

        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(70));
        person.setLifeDateSource(SourceOfData.EXTERNAL_BANKID);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("DEFAULT"));

        ResolvedReaderCategory result = customResolver.resolve(ctx, person);

        assertInstanceOf(TerminateResolving.class, result);
    }

    @Test
    void shouldTerminateResolving_WhenPersonHasValidRegistration() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(66));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("DEFAULT"));
        person.getReaderAccounts().getFirst().setRegistrationDate(LocalDate.now());
        person.getReaderAccounts().getFirst().setRegistrationExpirationDate(LocalDate.now().plusYears(1));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(TerminateResolving.class, result);
    }

    @Test
    void shouldTerminateResolving_WhenPersonHasUnchangeableCategory() {
        CompositeAutomaticReaderCategoryResolver customResolver = new CompositeAutomaticReaderCategoryResolver()
                .add(new EnabledAutomaticReaderCategoryResolver(_ -> true), 10)
                .add(new UnchangeableCategoryAutomaticReaderCategoryResolver(_ -> List.of(ReaderCategory.testing("DEFAULT"))), 20)
                .add(new PersonHasValidRegistrationAutomaticReaderCategoryResolver(), 30)
                .add(new FullRegistrationToDefaultAutomaticReaderCategoryResolver(defaultByReaderFullRegistrationReaderCategory, defaultReaderCategoryProvider, trustedUserAndSourcePredicate), 40)
                .add(new ChildrenToStudentAutomaticReaderCategoryResolver(childAgeLimitProvider, studentNotVerifiedAgeLimitProvider, childReaderCategoryProvider, studentReaderCategoryProvider), 50)
                .add(new NotVerifiedStudentToAdultAutomaticReaderCategoryResolver(studentNotVerifiedAgeLimitProvider, defaultReaderCategoryProvider), 60)
                .add(new StudentToDefaultAutomaticReaderCategoryResolver(studentAgeLimitProvider, studentReaderCategoryProvider, defaultReaderCategoryProvider), 70)
                .add(new DiscountServicePropsAutomaticReaderCategoryResolver(studentReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.STUDENT), 80)
                .add(new AnyCategoryToVerifiedStudentOrTeacherAutomaticReaderCategoryResolver(studentReaderCategoryProvider, studentAgeLimitProvider, teacherReaderCategoryProvider, aliveServiceUserVerifier), 85)
                .add(new DiscountServicePropsAutomaticReaderCategoryResolver(teacherReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.TEACHER), 90)
                .add(new DiscountServicePropsAutomaticReaderCategoryResolver(teacherReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.RETIREE), 100)
                .add(new RetireeAutomaticReaderCategoryResolver(retireeAgeLimitProvider, retireeReaderCategoryProvider, trustedUserAndSourcePredicate), 110)
                .add(new DiscountServicePropsAutomaticReaderCategoryResolver(blindReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.BLIND), 120)
                .add(new ZtpAutomaticReaderCategoryResolver(childAgeLimitProvider,
                        childReaderCategoryProvider,
                        childZtpReaderCategoryProvider,
                        ztpReaderCategoryProvider,
                        new DiscountServicePropsAutomaticReaderCategoryResolver(ztpReaderCategoryProvider, userServicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.ZTP)), 130);

        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(70));
        person.setLifeDateSource(SourceOfData.EXTERNAL_BANKID);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("DEFAULT"));

        ResolvedReaderCategory result = customResolver.resolve(ctx, person);

        assertInstanceOf(TerminateResolving.class, result);
    }

    @Test
    void shouldKeepChildCategory_WhenPersonIsStillChild() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(14));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("CHILD"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(CurrentReaderCategory.class, result);
    }

    @Test
    void shouldTransitionToStudentCategory_WhenPersonReachesStudentAge() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(16));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("CHILD"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("STUDENT"));
    }

    @Test
    void shouldNotTransitionToStudentCategory_WhenPersonDoesNotHaveChildCategory() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(16));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("NOTCHILD"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(CurrentReaderCategory.class, result);
    }

    @Test
    void shouldNotBeStudent_WhenAgeIsGreaterThan26() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(26));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("STUDENT"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("DEFAULT"));
    }

    @Test
    void shouldStillBeFullReaderCategory_WhenIsNotValidUser() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(70));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("FULL"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(TerminateResolving.class, result);
    }

    @ParameterizedTest
    @ValueSource(strings = {"DEFAULT", "CHILD", "TEACHER", "STUDENT"})
    void shouldAssignTeacherCategory_WhenPersonWithAnyCategoryIsVerifiedTeacher(String category) {
        when(aliveServiceUserVerifier.verify(any(), any())).thenReturn(new VerifiedTeacher(1234));

        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(30));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing(category));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("TEACHER"));
    }

    @Test
    void shouldRemainSameCategory_WhenPersonIsVerifiedStudentButIsOlderThenStudentAgeLimitAndDoesNotHaveStudentCategory() {
        when(aliveServiceUserVerifier.verify(any(), any())).thenReturn(new VerifiedStudent(1324));

        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(30));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("DEFAULT"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(CurrentReaderCategory.class, result);
    }

    @Test
    void shouldRemainStudentCategory_WhenVerifiedStudentAndAgeIsUnder26() {
        when(aliveServiceUserVerifier.verify(any(), any())).thenReturn(new VerifiedStudent(4584));

        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(25));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("STUDENT"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("STUDENT"));
    }

    @Test
    void shouldTransitionStudentCategory_WhenVerifiedStudentAndAgeIsUnder26() {
        when(aliveServiceUserVerifier.verify(any(), any())).thenReturn(new VerifiedStudent(1234));

        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(25));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("DEFAULT"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("STUDENT"));
    }

    @Test
    void shouldTransitionToDefaultCategory_WhenStudentIsOlderThan26() {
        when(aliveServiceUserVerifier.verify(any(), any())).thenReturn(new VerifiedStudent(1234));

        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(27));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("STUDENT"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("DEFAULT"));
    }

    @Test
    void shouldTransitionToStudentCategory_WhenValidStudent() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(27));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("DEFAULT"));
        person.setUserServiceProperties(List.of(new UserServiceProperty(UuidGenerator.forIdentifier(), person.getId(), DISCOUNT_SERVICE, STUDENT, "true", Instant.now().plus(30, ChronoUnit.DAYS))));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("STUDENT"));
    }

    @Test
    void shouldTransitionToTeacherCategory_WhenValidTeacher() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(27));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("DEFAULT"));
        person.setUserServiceProperties(List.of(new UserServiceProperty(UuidGenerator.forIdentifier(), person.getId(), DISCOUNT_SERVICE, TEACHER, "true", Instant.now().plus(30, ChronoUnit.DAYS))));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("TEACHER"));
    }

    @Test
    void shouldTransitionToBlindCategory_WhenValidBlind() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(27));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("DEFAULT"));
        person.setUserServiceProperties(List.of(new UserServiceProperty(UuidGenerator.forIdentifier(), person.getId(), DISCOUNT_SERVICE, BLIND, "true", Instant.now().plus(30, ChronoUnit.DAYS))));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("BLIND"));
    }

    @Test
    void shouldTransitionToZTPCategory_WhenValidZTP() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(27));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("DEFAULT"));
        person.setUserServiceProperties(List.of(new UserServiceProperty(UuidGenerator.forIdentifier(), person.getId(), DISCOUNT_SERVICE, ZTP, "true", Instant.now().plus(30, ChronoUnit.DAYS))));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("ZTP"));
    }

    @Test
    void shouldTransitionToChildZTPCategory_WhenValidZTPAndChildCategory() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(14));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("CHILD"));
        person.setUserServiceProperties(List.of(new UserServiceProperty(UuidGenerator.forIdentifier(), person.getId(), DISCOUNT_SERVICE, ZTP, "true", Instant.now().plus(30, ChronoUnit.DAYS))));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("CHILDZTP"));
    }


    @Test
    void shouldTransitionToDefaultCategory_WhenOld() {
        Person person = Person.testingReader(1234);

        Department ctx = Department.testing(14);
        person.setBirthDate(LocalDate.now().minusYears(27));
        person.setLifeDateSource(SourceOfData.INTERNAL);
        person.getReaderAccounts().getFirst().setReaderCategory(ReaderCategory.testing("CHILD"));

        ResolvedReaderCategory result = defaultResolver.resolve(ctx, person);

        assertInstanceOf(NewReaderCategory.class, result);
        assertEquals(((NewReaderCategory) result).newCategory(), ReaderCategory.testing("DEFAULT"));
    }
}