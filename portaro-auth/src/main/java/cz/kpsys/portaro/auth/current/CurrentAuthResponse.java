package cz.kpsys.portaro.auth.current;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.HierarchicalDepartment;
import lombok.NonNull;

import java.util.List;
import java.util.Set;

public record CurrentAuthResponse(

        boolean evided,
        @NonNull AuthableUserResponse activeUser,
        @NonNull Set<String> role,
        @NonNull List<Department> readableDepartments,
        @NonNull List<Department> editableDepartments,
        @NonNull List<HierarchicalDepartment> readableHierarchicalDepartments

) {}
