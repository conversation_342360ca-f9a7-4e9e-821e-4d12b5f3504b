package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.value.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cz.kpsys.portaro.record.detail.FieldStorageBehaviour.OriginStrategy.ALWAYS_SELF_ORIGIN;

@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordFieldsToFieldsConverter implements Function<List<RecordField>, List<? extends Field<?>>> {

    @Override
    public List<? extends Field<?>> apply(@NonNull List<RecordField> source) {
        return source.stream()
                .sorted(Comparator.comparing(RecordField::getFieldId, FieldId.NUMERICALLY_COMPATIBLE_SORTER).thenComparing(RecordField::getCurrentLevelFieldIndex))
                .map(this::toPrefabField)
                .toList();
    }

    private @NonNull Field<?> toPrefabField(@NonNull RecordField recordField) {
        return new Field<>(
                recordField.getId(),
                recordField.getRecordFieldId(),
                recordField.getFieldType(),
                recordField.getRecordLink(),
                recordField.getTextSuffix(),
                getValue(recordField),
                getSubfields(recordField)
        );
    }

    private @Nullable ScalarFieldValue<?> getValue(@NonNull RecordField recordField) {
        if (recordField.getTextValue() == null) {
            return null;
        }

        FieldType fieldType = recordField.getFieldType();

        Set<RecordIdFondPair> origins;
        if (fieldType.getFieldStorageBehaviour().originStrategy().equals(ALWAYS_SELF_ORIGIN)) {
            origins = Set.of(recordField.getRecordFieldId().recordIdFondPair());
        } else if (recordField.getOriginRecordId() != null) {
            origins = Set.of(recordField.getOriginRecordId());
        } else {
            log.warn("Given field (in record_field) {} has no origin defined", recordField.getRecordFieldId());
            origins = Set.of(recordField.getRecordFieldId().recordIdFondPair());
        }

        RecordFieldEntity sourceEntity = Objects.requireNonNull(recordField.getSourceEntity());

        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.DATE) && sourceEntity.dateValue() != null) {
            return LocalDateFieldValue.of(sourceEntity.dateValue(), origins);
        }

        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.DATETIME) && sourceEntity.datetimeValue() != null) {
            return InstantFieldValue.of(sourceEntity.datetimeValue(), origins);
        }

        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.BOOLEAN) && sourceEntity.booleanValue() != null) {
            return BooleanFieldValue.of(sourceEntity.booleanValue(), origins);
        }

        if ((fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER) || fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_2)) && sourceEntity.numericValue() != null) {
            return NumberFieldValue.of(sourceEntity.numericValue(), origins);
        }

        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_4)) {
            throw new UnsupportedOperationException("Number with precision 4 is not supported");
        }

        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_6)) {
            throw new UnsupportedOperationException("Number with precision 6 is not supported");
        }

        if (fieldType.getCodebook().isPresent()) {
            LabeledIdentified<String> acceptableValue = fieldType.getCodebook().get().getById(recordField.getTextValue());
            return AcceptableValueFieldValue.ofStringIdentified(acceptableValue, origins);
        }

        return StringFieldValue.of(recordField.getTextValue(), origins);
    }

    private @NonNull List<Field<?>> getSubfields(@NonNull RecordField recordField) {
        return recordField.getSubfields().stream()
                .sorted(Comparator.comparing(RecordField::getCurrentLevelFieldIndex))
                .map(this::toPrefabField)
                .collect(Collectors.toList());
    }

}
