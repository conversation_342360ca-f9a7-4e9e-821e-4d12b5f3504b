package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.Optional;
import java.util.Set;

public record StringFieldValue(

    @NonNull
    String value,

    @Nullable
    String label,

    @NonNull
    Text text,

    @NonNull
    @NotEmpty
    Set<RecordIdFondPair> origins

) implements ScalarFieldValue<String> {

    public static @NonNull StringFieldValue of(@NonNull String raw, @NonNull @NotEmpty Set<RecordIdFondPair> origins) {
        return new StringFieldValue(
                raw,
                StringUtil.notEmptyString(raw),
                getUserFriendlyText(raw),
                origins
        );
    }

    public static @NonNull StringFieldValue of(@NonNull String value, @NonNull Text text, @NonNull @NotEmpty Set<RecordIdFondPair> origins) {
        return new StringFieldValue(
                value,
                StringUtil.notEmptyString(value),
                text,
                origins
        );
    }

    public static @NonNull StringFieldValue testing(@NonNull String raw, @NonNull RecordIdFondPair recordIdFondPair) {
        return StringFieldValue.of(raw, Set.of());
    }

    public static Optional<String> extract(@NonNull Field<StringFieldValue> field) {
        return field.typedValue(StringFieldValue.class).map(StringFieldValue::value);
    }

    private static Text getUserFriendlyText(String raw) {
        if (StringUtil.isNullOrBlank(raw)) {
            return Texts.ofEmpty();
        }
        return Texts.ofNative(raw);
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, StringFieldValue.class, StringFieldValue::value);
    }

    @Override
    public int hashCode() {
        return value().hashCode();
    }
}
