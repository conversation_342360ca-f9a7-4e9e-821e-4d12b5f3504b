package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Contentable;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Labeled;
import cz.kpsys.portaro.commons.object.Valuable;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.validation.nullablenotempty.NullableNotEmpty;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.detail.value.*;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.EmptyFieldCreation;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.jspecify.annotations.Nullable;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class Field<VH extends ScalarFieldValue<?>> implements Identified<UUID>, WithFieldType, FieldLike, Labeled, Valuable<Object>, CreatableFieldContainer, WithRepetition, Contentable, Serializable {

    public static final String NO_SUFFIX = null;
    public static final RecordIdFondPair NO_LINK = null;

    public static <FVH extends ScalarFieldValue<?>> FVH noValue() {
        return null;
    }

    public static List<Field<?>> noSubfields() {
        return new ArrayList<>();
    }

    public static final Comparator<Field<?>> NUMERICALLY_COMPATIBLE_SORTER = Comparator.comparing(Field::getFieldId, FieldId.NUMERICALLY_COMPATIBLE_SORTER);

    @Getter
    @NonNull
    UUID id;

    @JsonIgnore
    @Getter
    @NonNull
    @NonFinal
    RecordFieldId recordFieldId;

    @JsonIgnore
    @Getter
    @NonNull
    EditableFieldType type;

    @Nullable
    @NonFinal
    RecordIdFondPair recordLink;

    @Nullable
    @NonFinal
    ScalarFieldValue<?> valueHolder;

    @JsonIgnore
    @Getter
    @Nullable
    @NullableNotEmpty
    @NonFinal
    String suffix;

    @JsonIgnore(false)
    @NonNull
    @Getter
    List<Field<?>> fields;

    @JsonIgnore
    @Getter
    @Nullable
    @NonFinal
    FailedResult error;

    public static <VH extends ScalarFieldValue<?>> Field<VH> empty(@NonNull UUID id,
                                                                   @NonNull RecordIdFondPair record,
                                                                   @NonNull RecordFieldId recordFieldId,
                                                                   @NonNull EditableFieldType type) {
        return new Field<VH>(
                id,
                recordFieldId,
                type,
                Field.NO_LINK,
                Field.NO_SUFFIX,
                Field.noValue(),
                Field.noSubfields()
        );
    }

    public Field(@NonNull UUID id,
                 @NonNull RecordFieldId recordFieldId,
                 @NonNull EditableFieldType type,
                 @Nullable RecordIdFondPair recordLink,
                 @Nullable @NullableNotEmpty String suffix,
                 @Nullable VH value,
                 @NonNull List<Field<?>> fields) {
        this.id = id;
        this.recordFieldId = recordFieldId;
        this.type = type;
        this.recordLink = recordLink;
        this.suffix = suffix;
        this.valueHolder = value;
        this.fields = fields;
    }

    public Field(@NonNull Field<VH> original, @NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        this.id = original.id;
        this.recordFieldId = RecordFieldId.of(newRecordIdFondPair, original.getFieldId());
        this.type = original.type;
        this.recordLink = original.recordLink;
        this.suffix = original.suffix;
        this.valueHolder = original.valueHolder;
        this.fields = original.fields.stream().map(field -> field.copy(newRecordIdFondPair, kindedId)).collect(Collectors.toList());
        this.error = original.error;
    }

    @JsonIgnore
    public @NonNull RecordIdFondPair getRecordIdFondPair() {
        return recordFieldId.recordIdFondPair();
    }

    @JsonIgnore
    @Override
    public @NonNull FieldId getFieldId() {
        return getRecordFieldId().fieldId();
    }

    @Override
    public @NonNull String getCode() {
        return getType().getCode();
    }

    @JsonIgnore
    @Override
    public @NonNull FieldTypeId fieldTypeId() {
        return getType().getFieldTypeId();
    }

    public Text getFieldTypeText() {
        return getType().getText();
    }

    public String getTypeId() {
        return getType().getId();
    }

    @JsonIgnore
    public int getRepetition() {
        return this.getFieldId().getRepetition();
    }

    /**
     * Deprecated - remove
     */
    @Deprecated
    @JsonIgnore
    public int getFieldRepetition() {
        if (this.getFieldId().hasParent()) {
            return this.getFieldId().existingParent().getRepetition();
        }
        return this.getFieldId().getRepetition();
    }

    @JsonIgnore
    public @NonNull FieldPayload<?> getPayload() {
        return new FieldPayload<>(valueHolder, recordLink);
    }

    @JsonIgnore
    public @Nullable ScalarFieldValue<?> getValueHolder() {
        return valueHolder;
    }

    public @Nullable Object getValue() {
        if (valueHolder == null) {
            return null;
        }
        return valueHolder.getValue();
    }

    public @Nullable String getRaw() {
        if (valueHolder == null) {
            return null;
        }
        return valueHolder.label();
    }

    @NonNull
    @Override
    public Text getText() {
        if (valueHolder != null) {
            return valueHolder.text();
        }
        if (!fields.isEmpty()) {
            List<Text> subfieldTexts = streamFields()
                    .filter(Field::hasValue)
                    .map(Labeled::getText)
                    .toList();
            return MultiText.ofTexts(subfieldTexts).withSpaceDelimiter();
        }
        return Texts.ofEmpty();
    }

    public boolean hasRecordLink() {
        return recordLink != null;
    }

    @JsonIgnore
    public @NonNull Optional<RecordIdFondPair> getRecordLink() {
        return Optional.ofNullable(recordLink);
    }

    @JsonIgnore
    public @NonNull RecordIdFondPair getExistingRecordLink() {
        if (!hasRecordLink()) {
            throw new IllegalStateException("Field %s does not have a record link".formatted(this));
        }
        return Objects.requireNonNull(recordLink);
    }

    public boolean setRecordLink(@NonNull RecordIdFondPair recordLink) {
        if (recordLink.equals(this.recordLink)) {
            return false;
        }
        this.recordLink = recordLink;
        return true;
    }

    @JsonIgnore
    public @NonNull Optional<RecordIdFondPair> getOrigin() {
        if (valueHolder == null) {
            return Optional.empty();
        }
        RecordIdFondPair resolvedOrigin = type.getFieldStorageBehaviour().originStrategy().resolveOrigin(this);
        return Optional.of(resolvedOrigin);
    }

    public boolean setSuffix(@Nullable String suffix) {
        if (Objects.equals(suffix, this.suffix)) {
            return false;
        }
        this.suffix = suffix;
        return true;
    }

    public boolean setValue(@NonNull ScalarFieldValue<?> value) {
        if (value.equals(this.valueHolder)) {
            return false;
        }
        this.error = null;
        this.valueHolder = value;
        return true;
    }

    public boolean setPayload(@NonNull FieldPayload<?> payload) {
        boolean changed = false;

        if (payload.recordLink() != null) {
            changed |= setRecordLink(payload.recordLink());
        } else if (recordLink != null) {
            recordLink = null;
            changed = true;
        }

        if (payload.valueHolder() != null) {
            changed |= setValue(payload.valueHolder());
        } else if (valueHolder != null) {
            valueHolder = null;
            changed = true;
        }

        return changed;
    }

    @JsonIgnore
    public boolean deletePayload() {
        if (valueHolder != null || recordLink != null) {
            valueHolder = null;
            recordLink = null;
            return true;
        }
        return false;
    }

    public boolean setEditedValue(@NonNull FieldValueCommand command) {
        FieldValueConverter fieldValueConverter = type.getValueConverter();
        FieldPayload<?> fieldPayload = fieldValueConverter.convert(command, type, FieldPayload.class, recordFieldId);
        return setPayload(fieldPayload);
    }


    /// Returns if field has value or record link or any subfield with value
    @Override
    public boolean isEmpty() {
        return valueHolder == null && recordLink == null && streamFields().allMatch(Field::isEmpty);
    }

    /// Returns if field has value or any subfield has value
    @JsonIgnore
    public boolean hasValue() {
        return valueHolder != null || streamFields().anyMatch(Field::hasValue);
    }

    /// Returns if field is autonomous and has value or any subfield has autonomous value
    @JsonIgnore
    public boolean hasAutonomousValue() {
        return (type.isAutonomous() && valueHolder != null) || streamFields().anyMatch(Field::hasAutonomousValue);
    }

    @JsonIgnore
    public boolean setFieldId(@NonNull FieldId fieldId) {
        if (!this.getFieldId().equals(fieldId)) {
            recordFieldId = recordFieldId.withFieldId(fieldId);

            for (Field<?> subfield : fields) {
                subfield.setFieldId(subfield.getFieldId().withParent(this.getFieldId()));
            }

            return true;
        }
        return false;
    }

    public boolean isUrl() {
        String raw = getRaw();
        if (raw != null) {
            String lowerCasedValue = raw.toLowerCase();
            return lowerCasedValue.startsWith("http://") || lowerCasedValue.startsWith("https://") || lowerCasedValue.startsWith("www.");
        }
        return false;
    }

    @JsonIgnore
    public void setError(@NonNull FailedResult failedResult) {
        this.valueHolder = null;
        this.error = failedResult;
    }

    @JsonIgnore
    public @NonNull FailedResult getExistingError() {
        return Objects.requireNonNull(getError());
    }

    @JsonIgnore
    public boolean hasError() {
        return error != null;
    }

    public Field<?> createField(EmptyFieldCreation command) {
        EditableFieldType subfieldType = getType().getSubfieldTypeOrParentVirtualGroupTypeFor(command.fieldTypeId());
        Field<?> field = subfieldType.createEmptyFieldByParentId(getRecordIdFondPair(), this.getFieldId(), command.id());
        add(field);

        if (false) { // docasne to tu nechame, ale vypada to, ze neni pripad, kdy bychom chteli setridit pole (ale mozna se pletu)
            List<? extends WithCode> subfieldTypes = getType().getSubfieldTypes();
            new FieldSorter().reorderFieldByCodeRule(field, this, subfieldTypes);
        }

        return field;
    }

    @JsonIgnore
    public Optional<VH> typedValue(Class<VH> desiredType) {
        if (valueHolder == null) {
            return Optional.empty();
        }
        VH fieldValue = ObjectUtil.cast(valueHolder, desiredType, () -> "Field " + fieldTypeId() + " has expected type " + desiredType.getSimpleName() + " but actual is " + valueHolder.getClass().getSimpleName());
        return Optional.of(fieldValue);
    }

    static boolean equals(Object thisObj, Object otherObj) {
        if (thisObj == null || otherObj == null) {
            return false;
        }
        if (thisObj == otherObj) {
            return true;
        }
        if (!(thisObj instanceof Field) || !(otherObj instanceof Field)) {
            return false;
        }

        final Identified<UUID> thisField = (Identified<UUID>) thisObj;
        final Identified<UUID> otherField = (Identified<UUID>) otherObj;
        return Objects.equals(thisField.getId(), otherField.getId());
    }

    @JsonIgnore
    public Field<VH> copy(@NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        return new Field<>(this, newRecordIdFondPair, kindedId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        return Field.equals(this, obj);
    }

    public boolean hasSameValue(Field<?> field) {
        if (!Objects.equals(getRaw(), field.getRaw())) {
            return false;
        }
        if (this.fields.size() != field.getFields().size()) {
            return false;
        }
        for (int i = 0; i < fields.size(); i++) {
            if (!fields.get(i).hasSameValue(field.getFields().get(i))) {
                return false;
            }
        }
        return true;
    }

    @Override
    public String toString() {
        StringBuilder value = new StringBuilder();
        if (hasRecordLink()) {
            if (!value.isEmpty()) {
                value.append(", ");
            }
            value.append("->").append(getExistingRecordLink().id().abbr());
        }
        if (getValue() != null) {
            if (!value.isEmpty()) {
                value.append(", ");
            }
            value.append(getValue());
        }
        if (!fields.isEmpty()) {
            if (!value.isEmpty()) {
                value.append(", ");
            }
            value.append(fields.size()).append(" subfields");
        }
        if (hasError()) {
            if (!value.isEmpty()) {
                value.append(", ");
            }
            value.append("ERR:").append(getExistingError());
        }
        if (value.isEmpty()) {
            value.append("<EMPTY>");
        }

        return getRecordFieldId() + " [" + value + "]";
    }

}
