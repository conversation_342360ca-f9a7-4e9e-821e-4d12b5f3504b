package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import lombok.NonNull;
import lombok.With;

import java.util.Objects;

/// Definition of field behaviour when loading or saving.
///
/// @param storeRecordField      when a row should be written to record_field
/// @param storeRecordFieldValue when should value be written to record_field
/// @param cacheValidity         how CACHE_VALID flag should be set
/// @param selfLink              whether targetRecordId should be set to itself (will be eligible for invalidation and removal when cache_valid=true)
/// @param originStrategy        how origin_id should behave
/// @param storeKatX_1           if a row should always be written to KatX_1
/// @param wholeFieldGenerated   indicates that data are completely generated (not linked or stored elsewhere, like BOUND)
///
/// WARNING! DO NOT REORDER FIELDS! OR CHECK DEFINITIONS AND REORDER THEM ALSO!
@With
public record FieldStorageBehaviour(

        @NonNull RecordFieldStorage storeRecordField,
        @NonNull RecordFieldValueStorage storeRecordFieldValue,
        @NonNull CacheValidity cacheValidity,
        boolean selfLink,
        @NonNull OriginStrategy originStrategy,
        boolean storeKatX_1,
        boolean wholeFieldGenerated

) {

    public enum RecordFieldStorage {
        ALWAYS, NEVER, ONLY_CACHED;

        public boolean shouldStore(boolean isFieldCacheEnabled) {
            return switch (this) {
                case ALWAYS -> true;
                case NEVER -> false;
                case ONLY_CACHED -> isFieldCacheEnabled;
            };
        }
    }

    public enum RecordFieldValueStorage {
        VALUE_ALWAYS, VALUE_NEVER, VALUE_ONLY_CACHED;
    }

    public enum CacheValidity {
        ALWAYS_VALID, IF_CACHE_ENABLED, NEVER_VALID;

        public boolean resolveValidity(boolean isFieldCacheEnabled) {
            return switch(this) {
                case ALWAYS_VALID -> true;
                case IF_CACHE_ENABLED -> isFieldCacheEnabled;
                case NEVER_VALID -> false;
            };
        }
    }

    public enum OriginStrategy {
        ALWAYS_SELF_ORIGIN,
        SELF_ORIGIN_FALLBACK_WHEN_MANY;

        /// the field must have a value; Otherwise, it throws an exception
        public @NonNull RecordIdFondPair resolveOrigin(@NonNull Field<?> field) {
            return switch (this) {
                case ALWAYS_SELF_ORIGIN -> field.getRecordIdFondPair();
                case SELF_ORIGIN_FALLBACK_WHEN_MANY -> {
                    var valueHolder = Objects.requireNonNull(field.getValueHolder(), () -> "Field " + field + " must have a value holder to resolve origin!");
                    if (valueHolder.origins().size() > 1) {
                        yield field.getRecordIdFondPair();
                    }
                    yield ListUtil.getSingle(valueHolder.origins());
                }

            };
        }
    }

}
