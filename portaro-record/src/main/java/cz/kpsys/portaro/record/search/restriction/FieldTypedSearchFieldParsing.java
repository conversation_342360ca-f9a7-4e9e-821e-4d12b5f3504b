package cz.kpsys.portaro.record.search.restriction;

import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.search.field.BasicSearchField;
import cz.kpsys.portaro.search.field.SearchField;
import lombok.NonNull;

import java.util.Optional;

import static cz.kpsys.portaro.record.detail.FieldTypeId.DELIMITER;

public record FieldTypedSearchFieldParsing(

        @NonNull
        FieldTypeId fieldTypeId,

        @NonNull
        Subject subject

) {

    public static final String DYNAMIC_FIELD_PREFIX = "field_";
    private static final String DYNAMIC_FIELD_DELIMITER = "_";
    private static final String DYNAMIC_FIELD_VALUE_SUFFIX = "_val";
    private static final String DYNAMIC_FIELD_LINK_SUFFIX = "_link";
    private static final String DYNAMIC_FIELD_SORT_SUFFIX = "_sort";

    public enum Subject {
        VALUE,
        LINK,
        SORT
    }

    public static FieldTypedSearchFieldParsing ofValue(@NonNull FieldTypeId fieldTypeId) {
        return new FieldTypedSearchFieldParsing(fieldTypeId, Subject.VALUE);
    }

    public static FieldTypedSearchFieldParsing ofLink(@NonNull FieldTypeId fieldTypeId) {
        return new FieldTypedSearchFieldParsing(fieldTypeId, Subject.LINK);
    }

    public static FieldTypedSearchFieldParsing ofSort(@NonNull FieldTypeId fieldTypeId) {
        return new FieldTypedSearchFieldParsing(fieldTypeId, Subject.SORT);
    }

    public static FieldTypedSearchFieldParsing parsePrefixedSuffixed(@NonNull String prefixedSuffixedSearchFieldName) {
        String suffixedSearchFieldName = StringUtil.removePrefix(prefixedSuffixedSearchFieldName, DYNAMIC_FIELD_PREFIX);

        Optional<String> ofValueSuffix = StringUtil.removeSuffixOpt(suffixedSearchFieldName, DYNAMIC_FIELD_VALUE_SUFFIX);
        if (ofValueSuffix.isPresent()) {
            String fieldTypeIdString = ofValueSuffix.get().replace(DYNAMIC_FIELD_DELIMITER, DELIMITER);
            return ofValue(FieldTypeId.parse(fieldTypeIdString));
        }

        Optional<String> ofLinkSuffix = StringUtil.removeSuffixOpt(suffixedSearchFieldName, DYNAMIC_FIELD_LINK_SUFFIX);
        if (ofLinkSuffix.isPresent()) {
            String fieldTypeIdString = ofLinkSuffix.get().replace(DYNAMIC_FIELD_DELIMITER, DELIMITER);
            return ofLink(FieldTypeId.parse(fieldTypeIdString));
        }

        Optional<String> ofSortSuffix = StringUtil.removeSuffixOpt(suffixedSearchFieldName, DYNAMIC_FIELD_SORT_SUFFIX);
        if (ofSortSuffix.isPresent()) {
            String fieldTypeIdString = ofSortSuffix.get().replace(DYNAMIC_FIELD_DELIMITER, DELIMITER);
            return ofSort(FieldTypeId.parse(fieldTypeIdString));
        }

        throw new IllegalArgumentException("Cannot parse " + prefixedSuffixedSearchFieldName + " as dynamic field - it does not contain allowed suffix");
    }

    public static boolean hasDynamicFieldPrefix(@NonNull String searchFieldName) {
        return searchFieldName.startsWith(DYNAMIC_FIELD_PREFIX);
    }

    public String toSearchFieldName() {
        String suffix = switch (subject) {
            case VALUE -> DYNAMIC_FIELD_VALUE_SUFFIX;
            case LINK -> DYNAMIC_FIELD_LINK_SUFFIX;
            case SORT -> DYNAMIC_FIELD_SORT_SUFFIX;
        };
        return DYNAMIC_FIELD_PREFIX +
               fieldTypeId.value().replace(DELIMITER, DYNAMIC_FIELD_DELIMITER) +
               suffix;
    }

    public SearchField toSearchField() {
        return BasicSearchField.generic(toSearchFieldName());
    }

}
