package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.BooleanFieldValue;
import cz.kpsys.portaro.record.detail.value.FieldValue;
import cz.kpsys.portaro.record.detail.value.NumberFieldValue;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.Set;
import java.util.function.Function;

public record Constant<RES extends FieldValue<?>>(

        @NonNull Function<@NonNull RecordIdFondPair, RES> valueGenerator,
        @NonNull ScalarDatatype datatype

) implements NoaryFunction<RES> {

    public static Constant<NumberFieldValue> ofZero() {
        return ofInteger(BigDecimal.ZERO);
    }

    public static Constant<NumberFieldValue> ofOne() {
        return ofInteger(BigDecimal.ONE);
    }

    public static Constant<NumberFieldValue> ofInteger(@NonNull BigDecimal value) {
        Assert.state(value.scale() == 0, () -> "Cannot create integer constant with no-integer value %s".formatted(value));
        return new Constant<>(new NumberConstantGenerator(value), CoreConstants.Datatype.NUMBER);
    }

    public static Constant<NumberFieldValue> ofDecimal2(@NonNull BigDecimal value) {
        Assert.state(value.scale() <= 2, () -> "Cannot create decimal2 constant with no-decimal2 value %s".formatted(value));
        return new Constant<>(new NumberConstantGenerator(value), CoreConstants.Datatype.NUMBER_DECIMAL_2);
    }

    public static Constant<StringFieldValue> ofString(@NonNull String value) {
        return new Constant<>(new StringConstantGenerator(StaticProvider.of(value)), CoreConstants.Datatype.TEXT);
    }

    public static Constant<StringFieldValue> ofString(@NonNull Provider<String> value) {
        return new Constant<>(new StringConstantGenerator(value), CoreConstants.Datatype.TEXT);
    }

    public static Constant<BooleanFieldValue> ofBoolean(@NonNull Boolean value) {
        return new Constant<>(new BooleanConstantGenerator(value), CoreConstants.Datatype.BOOLEAN);
    }

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<RES>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        return datatype;
    }

    public @NonNull FormulaEvaluation<RES> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        RES computedValue = valueGenerator.apply(sourceNode.record());
        return FormulaEvaluation.success(computedValue);
    }

    @Override
    public String toString() {
        return "Const(%s)".formatted(valueGenerator);
    }

    private record NumberConstantGenerator(@NonNull BigDecimal value) implements Function<RecordIdFondPair, NumberFieldValue> {

        @Override
        public NumberFieldValue apply(RecordIdFondPair record) {
            return NumberFieldValue.of(value, Set.of(record));
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }
    }

    private record StringConstantGenerator(@NonNull Provider<String> valueProvider) implements Function<RecordIdFondPair, StringFieldValue> {

        @Override
        public StringFieldValue apply(RecordIdFondPair record) {
            return StringFieldValue.of(valueProvider.get(), Set.of(record));
        }

        @Override
        public String toString() {
            return valueProvider.get();
        }
    }

    private record BooleanConstantGenerator(@NonNull Boolean value) implements Function<RecordIdFondPair, BooleanFieldValue> {

        @Override
        public BooleanFieldValue apply(RecordIdFondPair record) {
            return BooleanFieldValue.of(value, Set.of(record));
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }
    }
}
