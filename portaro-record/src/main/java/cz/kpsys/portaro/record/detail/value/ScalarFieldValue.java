package cz.kpsys.portaro.record.detail.value;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.object.LabeledRecord;
import cz.kpsys.portaro.commons.object.ValuableRecord;
import cz.kpsys.portaro.record.RecordIdFondPair;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Set;

public sealed interface ScalarFieldValue<VALUE> extends FieldValue<VALUE>, LabeledRecord, ValuableRecord<VALUE> permits AcceptableValueFieldValue, BooleanFieldValue, InstantFieldValue, LocalDateFieldValue, NumberFieldValue, StringFieldValue {

    @Nullable
    String label();

    @NonNull
    @NotEmpty
    Set<RecordIdFondPair> origins();

    @JsonIgnore
    default VectorFieldValue<ScalarFieldValue<VALUE>, VALUE> asVector() {
        return new VectorFieldValue<>(List.of(this), text());
    }

}
