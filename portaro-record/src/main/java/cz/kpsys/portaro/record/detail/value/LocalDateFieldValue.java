package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.time.LocalDate;
import java.util.Optional;
import java.util.Set;

public record LocalDateFieldValue(

    @NonNull
    LocalDate value,

    @NonNull
    String label,

    @NonNull
    Text text,

    @NonNull
    @NotEmpty
    Set<RecordIdFondPair> origins

) implements ScalarFieldValue<LocalDate> {

    public static @NonNull LocalDateFieldValue of(@NonNull LocalDate date, @NonNull @NotEmpty Set<RecordIdFondPair> origins) {
        return new LocalDateFieldValue(
                date,
                LocalDateTypedValueConverter.formatLabel(date),
                Texts.ofNative(LocalDateTypedValueConverter.formatText(date)),
                origins
        );
    }

    public static Optional<LocalDate> extract(@NonNull Field<LocalDateFieldValue> field) {
        return field.typedValue(LocalDateFieldValue.class).map(LocalDateFieldValue::value);
    }


    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, LocalDateFieldValue.class, LocalDateFieldValue::value);
    }

    @Override
    public int hashCode() {
        return value().hashCode();
    }

}
