package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.util.Optional;
import java.util.Set;

public record BooleanFieldValue(

    @NonNull
    Boolean value,

    @NonNull
    String label,

    @NonNull
    Text text,

    @NonNull
    @NotEmpty
    Set<RecordIdFondPair> origins

) implements ScalarFieldValue<Boolean> {

    public static BooleanFieldValue of(@NonNull Boolean value, @NonNull @NotEmpty Set<RecordIdFondPair> origins) {
        return new BooleanFieldValue(value, value.toString(), Texts.ofBoolean(value), origins);
    }

    public static Optional<Boolean> extract(@NonNull Field<BooleanFieldValue> field) {
        return field.typedValue(BooleanFieldValue.class).map(BooleanFieldValue::value);
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, BooleanFieldValue.class, BooleanFieldValue::value);
    }

    @Override
    public int hashCode() {
        return value().hashCode();
    }

}
