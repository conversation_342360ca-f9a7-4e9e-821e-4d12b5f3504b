package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Labeled;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.Named;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.fn.FormulaEvaluation;
import cz.kpsys.portaro.record.detail.spec.RecordFieldTypeId;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.util.Optional;
import java.util.Set;

public record AcceptableValueFieldValue<VAL extends Identified<?>>(

    @NonNull
    VAL value,

    @NonNull
    String label,

    @NonNull
    Text text,

    @NonNull
    @NotEmpty
    Set<RecordIdFondPair> origins

) implements ScalarFieldValue<VAL> {

    public static <VAL extends Identified<?>> AcceptableValueFieldValue<VAL> ofGeneric(@NonNull VAL value, @NonNull @NotEmpty Set<RecordIdFondPair> origins) {
        String label = String.valueOf(value.getId());

        Text text = switch (value) {
            case Labeled labeled -> labeled.getText();
            case Named named -> Texts.ofNative(named.getName());
            default -> Texts.ofNative(label);
        };

        return new AcceptableValueFieldValue<>(value, label, text, origins);
    }

    public static @NonNull AcceptableValueFieldValue<LabeledIdentified<String>> ofStringIdentified(LabeledIdentified<String> value, @NonNull @NotEmpty Set<RecordIdFondPair> origins) {
        return new AcceptableValueFieldValue<>(
                value,
                value.getId(),
                value.getText(),
                origins
        );
    }

    public static <R extends Identified<?>> Optional<R> extract(@NonNull Field<AcceptableValueFieldValue<R>> field) {
        if (field.getValueHolder() == null) {
            return Optional.empty();
        }
        AcceptableValueFieldValue<?> fieldValue = ObjectUtil.cast(field.getValueHolder(), AcceptableValueFieldValue.class, () -> "Field " + field.fieldTypeId() + " has expected type " + AcceptableValueFieldValue.class.getSimpleName() + " but actual is " + field.getValueHolder().getClass().getSimpleName());
        R value = (R) fieldValue.value;
        return Optional.of(value);
    }

    public <ID> FormulaEvaluation<AcceptableValueFieldValue<Identified<ID>>> evaluateAssertIdType(Class<ID> idType, RecordFieldTypeId srcRecordFieldType, Formula<?> operand) {
        if (!idType.isInstance(value.getId())) {
            Text errorText = Texts.ofNative("Type of this acceptable value should be " + idType.getSimpleName() + " but is " + value.getId().getClass().getSimpleName() + " in " + operand);
            return FormulaEvaluation.failure(IncorrectTypeFail.of(errorText, srcRecordFieldType, operand, idType, value.getId().getClass()));
        }
        return FormulaEvaluation.success((AcceptableValueFieldValue<Identified<ID>>) this);
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, AcceptableValueFieldValue.class, AcceptableValueFieldValue::value);
    }

    @Override
    public int hashCode() {
        return value().hashCode();
    }
}
