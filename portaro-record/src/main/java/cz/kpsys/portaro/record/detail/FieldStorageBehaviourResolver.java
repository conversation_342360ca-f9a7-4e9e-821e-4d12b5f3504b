package cz.kpsys.portaro.record.detail;

import lombok.NonNull;

import static cz.kpsys.portaro.record.detail.FieldStorageBehaviour.CacheValidity.*;
import static cz.kpsys.portaro.record.detail.FieldStorageBehaviour.OriginStrategy.ALWAYS_SELF_ORIGIN;
import static cz.kpsys.portaro.record.detail.FieldStorageBehaviour.OriginStrategy.SELF_ORIGIN_FALLBACK_WHEN_MANY;
import static cz.kpsys.portaro.record.detail.FieldStorageBehaviour.RecordFieldStorage.*;
import static cz.kpsys.portaro.record.detail.FieldStorageBehaviour.RecordFieldValueStorage.*;

/// WARNING! When edithing this watch out for appserver lagging behind Portaro! Only master JARs are released for appserver!
public class FieldStorageBehaviourResolver {

    private static final FieldStorageBehaviour PHYSICAL                      = new FieldStorageBehaviour(ALWAYS,      VALUE_ALWAYS,      ALWAYS_VALID,     false, ALWAYS_SELF_ORIGIN,             true,  false);
    private static final FieldStorageBehaviour PHYSICAL_LINK_NO_VALUE        = new FieldStorageBehaviour(ALWAYS,      VALUE_NEVER,       ALWAYS_VALID,     false, SELF_ORIGIN_FALLBACK_WHEN_MANY, true,  false);
    private static final FieldStorageBehaviour PHYSICAL_LINK_GENERATED_VALUE = new FieldStorageBehaviour(ALWAYS,      VALUE_ONLY_CACHED, IF_CACHE_ENABLED, false, SELF_ORIGIN_FALLBACK_WHEN_MANY, true,  false);
    private static final FieldStorageBehaviour GENERATED_PHYSICAL            = new FieldStorageBehaviour(ALWAYS,      VALUE_ALWAYS,      ALWAYS_VALID,     false, ALWAYS_SELF_ORIGIN,             true,  true);
    private static final FieldStorageBehaviour GENERATED                     = new FieldStorageBehaviour(ONLY_CACHED, VALUE_ONLY_CACHED, IF_CACHE_ENABLED, true,  SELF_ORIGIN_FALLBACK_WHEN_MANY, false, true);
    private static final FieldStorageBehaviour GENERATED_NO_PERSIST          = new FieldStorageBehaviour(NEVER,       VALUE_NEVER,       NEVER_VALID,      true,  SELF_ORIGIN_FALLBACK_WHEN_MANY, false, true);
    private static final FieldStorageBehaviour GENERATED_PARENT              = new FieldStorageBehaviour(NEVER,       VALUE_NEVER,       NEVER_VALID,      false, SELF_ORIGIN_FALLBACK_WHEN_MANY, false, true);
    private static final FieldStorageBehaviour BOUND                         = new FieldStorageBehaviour(NEVER,       VALUE_NEVER,       NEVER_VALID,      false, ALWAYS_SELF_ORIGIN,             false, false);
    @Deprecated
    private static final FieldStorageBehaviour INDICATORS                    = new FieldStorageBehaviour(ALWAYS,      VALUE_ALWAYS,      ALWAYS_VALID,     false, ALWAYS_SELF_ORIGIN,             false, false);

    public @NonNull FieldStorageBehaviour resolve(@NonNull FieldType fieldType) {
        return resolve(fieldType.getGeneration());
    }

    public @NonNull FieldStorageBehaviour resolve(@NonNull FdefGeneration generation) {
        return switch (generation) {
            case PHYSICAL -> PHYSICAL;
            case PHYSICAL_LINK_NO_VALUE -> PHYSICAL_LINK_NO_VALUE;
            case PHYSICAL_LINK_GENERATED_VALUE -> PHYSICAL_LINK_GENERATED_VALUE;
            case GENERATED -> GENERATED;
            case GENERATED_PHYSICAL -> GENERATED_PHYSICAL;
            case GENERATED_NOPERSIST -> GENERATED_NO_PERSIST;
            case GENERATED_FIELD_PARENT -> GENERATED_PARENT;
            case BOUND -> BOUND;
            case INDICATORS -> INDICATORS;
        };
    }

    public static @NonNull FieldStorageBehaviour getDefaultBehaviour() {
        return PHYSICAL;
    }

    // TODO: should be read from fdef
    @Deprecated
    public static @NonNull FieldStorageBehaviour getCachedComplexAuthoritySubfieldBehaviour() {
        return GENERATED;
    }

    /// Appserver KAT1_1 storage expects complex auth subfields to be physical fields
    @Deprecated
    public static @NonNull FieldStorageBehaviour getAppserverComplexAuthLegacySubfieldBehaviour() {
        return PHYSICAL;
    }

}
