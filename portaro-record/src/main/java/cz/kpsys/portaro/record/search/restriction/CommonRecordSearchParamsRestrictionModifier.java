package cz.kpsys.portaro.record.search.restriction;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.detail.constraints.LinkConstraintsResolver;
import cz.kpsys.portaro.record.detail.spec.RecordIdFieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.field.StaticSearchFields;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.FalseRestriction;
import cz.kpsys.portaro.search.restriction.Not;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.*;
import cz.kpsys.portaro.search.restriction.modifier.RestrictionModifier;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.util.List;
import java.util.function.Function;

import static cz.kpsys.portaro.record.RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS;
import static cz.kpsys.portaro.record.search.restriction.FieldTypedSearchFieldParsing.DYNAMIC_FIELD_PREFIX;
import static cz.kpsys.portaro.search.field.StaticSearchFields.RECORD_STATUS;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CommonRecordSearchParamsRestrictionModifier implements RestrictionModifier<MapBackedParams> {

    @NonNull LinkConstraintsResolver linkConstraintsResolver;
    @NonNull Function<Fond, List<Fond>> enabledLoadableFondsExpander;

    @Override
    public Conjunction<SearchField> modify(Conjunction<SearchField> conjunction, MapBackedParams p, Department ctx) {
        Assert.state(!p.hasNotNull(CoreSearchParams.SUBKIND), () -> CoreSearchParams.SUBKIND + " parameter must be already expanded to fonds before conjunction modifier");
        Assert.state(!p.hasNotNull(RecordConstants.SearchParams.ROOT_FOND), () -> RecordConstants.SearchParams.ROOT_FOND + " parameter must be already expanded to fonds before conjunction modifier");

        if (!p.hasNotNull(CoreSearchParams.FINAL_RAW_QUERY)) {

            //document status
            conjunction.addIf(p.hasLength(FORBIDDEN_RECORD_STATUS), () -> new Not<>(new Term<>(RECORD_STATUS, new In(p.get(FORBIDDEN_RECORD_STATUS)))));

            //related record (napr. hledani dokumentu podle autority)
            conjunction.addIfHas(p, RecordConstants.SearchParams.RECORD_RELATED_RECORD, val -> new Term<>(StaticSearchFields.RECORD_RELATED_RECORD, new Eq(val)));

            //forbidden record ids
            conjunction.addIf(p.hasLength(RecordConstants.SearchParams.FORBIDDEN_RECORD), () -> new Not<>(new Term<>(StaticSearchFields.RECORD_ID, new In(p.get(RecordConstants.SearchParams.FORBIDDEN_RECORD)))));
        }

        conjunction.addIfHas(p, RecordConstants.SearchParams.RECORD, val -> new Term<>(StaticSearchFields.RECORD_ID, new In(val)));

        conjunction.addIfHas(p, CoreSearchParams.NAME, val -> new Term<>(StaticSearchFields.NAME, new EqWords(val)));

        conjunction.addIfHas(p, RecordConstants.SearchParams.PREFIX, val -> new Term<>(StaticSearchFields.NAME, new StartsWithWords(val)));

        if (p.hasAnyPrefix(DYNAMIC_FIELD_PREFIX)) {
            buildFieldRestrictions(p, conjunction);
        }

        if (p.hasNotNull(RecordConstants.SearchParams.CONSTRAINTS_RECORD_FIELD_TYPE)) {
            RecordIdFieldTypeId constraintsRecordFieldType = p.get(RecordConstants.SearchParams.CONSTRAINTS_RECORD_FIELD_TYPE);
            LinkConstraintsResolver.LinkConstraints constraints = linkConstraintsResolver.resolve(constraintsRecordFieldType, ctx);

            conjunction.addIf(constraints.forceReturnEmpty(), FalseRestriction::create);
            conjunction.addIfNotNull(constraints::fond, fond -> {
                List<Fond> loadableFonds = enabledLoadableFondsExpander.apply(fond);
                return new Term<>(StaticSearchFields.FOND, new In(loadableFonds));
            });
            conjunction.addIfNotNull(constraints::relatedRecordHeaders, relatedRecordHeaders -> new Term<>(StaticSearchFields.RECORD_RELATED_RECORD, new In(relatedRecordHeaders)));
            conjunction.addIfNotNull(constraints::restriction, restriction -> restriction);
        }

        return conjunction;
    }

    private <PARAMS extends MapBackedParams> void buildFieldRestrictions(PARAMS p, Conjunction<SearchField> conjunction) {
        List<MapBackedParams.Entry<Object>> dynamicSearchParams = p.getByPrefix(DYNAMIC_FIELD_PREFIX);
        for (MapBackedParams.Entry<Object> dynamicSearchParam : dynamicSearchParams) {
            var matcher = resolveDynamicParamMatcher(dynamicSearchParam);
            FieldTypedSearchFieldParsing parsing = FieldTypedSearchFieldParsing.parsePrefixedSuffixed(dynamicSearchParam.param().getId());
            conjunction.addTerm(parsing.toSearchField(), matcher);
        }
    }

    private @NonNull SearchMatcher resolveDynamicParamMatcher(MapBackedParams.Entry<Object> dynamicSearchParam) {
        if (dynamicSearchParam.value() == null) {
            return new IsNull();
        }
        if (dynamicSearchParam.value() instanceof String stringValue) {
            return new EqWords(stringValue);
        }
        return new Eq(dynamicSearchParam.value());
    }

}
