package cz.kpsys.portaro.record.detail.appservermarc;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.bool.StringToBooleanConverter;
import cz.kpsys.portaro.collection.ListCutter;
import cz.kpsys.portaro.commons.date.StringToInstantConverter;
import cz.kpsys.portaro.commons.date.StringToLocalDateConverter;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.marcxml.MarcConstants;
import cz.kpsys.portaro.number.StringToBigDecimalConverter;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.RecordIdentifier;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.detail.convert.MarcXmlToDetailConverter;
import cz.kpsys.portaro.record.detail.convert.RecordDetailFromMarcXmlConstructionException;
import cz.kpsys.portaro.record.detail.value.*;
import cz.kpsys.portaro.record.edit.AllPassingTestingFieldTypesByFondLoader;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.fond.FondTypeResolver;
import jakarta.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jdom2.DataConversionException;
import org.jdom2.Document;
import org.jdom2.Element;
import org.jdom2.Namespace;
import org.jdom2.input.SAXBuilder;
import org.jdom2.output.XMLOutputter;
import org.jspecify.annotations.Nullable;
import org.springframework.core.convert.converter.Converter;

import java.io.StringReader;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class MarcXmlToDetailConverterImpl implements MarcXmlToDetailConverter {

    boolean importMode;
    @NonNull FieldTypesByFondLoader editableFieldTypesByFondLoader;
    @NonNull StringToLocalDateConverter marcXmlSubfieldStringToLocalDateConverter = StringToLocalDateConverter.withoutIsoFallback(MarcConstants.MARCXML_LOCAL_DATE_FORMATTER);
    @NonNull StringToInstantConverter marcXmlSubfieldStringToInstantConverter;
    @NonNull Converter<String, BigDecimal> marcXmlSubfieldStringToNumberConverter = StringToBigDecimalConverter.INSTANCE;
    @NonNull Converter<@NonNull String, @NonNull Boolean> booleanConverter = StringToBooleanConverter.notAllowingNullSource();
    @NonNull AppserverMarcXmlDeserializer appserverMarcXmlDeserializer = new AppserverMarcXmlDeserializer();
    @NonNull ByIdLoadable<Fond, Integer> fondLoader;

    @NonNull
    public static MarcXmlToDetailConverterImpl testing(Fond fond) {
        return testing(fond, new TestingFieldTypeLoader());
    }

    @NonNull
    public static MarcXmlToDetailConverterImpl testing(Fond fond, TestingFieldTypeLoader fieldTypeLoader) {
        return new MarcXmlToDetailConverterImpl(
                false,
                new AllPassingTestingFieldTypesByFondLoader(fieldTypeLoader),
                StringToInstantConverter.withoutIsoFallback(StaticProvider.of(ZoneId.of("Europe/Prague")), DateTimeFormatter.ofPattern("yyyyMMddHHmmss")),
                _ -> fond
        );
    }


    @Override
    public IdentifiedFieldContainer convertSingle(@NonNull String xmlString) throws RecordDetailFromMarcXmlConstructionException {
        try {
            Document xmlDocument = new SAXBuilder().build(new StringReader(xmlString));
            return convertMultipleRecords(marcRecord -> fondLoader.getById(marcRecord.getFondId().orElseThrow()), xmlDocument).stream().findFirst().orElseThrow();

        } catch (Exception ex) {
            throw new RecordDetailFromMarcXmlConstructionException(xmlString, ex);
        }
    }

    @Override
    public List<IdentifiedFieldContainer> convertMultipleRecords(@NonNull Function<RecordAppserverMarcResponse, Fond> recordFondResolver, @NonNull Document xmlDocument) throws RecordDetailFromMarcXmlConstructionException {
        //===================VYTAZENI DETAILU Z XML=============================
        Namespace ns = xmlDocument.getRootElement().getNamespace();
        List<Element> recordElems = xmlDocument.getRootElement().getChildren(AppserverMarcXmlDeserializer.RECORD, ns);

        return ListUtil.convert(recordElems, recordElem -> {
            try {
                return mapSingleRecordElementToDetail(recordFondResolver, recordElem, ns);

            } catch (DataConversionException e) {
                XMLOutputter outputter = new XMLOutputter();
                String xmlString = outputter.outputString(recordElems);
                throw new RecordDetailFromMarcXmlConstructionException(xmlString, e);
            }
        });
    }


    private IdentifiedFieldContainer mapSingleRecordElementToDetail(@NonNull Function<RecordAppserverMarcResponse, Fond> recordFondResolver, Element recordElem, Namespace ns) throws DataConversionException {
        RecordAppserverMarcResponse marcRecord = appserverMarcXmlDeserializer.deserialize(recordElem, ns);
        RecordIdentifier recordIdentifier = RecordIdentifier.of(marcRecord.getId());
        Fond fond = recordFondResolver.apply(marcRecord);
        RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(recordIdentifier, fond);
        return new Assembler(recordIdFondPair, marcRecord, FondTypeResolver.isAuthorityFond(fond), editableFieldTypesByFondLoader.toStaticLoader(fond)).createDetailFromMarcResponse();
    }




    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private class Assembler {

        @NonNull RecordIdFondPair recordIdFondPair;
        @NonNull RecordAppserverMarcResponse marcRecord;
        boolean recordIsAuthority;
        @NonNull FieldTypesByFondLoader.StaticFondLoader staticFondFieldTypeLoader;

        private @NonNull IdentifiedFieldContainer createDetailFromMarcResponse() {
            FondedIdentifiedFieldContainer container = new FondedIdentifiedFieldContainer(recordIdFondPair);

            processLeader(marcRecord.getLeader(), container);
            processToc(marcRecord.getToc(), container);
            processControlfields(marcRecord.getControlfields(), container);
            processDatafields(marcRecord.getDatafields(), container);

            return container;
        }

        private void processLeader(LeaderAppserverMarcResponse leader, FieldContainer container) {
            EditableFieldType fieldType = staticFondFieldTypeLoader.getTopfieldTypeById(recordIsAuthority ? FieldTypes.AUTHORITY_LEADER_FIELD_TYPE_ID : FieldTypes.DOCUMENT_LEADER_FIELD_TYPE_ID);
            FieldPayload<?> payload = getPayload(leader.value(), fieldType, null, leader);
            container.add(fieldType.createFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier(), payload));
        }

        private void processToc(Optional<TocAppserverMarcResponse> toc, FieldContainer container) {
            if (toc.isPresent()) {
                EditableFieldType tocFieldType = staticFondFieldTypeLoader.getTopfieldTypeById(FieldTypes.TOC_FIELD_TYPE_ID);
                Field<?> tocField = tocFieldType.createEmptyFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier());
                container.add(tocField);

                {
                    EditableFieldType tocContentFieldType = tocFieldType.getSubfieldTypeFor(FieldTypes.TOC_CONTENT_FIELD_TYPE_ID);
                    FieldPayload<?> payload = getPayload(toc.get().value(), tocContentFieldType, null, toc.get());
                    tocField.add(tocContentFieldType.createFieldByParentId(recordIdFondPair, tocField.getFieldId(), UuidGenerator.forIdentifier(), payload));
                }

                if (toc.get().pdf() != null) {
                    EditableFieldType tocUrlFieldType = tocFieldType.getSubfieldTypeFor(FieldTypes.TOC_URL_FIELD_TYPE_ID);
                    FieldPayload<?> payload = getPayload(toc.get().pdf(), tocUrlFieldType, null, toc.get());
                    tocField.add(tocUrlFieldType.createFieldByParentId(recordIdFondPair, tocField.getFieldId(), UuidGenerator.forIdentifier(), payload));
                }
            }
        }

        private void processControlfields(List<ControlfieldAppserverMarcResponse> controlfields, FieldContainer container) {
            for (ControlfieldAppserverMarcResponse marcControlfield : controlfields) {
                EditableFieldType fieldType = staticFondFieldTypeLoader.getTopfieldTypeById(FieldTypeId.recordField(recordIsAuthority, marcControlfield.number()));

                if (isAllowed(fieldType)) {
                    FieldPayload<?> payload = getPayload(marcControlfield.value(), fieldType, null, marcControlfield);
                    container.add(fieldType.createFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier(), payload));
                }
            }
        }

        private void processDatafields(List<DatafieldAppserverMarcResponse> datafields, FieldContainer container) {
            for (DatafieldAppserverMarcResponse marcDatafield : datafields) {
                EditableFieldType fieldType = staticFondFieldTypeLoader.getTopfieldTypeById(FieldTypeId.recordField(recordIsAuthority, marcDatafield.number()));

                if (!fieldType.hasAutonomousSubfieldTypes()) {
                    log.warn("Field {} in given XML is datafield but it has no autonomous subfields in FDEF (field {}, defined field type {})", marcDatafield.number(), marcDatafield, fieldType);
                }

                if (isAllowed(fieldType)) {
                    Field<?> datafield = fieldType.createEmptyFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier());
                    container.add(datafield);

                    processInd(fieldType, datafield, FieldTypes.IND_1_FIELD_CODE, marcDatafield.ind1());
                    processInd(fieldType, datafield, FieldTypes.IND_2_FIELD_CODE, marcDatafield.ind2());
                    processSubfields(ListCutter.ofCopyOf(marcDatafield.subfields()), fieldType, datafield);
                }
            }
        }

        private void processInd(@NonNull EditableFieldType parentFieldType, @NonNull Field<?> datafield, @NonNull String fieldCode, @NonNull @NotEmpty String indValue) {
            if (indValue.equals(IndicatorType.STANDARD_EMPTY_IND)) {
                return;
            }
            EditableFieldType indFieldType = parentFieldType.getSubfieldTypeOrParentVirtualGroupTypeFor(fieldCode);
            FieldPayload<?> payload = getPayload(indValue, indFieldType, null, indFieldType.getFieldTypeId());
            datafield.add(indFieldType.createFieldByParentId(recordIdFondPair, datafield.getFieldId(), UuidGenerator.forIdentifier(), payload));
        }

        private void processSubfields(@NonNull ListCutter<SubfieldAppserverMarcResponse> subfieldsCutter, @NonNull EditableFieldType parentFieldType, @NonNull Field<?> container) {
            while (subfieldsCutter.hasRemainingItems()) {
                SubfieldAppserverMarcResponse marcSubfield = subfieldsCutter.cutFirst();
                EditableFieldType fieldType = parentFieldType.getSubfieldTypeOrParentVirtualGroupTypeFor(marcSubfield.code());

                if (fieldType.isVirtualGroup()) {
                    List<? extends FieldType> groupedFieldTypes = fieldType.getSubfieldTypes();
                    boolean groupOfComplexRecordLinkFields = FieldType.areAllgroupedFieldTypesOfSameComplexRecordLink(groupedFieldTypes, fieldType);
                    List<SubfieldAppserverMarcResponse> siblingSubfields = ListUtil.createNewListPrepending(
                            marcSubfield,
                            subfieldsCutter.cut(subfield -> groupedFieldTypes.stream().anyMatch(groupedFieldType -> subfield.code().equals(groupedFieldType.getCode())))
                    );

                    Field<?> groupField = fieldType.createEmptyFieldByParentId(recordIdFondPair, container.getFieldId(), UuidGenerator.forIdentifier());
                    container.add(groupField);
                    if (groupOfComplexRecordLinkFields) {
                        RecordIdFondPair recordLink = getRecordLinkIfExists(siblingSubfields.getFirst());
                        if (recordLink != null) { // appserver in some cases returns authority field without authority (e.g., imported ereading books (https://katalog.kfbz.cz/records/51f0b26b-f267-4090-aca9-0d0ce0126d31))
                            groupField.setRecordLink(recordLink);
                        }
                    }
                    processSubfields(ListCutter.ofCopyOf(siblingSubfields), fieldType, groupField);
                    if (groupField.isEmpty()) {
                        container.remove(groupField);
                    }
                } else if (isAllowed(fieldType)) {
                    FieldPayload<?> payload = getPayload(marcSubfield.value(), fieldType, getRecordLinkIfExists(marcSubfield), marcSubfield);
                    container.add(fieldType.createFieldByParentId(recordIdFondPair, container.getFieldId(), UuidGenerator.forIdentifier(), payload));
                }
            }
        }

        @Nullable
        private RecordIdFondPair getRecordLinkIfExists(@NonNull SubfieldAppserverMarcResponse marcSubfield) {
            return ObjectUtil.elvis(marcSubfield.recordId(), id -> {
                Fond fond = fondLoader.getById(Objects.requireNonNull(marcSubfield.fondId()));
                return RecordIdFondPair.of(id, fond);
            });
        }

        @NonNull
        private FieldPayload<?> getPayload(@NonNull String stringValue, @NonNull FieldType fieldType, @Nullable RecordIdFondPair recordLink, Object fieldIdentifierForLog) {
            ScalarFieldValue<?> fieldValue = getFieldValue(stringValue, fieldType, Set.of(recordIdFondPair), fieldIdentifierForLog); // TODO: origin_id is not supported yet for loading from XML, so we will set current recordd
            return FieldPayload.of(fieldValue, recordLink);
        }

        private @NonNull ScalarFieldValue<?> getFieldValue(@NonNull String stringValue, @NonNull FieldType fieldType, @NonNull @NotEmpty Set<RecordIdFondPair> origins, Object fieldIdentifierForLog) {
            try {
                if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.DATE)) {
                    LocalDate localDate = Objects.requireNonNull(marcXmlSubfieldStringToLocalDateConverter.convert(stringValue));
                    return LocalDateFieldValue.of(localDate, origins);
                }
                if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.DATETIME)) {
                    Instant instant = Objects.requireNonNull(marcXmlSubfieldStringToInstantConverter.convert(stringValue));
                    return InstantFieldValue.of(instant, origins);
                }
                if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.BOOLEAN)) {
                    return BooleanFieldValue.of(Objects.requireNonNull(booleanConverter.convert(stringValue)), origins);
                }
                if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER) || fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_2)) {
                    BigDecimal number = Objects.requireNonNull(marcXmlSubfieldStringToNumberConverter.convert(stringValue));
                    return NumberFieldValue.of(number, origins);
                }
                if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_4)) {
                    throw new UnsupportedOperationException("Number with precision 4 is not supported");
                }
                if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_6)) {
                    throw new UnsupportedOperationException("Number with precision 6 is not supported");
                }
                if (fieldType.getCodebook().isPresent()) {
                    LabeledIdentified<String> acceptableValue = fieldType.getCodebook().get().getById(stringValue);
                    return AcceptableValueFieldValue.ofStringIdentified(acceptableValue, origins);
                }
            } catch (Exception e) {
                //napr. pri importu nemuseji typy souhlasit
                log.warn("Incorrect format of field value (field {}, value {})", fieldIdentifierForLog, stringValue, e);
            }
            return StringFieldValue.of(stringValue, origins);
        }

        private boolean isAllowed(FieldType type) {
            return !importMode || EnumSet.of(TransferType.ADD, TransferType.OVERWRITE, TransferType.REWRITE_WHEN_CREATING).contains(type.getTransferType());
        }

    }

}
