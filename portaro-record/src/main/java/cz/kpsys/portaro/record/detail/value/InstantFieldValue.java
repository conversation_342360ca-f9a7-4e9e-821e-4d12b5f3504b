package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.time.Instant;
import java.util.Set;

public record InstantFieldValue(

    @NonNull
    Instant value,

    @NonNull
    String label,

    @NonNull
    Text text,

    @NonNull
    @NotEmpty
    Set<RecordIdFondPair> origins

) implements ScalarFieldValue<Instant> {

    public static @NonNull InstantFieldValue of(@NonNull Instant date, @NonNull @NotEmpty Set<RecordIdFondPair> origins) {
        return new InstantFieldValue(
                date,
                InstantTypedValueConverter.formatLabel(date),
                Texts.ofNative(InstantTypedValueConverter.formatText(date)),
                origins
        );
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, InstantFieldValue.class, InstantFieldValue::value);
    }

    @Override
    public int hashCode() {
        return value().hashCode();
    }

}
