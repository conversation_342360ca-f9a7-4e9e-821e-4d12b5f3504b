package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.RecordIdentifier;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.detail.spec.RecordSpec;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;
import cz.kpsys.portaro.record.edit.AllPassingTestingFieldTypesByFondLoader;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import cz.kpsys.portaro.record.edit.StaticRecordEntryFieldTypeIdResolver;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;
import java.util.function.Consumer;

@Tag("ci")
@Tag("unit")
class LoadedRecordsTest {

    @Test
    void singleLoadedControlfieldShouldNotRequireNextSearch() {
        FieldsTester fieldsTester = new FieldsTester();
        RecordIdentifier recordIdentifier = RecordIdentifier.of(UUID.fromString("7bf719d4-ae64-45ed-bb08-35ef8bd9e37b"));
        RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(recordIdentifier, Fond.testingPerson());

        List<Field<?>> loadedFields = List.of(
                fieldsTester.scalar(recordIdFondPair, "a1#1", "a1", StringFieldValue.testing("kpw5057178", recordIdFondPair))
        );

        LoadedRecords loadedRecords = LoadedRecords.empty(fieldsTester.fieldTypesByFondLoader, new StaticRecordEntryFieldTypeIdResolver());
        loadedRecords.addLoadedFields(loadedFields);
        RecordSpecSet nextSpecs = loadedRecords.updateLoadedAndGetNextSpecs(RecordSpecSet.of(RecordSpec.ofWholeRecord(recordIdFondPair)));
        Assertions.assertEquals(RecordSpecSet.ofEmptyUnmodifiable(), nextSpecs);
    }

    @Test
    void twoSubfieldsOfSameTypeShouldNotThrowException() {
        FieldsTester fieldsTester = new FieldsTester();
        RecordIdentifier recordIdentifier = RecordIdentifier.of(UUID.fromString("7bf719d4-ae64-45ed-bb08-35ef8bd9e37b"));
        RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(recordIdentifier, Fond.testingPerson());

        List<Field<?>> loadedFields = List.of(
                fieldsTester.subfielded(recordIdFondPair, "d41#0", "d41", List.of(
                        fieldsTester.scalar(recordIdFondPair, "d41#0.a#0", "d41.a", StringFieldValue.testing("cze", recordIdFondPair)),
                        fieldsTester.scalar(recordIdFondPair, "d41#0.a#1", "d41.a", StringFieldValue.testing("eng", recordIdFondPair))
                ))
        );

        LoadedRecords loadedRecords = LoadedRecords.empty(fieldsTester.fieldTypesByFondLoader, new StaticRecordEntryFieldTypeIdResolver());
        loadedRecords.addLoadedFields(loadedFields);
        RecordSpecSet nextSpecs = loadedRecords.updateLoadedAndGetNextSpecs(RecordSpecSet.of(RecordSpec.ofWholeRecord(recordIdFondPair)));
        Assertions.assertEquals(RecordSpecSet.ofEmptyUnmodifiable(), nextSpecs);
    }

    @Test
    void singleLoadedSubfieldShouldNotRequireNextSearch() {
        FieldsTester fieldsTester = new FieldsTester();
        RecordIdentifier recordIdentifier = RecordIdentifier.of(UUID.fromString("7bf719d4-ae64-45ed-bb08-35ef8bd9e37b"));
        RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(recordIdentifier, Fond.testingPerson());

        List<Field<?>> loadedFields = List.of(
                fieldsTester.subfielded(recordIdFondPair, "a150#1", "a150", List.of(
                        fieldsTester.scalar(recordIdFondPair, "a150#1.a#0", "a150.a", StringFieldValue.testing("kreslený humor", recordIdFondPair))
                ))
        );

        LoadedRecords loadedRecords = LoadedRecords.empty(fieldsTester.fieldTypesByFondLoader, new StaticRecordEntryFieldTypeIdResolver());
        fieldsTester.withCustomFields(fieldTypes -> {
            fieldTypes.withCustomDatafieldType("a150", "a150");
            fieldTypes.withCustomDatafieldType("a151", "a151");
        });
        loadedRecords.addPrimary(recordIdFondPair);
        loadedRecords.addLoadedFields(loadedFields);

        RecordSpecSet nextSpecs = loadedRecords.updateLoadedAndGetNextSpecs(RecordSpecSet.of(RecordSpec.ofWholeRecord(recordIdFondPair)));
        Assertions.assertEquals(RecordSpecSet.ofEmptyUnmodifiable(), nextSpecs);
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class FieldsTester {

        private final TestingFieldTypeLoader fieldTypeLoader = new TestingFieldTypeLoader();
        @NonNull AllPassingTestingFieldTypesByFondLoader fieldTypesByFondLoader = new AllPassingTestingFieldTypesByFondLoader(fieldTypeLoader);
        @NonNull FieldTypesByFondLoader.StaticFondLoader fondedFieldTypeLoader = fieldTypesByFondLoader.toStaticLoader(Fond.testingPerson());

        public FieldsTester withCustomFields(@NonNull Consumer<TestingFieldTypeLoader> loaderConsumer) {
            loaderConsumer.accept(fieldTypeLoader);
            return this;
        }

        public EditableFieldType getFieldType(@NonNull String fieldTypeId) {
            return fondedFieldTypeLoader.getById(FieldTypeId.parse(fieldTypeId));
        }

        public @NonNull Field<StringFieldValue> scalar(@NonNull RecordIdFondPair recordIdFondPair,
                                                       @NonNull String id,
                                                       @NonNull String fieldTypeId,
                                                       @NonNull StringFieldValue fieldValueHolder) {
            return FieldFactory.scalar(RecordFieldId.of(recordIdFondPair, FieldId.parse(id)), getFieldType(fieldTypeId), fieldValueHolder);
        }

        public @NonNull Field<ScalarFieldValue<?>> subfielded(@NonNull RecordIdFondPair recordIdFondPair,
                                                              @NonNull String id,
                                                              @NonNull String fieldTypeId,
                                                              @NonNull List<@NonNull Field<?>> fields) {
            return FieldFactory.subfielded(RecordFieldId.of(recordIdFondPair, FieldId.parse(id)), getFieldType(fieldTypeId), fields);
        }

        public @NonNull Field<ScalarFieldValue<?>> linked(@NonNull RecordIdFondPair recordIdFondPair,
                                                          @NonNull String id,
                                                          @NonNull String fieldTypeId,
                                                          @NonNull RecordIdFondPair recordLink) {
            return FieldFactory.linked(RecordFieldId.of(recordIdFondPair, FieldId.parse(id)), getFieldType(fieldTypeId), recordLink);
        }
    }

}
