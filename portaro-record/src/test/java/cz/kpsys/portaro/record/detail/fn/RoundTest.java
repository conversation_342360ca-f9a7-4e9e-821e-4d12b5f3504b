package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.value.NumberFieldValue;
import cz.kpsys.portaro.record.fond.Fond;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Tag("ci")
@Tag("unit")
class RoundTest {

    @ParameterizedTest
    @CsvSource(value = {
            "10.004, 10.00",
            "10.005, 10.01",
            "10.4, 10.40",
            "10.5, 10.50",
            "10, 10.00",
    })
    void shouldCompute(String input, String expected) {
        Round function = Formulas.round2(Formulas.constDecimal2(BigDecimal.valueOf(0))); // hodnota operatoru tady neni du<PERSON>, tak<PERSON> nastavujeme klidne nesmyslnou 0
        FormulaEvaluation<NumberFieldValue> computed = function.compute(NumberFieldValue.testing(new BigDecimal(input), RecordIdFondPair.of("1b923dba-3700-468a-b379-30a1c8a4a7a8", Fond.testingMonography())));
        BigDecimal actual = computed.existingSuccess().value();
        assertEquals(new BigDecimal(expected), actual);
    }
}
