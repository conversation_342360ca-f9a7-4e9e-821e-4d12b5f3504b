package cz.kpsys.portaro.user.category.resolvers;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.util.DateUtils;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.TrustedUserAndSourcePredicate;
import cz.kpsys.portaro.user.category.AutomaticReaderCategoryResolver;
import cz.kpsys.portaro.user.category.ResolvedReaderCategory;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RetireeAutomaticReaderCategoryResolver implements AutomaticReaderCategoryResolver {

    @NonNull ContextualProvider<Department, @Nullable Integer> retireeAgeLimitProvider;
    @NonNull ContextualProvider<Department, @NonNull ReaderCategory> retireeReaderCategoryProvider;
    @NonNull TrustedUserAndSourcePredicate trustedUserAndSourcePredicate;

    @Override
    public @NonNull ResolvedReaderCategory resolve(@NonNull ResolvedReaderCategory resolvedReaderCategory, @NonNull Department ctx, @NonNull Person person) {
        Integer age = DateUtils.ageFromOrNull(person.getBirthDate());
        if (ResolversHelper.isOlderOrSameAsLimit(age, retireeAgeLimitProvider.getOn(ctx)) && birthDateIsFromTrustedSource(ctx, person)) {
            ReaderCategory readerCategory = retireeReaderCategoryProvider.getOn(ctx);
            log.info("Resolve new reader category to {} for person {} because of age {}", readerCategory, person.getId(), age);
            return ReaderRole.hasCategory(person, retireeReaderCategoryProvider.getOn(ctx)) ? ResolvedReaderCategory.currentReaderCategory(readerCategory) : ResolvedReaderCategory.newReaderCategory(readerCategory);
        }
        return resolvedReaderCategory;
    }

    private boolean birthDateIsFromTrustedSource(Department ctx, Person targetPerson) {
        return trustedUserAndSourcePredicate.isTrustedSource(targetPerson.getLifeDateSource(), ctx);
    }
}
