package cz.kpsys.portaro.user.category;

import cz.kpsys.portaro.commons.object.NonNullOrderedRecord;
import cz.kpsys.portaro.commons.object.Ordered;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.Person;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Predicate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CompositeAutomaticReaderCategoryResolver implements AutomaticReaderCategoryResolver {

    @NonNull List<ConditionedAutomaticReaderCategoryResolver> resolvers = new CopyOnWriteArrayList<>();

    public CompositeAutomaticReaderCategoryResolver add(@NonNull AutomaticReaderCategoryResolver resolver, Integer order) {
        ifAdd(_ -> true, resolver, order);
        return this;
    }

    public CompositeAutomaticReaderCategoryResolver ifAdd(@NonNull Predicate<Department> condition, @NonNull AutomaticReaderCategoryResolver resolver, Integer order) {
        resolvers.add(new ConditionedAutomaticReaderCategoryResolver(condition, resolver, order));
        resolvers.sort(Ordered.COMPARATOR);
        return this;
    }

    @Override
    public @NonNull ResolvedReaderCategory resolve(@NonNull ResolvedReaderCategory resolvedReaderCategory, @NonNull Department ctx, @NonNull Person user) {
        ResolvedReaderCategory category = resolvedReaderCategory;
        List<AutomaticReaderCategoryResolver> activeResolvers = resolvers.stream().filter(resolver -> resolver.condition().test(ctx)).map(ConditionedAutomaticReaderCategoryResolver::resolver).toList();
        for (AutomaticReaderCategoryResolver resolver : activeResolvers) {
            ResolvedReaderCategory resolvedCategory = resolver.resolve(category, ctx, user);
            switch (resolvedCategory) {
                case NewReaderCategory newReaderCategory -> category = newReaderCategory;
                case CurrentReaderCategory currentReaderCategory -> {
                    category = currentReaderCategory;
                }
                case TerminateResolving terminateResolving -> {
                    return terminateResolving;
                }
            }
        }
        return category;
    }

    private record ConditionedAutomaticReaderCategoryResolver(
            @NonNull Predicate<Department> condition,
            @NonNull AutomaticReaderCategoryResolver resolver,
            @NonNull Integer order
    ) implements NonNullOrderedRecord {

    }
}