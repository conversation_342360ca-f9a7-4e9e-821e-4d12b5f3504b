package cz.kpsys.portaro.user.category.resolvers;


import cz.kpsys.portaro.user.category.CurrentReaderCategory;
import cz.kpsys.portaro.user.category.NewReaderCategory;
import cz.kpsys.portaro.user.category.ResolvedReaderCategory;
import cz.kpsys.portaro.user.category.TerminateResolving;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import org.jspecify.annotations.Nullable;

import java.util.Objects;

public class ResolversHelper {

    public static boolean isOlderOrSameAsLimit(@Nullable Integer age, @Nullable Integer limit) {
        if (age == null || limit == null) {
            return false;
        }
        return age >= limit;
    }

    public static boolean isYounger(@Nullable Integer age, @Nullable Integer limit) {
       return !isOlderOrSameAsLimit(age, limit);
    }

    public static boolean isYoungerOrSameAsLimit(@Nullable Integer age, @Nullable Integer limit) {
        if (age == null || limit == null) {
            return false;
        }
        return age <= limit;
    }

    public static boolean hasReaderCategory(ResolvedReaderCategory resolvedReaderCategory, ReaderCategory readerCategory) {
        return switch (resolvedReaderCategory) {
            case CurrentReaderCategory currentReaderCategory -> Objects.equals(currentReaderCategory.category(), readerCategory);
            case NewReaderCategory newReaderCategory -> Objects.equals(newReaderCategory.newCategory(), readerCategory);
            case TerminateResolving _ -> false;
        };
    }
}
