package cz.kpsys.portaro.user.category.resolvers;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.util.DateUtils;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.category.AutomaticReaderCategoryResolver;
import cz.kpsys.portaro.user.category.ResolvedReaderCategory;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ZtpAutomaticReaderCategoryResolver implements AutomaticReaderCategoryResolver {

    @NonNull ContextualProvider<Department, @Nullable Integer> childAgeLimitProvider;
    @NonNull ContextualProvider<Department, @Nullable ReaderCategory> childReaderCategoryProvider;
    @NonNull ContextualProvider<Department, @Nullable ReaderCategory> childZtpReaderCategoryProvider;
    @NonNull ContextualProvider<Department, @Nullable ReaderCategory> ztpReaderCategoryProvider;
    @NonNull AutomaticReaderCategoryResolver genericZtpAutomaticReaderCategoryResolver;

    @Override
    public @NonNull ResolvedReaderCategory resolve(@NonNull ResolvedReaderCategory resolvedReaderCategory, @NonNull Department ctx, @NonNull Person person) {
        Integer age = DateUtils.ageFromOrNull(person.getBirthDate());

        boolean isYoungerThenLimit = ResolversHelper.isYounger(age, childAgeLimitProvider.getOn(ctx));
        boolean personHasChildrenReaderCategory = ResolversHelper.hasReaderCategory(resolvedReaderCategory, childReaderCategoryProvider.getOn(ctx));
        ResolvedReaderCategory resolve = genericZtpAutomaticReaderCategoryResolver.resolve(resolvedReaderCategory, ctx, person);
        boolean personIsZTP = ResolversHelper.hasReaderCategory(resolve, ztpReaderCategoryProvider.getOn(ctx));
        if (personIsZTP && personHasChildrenReaderCategory && isYoungerThenLimit && childZtpReaderCategoryProvider.getOn(ctx) != null) {
            return ResolvedReaderCategory.newReaderCategory(childZtpReaderCategoryProvider.getOn(ctx));
        }
        return resolve;
    }
}
