package cz.kpsys.portaro.proveniencesmap;

import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.maps.Coordinates;
import cz.kpsys.portaro.maps.GoogleMapsGeocoder;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.TestingFondedFieldTypeFactory;
import cz.kpsys.portaro.record.detail.value.FieldPayload;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;
import cz.kpsys.portaro.record.fond.Fond;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import static cz.kpsys.portaro.record.detail.FieldTypeId.top;
import static org.junit.jupiter.api.Assertions.assertEquals;

@Tag("ci")
@Tag("unit")
public class CoordinatesFromFieldFinderTest {

    public static final String GPS_COORD_STRING = "54°42'56.4\"N 20°30'50.9\"E";
    public static final Coordinates COORDINATES = new Coordinates(54.715667, 20.514139);

    Field<?> fa370;


    @BeforeEach
    public void prepare() {
        Fond fond = Fond.testingPerson();
        RecordIdFondPair recordIdFondPair = RecordIdFondPair.of("d43b987d-65c3-491c-8a73-beddd5caa598", fond);
        TestingFondedFieldTypeFactory fieldTypeFactory = TestingFondedFieldTypeFactory.forFond(fond);

        FieldTypeId a370 = top("a370");
        fa370 = fieldTypeFactory.datafieldType(a370).createEmptyFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier());
        fa370.add(fieldTypeFactory.standardSubfieldType(a370.sub("c")).createFieldByParentId(recordIdFondPair, fa370.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.testing("Česká republika", recordIdFondPair))));
        fa370.add(fieldTypeFactory.standardSubfieldType(a370.sub("f")).createFieldByParentId(recordIdFondPair, fa370.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.testing("Český Krumlov", recordIdFondPair))));
        fa370.add(fieldTypeFactory.standardSubfieldType(a370.sub("f")).createFieldByParentId(recordIdFondPair, fa370.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.testing(GPS_COORD_STRING, recordIdFondPair))));
    }

    @Test
    public void testConvert() {
        CoordinatesFromFieldFinder finder = new CoordinatesFromFieldFinder(new GoogleMapsGeocoder());
        Coordinates actual = finder.find(fa370);
        assertEquals(COORDINATES, actual);
    }
}
