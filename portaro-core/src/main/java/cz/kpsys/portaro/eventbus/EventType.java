package cz.kpsys.portaro.eventbus;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
@Getter
public enum EventType implements Identified<String> {
    INITIALIZE("initialize"),
    NEW_MESSAGE("new-message"),
    MENTION("mention"),
    PARTICIPANT_ADDED("participant-added"),
    PARTICIPANT_REMOVED("participant-removed"),
    THREAD_DELETED("thread-deleted"),
    THREAD_CREATED("thread-created"),
    USER_JOINED("user-joined"),
    USER_LEAVED("user-leaved");

    public static final Codebook<EventType, String> CODEBOOK = new StaticCodebook<>(values());

    @NonNull String id;

    @Override
    public String toString() {
        return id;
    }
}
