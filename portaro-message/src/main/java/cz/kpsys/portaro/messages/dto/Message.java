package cz.kpsys.portaro.messages.dto;

import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.messages.constants.MessageSeverity;
import cz.kpsys.portaro.messages.constants.MessageTopic;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.BasicUser;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.time.Instant;
import java.util.UUID;

public sealed interface Message extends IdentifiedRecord<UUID> permits UserToThreadMessage, UserToUserMessage {

    @NonNull
    String content();

    @NonNull
    MessageTopic topic();

    @NonNull
    MessageSeverity severity();

    @NonNull
    BasicUser senderUser();

    @NonNull
    Department department();

    @NonNull
    Boolean confirmationNecessary();

    @Nullable
    ContentType contentType();

    @Nullable
    Integer directoryId();

    @NonNull
    Instant creationDate();

    @Nullable
    Instant activationDate();

    Message withActivationDate(Instant activationDate);

    static UserToUserMessage ofUserToUser(
            UUID id,
            String content,
            MessageTopic topic,
            MessageSeverity severity,
            BasicUser senderUser,
            BasicUser targetUser,
            Department department,
            Boolean confirmationNecessary,
            ContentType contentType,
            Integer directoryId,
            Instant creationDate,
            Instant activationDate
    ) {
        return new UserToUserMessage(
                id, content, topic, severity, senderUser,
                targetUser, department, confirmationNecessary,
                contentType, directoryId, creationDate, activationDate
        );
    }

    static UserToThreadMessage ofUserToThread(
            UUID id,
            String content,
            MessageTopic topic,
            MessageSeverity severity,
            BasicUser senderUser,
            Record thread,
            Department department,
            Boolean confirmationNecessary,
            ContentType contentType,
            Integer directoryId,
            Instant creationDate,
            Instant activationDate
    ) {
        return new UserToThreadMessage(
                id, content, topic, severity, senderUser,
                thread, department, confirmationNecessary,
                contentType, directoryId, creationDate, activationDate
        );
    }
}
