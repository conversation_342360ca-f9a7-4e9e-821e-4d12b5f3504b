package cz.kpsys.portaro.messages.constants;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.property.JavatypedDatatypedProperty;
import cz.kpsys.portaro.property.PropertyFactory;
import cz.kpsys.portaro.search.SearchParamsConstants;
import cz.kpsys.portaro.user.BasicUser;
import org.springframework.core.convert.TypeDescriptor;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

public class MessageConstants {

    public static final String SMS_URL = "/api/sms";
    public static final String THREAD_BY_ID_URL = "/api/threads/{id}";
    public static final String THREAD_BY_RECORD_URL = "/api/threads/record/{recordId}";
    public static final String USER_THREADS_URL = "/api/threads/user/{user}";
    public static final String USER_READ_ALL_THREAD_MESSAGES_URL = "/api/threads/read-all-messages";
    public static final String THREADS_CREATE_URL = "/api/threads/create";
    public static final String THREADS_LEAVE_URL = "/api/threads/leave";
    public static final String THREADS_JOIN_URL = "/api/threads/join";
    public static final String THREADS_ADD_PARTICIPANTS_URL = "/api/threads/add-participants";
    public static final String THREADS_REMOVE_PARTICIPANTS_URL = "/api/threads/remove-participants";
    public static final String THREADS_UPDATE_URL = "/api/threads/update";
    public static final String THREADS_SEND_MESSAGE_URL = "/api/threads/send";
    public static final String THREADS_PUBLISH_MESSAGE_URL = "/api/threads/publish";
    public static final String SMS_CAPABLE_URL = "/api/sms/sms-capable-user-validator";

    public static class SearchParams implements SearchParamsConstants {
        public static final JavatypedDatatypedProperty<BasicUser> SENDER_USER = PropertyFactory.ofSearchProperty("senderUser", Texts.ofMessageCoded("message.SenderUser"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.NUMBER), TypeDescriptor.valueOf(BasicUser.class));

        public static final JavatypedDatatypedProperty<BasicUser> TARGET_USER = PropertyFactory.ofSearchProperty("targetUser", Texts.ofMessageCoded("message.TargetUser"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.NUMBER), TypeDescriptor.valueOf(BasicUser.class));

        public static final JavatypedDatatypedProperty<List<MessageTopic>> TOPIC = PropertyFactory.ofSearchProperty("topic", Texts.ofMessageCoded("message.Topic"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.NUMBER), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(MessageTopic.class)));

        public static final JavatypedDatatypedProperty<List<MessageSeverity>> SEVERITY = PropertyFactory.ofSearchProperty("severity", Texts.ofMessageCoded("message.Severity"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.TEXT), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(MessageSeverity.class)));

        public static final JavatypedDatatypedProperty<Instant> CREATE_DATE = PropertyFactory.ofSearchProperty("dateCreated", Texts.ofMessageCoded("message.DateCreated"), CoreConstants.Datatype.DATETIME, TypeDescriptor.valueOf(Instant.class));

        public static final JavatypedDatatypedProperty<UUID> MESSAGE = PropertyFactory.ofSearchProperty("message", Texts.ofMessageCoded("message.Message"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(UUID.class));

        public static final JavatypedDatatypedProperty<List<UUID>> MESSAGES = PropertyFactory.ofSearchProperty("messages", Texts.ofMessageCoded("message.Message"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.TEXT), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(UUID.class)));

        public static final JavatypedDatatypedProperty<List<MessageMedium>> MESSAGE_MEDIUM = PropertyFactory.ofSearchProperty("messageMedium", Texts.ofMessageCoded("message.Medium"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.TEXT), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(MessageMedium.class)));

        public static final JavatypedDatatypedProperty<List<MessageSendingsSendStatus>> MESSAGE_STATUS = PropertyFactory.ofSearchProperty("messageStatus", Texts.ofMessageCoded("commons.stav"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.TEXT), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(MessageSendingsSendStatus.class)));

        public static final JavatypedDatatypedProperty<UUID> THREAD = PropertyFactory.ofSearchProperty("thread", Texts.ofNative("Id vlákna"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(UUID.class));
    }

}
