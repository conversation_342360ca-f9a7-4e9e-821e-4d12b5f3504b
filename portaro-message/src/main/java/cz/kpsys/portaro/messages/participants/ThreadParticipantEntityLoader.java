package cz.kpsys.portaro.messages.participants;

import lombok.NonNull;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface ThreadParticipantEntityLoader extends JpaRepository<ThreadParticipantEntity, UUID> {

    Optional<ThreadParticipantEntity> getByThreadAndParticipantAndTypeIn(UUID thread, UUID participant, Collection<String> type);

    List<ThreadParticipantEntity> getAllByParticipantAndType(@NonNull UUID participant, @NonNull String type);

    List<ThreadParticipantEntity> getAllByThreadAndType(@NonNull UUID thread, @NonNull String type);

    List<ThreadParticipantEntity> getAllByThreadAndTypeIn(UUID thread, Collection<String> types);

    List<ThreadParticipantEntity> getAllByThread(@NonNull UUID thread);

    boolean existsByTypeAndThread(String type, UUID thread);

    void deleteByThreadAndParticipant(@NonNull UUID thread, @NonNull UUID participant);
}