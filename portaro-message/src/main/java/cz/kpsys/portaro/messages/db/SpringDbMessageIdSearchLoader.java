package cz.kpsys.portaro.messages.db;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.database.SelectedColumnRowMapper;
import cz.kpsys.portaro.messages.constants.MessageConstants;
import cz.kpsys.portaro.messages.constants.MessageSendingsSendStatus;
import cz.kpsys.portaro.search.AbstractSingleColumnSpringDbSearchLoader;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.Paging;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.COLSEQ;
import static cz.kpsys.portaro.commons.db.QueryUtils.TC;
import static cz.kpsys.portaro.databasestructure.MessageDb.MESSAGE;
import static cz.kpsys.portaro.databasestructure.MessageDb.MESSAGE_SENDING;

public class SpringDbMessageIdSearchLoader extends AbstractSingleColumnSpringDbSearchLoader<MapBackedParams, UUID, Paging> {

    public SpringDbMessageIdSearchLoader(NamedParameterJdbcOperations jdbcTemplate, QueryFactory queryFactory) {
        super(jdbcTemplate, queryFactory, MESSAGE.TABLE, MESSAGE.ID, new SelectedColumnRowMapper<>(UUID.class, MESSAGE.ID));
    }

    @Override
    protected Map<String, String> sortToDbResultSetLabelMapping() {
        return Map.of(TC(MESSAGE.TABLE, MESSAGE.ID), MESSAGE.ID, TC(MESSAGE.TABLE, MESSAGE.CREATION_DATE), MESSAGE.CREATION_DATE);
    }

    @Override
    protected Map<String, String> sortToUniqueOrderDbColumnsMapping() {
        return Map.of(MESSAGE.ID, TC(MESSAGE.TABLE, MESSAGE.ID));
    }

    @Override
    protected void select(@NonNull SelectQuery sq, @NonNull MapBackedParams mapBackedParams, @Nullable SortingItem customSorting) {
        sq.select(
                TC(MESSAGE.TABLE, MESSAGE.ID),
                TC(MESSAGE.TABLE, MESSAGE.CREATION_DATE)
        );
    }

    @Override
    protected boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {

        sq.from(MESSAGE.TABLE);

        if (p.hasNotNull(MessageConstants.SearchParams.SENDER_USER)) {
            sq.where().and().eq(TC(MESSAGE.TABLE, MESSAGE.SENDER_USER_ID), p.get(MessageConstants.SearchParams.SENDER_USER).getId());
        }

        if (p.hasNotNull(MessageConstants.SearchParams.TARGET_USER)) {
            // TODO: rewrite to exists-query to mitigate duplicate rows
            sq.joins().add(MESSAGE_SENDING.TABLE, COLSEQ(TC(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.MESSAGE_ID), TC(MESSAGE.TABLE, MESSAGE.ID)));
            sq.where().and()
                    .brackets()
                    .eq(TC(MESSAGE.TABLE, MESSAGE.TARGET_USER_ID), p.get(MessageConstants.SearchParams.TARGET_USER).getId());
        }

        if (p.hasNotNull(MessageConstants.SearchParams.MESSAGE_MEDIUM)) {
            if (!p.hasLength(MessageConstants.SearchParams.MESSAGE_MEDIUM)) {
                return false;
            }

            sq.exists(queryFactory, MESSAGE.TABLE, MESSAGE.ID, MESSAGE_SENDING.TABLE, MESSAGE_SENDING.MESSAGE_ID, "message_medium", (where, alias) -> {
                where.and().in(TC(alias, MESSAGE_SENDING.MESSAGE_MEDIUM_ID), ListUtil.getListOfIds(p.get(MessageConstants.SearchParams.MESSAGE_MEDIUM)));
            });
        }

        if (p.hasNotNull(MessageConstants.SearchParams.TOPIC)) {
            if (!p.hasLength(MessageConstants.SearchParams.TOPIC)) {
                return false;
            }

            sq.where().and().in(TC(MESSAGE.TABLE, MESSAGE.MESSAGE_TOPIC_ID), ListUtil.getListOfIds(p.get(MessageConstants.SearchParams.TOPIC)));
        }

        if (p.hasNotNull(MessageConstants.SearchParams.MESSAGE_STATUS)) {
            if (!p.hasLength(MessageConstants.SearchParams.MESSAGE_STATUS)) {
                return false;
            }

            List<MessageSendingsSendStatus> messageSendingsSendStatuses = p.get(MessageConstants.SearchParams.MESSAGE_STATUS);

            //noinspection SlowListContainsAll
            if (!messageSendingsSendStatuses.containsAll(List.of(MessageSendingsSendStatus.SENT, MessageSendingsSendStatus.UNSENT))) {
                sq.exists(queryFactory, MESSAGE.TABLE, MESSAGE.ID, MESSAGE_SENDING.TABLE, MESSAGE_SENDING.MESSAGE_ID, "message_status", (where, alias) -> {
                    if (messageSendingsSendStatuses.contains(MessageSendingsSendStatus.SENT)) {
                        where.and().isNotNull(TC(alias, MESSAGE_SENDING.SENDING_EVENT_ID));
                    }
                    if (messageSendingsSendStatuses.contains(MessageSendingsSendStatus.UNSENT)) {
                        where.and().isNull(TC(alias, MESSAGE_SENDING.SENDING_EVENT_ID));
                    }
                });
            }
        }

        if (p.hasNotNull(MessageConstants.SearchParams.SEVERITY)) {
            if (!p.hasLength(MessageConstants.SearchParams.SEVERITY)) {
                return false;
            }

            sq.where().and().in(TC(MESSAGE.TABLE, MESSAGE.MESSAGE_SEVERITY_ID), ListUtil.getListOfIds(p.get(MessageConstants.SearchParams.SEVERITY)));
        }

        if (p.hasNotNull(MessageConstants.SearchParams.THREAD)) {
            sq.where().and().eq(TC(MESSAGE.TABLE, MESSAGE.THREAD_RECORD_ID), p.get(MessageConstants.SearchParams.THREAD));
        }

        if (p.hasNotNull(MessageConstants.SearchParams.CREATE_DATE)) {
            sq.where()
                    .and().gtEq(TC(MESSAGE.TABLE, MESSAGE.CREATION_DATE), p.get(MessageConstants.SearchParams.CREATE_DATE))
                    .and().ltEq(TC(MESSAGE.TABLE, MESSAGE.CREATION_DATE), p.get(MessageConstants.SearchParams.CREATE_DATE).plus(1, ChronoUnit.DAYS));
        }

        return true;
    }

    @Override
    protected Sorting mandatorySorting(@Nullable SortingItem customSorting, @NonNull MapBackedParams p) {
        return Sorting.ofDesc(TC(MESSAGE.TABLE, MESSAGE.CREATION_DATE));
    }
}
