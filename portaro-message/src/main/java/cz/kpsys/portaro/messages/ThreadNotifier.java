package cz.kpsys.portaro.messages;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.eventbus.Event;
import cz.kpsys.portaro.eventbus.EventBuilder;
import cz.kpsys.portaro.eventbus.EventType;
import cz.kpsys.portaro.eventbus.Publisher;
import cz.kpsys.portaro.messages.dto.Message;
import cz.kpsys.portaro.messages.dto.MessageMention;
import cz.kpsys.portaro.messages.dto.UserToThreadMessage;
import cz.kpsys.portaro.messages.dto.UserToUserMessage;
import cz.kpsys.portaro.messages.participants.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.BasicUser;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.function.Predicate;

import static cz.kpsys.portaro.commons.util.MapUtil.joinByKey;
import static cz.kpsys.portaro.eventbus.EventType.*;

@RequiredArgsConstructor
@Slf4j
public class ThreadNotifier implements Publisher {

    @NonNull IdAndIdsLoadable<BasicUser, UUID> basicUserByUuidLoader;
    @NonNull ThreadParticipantLoader threadParticipantLoader;
    @NonNull ThreadParticipantSaver threadParticipantSaver;
    @NonNull MessageToResponseConverter messageToResponseConverter;

    private BiConsumer<BasicUser, Event> eventConsumer;

    @Override
    public void onEventDispatch(@NonNull BiConsumer<BasicUser, Event> consumer) {
        this.eventConsumer = consumer;
    }

    public void newMessage(@NonNull Message newMessage, @NonNull List<MessageMention> messageMentions) {
        switch (newMessage) {
            case UserToThreadMessage userToThreadMessage -> notifyThreadUsers(userToThreadMessage, messageMentions);
            case UserToUserMessage _ -> {
            }
        }
    }

    public void participantAdded(@NonNull Record thread, BasicUser addedParticipant, @NonNull UserAuthentication currentAuth) {
        List<BasicUser> threadParticipantUsers = getParticipantsWithoutInitiator(thread, currentAuth.getActiveUser());

        threadParticipantUsers.forEach((value) -> {
            eventConsumer.accept(
                    value,
                    EventBuilder.event()
                            .type(PARTICIPANT_ADDED)
                            .data(new ParticipantAddedEventData(thread.getId(), thread.getName(), addedParticipant))
                            .build()
            );
        });
    }

    public void participantRemoved(@NonNull Record thread, BasicUser removedParticipant, @NonNull UserAuthentication currentAuth) {
        List<BasicUser> threadParticipantUsers = getParticipantsWithoutInitiator(thread, currentAuth.getActiveUser());

        threadParticipantUsers.forEach((value) -> {
            eventConsumer.accept(
                    value,
                    EventBuilder.event()
                            .type(PARTICIPANT_REMOVED)
                            .data(new ParticipantRemovedEventData(thread.getId(), thread.getName(), removedParticipant))
                            .build()
            );
        });
    }

    public void userLeavedThread(@NonNull Record thread, @NonNull BasicUser user) {
        List<BasicUser> usersToGetNotified = basicUserByUuidLoader.getAllByIds(threadParticipantLoader.getParticipantsByThread(thread.getId()).stream()
                .map(ThreadParticipant::participantId)
                .toList());

        usersToGetNotified.forEach((value) -> {
            eventConsumer.accept(
                    value,
                    EventBuilder.event()
                            .type(USER_LEAVED)
                            .data(new UserLeavedThreadEventData(thread.getId(), thread.getName(), user))
                            .build()
            );
        });
    }

    public void userJoinedThread(@NonNull Record thread, @NonNull BasicUser user) {
        List<BasicUser> threadParticipantUsers = getParticipantsWithoutInitiator(thread, user);

        threadParticipantUsers.forEach((value) -> {
            eventConsumer.accept(
                    value,
                    EventBuilder.event()
                            .type(USER_JOINED)
                            .data(new ParticipantAddedEventData(thread.getId(), thread.getName(), user))
                            .build()
            );
        });
    }


    public void newThread(@NonNull Record thread, @NonNull UserAuthentication currentAuth) {
        List<BasicUser> threadParticipantUsers = getParticipantsWithoutInitiator(thread, currentAuth.getActiveUser());

        threadParticipantUsers.forEach((value) -> {
            eventConsumer.accept(
                    value,
                    EventBuilder.event()
                            .type(THREAD_CREATED)
                            .data(new ParticipantAddedEventData(thread.getId(), thread.getName(), value))
                            .build()
            );
        });
    }

    private List<BasicUser> getParticipantsWithoutInitiator(@NonNull Record thread, @NonNull BasicUser initiator) {
        List<ParticipantThreadParticipant> allThreadParticipantWithoutRemover = threadParticipantLoader.getParticipantsByThread(thread.getId()).stream()
                .filter(excludeInitiator(initiator))
                .toList();

        return basicUserByUuidLoader.getAllByIds(allThreadParticipantWithoutRemover.stream()
                .map(ThreadParticipant::participantId)
                .toList());
    }

    private Predicate<? super ThreadParticipant> excludeInitiator(@NonNull BasicUser initiator) {
        return threadParticipant -> !Objects.equals(threadParticipant.participantId(), initiator.getRid());
    }

    private void notifyThreadUsers(UserToThreadMessage newMessage, @NonNull List<MessageMention> messageMentions) {
        log.debug("New message received: [{}]", newMessage);

        List<ThreadParticipant> allThreadParticipantWithoutSender = threadParticipantLoader.getParticipantsOrMentionsByThread(newMessage.thread().getId()).stream()
                .filter(messageSender(newMessage))
                .filter(onlyMentionedMentionParticipants(messageMentions))
                .toList();

        Map<ThreadParticipant, BasicUser> threadParticipantBasicUserMap = joinByKey(
                allThreadParticipantWithoutSender,
                basicUserByUuidLoader.getAllByIds(allThreadParticipantWithoutSender.stream()
                        .map(ThreadParticipant::participantId)
                        .toList()),
                ThreadParticipant::participantId,
                BasicUser::getRid
        );

        notifyOtherParticipantsAboutNewMessage(newMessage, threadParticipantBasicUserMap);
        updateOthersParticipantsFirstUnreadMessage(newMessage, threadParticipantBasicUserMap);
    }


    private void updateOthersParticipantsFirstUnreadMessage(UserToThreadMessage newMessage, Map<? extends ThreadParticipant, BasicUser> threadParticipantBasicUserMap) {
        List<ThreadParticipant> updatedParticipantsWithoutUnreadedMessageBefore = threadParticipantBasicUserMap.keySet().stream()
                .filter(participant -> switch (participant) {
                    case MainThreadParticipant _ -> false;
                    case MentionThreadParticipant mentionThreadParticipant -> mentionThreadParticipant.firstUnreadMessageId() == null;
                    case ParticipantThreadParticipant participantThreadParticipant -> participantThreadParticipant.firstUnreadMessageId() == null;
                })
                .map(participant -> (ThreadParticipant) switch (participant) {
                    case MentionThreadParticipant mention -> mention.withFirstUnreadMessageId(newMessage.getId());
                    case ParticipantThreadParticipant threadParticipant -> threadParticipant.withFirstUnreadMessageId(newMessage.getId());
                    default -> throw new IllegalStateException("Unexpected value: " + participant);
                }).toList();
        threadParticipantSaver.saveAll(updatedParticipantsWithoutUnreadedMessageBefore);
    }

    private void notifyOtherParticipantsAboutNewMessage(UserToThreadMessage newMessage, Map<ThreadParticipant, BasicUser> threadParticipantBasicUserMap) {
        threadParticipantBasicUserMap.forEach((key, value) -> {
            EventType type = switch (key) {
                case MentionThreadParticipant _ -> EventType.MENTION;
                case ParticipantThreadParticipant _ -> EventType.NEW_MESSAGE;
                default -> throw new IllegalStateException("Unexpected value: " + key);
            };
            eventConsumer.accept(
                    value,
                    EventBuilder.event()
                            .type(type)
                            .data(new NewMessageEventData(messageToResponseConverter.convertSingle(newMessage), newMessage.thread().getId(), newMessage.thread().getName()))
                            .build()
            );
        });
    }

    private static @NonNull Predicate<ThreadParticipant> messageSender(UserToThreadMessage newMessage) {
        return participant -> !participant.participantId().equals(newMessage.senderUser().getRid());
    }

    private static @NonNull Predicate<ThreadParticipant> onlyMentionedMentionParticipants(@NonNull List<MessageMention> messageMentions) {
        return participant -> switch (participant) {
            case MentionThreadParticipant _ -> messageMentions.stream().anyMatch(m -> m.recordId().equals(participant.participantId()));
            default -> true;
        };
    }
}