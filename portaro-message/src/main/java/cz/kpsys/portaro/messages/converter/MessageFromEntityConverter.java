package cz.kpsys.portaro.messages.converter;

import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.BatchFiller;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.messages.constants.MessageSeverity;
import cz.kpsys.portaro.messages.constants.MessageTopic;
import cz.kpsys.portaro.messages.db.entity.MessageEntity;
import cz.kpsys.portaro.messages.dto.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class MessageFromEntityConverter implements Converter<List<? extends MessageEntity>, List<Message>> {

    @NonNull AllByIdsLoadable<BasicUser, Integer> basicUserLoader;
    @NonNull AllByIdsLoadable<Record, UUID> recordLoader;
    @NonNull AllByIdsLoadable<Department, Integer> departmentLoader;
    @NonNull MessageSendingBatchLoader batchMessageSendingLoader;

    @Override
    public List<Message> convert(@NonNull List<? extends MessageEntity> messageEntities) {

        Map<? extends MessageEntity, @NonNull BasicUser> senderUsers = BatchFiller.of(basicUserLoader).load(messageEntities, MessageEntity::getSenderUserId);
        Map<? extends MessageEntity, @NonNull BasicUser> targetUsers = BatchFiller.of(basicUserLoader).loadNullable(messageEntities, MessageEntity::getTargetUserId);
        Map<? extends MessageEntity, @NonNull Department> departments = BatchFiller.of(departmentLoader).load(messageEntities, MessageEntity::getDepartmentId);
        Map<? extends MessageEntity, @NonNull Record> threads = BatchFiller.of(recordLoader).loadNullable(messageEntities, MessageEntity::getThreadRecordId);
        Map<UUID, @NonNull List<MessageSending>> messageSending = batchMessageSendingLoader.loadByIds(ListUtil.getListOfIds(messageEntities));

        return ListUtil.convert(messageEntities, entity -> {
            if (entity.getTargetUserId() != null) {
                return Message.ofUserToUser(
                        entity.getId(),
                        getAlternativeMessageContent(entity, messageSending.get(entity.getId())),
                        MessageTopic.CODEBOOK.getById(entity.getMessageTopicId()),
                        MessageSeverity.CODEBOOK.getById(entity.getMessageSeverityId()),
                        senderUsers.get(entity),
                        targetUsers.get(entity),
                        departments.get(entity),
                        entity.getConfirmationNecessary(),
                        ObjectUtil.elvis(entity.getContentType(), ContentType.CODEBOOK::getById),
                        entity.getDirectoryId(),
                        entity.getCreationDate(),
                        entity.getActivationDate()
                );
            } else {
                return Message.ofUserToThread(
                        entity.getId(),
                        getAlternativeMessageContent(entity, messageSending.get(entity.getId())),
                        MessageTopic.CODEBOOK.getById(entity.getMessageTopicId()),
                        MessageSeverity.CODEBOOK.getById(entity.getMessageSeverityId()),
                        senderUsers.get(entity),
                        threads.get(entity),
                        departments.get(entity),
                        entity.getConfirmationNecessary(),
                        ObjectUtil.elvis(entity.getContentType(), ContentType.CODEBOOK::getById),
                        entity.getDirectoryId(),
                        entity.getCreationDate(),
                        entity.getActivationDate()
                );
            }
        });
    }

    public String getAlternativeMessageContent(MessageEntity entity, List<MessageSending> messageSendings) {
        return Optional.ofNullable(entity.getContent())
                .or(() -> messageSendings.stream()
                        .map(this::getAlternativeMessageContent)
                        .filter(Optional::isPresent)
                        .map(Optional::orElseThrow)
                        .findFirst()).orElse("Zpráva neobsahuje text");
    }

    private @NonNull Optional<String> getAlternativeMessageContent(MessageSending sending) {
        switch (sending) {
            case MessageSendingEmail messageSendingEmail -> {
                return Optional.of(messageSendingEmail.getBody());
            }
            case MessageSendingInternal messageSendingInternal -> {
                return Optional.of(messageSendingInternal.getBody());
            }
            case MessageSendingPost _ -> {
                return Optional.empty();
            }
            case MessageSendingSms messageSendingSms -> {
                return Optional.of(messageSendingSms.getContent());
            }
        }
    }
}
