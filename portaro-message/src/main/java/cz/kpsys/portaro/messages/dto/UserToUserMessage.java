package cz.kpsys.portaro.messages.dto;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.messages.constants.MessageSeverity;
import cz.kpsys.portaro.messages.constants.MessageTopic;
import cz.kpsys.portaro.user.BasicUser;
import lombok.NonNull;
import lombok.With;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.util.UUID;

@With
public record UserToUserMessage(

        @NonNull
        UUID id,

        @NonNull
        String content,

        @NonNull
        MessageTopic topic,

        @NonNull
        MessageSeverity severity,

        @NonNull
        BasicUser senderUser,

        @NonNull
        BasicUser targetUser,

        @NonNull
        Department department,

        @NonNull
        Boolean confirmationNecessary,

        @Nullable
        ContentType contentType,

        @Nullable
        Integer directoryId,

        @NonNull
        Instant creationDate,

        @Nullable
        Instant activationDate

        ) implements Message {
}
