package cz.kpsys.portaro.messages.thread;

import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.messages.MessageSecurityActions;
import cz.kpsys.portaro.messages.ThreadNotifier;
import cz.kpsys.portaro.messages.dto.UserReadAllThreadMessagesCommand;
import cz.kpsys.portaro.messages.participants.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordWellKnownFields;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ThreadUpdater {

    @NonNull ThreadParticipantSaver threadParticipantSaver;
    @NonNull ThreadParticipantRemover threadParticipantRemover;
    @NonNull ThreadParticipantLoader threadParticipantLoader;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull TransactionTemplate transactionTemplate;
    @NonNull SecurityManager securityManager;
    @NonNull ThreadNotifier threadNotifier;
    @NonNull IdAndIdsLoadable<BasicUser, UUID> basicUserByUuidLoader;

    public void addParticipants(@NonNull AddThreadParticipantsCommand command) {
        securityManager.throwIfCannot(MessageSecurityActions.EDIT_THREAD, command.currentAuth(), command.ctx(), command.thread().getId());
        transactionTemplate.executeWithoutResult(_ -> {
            List<ParticipantThreadParticipant> existingParticipants = threadParticipantLoader.getParticipantsByThread(command.thread().getId());

            Set<UUID> existingParticipantsRids = existingParticipants.stream()
                    .map(ThreadParticipant::participantId)
                    .collect(Collectors.toSet());

            List<@NonNull BasicUser> addableUsers = command.participants().stream()
                    .filter(user -> !existingParticipantsRids.contains(user.getRid()))
                    .toList();

            List<ParticipantThreadParticipant> newParticipants = addableUsers
                    .stream()
                    .map(user -> ThreadParticipant.participant(
                            UuidGenerator.forIdentifier(),
                            command.thread().getId(),
                            user.getRid(),
                            Instant.now(),
                            null,
                            false
                    ))
                    .toList();

            threadParticipantSaver.saveAll(newParticipants);

            addableUsers.forEach(participant -> {
                threadNotifier.participantAdded(command.thread(), participant, command.currentAuth());
            });
        });
    }

    public Record updateThread(@NonNull UpdateThreadCommand command) {
        securityManager.throwIfCannot(MessageSecurityActions.EDIT_THREAD, command.currentAuth(), command.ctx(), command.thread().getId());
        return transactionTemplate.execute(_ -> {
            RecordEditation recordEditation = recordEditationFactory
                    .on(command.ctx())
                    .ofExisting(command.thread())
                    .build(command.currentAuth());

            recordEditationHelper.setStringSubfieldValue(command.name(), true, RecordWellKnownFields.DocumentTitle.TYPE_ID, true, RecordWellKnownFields.DocumentTitle.Name.TYPE_ID, recordEditation, command.ctx(), command.currentAuth());

            recordEditation.saveIfModified(command.ctx(), command.currentAuth());

            return recordEditation.getRecord();
        });
    }

    public void removeParticipants(@NonNull RemoveThreadParticipantsCommand command) {
        securityManager.throwIfCannot(MessageSecurityActions.EDIT_THREAD, command.currentAuth(), command.ctx(), command.thread().getId());
        transactionTemplate.executeWithoutResult(_ -> {
            for (BasicUser participant : command.participants()) {
                threadParticipantRemover.removeByThreadAndParticipant(command.thread().getId(), participant);
                threadNotifier.participantRemoved(command.thread(), participant, command.currentAuth());
            }
        });
    }

    public void userReadAllThreadMessages(UserReadAllThreadMessagesCommand command) {
        transactionTemplate.executeWithoutResult(_ -> {
            ThreadParticipant threadParticipant = threadParticipantLoader.getParticipantOrMention(command.thread(), command.user());
            ThreadParticipant updatedParticipant = switch (threadParticipant) {
                case MainThreadParticipant _ -> throw new IllegalStateException("Cannot clear unread message for MainThreadParticipant.");
                case MentionThreadParticipant mention -> mention.withFirstUnreadMessageId(null);
                case ParticipantThreadParticipant participant -> participant.withFirstUnreadMessageId(null);
            };
            threadParticipantSaver.save(updatedParticipant);
        });
    }

    public void userLeaveThread(LeaveThreadCommand command) {
        securityManager.throwIfCannot(MessageSecurityActions.LEAVE_THREAD, command.currentAuth(), command.ctx(), command.thread().getId());
        transactionTemplate.executeWithoutResult(_ -> {
            UUID rid = command.currentAuth().getActiveUser().getRecordId();
            if (rid == null) {
                return;
            }

            BasicUser user = basicUserByUuidLoader.getById(command.currentAuth().getActiveUser().getRecordId());
            threadParticipantRemover.removeByThreadAndParticipant(command.thread().getId(), user);
            threadNotifier.userLeavedThread(command.thread(), user);
        });
    }

    public void userJoinThread(UserJoinThreadCommand command) {
        securityManager.throwIfCannot(MessageSecurityActions.JOIN_THREAD, command.currentAuth(), command.ctx(), command.thread().getId());
        transactionTemplate.executeWithoutResult(_ -> {
            UUID rid = command.currentAuth().getActiveUser().getRecordId();
            if (rid == null) {
                return;
            }
            BasicUser user = basicUserByUuidLoader.getById(rid);
            Optional<ThreadParticipant> participantOrMention = threadParticipantLoader.getOptionalParticipantOrMention(command.thread(), user);

            if (participantOrMention.isEmpty()) {
                threadParticipantSaver.save(ThreadParticipant.participant(
                        UuidGenerator.forIdentifier(),
                        command.thread().getId(),
                        user.getRid(),
                        Instant.now(),
                        null,
                        false
                ));
            } else {
                threadParticipantSaver.updateToParticipant(participantOrMention.get());
            }

            threadNotifier.userJoinedThread(command.thread(), user);
        });
    }
}
