package cz.kpsys.portaro.messages.participants;

import cz.kpsys.portaro.commons.object.Identified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.time.Instant;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.MessageDb.THREAD_PARTICIPANT.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class ThreadParticipantEntity implements Identified<UUID> {

    @Id
    @Column(name = ID)
    @EqualsAndHashCode.Include
    @NonNull
    UUID id;

    @Column(name = THREAD_ID)
    @NonNull
    UUID thread;

    @Column(name = PARTICIPANT_ID)
    @NonNull
    UUID participant;

    @Column(name = TYPE)
    @NonNull
    String type;

    @Column(name = CREATE_DATE)
    @NonNull
    Instant createDate;

    @Column(name = FIRST_UNREAD_MESSAGE_ID)
    @Nullable
    UUID firstUnreadMessageId;

    @Column(name = ADMINISTRATOR)
    boolean administrator;
}