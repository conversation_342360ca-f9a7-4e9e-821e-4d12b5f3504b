package cz.kpsys.portaro.messages.thread;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.NonNull;

import java.util.UUID;

public record UserJoinThreadRequest(
        @Schema(implementation = UUID.class, description = "Thread")
        @NotNull
        Record thread
) {

    public UserJoinThreadCommand toCommand(@NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        return new UserJoinThreadCommand(ctx, currentAuth, thread());
    }
}
