package cz.kpsys.portaro.messages.participants;

import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ThreadParticipantSaver implements Saver<ThreadParticipant, ThreadParticipantEntity> {

    @NonNull JpaRepository<ThreadParticipantEntity, UUID> threadParticipantRepository;

    public @NonNull ThreadParticipantEntity save(@NonNull ThreadParticipant threadParticipant) {
        ThreadParticipantEntity threadParticipantEntity = this.convert(threadParticipant);
        return threadParticipantRepository.save(threadParticipantEntity);
    }

    public void updateToParticipant(ThreadParticipant threadParticipant) {
        ThreadParticipantEntity threadParticipantEntity = this.convert(threadParticipant);
        threadParticipantEntity.setType(ThreadParticipantType.PARTICIPANT.getId());
        threadParticipantRepository.save(threadParticipantEntity);
    }

    public @NonNull List<ThreadParticipantEntity> saveAll(@NonNull List<? extends ThreadParticipant> threadParticipants) {
        return threadParticipantRepository.saveAll(ListUtil.convert(threadParticipants, this::convert));
    }

    private ThreadParticipantEntity convert(ThreadParticipant threadParticipant) {
        return switch (threadParticipant) {
            case MainThreadParticipant mainThreadParticipant -> new ThreadParticipantEntity(mainThreadParticipant.id(),
                    mainThreadParticipant.thread(),
                    mainThreadParticipant.participantId(),
                    ThreadParticipantType.MAIN.getId(),
                    mainThreadParticipant.createDate(),
                    null,
                    false);
            case ParticipantThreadParticipant participantThreadParticipant -> new ThreadParticipantEntity(participantThreadParticipant.id(),
                    participantThreadParticipant.thread(),
                    participantThreadParticipant.participantId(),
                    ThreadParticipantType.PARTICIPANT.getId(),
                    participantThreadParticipant.createDate(),
                    participantThreadParticipant.firstUnreadMessageId(),
                    participantThreadParticipant.administrator());
            case MentionThreadParticipant mentionThreadParticipant -> new ThreadParticipantEntity(mentionThreadParticipant.id(),
                    mentionThreadParticipant.thread(),
                    mentionThreadParticipant.participantId(),
                    ThreadParticipantType.MENTION.getId(),
                    mentionThreadParticipant.createDate(),
                    mentionThreadParticipant.firstUnreadMessageId(),
                    false);
        };
    }
}
