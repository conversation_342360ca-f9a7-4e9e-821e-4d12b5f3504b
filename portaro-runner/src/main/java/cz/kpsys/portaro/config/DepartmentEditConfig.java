package cz.kpsys.portaro.config;

import cz.kpsys.portaro.appserver.GenericTableWriteSaver;
import cz.kpsys.portaro.appserver.dml.DmlAppserverService;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualProvider;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualProvider;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.ProvidedProxiedList;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.TtlCachedProvider;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.database.FlushingJpaSaver;
import cz.kpsys.portaro.database.IntegerValueDatabaseLoader;
import cz.kpsys.portaro.databasestructure.PlacementDb;
import cz.kpsys.portaro.department.*;
import cz.kpsys.portaro.department.activation.DefaultDepartmentActivator;
import cz.kpsys.portaro.department.activation.DepartmentActivator;
import cz.kpsys.portaro.department.creation.DefaultDepartmentCreator;
import cz.kpsys.portaro.department.creation.DepartmentCreationRequest;
import cz.kpsys.portaro.department.creation.DepartmentCreator;
import cz.kpsys.portaro.department.deletion.DefaultDepartmentDeleter;
import cz.kpsys.portaro.department.deletion.DepartmentDeleter;
import cz.kpsys.portaro.department.editation.DefaultDepartmentUpdater;
import cz.kpsys.portaro.department.editation.DepartmentEditationRequest;
import cz.kpsys.portaro.department.editation.DepartmentRecordUpdater;
import cz.kpsys.portaro.department.editation.DepartmentUpdater;
import cz.kpsys.portaro.domaineddepartment.*;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.form.valueeditor.text.TextValueEditorModifier;
import cz.kpsys.portaro.formconfig.formfield.FormFieldsNamesContextualFunction;
import cz.kpsys.portaro.formconfig.valueeditor.AcceptableValuesResolver;
import cz.kpsys.portaro.formconfig.valueeditor.AuthenticatedAcceptableValuesResolver;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.localization.Localization;
import cz.kpsys.portaro.localization.LocalizationDeleter;
import cz.kpsys.portaro.location.DepartmentLocationRelationDeleter;
import cz.kpsys.portaro.location.DepartmentLocationRelationLoader;
import cz.kpsys.portaro.location.Location;
import cz.kpsys.portaro.location.LocationWithDepartments;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordStatusResolver;
import cz.kpsys.portaro.record.deletion.RecordHoldingDeletionCommand;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import cz.kpsys.portaro.record.holding.RecordHoldingLoader;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.setting.CoreSettingKeys;
import cz.kpsys.portaro.setting.CustomSetting;
import cz.kpsys.portaro.setting.CustomSettingLoader;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.deletion.UserDeletionCommand;
import cz.kpsys.portaro.user.edit.UserEditationFactory;
import cz.kpsys.portaro.web.page.CompositeCurrentPageDialogsResolver;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class DepartmentEditConfig {

    @NonNull DepartmentAccessor departmentAccessor;
    @NonNull SettingLoader settingLoader;
    @NonNull Runnable saveTransactionAuthenticator;
    @NonNull Saver<CustomSetting<String>, ?> customSettingSaver;
    @NonNull Saver<LocationWithDepartments, LocationWithDepartments> locationWithDepartmentsSaver;
    @NonNull AllValuesProvider<Location> locationLoader;
    @NonNull Eventer eventer;
    @NonNull Codebook<Department, Integer> departmentLoader;
    @NonNull CompositeCurrentPageDialogsResolver currentPageDialogsResolver;
    @NonNull CustomSettingLoader customSettingLoader;
    @NonNull Provider<@NullableNotBlank String> rootServerUrlProvider;
    @NonNull DepartmentLocationRelationLoader departmentLocationRelationLoader;
    @NonNull Deleter<Location> locationDeleter;
    @NonNull AllValuesProvider<Localization> localizationLoader;
    @NonNull Saver<Localization, Localization> localizationSaver;
    @NonNull Deleter<CustomSetting<String>> customSettingDeleter;
    @NonNull DepartmentLocationRelationDeleter departmentLocationRelationDeleter;
    @NonNull RecordHoldingLoader recordHoldingLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, User> userSearchLoader;
    @NonNull Deleter<UserDeletionCommand> userDeleter;
    @NonNull UserEditationFactory userEditationFactory;
    @NonNull Deleter<RecordHoldingDeletionCommand> recordHoldingDeleter;
    @NonNull AuthenticatedContextualProvider<Department, List<Department>> editableDepartmentsAuthenticatedContextualProvider;
    @NonNull QueryFactory queryFactory;
    @NonNull EntityManager entityManager;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull CacheService cacheService;
    @NonNull DmlAppserverService dmlAppserverService;
    @NonNull LocalizationDeleter localizationDeleter;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;
    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull RecordStatusResolver recordStatusResolver;

    @Bean
    public AcceptableValuesResolver<DomainedDepartmentCreationRequest, Department> domainedDepartmentCreationAllowedParentDepartmentsResolver() {
        return (request, ctx) -> {
            if (settingLoader.getOnRoot(SettingKeys.DEPARTMENT_CREATION_ACCEPTABLE_PARENT_DEPARTMENTS_SHOW_ALL_CHILDS)) {
                return departmentAccessor.getChildren(ctx);
            }
            List<Integer> departmentIds = settingLoader.getOnRoot(SettingKeys.DEPARTMENT_CREATION_ACCEPTABLE_PARENT_DEPARTMENTS);
            return ListUtil.filterAndSortByRule(departmentAccessor.getAll(), departmentIds);
        };
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<DomainedDepartmentCreationRequest> domainedDepartmentCreationRequestDefaulter() {
        return new DomainedDepartmentCreationRequest.DomainedDepartmentCreationRequestDefaulter(domainedDepartmentCreationAllowedParentDepartmentsResolver());
    }

    @Bean
    public TextValueEditorModifier<DomainedDepartmentCreationRequest> departmentCreationServerUrlEditorModifier() {
        return new DomainedDepartmentCreationRequest.DepartmentCreationServerUrlEditorModifier(
                rootServerUrlProvider.throwingWhenNull(),
                settingLoader.getDepartmentedProvider(SettingKeys.DEPARTMENT_CREATION_CUSTOM_PARENT_DOMAIN_ENABLED),
                settingLoader.getDepartmentedProvider(SettingKeys.DEPARTMENT_CREATION_CUSTOM_PARENT_DOMAIN_VALUE).throwingWhenNull()
        );
    }

    @Bean
    public DomainedDepartmentCreator domainedDepartmentCreator() {
        return new DefaultDomainedDepartmentCreator(
                new DomainedDepartmentCreationRequestToDepartmentConverter(departmentLoader),
                departmentSaver(),
                departmentLoader,
                customSettingSaver,
                locationWithDepartmentsSaver,
                locationLoader,
                CoreSettingKeys.SERVER_URL,
                eventer,
                customSettingLoader);
    }

    @Bean
    public DepartmentActivator departmentActivator() {
        return new DefaultDepartmentActivator(departmentLoader, departmentSaver(), eventer);
    }

    @Bean
    public UniquenessResolver<String> serverUrlUniquenessResolver() {
        return new ServerUrlUniquenessResolver(
                new ProvidedProxiedList<>(TtlCachedProvider.ofSeconds(10, () -> new ArrayList<>(settingLoader.getContextToValueMap(CoreSettingKeys.SERVER_URL).values())))
        );
    }

    @Bean
    public Saver<Department, DepartmentEntity> departmentJpaSaver() {
        var saver = new GenericHookableSaver<>(new TransactionalSaver<>(new PreConvertingSaver<>(
                new DepartmentToEntityConverter(),
                new FlushingJpaSaver<>(new SimpleJpaRepository<>(DepartmentEntity.class, entityManager))), defaultTransactionTemplateFactory.get()));
        saver.addPreHook(saveTransactionAuthenticator);
        var cacheCleaner = cacheService.createCleanerFor(Department.class.getSimpleName());
        saver.addPostSuccessHook(cacheCleaner::clearCache);
        return saver;
    }

    @Bean
    public Saver<Department, DepartmentEntity> departmentSaver() {
        var saver = new GenericHookableSaver<>(new PreConvertingSaver<>(
                new DepartmentToEntityConverter(),
                GenericTableWriteSaver.of(
                        new DepartmentSaveTableWriteGenerator(),
                        dmlAppserverService,
                        cacheService.createCleanerFor(Department.class.getSimpleName()))));
        saver.addPreHook(saveTransactionAuthenticator);
        return saver;
    }

    @Bean
    public DepartmentCreator departmentCreator() {
        return new DefaultDepartmentCreator(
                ContextIgnoringContextualProvider.of(IntegerValueDatabaseLoader.ofMaxPlusOne(notAutoCommittingJdbcTemplate, queryFactory, PlacementDb.DEF_PUJC.TABLE, PlacementDb.DEF_PUJC.ID_PUJC)),
                departmentSaver(),
                departmentLoader,
                eventer
        );
    }

    @Bean
    public DepartmentUpdater departmentUpdater() {
        return new DefaultDepartmentUpdater(
                departmentLoader,
                departmentSaver(),
                departmentRecordUpdater(),
                recordLoader,
                recordStatusResolver
        );
    }

    @Bean
    public DepartmentRecordUpdater departmentRecordUpdater() {
        return new DepartmentRecordUpdater(
                recordLoader,
                recordEditationFactory,
                recordEntryFieldTypeIdResolver,
                recordEditationHelper
        );
    }


    @Bean
    public AuthenticatedAcceptableValuesResolver<DepartmentEditationRequest, Department> departmentEditationAllowedParentDepartmentsResolver() {
        return (request, currentDepartment, currentAuth) -> {
            var allDepartments = editableDepartmentsAuthenticatedContextualProvider.getOn(currentAuth, currentDepartment);
            var subDepartmentsOfEditedDepartment = departmentAccessor.getAllByScope(request.department(), HierarchyLoadScope.SUBTREE);
            return ListUtil.removeAll(allDepartments, subDepartmentsOfEditedDepartment); // to prevent cycling :-)
        };
    }

    @Bean
    public FormFieldsNamesContextualFunction<DepartmentEditationRequest, Department> departmentEditationRequestRequiredPropsProvider() {
        var potentiallyRequiredFields = List.of(DepartmentEditationRequest.Fields.parentDepartment, DepartmentEditationRequest.Fields.order);

        return (departmentEditationRequest, ctx) -> {
            var declaredFields = ObjectUtil.getFieldNameStream(DepartmentEditationRequest.class).toList();
            if (departmentEditationRequest.department().equals(ctx)) {
                return ListUtil.removeAll(declaredFields, potentiallyRequiredFields);
            }
            return declaredFields;
        };
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<DepartmentCreationRequest> departmentCreationRequestDefaulter() {
        return new DepartmentCreationRequest.DepartmentCreationRequestDefaulter(editableDepartmentsAuthenticatedContextualProvider);
    }

    @Bean
    public DepartmentDeleter departmentDeleter() {
        return new DefaultDepartmentDeleter(
                departmentAccessor,
                departmentLocationRelationLoader,
                locationDeleter,
                localizationLoader,
                localizationSaver,
                departmentSaver(),
                eventer,
                customSettingDeleter,
                customSettingLoader,
                departmentLocationRelationDeleter,
                recordHoldingLoader,
                userSearchLoader,
                userDeleter,
                userEditationFactory,
                recordHoldingDeleter,
                localizationDeleter
        );
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerCurrentPageDialogs() {
        currentPageDialogsResolver
                .add(1000, new DepartmentCreatedResponseCurrentPageDialogResolver());
    }

}
