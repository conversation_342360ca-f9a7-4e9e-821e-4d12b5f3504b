package cz.kpsys.portaro.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.app.CatalogConstants;
import cz.kpsys.portaro.appserver.dml.DmlAppserverService;
import cz.kpsys.portaro.appserver.dml.TableWriteGenerator;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.Authenticity;
import cz.kpsys.portaro.auth.LastModificationUserAuthResolver;
import cz.kpsys.portaro.auth.SideThreadAuthenticationIsolator;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.auth.credreg.CredentialsRegistrationSecurityActions;
import cz.kpsys.portaro.auth.internal.BarCodeAndBirthYearStringPairUserProvider;
import cz.kpsys.portaro.auth.internal.InternalAuthSecurityActions;
import cz.kpsys.portaro.auth.internal.InternalLoginCredentialsProperties;
import cz.kpsys.portaro.auth.internal.PasswordCheckingStringPairUserProvider;
import cz.kpsys.portaro.auth.internal.credentials.*;
import cz.kpsys.portaro.auth.password.PasswordChecker;
import cz.kpsys.portaro.auth.password.PasswordEncoderDelegatingPasswordChecker;
import cz.kpsys.portaro.auth.password.UniversalPasswordPasswordChecker;
import cz.kpsys.portaro.auth.view.AuthenticationSystemViewProvider;
import cz.kpsys.portaro.auth.view.FormAuthenticationSystemView;
import cz.kpsys.portaro.commons.barcode.BarCodeValidator;
import cz.kpsys.portaro.commons.barcode.EanBarcodeValidator;
import cz.kpsys.portaro.commons.cache.CacheCleaningDeleter;
import cz.kpsys.portaro.commons.cache.CacheCleaningSaver;
import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.convert.*;
import cz.kpsys.portaro.commons.localization.ContextualLocaleLocalizer;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.*;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.commons.phonenumber.PhoneNumberValidator;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.validation.uppercase.Uppercase;
import cz.kpsys.portaro.conversation.ActionRequestUrlGenerator;
import cz.kpsys.portaro.database.FlushingJpaDeleter;
import cz.kpsys.portaro.database.IntegerValueDatabaseLoader;
import cz.kpsys.portaro.database.MultiJpaDeleter;
import cz.kpsys.portaro.databaseproperties.DatabaseProperties;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.exemplar.Exemplar;
import cz.kpsys.portaro.exemplar.ExemplarSecurityActions;
import cz.kpsys.portaro.finance.AmountType;
import cz.kpsys.portaro.form.valueeditor.LocalizationsAwareValueEditor;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import cz.kpsys.portaro.form.valueeditor.ValueEditorModifier;
import cz.kpsys.portaro.form.valueeditor.password.PasswordValueEditor;
import cz.kpsys.portaro.form.valueeditor.text.TextValueEditorModifier;
import cz.kpsys.portaro.formconfig.formfield.FormFieldsNamesContextualFunction;
import cz.kpsys.portaro.formconfig.valueeditor.AcceptableValuesResolver;
import cz.kpsys.portaro.formconfig.valueeditor.ValueEditorPrototypeResolver;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.licence.FeatureManager;
import cz.kpsys.portaro.loan.ill.IllSettingKeys;
import cz.kpsys.portaro.mail.MailService;
import cz.kpsys.portaro.object.TypedContextualObjectModifier;
import cz.kpsys.portaro.payment.DepartmentedTypedUserAmount;
import cz.kpsys.portaro.payment.Transaction;
import cz.kpsys.portaro.payment.TransactionCreator;
import cz.kpsys.portaro.payment.TransactionLoader;
import cz.kpsys.portaro.record.deletion.RecordDeleter;
import cz.kpsys.portaro.record.holding.RecordHoldingUpserter;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.security.*;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.security.permission.ActionPermissionAccessor;
import cz.kpsys.portaro.security.permission.DefaultPermissionEntity;
import cz.kpsys.portaro.security.permission.UserPermissionEntity;
import cz.kpsys.portaro.setting.CoreSettingKeys;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sip2.server.impl.Sip2ServerImplConstants;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.template.TemplateEngine;
import cz.kpsys.portaro.token.Auth0JwtTemporalTokenRepository;
import cz.kpsys.portaro.token.TemporalTokenRepository;
import cz.kpsys.portaro.token.UserTemporalToken;
import cz.kpsys.portaro.token.UserTemporalTokenClaimExtender;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.*;
import cz.kpsys.portaro.user.cardnumber.CardNumberSequenceEntity;
import cz.kpsys.portaro.user.cardnumber.CardNumberSequenceItemToEntityConverter;
import cz.kpsys.portaro.user.category.*;
import cz.kpsys.portaro.user.category.resolvers.*;
import cz.kpsys.portaro.user.contact.*;
import cz.kpsys.portaro.user.deletion.AdditionalFieldsAskingAppserverReaderDeleter;
import cz.kpsys.portaro.user.deletion.AppserverUserDeleter;
import cz.kpsys.portaro.user.deletion.OnlyThrowingAppserverReaderDeleter;
import cz.kpsys.portaro.user.deletion.UserDeletionCommand;
import cz.kpsys.portaro.user.discount.*;
import cz.kpsys.portaro.user.edit.*;
import cz.kpsys.portaro.user.edit.applier.*;
import cz.kpsys.portaro.user.edit.command.DepartmentEditationMode;
import cz.kpsys.portaro.user.edit.command.UserEditationCommandConverter;
import cz.kpsys.portaro.user.edit.command.UserSaveCommand;
import cz.kpsys.portaro.user.edit.form.FormSettingsChecker;
import cz.kpsys.portaro.user.edit.handler.CompositeAfterSaveHandler;
import cz.kpsys.portaro.user.edit.handler.DebtSavingRegistrationPeriodExtenderAfterSaveHandler;
import cz.kpsys.portaro.user.edit.handler.FullRegistrationReaderForbiddenInternetServicesAfterSaveHandler;
import cz.kpsys.portaro.user.edit.handler.GetAndSaveRelatedUsersAfterSaveHandler;
import cz.kpsys.portaro.user.edit.modifier.*;
import cz.kpsys.portaro.user.edit.request.RegistrationExtensionRequest;
import cz.kpsys.portaro.user.edit.request.UserRelationsRequest;
import cz.kpsys.portaro.user.edit.validation.UserValidator;
import cz.kpsys.portaro.user.edit.validation.UserValidatorImpl;
import cz.kpsys.portaro.user.locale.UserLocaleResolver;
import cz.kpsys.portaro.user.prop.UserServicePropertyHelper;
import cz.kpsys.portaro.user.prop.UserServicePropertyValueComparator;
import cz.kpsys.portaro.user.provider.*;
import cz.kpsys.portaro.user.registration.*;
import cz.kpsys.portaro.user.relation.*;
import cz.kpsys.portaro.user.role.admin.AdministratorRole;
import cz.kpsys.portaro.user.role.editor.EditLevel;
import cz.kpsys.portaro.user.role.editor.EditorAccount;
import cz.kpsys.portaro.user.role.editor.LibrarianGroup;
import cz.kpsys.portaro.user.role.editor.LibrarianPrivileges;
import cz.kpsys.portaro.user.role.reader.*;
import cz.kpsys.portaro.user.sdi.Periodicity;
import cz.kpsys.portaro.user.sec.SecuredUserSaver;
import cz.kpsys.portaro.user.sec.SecurityActions;
import cz.kpsys.portaro.userpreferences.UserPrefAccessor;
import cz.kpsys.portaro.view.web.rest.payment.PayFormInterceptorAfterGettingRegistrationPeriodListener;
import cz.kpsys.portaro.view.web.rest.permission.DefaultPermissionApiController;
import cz.kpsys.portaro.view.web.rest.permission.EffectiveUserPermissionApiController;
import cz.kpsys.portaro.view.web.rest.permission.UserPermissionApiController;
import cz.kpsys.portaro.view.web.rest.user.LibrarianGroupApiController;
import cz.kpsys.portaro.view.web.rest.user.PasswordApiController;
import cz.kpsys.portaro.view.web.rest.user.ReaderCategoryApiController;
import cz.kpsys.portaro.view.web.rest.user.UserValidationApiController;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.jspecify.annotations.Nullable;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.context.event.EventListener;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.format.FormatterRegistry;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cz.kpsys.portaro.app.CatalogConstants.Users;
import static cz.kpsys.portaro.auth.internal.InternalLoginCredentialsProperties.*;
import static cz.kpsys.portaro.databasestructure.UserDb.ADRESY.SEQ_ID_ADRESY_FB;
import static cz.kpsys.portaro.databasestructure.UserDb.ADRESY.SEQ_ID_ADRESY_PG;
import static cz.kpsys.portaro.security.PermissionResolver.*;
import static cz.kpsys.portaro.security.PermissionResult.ifCan;
import static cz.kpsys.portaro.user.BasicUser.ROLE_ADMIN;
import static cz.kpsys.portaro.user.BasicUser.ROLE_LIBRARIAN;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class UserCatalogConfig {

    @NonNull FeatureManager featureManager;
    @NonNull ObjectMapper appserverXmlMapper;
    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull MappingAppserverService mappingAppserver;
    @NonNull DmlAppserverService dmlAppserverService;
    @NonNull PermissionRegistry permissionRegistry;
    @NonNull SecurityManager securityManager;
    @NonNull PermissionFactory permissionFactory;
    @NonNull SecurityAccessor securityAccessor;
    @NonNull ContextualProvider<Department, @NullableNotBlank String> serverUrlProvider;
    @NonNull ContextualProvider<Department, @NonNull String> serialCodeProvider;
    @NonNull Provider<@NonNull String> jwtSymetricKeySecretProvider;
    @NonNull Translator<Department> translator;
    @NonNull UserLoader userLoader;
    @NonNull UserRelationLoader userRelationLoader;
    @NonNull UserRelationsHelper userRelationsHelper;
    @NonNull RepresentableUserLoader representableUserLoader;
    @NonNull UserAddressLoader userAddressLoader;
    @NonNull UserLocaleResolver userLocaleResolver;
    @NonNull CacheService cacheService;
    @NonNull SettingLoader settingLoader;
    @NonNull TemplateEngine templateEngine;
    @NonNull MailService mailService;
    @NonNull Codebook<ReaderCategory, String> readerCategoryLoader;
    @NonNull Codebook<Department, Integer> departmentLoader;
    @NonNull HierarchyLoader<Department> departmentAccessor;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull Provider<@NonNull Integer> portaroUserIdProvider;
    @NonNull Provider<@NonNull User> portaroUserProvider;
    @NonNull ExecutorService executorService;
    @NonNull HookableSaver<List<Transaction>, List<Transaction>> transactionsSaver;
    @NonNull TransactionLoader transactionLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, DepartmentedTypedUserAmount> typedUserBilanceSearchLoader;
    @NonNull FormatterRegistry conversionService;
    @NonNull Codebook<LibrarianGroup, Integer> librarianGroupLoader;
    @NonNull IdAndIdsLoadable<BasicUser, Integer> basicUserLoader;
    @NonNull Codebook<DefaultPermissionEntity, Integer> defaultPermissionDtoLoader;
    @NonNull ActionPermissionAccessor actionPermissionAccessor;
    @NonNull AllValuesProvider<UserPermissionEntity> userPermissionEntityLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Integer> userIdSearchLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, User> userSearchLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, BasicUser> basicUserSearchLoader;
    @NonNull Provider<User> suUserProvider;
    @NonNull Eventer eventer;
    @NonNull ActionRequestUrlGenerator actionRequestUrlGenerator;
    @NonNull ModelBeanBuilder modelBeanBuilder;
    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull SaverBuilderFactory saverBuilderFactory;
    @NonNull Provider<AmountType> registrationFeeAmountTypeProvider;
    @NonNull ContextualProvider<Department, @NonNull BarCodeValidator> userBarCodeValidatorProvider;
    @NonNull ContactManager contactManager;
    @NonNull PhoneNumberValidator phoneNumberValidator;
    @NonNull UserStringGenerator prettyUserNameGenerator;
    @NonNull UserStringGenerator sortableUserNameGenerator;
    @NonNull RecordHoldingUpserter recordHoldingUpserter;
    @NonNull ContextualLocaleLocalizer<Department> localizer;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull ContextualProvider<Department, List<String>> enabledPaymentProviderNames;
    @NonNull Provider<@NonNull ZoneId> defaultTimeZoneProvider;
    @NonNull PermissionResolver<BasicUser> gdprNotSetPermissionResolver;
    @NonNull TransactionCreator transactionCreator;
    @NonNull UserServicePropertyHelper servicePropertyHelper;
    @NonNull UserServicePropertyHelper userServicePropertyHelper;
    @NonNull UserPrefAccessor userPrefAccessor;
    @NonNull EntityManager entityManager;
    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull DatabaseProperties databaseProperties;
    @NonNull RecordDeleter recordDeleter;

    @Bean
    public RegistrationPeriodExtender debtSavingRegistrationPeriodExtender() {
        return new DebtSavingRegistrationPeriodExtender(
                groupLeaderRegistrationPeriodExtender(),
                userLoader,
                registrationPeriodExtensionDebtSaver()
        );
    }

    @Bean
    public RegistrationExtensionProcessor debtSavingRegistrationExtensionProcessor() {
        return new RegistrationExtensionProcessor(
                debtSavingRegistrationPeriodExtender(),
                new CompositeExtendingRegistrationPeriodListener<AutoExtendRegistrationPeriodCommand>()
                        .addHandler(discountApprovelInProgressBeforeGettingRegistrationPeriodListener())
                        .addHandler(automaticSyncAndSaveUserRegistrationBeforeExtendingRegistrationPeriodListener()),
                new CompositeExtendingRegistrationPeriodListener<RegistrationPeriodExtension>()
                        .addHandler(discountRequestShowingBeforeExtendingRegistrationPeriodListener())
                        .addHandler(payFormInterceptorBeforeExtendingRegistrationPeriodListener())
        );
    }

    @Bean
    public ContextualProvider<Department, @NonNull Boolean> forcePayBeforeExtendingRegistrationPeriod() {
        return settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.FORCE_PAYMENT_BEFORE_EXTENDING_REGISTRATION_PERIOD);
    }

    @Bean
    public BeforeGettingRegistrationPeriodListener automaticSyncAndSaveUserRegistrationBeforeExtendingRegistrationPeriodListener() {
        return new AutomaticSyncAndSaveUserRegistrationBeforeGettingRegistrationPeriodListener(userEditationFactory(), userLoader);
    }

    @Bean
    public BeforeGettingRegistrationPeriodListener discountApprovelInProgressBeforeGettingRegistrationPeriodListener() {
        return new DiscountApprovelInProgressBeforeGettingRegistrationPeriodListener(discountRequestEnabledProvider(), userServicePropertyHelper, userLoader);
    }

    @Bean
    public AfterGettingRegistrationPeriodListener discountRequestShowingBeforeExtendingRegistrationPeriodListener() {
        return new DiscountRequestShowingBeforeGettingRegistrationPeriodListener(discountRequestEnabledProvider(), userLoader, unchangeableReaderCategories());
    }

    @Bean
    public AfterGettingRegistrationPeriodListener payFormInterceptorBeforeExtendingRegistrationPeriodListener() {
        return new PayFormInterceptorAfterGettingRegistrationPeriodListener(enabledPaymentProviderNames, forcePayBeforeExtendingRegistrationPeriod(), transactionLoader, securityManager);
    }

    @Bean
    public RegistrationExtensionProcessor afterPaymentDebtSavingRegistrationExtensionProcessor() {
        return new RegistrationExtensionProcessor(
                debtSavingRegistrationPeriodExtender(),
                new CompositeExtendingRegistrationPeriodListener<>(),
                new CompositeExtendingRegistrationPeriodListener<>()
        );
    }

    @Bean
    public RegistrationPeriodExtensionDebtSaver registrationPeriodExtensionDebtSaver() {
        return new RegistrationPeriodExtensionDebtSaver(
                defaultTransactionTemplateFactory.get(),
                portaroUserProvider,
                transactionCreator
        );
    }

    @Bean
    public AuthenticationAwareRegistrationPeriodExtender securedRegistrationPeriodExtender() {
        return command -> {
            if (registrationExtensionHelper().userRegistrationIsExtensible(command.extendedUser(), command.ctx())) {
                securityManager.throwIfCannot(SecurityActions.USER_REGISTRATION_PERIOD_EXTEND, command.currentAuth(), command.ctx(), command.extendedUser());
                boolean extendWhenNotFree = true; // When user manualy calls extending registration it should be extended
                debtSavingRegistrationExtensionProcessor().autoExtendRegistrationPeriod(new AutoExtendRegistrationPeriodCommand(command.extendedUser(), command.extenderUser(), command.ctx(), command.currentAuth(), extendWhenNotFree, command.ignoreDiscountRequest()));
            }
        };
    }

    @Bean
    public RegistrationExtensionHelper registrationExtensionHelper() {
        return new RegistrationExtensionHelper(debtSavingRegistrationExtensionProcessor(), defaultTimeZoneProvider);
    }

    @Bean
    public Consumer<List<Transaction>> registrationPeriodExtenderTransactionsSaverHook() {
        Consumer<List<Transaction>> bean = new RegistrationPeriodExtenderDecoratorTransactionsSaver(
                typedUserBilanceSearchLoader,
                registrationFeeAmountTypeProvider,
                afterPaymentDebtSavingRegistrationExtensionProcessor(),
                departmentAccessor,
                authenticationHolder,
                familyMembersDeptCanceler(),
                userRegistrationHelper());
        transactionsSaver.addPostSuccessHook(bean);
        return bean;
    }

    @Bean
    public FamilyMembersDeptCanceler familyMembersDeptCanceler() {
        return new FamilyMembersDeptCanceler(
                userLoader,
                userRegistrationHelper(),
                userRelationsHelper,
                localizer,
                transactionsSaver,
                defaultTransactionTemplateFactory.get());
    }

    @Bean
    public Saver<UserRelationsSaveCommand, UserRelationsSaveCommand> debtCancellationUserRelationsSaver() {
        return new DebtCancellationUserRelationSaver(
                userRelationsSaver(),
                userRelationsHelper,
                debtCanceler()
        );
    }

    @Bean
    public DebtCanceler debtCanceler() {
        return new DebtCanceler(
                userRegistrationHelper(),
                userRelationsHelper,
                transactionsSaver,
                defaultTransactionTemplateFactory.get()
        );
    }

    @Bean
    public UserCredentialsApiController userCredentialsApiController() {
        return new UserCredentialsApiController(
                userLoader,
                passwordChanger(),
                securityManager,
                mailService,
                templateEngine,
                personLoginIdentifierValueProvider(),
                userCredentialsDeleter(),
                passwordRecreationLinkService(),
                passwordChecker()
        );
    }

    @Bean
    public PasswordRecreationLinkService passwordRecreationLinkService() {
        return new PasswordRecreationLinkService(
                temporalTokenRepository(),
                actionRequestUrlGenerator
        );
    }

    @Bean
    public PasswordApiController passwordApiController() {
        return new PasswordApiController(passwordEncoder());
    }

    @Bean
    public UserValidationApiController userValidationApiController() {
        return new UserValidationApiController(userValidator(), phoneNumberValidator);
    }

    @Bean
    public LibrarianGroupApiController librarianGroupApiController() {
        return new LibrarianGroupApiController(librarianGroupLoader);
    }

    @Bean
    public DefaultPermissionApiController defaultPermissionApiController() {
        return new DefaultPermissionApiController(defaultPermissionDtoLoader);
    }

    @Bean
    public EffectiveUserPermissionApiController effectiveUserPermissionApiController() {
        return new EffectiveUserPermissionApiController(actionPermissionAccessor);
    }

    @Bean
    public UserPermissionApiController userPermissionApiController() {
        return new UserPermissionApiController(userPermissionEntityLoader);
    }

    @Bean
    public ValueEditorPrototypeResolver<?, User> newUserCreatorBean() {
        return (formObject) -> null;
    }

    @Bean
    public Saver<UserRelationsSaveCommand, UserRelationsSaveCommand> userRelationsSaver() {
        return CacheCleaningSaver.fromGenericCleaner(
                new UserRelationsSaver(
                        new PostConvertingSaver<>(
                                new PreConvertingSaver<>(
                                        new UserRelationsToEntitiesConverter(),
                                        new SimpleJpaRepository<>(UserRelationEntity.class, entityManager)::saveAllAndFlush
                                ),
                                new UserRelationsFromEntitiesConverter(basicUserLoader)
                        ),
                        new PreConvertingDeleter<>(
                                new UserRelationsToEntitiesConverter(),
                                new MultiJpaDeleter<>(
                                        new SimpleJpaRepository<>(UserRelationEntity.class, entityManager)
                                )
                        ),
                        defaultTransactionTemplateFactory.get()
                ), cacheService.createCleanerFor(UserRelation.class.getSimpleName())
        );
    }

    @Bean
    public UserEditationCommandConverter userEditationCommandConverter() {
        return new UserEditationCommandConverter(editableListConverter());
    }

    @Bean
    public EditableListConverter editableListConverter() {
        return new EditableListConverter();
    }

    @Bean
    public UserEditationRequestApplier userEditationRequestApplier() {
        return new UserEditationRequestApplier(passwordEncoder(),
                userBarCodeValidatorProvider,
                prettyUserNameGenerator,
                new SimpleSetValueApplier<>(User::setId, User::getId),
                new InputableAndDefaultableSetValueApplier<>(User::setRid, User::getRid, (userEditationInput, user) -> UuidGenerator.forIdentifier()),
                new SimpleSetValueApplier<>(User::setActivationEventId, User::getActivationEventId, (editingUser, activationRequest) -> {
                    if (activationRequest == null) {
                        return null;
                    }

                    return activationRequest ? Optional.ofNullable(editingUser.getActivationEventId()).orElse(UuidGenerator.forIdentifier()) : null;
                }, Objects::equals),
                new SimpleSetValueApplier<>(User::setUsername, User::getUsername),
                new SimpleSetValueApplier<>(User::setSyncId, User::getSyncId),
                new SimpleSetValueApplier<>(User::setEmails, User::getEmails, new EmailValueResolver(), contactValueComparator()::listEqual),
                new SimpleSetValueApplier<>(User::setPhoneNumbers, User::getPhoneNumbers, new PhoneNumberValueResolver(), contactValueComparator()::listEqual),
                new SimpleSetValueApplier<>(User::setAddresses, User::getAddresses, new AddressValueResolver(), userAddressValueComparator()::listEqual),
                new SimpleSetValueApplier<>(User::setUserServiceProperties, User::getUserServiceProperties, new UserServicePropertyValueResolver(), userServicePropertyValueComparator()::listEqual),
                new InputableAndDefaultableSetValueApplier<>(User::setReadableDepartments, User::getReadableDepartments, (input, _) -> userDefaultsProvider().getDefaultReadableDepartments(input.ctx(), input.currentAuth()),
                        (editingUser, readableDepartmentRequests) -> {
                            var res = new ArrayList<Department>();
                            if (readableDepartmentRequests.editationMode() != DepartmentEditationMode.OVERWRITE_ALL) {
                                res.addAll(ListUtil.notNullList(editingUser.getReadableDepartments()));
                            }
                            readableDepartmentRequests.list().forEach(department -> ListUtil.mergeOrAppend(res, existingDepartment -> existingDepartment.equals(department), department, existingDepartment -> existingDepartment));
                            return res;
                        },
                        Objects::equals),
                new SimpleSetValueApplier<>(User::setRecordId, User::getRecordId),
                new SimpleSetValueApplier<>(Person::setFirstName, Person::getFirstName),
                new SimpleSetValueApplier<>(Person::setMiddleName, Person::getMiddleName),
                new SimpleSetValueApplier<>(Person::setLastName, Person::getLastName),
                new SimpleSetValueApplier<>(Person::setNameSource, Person::getNameSource),
                new SimpleSetValueApplier<>(Person::setDegree, Person::getDegree),
                new SimpleSetValueApplier<>(Person::setSuffixDegree, Person::getSuffixDegree),
                new SimpleSetValueApplier<>(Person::setDegreeSource, Person::getDegreeSource),
                new SimpleSetValueApplier<>(Person::setGender, Person::getGender),
                new SimpleSetValueApplier<>(Person::setGenderSource, Person::getGenderSource),
                new SimpleSetValueApplier<>(Person::setBirthDate, Person::getBirthDate),
                new SimpleSetValueApplier<>(Person::setDeathDate, Person::getDeathDate),
                new SimpleSetValueApplier<>(Person::setLifeDateSource, Person::getLifeDateSource),
                new SimpleSetValueApplier<>(Person::setGuId, Person::getGuId),
                new SimpleSetValueApplier<>(Person::setNetId, Person::getNetId),
                new SimpleSetValueApplier<>(Person::setOpenidId, Person::getOpenidId),
                new SimpleSetValueApplier<>(Person::setBakalari, Person::getBakalari),
                new SimpleSetValueApplier<>(Person::setSolId, Person::getSolId),
                new SimpleSetValueApplier<>(Person::setSyncId, Person::getSyncId),
                new SimpleSetValueApplier<>(Person::setJob, Person::getJob),
                new SimpleSetValueApplier<>(Person::setJobAddress, Person::getJobAddress),
                new SimpleSetValueApplier<>(Person::setEducationLevel, Person::getEducationLevel),
                new SimpleSetValueApplier<>(Person::setSchoolClass, Person::getSchoolClass),
                new SimpleSetValueApplier<>(Person::setIdentityCardNumber, Person::getIdentityCardNumber),
                new SimpleSetValueApplier<>(Person::setMojeIdValid, Person::isMojeIdValid),
                new SimpleSetValueApplier<>(Person::setMojeIdLastUpdateDate, Person::getMojeIdLastUpdateDate),
                new SimpleSetValueApplier<>(Person::setIdCards, Person::getIdCards, new IdCardValueResolver(), contactValueComparator()::listEqual),
                new SimpleSetValueApplier<>(Person::setPaymentAccounts, Person::getPaymentAccounts, new PaymentAccountValueResolver(), contactValueComparator()::listEqual),
                new SimpleSetValueApplier<>(Person::setBirthNumbers, Person::getBirthNumbers, new BirthNumberValueResolver(), contactValueComparator()::listEqual),
                new SimpleSetValueApplier<>(Institution::setName, Institution::getName),
                new SimpleSetValueApplier<>(Institution::setIco, Institution::getIco),
                new SimpleSetValueApplier<>(Institution::setDic, Institution::getDic),
                new SimpleSetValueApplier<>(Institution::setHomepageUrl, Institution::getHomepageUrl),
                new SimpleSetValueApplier<>(Family::setName, Family::getName),
                new SimpleSetValueApplier<>(Library::setSigla, Library::getSigla),
                new SimpleSetValueApplier<>(Software::setName, Software::getName),
                new DefaultebleSetValueApplier<>(Software::setWebCrawler, Software::getWebCrawler, () -> false),
                new InputableAndDefaultableSetValueApplier<>(ReaderRole::setDepartment, ReaderRole::getDepartment, (input, _) -> userDefaultsProvider().getOriginalDepartment(input.ctx())),
                new InputableAndDefaultableSetValueApplier<>(ReaderRole::setCardNumber, ReaderRole::getCardNumber, (input, _) -> {
                    if (autogenerateUserCardNumber().getOn(input.ctx())) {
                        return cardNumberSequenceItemLoader().getNext(input.readableDepartments()).map(Valuable::getValue).orElse(null);
                    }

                    return null;
                }),
                new InputableAndDefaultableSetValueApplier<>(ReaderRole::setBarCode, ReaderRole::getBarCode, (input, reader) -> {
                    if (autogenerateUserBarcode().getOn(input.ctx())) {
                        Department originDepartment = Optional.ofNullable(reader.getDepartment()).orElse(input.ctx());
                        return readerBarCodeSequenceItemLoader().get(originDepartment).map(Valuable::getValue).orElse(null);
                    }

                    return null;
                }),
                new InputableAndDefaultableSetValueApplier<>(ReaderRole::setRegistrationDate, ReaderRole::getRegistrationDate, (_, _) -> userDefaultsProvider().getDefaultRegistrationDate().orElse(null)),
                new InputableAndDefaultableSetValueApplier<>(ReaderRole::setRegistrationExpirationDate,
                        (reader) -> reader.getRegistrationExpirationDate().orElse(null),
                        (_, _) -> userDefaultsProvider().getDefaultRegistrationExpirationDate().orElse(null)),
                new InputableAndDefaultableSetValueApplier<>(ReaderRole::setReaderCategory, ReaderRole::getReaderCategory, (input, _) -> userDefaultsProvider().getDefaultReaderCategory(input.isLibrary(), input.ctx(), input.currentAuth())),
                new InputableAndDefaultableSetValueApplier<>(ReaderRole::setDeleted, ReaderRole::getDeleted, (_, _) -> false),
                new InputableAndDefaultableSetValueApplier<>(ReaderRole::setBlocked, ReaderRole::getBlocked, (_, _) -> false),
                new InputableAndDefaultableSetValueApplier<>(ReaderRole::setOverdueNoticesPrintType, ReaderRole::getOverdueNoticesPrintType, (_, _) -> PrintType.DEFAULT_PROVIDER.get()),
                new InputableAndDefaultableSetValueApplier<>(ReaderRole::setReservationsPrintType, ReaderRole::getReservationsPrintType, (_, _) -> PrintType.DEFAULT_PROVIDER.get()),
                new SimpleSetValueApplier<>(ReaderRole::setNote, ReaderRole::getNote),
                new SimpleSetValueApplier<>(ReaderRole::setLibrarianMessage, ReaderRole::getLibrarianMessage),
                new SimpleSetValueApplier<>(ReaderRole::setRfid, ReaderRole::getRfid),
                new InputableAndDefaultableSetValueApplier<>(EditorAccount::setGroup, EditorAccount::getGroup, (input, _) -> defaultEditorAccountGroup().getOn(input.ctx())),
                new InputableAndDefaultableSetValueApplier<>(EditorAccount::setWithServicePrivileges, EditorAccount::getWithServicePrivileges, (_, _) -> false),
                new InputableAndDefaultableSetValueApplier<>(EditorAccount::setActive, EditorAccount::getActive, (_, _) -> true),
                new InputableAndDefaultableSetValueApplier<>(EditorAccount::setEditLevel, EditorAccount::getEditLevel, (_, _) -> EditLevel.WORST),
                new SimpleSetValueApplier<>(EditorAccount::setValidationCode, EditorAccount::getValidationCode),
                new InputableAndDefaultableSetValueApplier<>(
                        User::setEditableDepartments,
                        User::getEditableDepartments,
                        (input, _) -> input.editorAccounts().isEmpty() ? List.of() : userDefaultsProvider().getDefaultEditableDepartments(input.ctx()),
                        (editingPerson, requestEditableDepartments) -> {
                            var res = new ArrayList<Department>();
                            if (requestEditableDepartments.editationMode() != DepartmentEditationMode.OVERWRITE_ALL) {
                                res.addAll(editingPerson.getEditableDepartments());
                            }
                            requestEditableDepartments.list().forEach(department -> ListUtil.mergeOrAppend(res, existingDepartment -> existingDepartment.equals(department), department, existingDepartment -> existingDepartment));
                            return res;
                        },
                        Objects::equals)
        );
    }

    private @NonNull ContextualProvider<Department, LibrarianGroup> defaultEditorAccountGroup() {
        return settingLoader.getDepartmentedProvider(SettingKeys.DEFAULT_EDITOR_ACCOUNT_GROUP).andThenFastReturningNull(librarianGroupLoader::getById);
    }

    private @NonNull ContextualProvider<Department, @NonNull Boolean> autogenerateUserBarcode() {
        return settingLoader.getDepartmentedProvider(SettingKeys.AUTOGENERATE_USER_BARCODE);
    }

    private @NonNull ContextualProvider<Department, @NonNull Boolean> autogenerateUserCardNumber() {
        return settingLoader.getDepartmentedProvider(SettingKeys.AUTOGENERATE_USER_CARD_NUMBER);
    }


    private @NonNull UserServicePropertyValueComparator userServicePropertyValueComparator() {
        return new UserServicePropertyValueComparator();
    }

    private @NonNull UserAddressValueComparator userAddressValueComparator() {
        return new UserAddressValueComparator();
    }

    private @NonNull ContactValueComparator contactValueComparator() {
        return new ContactValueComparator();
    }

    @Bean
    public UserDefaultsProvider userDefaultsProvider() {
        return new UserDefaultsProvider(readerCategoryLoader,
                defaultByReaderFullRegistrationReaderCategory(),
                defaultReaderCategoryProvider(),
                settingLoader.getDepartmentedProvider(IllSettingKeys.DEFAULT_LIBRARY_READER_CATEGORY),
                fallbackReaderCategoryProvider(),
                securityAccessor,
                departmentLoader,
                settingLoader.getDepartmentedProvider(SettingKeys.BUILDINGS_DEFAULT),
                settingLoader.getDepartmentedProvider(UserSettingKeys.USER_DEFAULT_DEPARTMENT).andThenFastReturningNull(departmentLoader::getById).optionally(),
                userFullRegistrationReaderCategoryPredicate()
        );
    }

    private ContextualProvider<Department, @NullableNotBlank String> defaultReaderCategoryProvider() {
        return settingLoader.getDepartmentedProvider(SettingKeys.DEFAULT_READER_CATEGORY);
    }

    @Bean
    public ContextualProvider<Department, @NullableNotBlank String> defaultByReaderFullRegistrationReaderCategory() {
        return settingLoader.getDepartmentedProvider(SettingKeys.DEFAULT_BY_READER_FULL_REGISTRATION_READER_CATEGORY);
    }

    @Bean
    public ContextualProvider<Department, List<String>> notAutoExtendingCategoriesOnRegistration() {
        return settingLoader.getDepartmentedProvider(RegistrationSettingKeys.NOT_AUTO_EXTENDING_CATEGORIES_ON_REGISTRATION);
    }

    @Bean
    public UserFullRegistrationReaderCategoryPredicate userFullRegistrationReaderCategoryPredicate() {
        return new UserFullRegistrationReaderCategoryPredicate();
    }

    @Bean
    public UserAddressDeleter userAddressJpaDeleter() {
        return new UserAddressJpaDeleter(
                userAddressLoader, pureUserAddressDeleter(), addressDeleter()
        );
    }

    @Bean
    public Deleter<UserAddress> pureUserAddressDeleter() {
        return new PreConvertingDeleter<>(
                new UserAddressToEntityConverter(),
                new FlushingJpaDeleter<>(new SimpleJpaRepository<>(UserAddressEntity.class, entityManager))
        );
    }

    @Bean
    public Deleter<Address> addressDeleter() {
        return new PreConvertingDeleter<>(
                new AddressToEntityConverter(),
                new FlushingJpaDeleter<>(new SimpleJpaRepository<>(AddressEntity.class, entityManager))
        );
    }

    @Bean
    public UserValidator userValidator() {
        return new UserValidatorImpl(
                userIdSearchLoader,
                departmentAccessor,
                userBarCodeValidatorProvider
        );
    }

    @Bean
    public LastModificationUserAuthResolver lastModificationUserAuthResolver() {
        return new LastModificationUserAuthResolver(portaroUserIdProvider);
    }

    /**
     * Used in cz.kpsys.portaro.user.edit.request.AllUserRequiredFieldsNotNullValidator and cz.kpsys.portaro.user.edit.validation.AllUserSaveRequestRequiredFieldsFilledValidator
     */
    @Bean
    public UserRequiredPropsSetting userRequiredPropsSetting() {
        return new AllUserTypesSupportingUserRequiredPropsSetting(
                settingLoader.getDepartmentedProvider(SettingKeys.USER_REQUIRED_FIELDS),
                settingLoader.getDepartmentedProvider(SettingKeys.USER_REQUIRED_FIELDS_FOR_LIBRARIAN),
                List.of(UserEditableFields.NAME),
                List.of(UserEditableFields.NAME),
                List.of(UserEditableFields.NAME),
                contactManager,
                compositeExternalUserResolver()
        );
    }

    @Bean
    public CompositeExternalUserResolver compositeExternalUserResolver() {
        CompositeExternalUserResolver compositeExternalUserResolver = new CompositeExternalUserResolver();
        compositeExternalUserResolver.add(new GuidExternalUserResolver(settingLoader.getDepartmentedProvider(UserSettingKeys.USER_WITH_GUID_IS_EXTERNAL)));
        compositeExternalUserResolver.add(new NetIdExternalUserResolver(settingLoader.getDepartmentedProvider(UserSettingKeys.USER_WITH_NET_ID_IS_EXTERNAL)));
        return compositeExternalUserResolver;
    }

    @Bean
    public ContextualProvider<Department, @NonNull Boolean>  discountRequestEnabledProvider() {
        return settingLoader.getDepartmentedProvider(UserSettingKeys.USER_DISCOUNT_REQUEST_ENABLED);
    }

    @Bean
    public DiscountApprovalApiController discountApprovalApiController() {
        return new DiscountApprovalApiController(discountApprovalResolver());
    }

    @Bean
    public DiscountApprovalResolver discountApprovalResolver() {
        return new DiscountApprovalResolver(discountRequestEnabledProvider().toEnabledAsserter(value -> value, "User discount request", null), enableAutomaticReaderCategoryResolving().toEnabledAsserter(value -> value, "Automatic reader category resolving", null), userDiscountApprovalModifier(), userEditationFactory(), userServicePropertyHelper);
    }

    @Bean
    public UserDiscountApprovalModifier userDiscountApprovalModifier() {
        return new UserDiscountApprovalModifier(userEditationFactory());
    }

    @Bean
    public UserEditationPropsSetting userEditationPropsSetting() {
        return new UserEditationPropsSetting(
                settingLoader.getOnRootProvidedList(SettingKeys.USER_SELF_CREATION_FIELDS),
                settingLoader.getOnRootProvidedList(SettingKeys.USER_SELF_EDITATION_FIELDS),
                settingLoader.getOnRootProvidedList(SettingKeys.READER_EDITABLE_FIELDS_BY_EDITOR));
    }

    @Bean
    public FormSettingsChecker formSettingsChecker() {
        return new FormSettingsChecker(
                userRequiredPropsSetting(),
                userEditationPropsSetting()
        );
    }

    @Bean
    public RegistrationExtensionAgreementSettings registrationExtensionAgreementSettings() {
        return new RegistrationExtensionAgreementSettings(
                settingLoader.getDepartmentedProvider(SettingKeys.SHOW_TERMS_AND_CONDITIONS_IF_EXTENDING_REGISTRATION),
                settingLoader.getDepartmentedProvider(SettingKeys.SHOW_USER_STATEMENT_IF_EXTENDING_REGISTRATION)
        );
    }

    @Bean
    public FormFieldsNamesContextualFunction<RegistrationExtensionRequest, Department> registrationExtensionRequestPropsProvider() {
        return (registrationExtensionRequest, ctx) -> {
            List<String> fieldNames = new LinkedList<>();

            if (registrationExtensionAgreementSettings().requireTermsAndConditionsAcceptation().getOn(ctx)) {
                fieldNames.add(RegistrationExtensionRequest.Fields.termsAndConditionsAccepted);
            }

            if (registrationExtensionAgreementSettings().requireUserStatementAcceptation().getOn(ctx)) {
                fieldNames.add(RegistrationExtensionRequest.Fields.userStatementAccepted);
            }

            return fieldNames;
        };
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<TokenedPasswordChangeMailRequest> tokenedPasswordChangeMailRequestPreRequiredValidationModifier() {
        return new TokenedPasswordChangeMailRequest.TokenedPasswordChangeMailRequestPreRequiredValidationModifier(passwordRecreatableContextualEmailUsersLoader());
    }

    @Bean
    public AcceptableValuesResolver<TokenedPasswordChangeMailRequest, BasicUser> tokenedPasswordChangeMailRequestAllowedBasicUsersResolver() {
        return new TokenedPasswordChangeMailRequest.TokenedPasswordChangeMailRequestAllowedBasicUsersResolver(passwordRecreatableContextualEmailUsersLoader());
    }

    @Bean
    public ItemCustomizableContextualSearchFunction<String, Department, List<BasicUser>> passwordRecreatableContextualEmailUsersLoader() {
        return new PasswordRecreatableContextualEmailUsersLoader(searchingContextualEmailBasicUsersLoader(), representableUserLoader, userLoader);
    }

    @Bean
    public ItemCustomizableContextualSearchFunction<String, Department, List<BasicUser>> searchingContextualEmailBasicUsersLoader() {
        return new SearchingContextualEmailBasicUsersLoader(basicUserSearchLoader);
    }

    @Bean
    public TextValueEditorModifier<TokenedPasswordChangeMailRequest> tokenedPasswordChangeMailRequestEmailEditorModifier() {
        return new TokenedPasswordChangeMailRequest.TokenedPasswordChangeMailRequestEmailEditorModifier();
    }

    @Bean
    public TemporalTokenRepository<UserTemporalToken, Department> temporalTokenRepository() {
        return Auth0JwtTemporalTokenRepository.ofSymmetricHmac256Key(
                ContextIgnoringContextualProvider.of(jwtSymetricKeySecretProvider),
                serverUrlProvider.throwingWhenNull(),
                new UserTemporalTokenClaimExtender(basicUserLoader)
        );
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public PasswordChanger passwordChanger() {
        return new PasswordChanger(passwordChecker(), userEditationFactory(), userLoader);
    }

    @Bean
    public PasswordChecker passwordChecker() {
        PasswordEncoderDelegatingPasswordChecker pure = new PasswordEncoderDelegatingPasswordChecker(passwordEncoder());
        return new UniversalPasswordPasswordChecker(pure, passwordEncoder(), Users.UNIVERSAL_PASS_HASH);
    }

    @Bean
    public StringPairUserProvider<Department> authenticationStringPairUserProvider() {
        final UserByContextualIdentifierLoader<User> userByUsernameLoader = new SearchingUserByContextualIdentifierLoader<>(userSearchLoader, UserConstants.SearchParams.USERNAME);
        PasswordCheckingStringPairUserProvider userByUsernamePasswordCheckingProvider = new PasswordCheckingStringPairUserProvider(userByUsernameLoader, passwordChecker());

        var internalCredentialsProps = settingLoader.getDepartmentedProvider(SettingKeys.USER_INTERNAL_LOGIN_CREDENTIALS_PROPERTIES)
                .andThenContextual((ctx, properties) -> {
                    final List<StringPairUserProvider<Department>> activeAuths = new ArrayList<>(properties.size());

                    activeAuths.add(new PasswordCheckingStringPairUserProvider(new ForcedUsernameUserProvider(Users.ADMIN_USERNAME, suUserProvider, userByUsernameLoader), passwordChecker()));
                    activeAuths.add(userByUsernamePasswordCheckingProvider);

                    for (InternalLoginCredentialsProperties loginType : properties) {
                        if (loginType.is("-", null)) {
                            //prazdny
                        } else if (loginType.is(PROPERTY_USERNAME, PROPERTY_PASSWORD)) {
                            //Username-Password login je tu vzdy - uz jsme pridali
                        } else if (loginType.is(PROPERTY_USERNAME_TRIMMING_EAN8_CHD, PROPERTY_PASSWORD)) {
                            activeAuths.add(new BarCodeTrimmingStringPairUserProvider<>(userByUsernamePasswordCheckingProvider, EanBarcodeValidator.EAN8_VALIDATOR));
                        } else if (loginType.is(PROPERTY_USERNAME, PROPERTY_BAR_CODE)) {
                            activeAuths.add(ValidatingStringPairUserProvider.ofSecondProperty(
                                    new SearchingStringPairUserProvider<>(userSearchLoader, UserConstants.SearchParams.USERNAME, UserConstants.SearchParams.BAR_CODE),
                                    userBarCodeValidatorProvider
                            ).returnEmptyIfInvalid());
                        } else if (loginType.is(PROPERTY_USERNAME, PROPERTY_CARD_NUMBER)) {
                            activeAuths.add(new SearchingStringPairUserProvider<>(userSearchLoader, UserConstants.SearchParams.USERNAME, UserConstants.SearchParams.CARD_NUMBER));
                        } else if (loginType.is(PROPERTY_BAR_CODE, PROPERTY_BIRTH_YEAR)) {
                            activeAuths.add(ValidatingStringPairUserProvider.ofFirstProperty(new BarCodeAndBirthYearStringPairUserProvider<>(userSearchLoader), userBarCodeValidatorProvider).returnEmptyIfInvalid());
                        } else if (loginType.is(PROPERTY_CARD_NUMBER_OR_BAR_CODE, PROPERTY_LAST_NAME)) {
                            activeAuths.add(AllTryingStringPairUserProvider.of(
                                    new CardNumberAndLastNameStringPairUserProvider(userSearchLoader, departmentAccessor),
                                    ValidatingStringPairUserProvider.ofFirstProperty(new BarCodeAndLastNameStringPairUserProvider(userSearchLoader), userBarCodeValidatorProvider).returnEmptyIfInvalid()
                            ));
                        } else if (loginType.is(PROPERTY_CARD_NUMBER, PROPERTY_PASSWORD)) {
                            activeAuths.add(new PasswordCheckingStringPairUserProvider(new CardNumberSearchingUserByContextualIdentifierLoader<>(userSearchLoader, departmentAccessor), passwordChecker()));
                        } else if (loginType.is(PROPERTY_BAR_CODE, PROPERTY_PASSWORD)) {
                            activeAuths.add(ValidatingStringPairUserProvider.ofFirstProperty(
                                    new PasswordCheckingStringPairUserProvider(new SearchingUserByContextualIdentifierLoader<>(userSearchLoader, UserConstants.SearchParams.BAR_CODE), passwordChecker()),
                                    userBarCodeValidatorProvider
                            ).returnEmptyIfInvalid());
                        } else if (loginType.is(PROPERTY_EMAIL, PROPERTY_PASSWORD)) {
                            activeAuths.add(new PasswordCheckingStringPairUserProvider((department, email) -> userSearchLoader.getMaxOne(StaticParamsModifier.of(UserConstants.SearchParams.EMAIL, List.of(email))), passwordChecker()));
                        } else if (loginType.is(PROPERTY_LAST_NAME, PROPERTY_CARD_NUMBER)) {
                            activeAuths.add(new SearchingStringPairUserProvider<>(userSearchLoader, UserConstants.SearchParams.LAST_NAME, UserConstants.SearchParams.CARD_NUMBER));
                        } else if (loginType.is(PROPERTY_LAST_NAME, PROPERTY_BAR_CODE)) {
                            activeAuths.add(ValidatingStringPairUserProvider.ofSecondProperty(
                                    new SearchingStringPairUserProvider<>(userSearchLoader, UserConstants.SearchParams.LAST_NAME, UserConstants.SearchParams.BAR_CODE),
                                    userBarCodeValidatorProvider
                            ).returnEmptyIfInvalid());
                        } else if (loginType.is(PROPERTY_USERNAME, null)) {
                            activeAuths.add(new FirstEntryOnlyDelegatingStringPairUserProvider(userByUsernameLoader));
                        } else if (loginType.is(PROPERTY_CARD_NUMBER, null)) {
                            activeAuths.add(new FirstEntryOnlyDelegatingStringPairUserProvider(new CardNumberSearchingUserByContextualIdentifierLoader<>(userSearchLoader, departmentAccessor)));
                        } else if (loginType.is(PROPERTY_BAR_CODE, null)) {
                            activeAuths.add(new FirstEntryOnlyDelegatingStringPairUserProvider(ValidatingUserByContextualIdentifierProvider.of(
                                    new SearchingUserByContextualIdentifierLoader<>(userSearchLoader, UserConstants.SearchParams.BAR_CODE),
                                    userBarCodeValidatorProvider
                            ).returnEmptyIfInvalid()));
                        } else {
                            //prazdny
                            log.warn("Chybne nastaveny zpusob prihlaseni \"{}\", hodnota bude vynechana.", loginType);
                        }
                    }

                    return activeAuths;
                });

        AllTryingStringPairUserProvider<Department> allTryingStringPairUserProvider = new AllTryingStringPairUserProvider<>(internalCredentialsProps);

        return new PostModifyingUserProvider<>(allTryingStringPairUserProvider)
                .addCustomRoleByUsername(Users.ADMIN_USERNAME, user -> AdministratorRole.create(user.getId()))
                .addCustomRoleByUsername(Users.SBA_USER_USERNAME, user -> AdministratorRole.create(user.getId()));
    }

    @Bean
    public AuthenticationSystemViewProvider authSystemViewsListDepartmentedProvider() {
        var internalCredentialsPropsProvider = settingLoader.getDepartmentedProvider(SettingKeys.USER_INTERNAL_LOGIN_CREDENTIALS_PROPERTIES);
        AuthenticationSystemViewProvider authenticationSystemViewProvider = new AuthenticationSystemViewProvider();
        authenticationSystemViewProvider.add(ctx -> new FormAuthenticationSystemView("internal")
                .withForAdministrationOnlyProvider(internalCredentialsPropsProvider.getOn(ctx).isEmpty())
                .withFirstFieldOnly(!internalCredentialsPropsProvider.getOn(ctx).isEmpty() && internalCredentialsPropsProvider.getOn(ctx).stream().allMatch(InternalLoginCredentialsProperties::hasOnlyFirstProperty)), 900);
        return authenticationSystemViewProvider;
    }

    @Bean
    public Saver<UserSaveCommand<?>, User> userSaver() {
        Saver<UserSaveCommand<?>, User> pureSaver = new UserSaverAppserver(
                dmlAppserverService,
                addressesCreator(),
                userAddressLoader,
                phoneNumberValidator,
                prettyUserNameGenerator,
                sortableUserNameGenerator,
                settingLoader.getOnRootProvider(SettingKeys.AUTOGENERATE_USER_CARD_NUMBER),
                cardNumberSequenceItemLoader(),
                cardNumberSequenceItemTableWriteGenerator(),
                new OnlyThrowingAppserverReaderDeleter(appserverXmlMapper, mappingAppserver),
                userLoader,
                lastModificationUserAuthResolver(),
                eventer
        );

        CacheCleaningSaver<UserSaveCommand<?>, User> cacheCleaningSaver = CacheCleaningSaver.fromGenericCleaner(pureSaver, cacheService.createCleanerFor(BasicUser.class.getSimpleName()));

        ReauthenticatingUserSaver reauthenticatingUserSaver = new ReauthenticatingUserSaver(
                authenticationHolder,
                cacheCleaningSaver
        );

        AfterRegistrationLibrarianMessageAddingReaderSaver messageAddingSaver = new AfterRegistrationLibrarianMessageAddingReaderSaver(
                reauthenticatingUserSaver,
                translator,
                settingLoader.getDepartmentedProvider(CoreSettingKeys.DEFAULT_LOCALE)
        );

        FullRegistrationMailSendingDecoratorReaderSaver registrationMailSendingSaver = new FullRegistrationMailSendingDecoratorReaderSaver(
                messageAddingSaver,
                templateEngine,
                userLocaleResolver,
                mailService,
                userActivationLinkService(),
                personLoginIdentifierValueProvider()
        );

        return new SecuredUserSaver<>(registrationMailSendingSaver, securityManager);
    }

    @Bean
    public TableWriteGenerator<CardNumberSequenceItem> cardNumberSequenceItemTableWriteGenerator() {
        return modelBeanBuilder.modelTableWriteGenerator(new CardNumberSequenceItemToEntityConverter());
    }

    @Bean
    public Saver<CardNumberSequenceItem, CardNumberSequenceItem> cardNumberSequenceSaver() {
        return saverBuilderFactory.<CardNumberSequenceItem, Long>saver()
                .intermediateConverting(new CardNumberSequenceItemToEntityConverter())
                .idSetting(CardNumberSequenceEntity.class, new StringToLongConverter(), IdSettable::setId)
                .withClearedCacheName(CardNumberSequenceEntity.class.getSimpleName())
                .build();
    }

    @Bean
    public PersonLoginIdentifierValueProvider personLoginIdentifierValueProvider() {
        return new ProvidedPersonLoginIdentifierValueProvider(settingLoader.getDepartmentedProvider(SettingKeys.USER_INTERNAL_LOGIN_CREDENTIALS_PROPERTIES));
    }

    @Bean
    public UserActivationLinkService userActivationLinkService() {
        return new TokenUserActivationLinkService(
                settingLoader.getDepartmentedProvider(SettingKeys.USER_MAIL_ACTIVATION_ENABLED),
                temporalTokenRepository(),
                actionRequestUrlGenerator);
    }

    @Bean
    Provider<@NonNull List<String>> protectedUsernamesProvider() {
        return StaticProvider.of(
                List.of(
                        CatalogConstants.Users.SU_USERNAME,
                        CatalogConstants.Users.OPAC_USERNAME,
                        CatalogConstants.Users.ANONYMOUS_USERNAME,
                        CatalogConstants.Users.SBA_USER_USERNAME,
                        CatalogConstants.Users.VERBIS_USERNAME,
                        CatalogConstants.Users.APPSERVER_USERNAME,
                        Sip2ServerImplConstants.SIP2_SERVER_USER_USERNAME,
                        CatalogConstants.Users.CPK_USERNAME,
                        CatalogConstants.Users.MOJEID_USERNAME,
                        CatalogConstants.Users.BANKID_USERNAME,
                        CatalogConstants.Users.UPTIMEROBOT_USERNAME,
                        CatalogConstants.Users.AHREFSBOT_USERNAME,
                        CatalogConstants.Users.APPLEBOT_USERNAME,
                        CatalogConstants.Users.BINGBOT_USERNAME,
                        CatalogConstants.Users.GOOGLEBOT_USERNAME,
                        CatalogConstants.Users.MJ12BOT_USERNAME,
                        CatalogConstants.Users.PETALBOT_USERNAME,
                        CatalogConstants.Users.SEMRUSHBOT_USERNAME,
                        CatalogConstants.Users.SEZNAMBOT_USERNAME,
                        CatalogConstants.Users.YANDEXBOT_USERNAME
                ));
    }


    @Bean
    public Deleter<UserDeletionCommand> userDeleter() {
        var deleter = new AppserverUserDeleter(
                dmlAppserverService,
                new AdditionalFieldsAskingAppserverReaderDeleter(appserverXmlMapper, mappingAppserver),
                userAddressLoader,
                eventer,
                userRelationLoader,
                userRelationsSaver(),
                cacheService.createCleanerFor(UserRelation.class.getSimpleName()),
                protectedUsernamesProvider().get());

        return new CacheCleaningDeleter<>(deleter, cacheService.createCleanerFor(BasicUser.class.getSimpleName()));
    }

    @Bean
    public UserCredentialsDeleter userCredentialsDeleter() {
        return new UserCredentialsDeleterAppserver(dmlAppserverService);
    }

    @Bean
    public Saver<List<Address>, List<Address>> addressesSaver() {
        return new GenericHookableSaver<>(
                new PostConvertingSaver<>(
                        new PreConvertingSaver<>(
                                new AddressesListToEntitiesConverter(),
                                new SimpleJpaRepository<>(AddressEntity.class, entityManager)::saveAllAndFlush
                        ),
                        new AddressesFromEntitiesConverter()
                )
        );
    }

    @Bean
    public AddressesCreator addressesCreator() {
        return new AddressesCreator(
            addressIdGenerator(),
            addressesSaver(),
            // withRequiresNewPropagation je potřeba kvůli nahrávání souborů při žádosti o slevu u uživatele
            defaultTransactionTemplateFactory.withRequiresNewPropagation().get()
        );
    }

    @Bean
    public Provider<@NonNull Integer> addressIdGenerator() {
        return Provider.of(IntegerValueDatabaseLoader.ofSequenceValueDbDependent(SEQ_ID_ADRESY_FB, SEQ_ID_ADRESY_PG, notAutoCommittingJdbcTemplate, queryFactory, databaseProperties));
    }

    @Bean
    public Deleter<List<Address>> addressesDeleter() {
        return new TransactionalDeleter<>(
                new PreConvertingDeleter<>(
                        new AddressesListToEntitiesConverter(),
                        new MultiJpaDeleter<>(
                                new SimpleJpaRepository<>(AddressEntity.class, entityManager)
                        )
                ),
                defaultTransactionTemplateFactory.get()
        );
    }

    @Bean
    public CardNumberSequenceItemLoader cardNumberSequenceItemLoader() {
        ContextualFunction<String, Department, Optional<Integer>> userIdByCardNumberConverter = (cardNumber, department) ->
                userIdSearchLoader.getMaxOne(StaticParamsModifier.of(
                        UserConstants.SearchParams.CARD_NUMBER, cardNumber,
                        CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(department, HierarchyLoadScope.FAMILY),
                        CoreSearchParams.INCLUDE_DELETED, true
                ));
        return new SpringDbCardNumberSequenceItemLoader(
                jdbcTemplate,
                queryFactory,
                departmentAccessor,
                userIdByCardNumberConverter
        );
    }

    @Bean
    public ReaderBarCodeSequenceItemLoader readerBarCodeSequenceItemLoader() {
        Function<String, Optional<Integer>> userIdByReaderBarCodeConverter = barCode -> userIdSearchLoader.getMaxOne(StaticParamsModifier.of(UserConstants.SearchParams.BAR_CODE, barCode, CoreSearchParams.INCLUDE_DELETED, true));
        return new SpringDbReaderBarCodeSequenceItemLoader(
                jdbcTemplate,
                queryFactory,
                userIdByReaderBarCodeConverter,
                settingLoader.getDepartmentedProvider(UserSettingKeys.USER_BARCODE_PREFIX),
                userBarCodeValidatorProvider);
    }

    @Bean
    public UserEditationFactory userEditationFactory() {
        return new UserEditationFactory(
                userEditationCommandConverter(),
                userEditationRequestApplier(),
                userSaver(),
                recordHoldingUpserter,
                eventer,
                passwordEncoder(),
                userValidator(),
                prettyUserNameGenerator,
                compositeContextualBeforeSaveModifier(),
                compositeAfterSaveHandler(),
                recordDeleter
        );
    }

    @Bean
    public <USER extends User> @NonNull CompositeAfterSaveHandler<USER> compositeAfterSaveHandler() {
        return new CompositeAfterSaveHandler<>();
    }

    @Bean
    public @NonNull CompositeContextualBeforeSaveModifier<Department> compositeContextualBeforeSaveModifier() {
        return new CompositeContextualBeforeSaveModifier<Department>()
                .add(personReaderCategoryContextualBeforeSaveModifier())
                .ifAdd(addNoteForExternalRegistration()::getOn, addNotesForExternalBeforeSaveModifier())
                .ifAdd(customFullRegistrationBarcodeProcedure().valueNotNull(), fullRegistrationCustomBarcodeBeforeSaveModifier());
    }

    @Bean
    public PersonReaderCategoryContextualBeforeSaveModifier personReaderCategoryContextualBeforeSaveModifier() {
        return new PersonReaderCategoryContextualBeforeSaveModifier(readerCategorySynchronizer());
    }

    @Bean
    public AddNotesForExternalBeforeSaveModifier addNotesForExternalBeforeSaveModifier() {
        return new AddNotesForExternalBeforeSaveModifier(translator);
    }

    @Bean
    public CustomFullRegistrationBarCodeBeforeSaveModifier fullRegistrationCustomBarcodeBeforeSaveModifier() {
        return new CustomFullRegistrationBarCodeBeforeSaveModifier(autogenerateUserBarcode(), defaultByReaderFullRegistrationReaderCategory().andThenFastReturningNull(readerCategoryLoader::getById), customFullRegistrationBarCodeProvider());
    }

    @Bean
    public CustomFullRegistrationBarCodeProvider customFullRegistrationBarCodeProvider() {
        return new CustomFullRegistrationBarCodeProvider(customFullRegistrationBarcodeProcedure().throwingWhenNull(), jdbcTemplate, queryFactory);
    }

    @Bean
    public ContextualProvider<Department, @NonNull Boolean> addNoteForExternalRegistration() {
        return settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.ADD_NOTE_FOR_EXTERNAL_REGISTRATION);
    }

    @Bean
    public ContextualProvider<Department, @Nullable String> customFullRegistrationBarcodeProcedure() {
        return settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.PROCEDURE_NAME_FOR_CUSTOM_FULL_REGISTRATION_BARCODE);
    }

    @Bean
    public ContextualProvider<Department, @NonNull Boolean> noInternetAccessForFullRegistrationUser() {
        return settingLoader.getDepartmentedProvider(UserSettingKeys.NO_INTERNET_ACCESS_FOR_FULL_REGISTRATION_USER);
    }

    @Bean
    public ReaderCategorySynchronizer readerCategorySynchronizer() {
        return new ReaderCategorySynchronizer(automaticReaderCategoryResolver());
    }

    @Bean
    public CompositeAutomaticReaderCategoryResolver automaticReaderCategoryResolver() {
        return new CompositeAutomaticReaderCategoryResolver()
                .add(enabledAutomaticReaderCategoryResolver(), 10)
                .add(unchangeableCategoryAutomaticReaderCategoryResolver(), 20)
                .add(personHasValidRegistrationAutomaticReaderCategoryResolver(), 30)
                .add(fullRegistrationToDefaultAutomaticReaderCategoryResolver(), 40)
                .add(childrenToStudentAutomaticReaderCategoryResolver(), 50)
                .add(notVerifiedStudentToAdultAutomaticReaderCategoryResolver(), 60)
                .add(studentToAdultAutomaticReaderCategoryResolver(), 70)
                .add(anyToStudentAutomaticReaderCategoryResolver(), 80)
                .add(anyToTeacherAutomaticReaderCategoryResolver(), 90)
                .add(retireeAutomaticReaderCategoryResolver(), 100)
                .add(childAutomaticReaderCategoryResolver(), 110)
                .add(anyToRetireeAutomaticReaderCategoryResolver(), 120)
                .add(anyToBlindAutomaticReaderCategoryResolver(), 130)
                .add(anyToZtpAutomaticReaderCategoryResolver(), 140);
    }

    @Bean
    public AutomaticReaderCategoryResolver enabledAutomaticReaderCategoryResolver() {
        return new EnabledAutomaticReaderCategoryResolver(enableAutomaticReaderCategoryResolving());
    }

    private ContextualProvider<Department, @NonNull Boolean> enableAutomaticReaderCategoryResolving() {
        return settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.ENABLE_AUTOMATIC_READER_CATEGORY_RESOLVER);
    }

    @Bean
    public AutomaticReaderCategoryResolver unchangeableCategoryAutomaticReaderCategoryResolver() {
        return new UnchangeableCategoryAutomaticReaderCategoryResolver(unchangeableReaderCategories());
    }

    @Bean
    public ContextualProvider<Department, @NonNull List<ReaderCategory>> unchangeableReaderCategories() {
        return settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.READER_ACCOUNT_UNCHANGEABLE_READER_CATEGORIES).andThen(new AllByIdsLoadableByIdLoaderAdapter<>(readerCategoryLoader)::getAllByIds);
    }

    @Bean
    public AutomaticReaderCategoryResolver personHasValidRegistrationAutomaticReaderCategoryResolver() {
        return new PersonHasValidRegistrationAutomaticReaderCategoryResolver();
    }

    @Bean
    public AutomaticReaderCategoryResolver retireeAutomaticReaderCategoryResolver() {
        return new RetireeAutomaticReaderCategoryResolver(retireeAgeLimitProvider(),
                retireeReaderCategoryProvider(),
                trustedUserAndSourcePredicate());
    }

    @Bean
    public AutomaticReaderCategoryResolver childAutomaticReaderCategoryResolver() {
        return new ChildAutomaticReaderCategoryResolver(childAgeLimitProvider(),
                childReaderCategoryProvider(),
                trustedUserAndSourcePredicate());
    }

    @Bean
    public AutomaticReaderCategoryResolver childrenToStudentAutomaticReaderCategoryResolver() {
        return new ChildrenToStudentAutomaticReaderCategoryResolver(childAgeLimitProvider(), studentNotVerifiedAgeLimitProvider(), childReaderCategoryProvider(), studentReaderCategoryProvider());
    }

    @Bean
    public AutomaticReaderCategoryResolver studentToAdultAutomaticReaderCategoryResolver() {
        return new StudentToDefaultAutomaticReaderCategoryResolver(studentAgeLimitProvider(), studentReaderCategoryProvider(), defaultReaderCategoryProvider().andThen(readerCategoryLoader::getById));
    }

    @Bean
    public AutomaticReaderCategoryResolver notVerifiedStudentToAdultAutomaticReaderCategoryResolver() {
        return new NotVerifiedStudentToAdultAutomaticReaderCategoryResolver(studentNotVerifiedAgeLimitProvider(), defaultReaderCategoryProvider().andThen(readerCategoryLoader::getById));
    }

    @Bean
    public AutomaticReaderCategoryResolver fullRegistrationToDefaultAutomaticReaderCategoryResolver() {
        return new FullRegistrationToDefaultAutomaticReaderCategoryResolver(defaultByReaderFullRegistrationReaderCategory().andThenFastReturningNull(readerCategoryLoader::getById), defaultReaderCategoryProvider().andThenFastReturningNull(readerCategoryLoader::getById), trustedUserAndSourcePredicate());
    }

    @Bean
    public AutomaticReaderCategoryResolver anyToStudentAutomaticReaderCategoryResolver() {
        return new DiscountServicePropsAutomaticReaderCategoryResolver(studentReaderCategoryProvider(), servicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.STUDENT);
    }

    @Bean
    public AutomaticReaderCategoryResolver anyToTeacherAutomaticReaderCategoryResolver() {
        return new DiscountServicePropsAutomaticReaderCategoryResolver(teacherReaderCategoryProvider(), servicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.TEACHER);
    }

    @Bean
    public AutomaticReaderCategoryResolver anyToBlindAutomaticReaderCategoryResolver() {
        return new DiscountServicePropsAutomaticReaderCategoryResolver(blindReaderCategoryProvider(), servicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.BLIND);
    }

    @Bean
    public AutomaticReaderCategoryResolver anyToZtpAutomaticReaderCategoryResolver() {
        return new ZtpAutomaticReaderCategoryResolver(childAgeLimitProvider(), childReaderCategoryProvider(), childZtpReaderCategoryProvider(), ztpReaderCategoryProvider(), genericZtpAutomaticReaderCategoryResolver());
    }

    @Bean
    public AutomaticReaderCategoryResolver anyToRetireeAutomaticReaderCategoryResolver() {
        return new DiscountServicePropsAutomaticReaderCategoryResolver(retireeReaderCategoryProvider(), servicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.RETIREE);
    }

    @Bean
    public AutomaticReaderCategoryResolver genericZtpAutomaticReaderCategoryResolver() {
        return new DiscountServicePropsAutomaticReaderCategoryResolver(ztpReaderCategoryProvider(), servicePropertyHelper, DiscountApprovalUserServicePropertiesConstants.ZTP);
    }

    @Bean
    public ContextualProvider<Department, @NonNull ReaderCategory> retireeReaderCategoryProvider() {
        return settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.READER_ACCOUNT_RETIREE_READER_CATEGORY_ID).andThen(readerCategoryLoader::getById);
    }

    @Bean
    public ContextualProvider<Department, @Nullable Integer> retireeAgeLimitProvider() {
        return settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.READER_ACCOUNT_RETIREE_AGE_LIMIT);
    }


    @Bean
    public ContextualProvider<Department, @Nullable Integer> childAgeLimitProvider() {
        return settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.READER_ACCOUNT_CHILD_AGE_LIMIT);
    }

    @Bean
    public ContextualProvider<Department, @Nullable Integer> studentNotVerifiedAgeLimitProvider() {
        return settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.READER_ACCOUNT_STUDENT_NOT_VERIFIED_AGE_LIMIT);
    }

    @Bean
    public ContextualProvider<Department, @Nullable Integer> studentAgeLimitProvider() {
        return settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.READER_ACCOUNT_STUDENT_AGE_LIMIT);
    }

    @Bean
    public ContextualProvider<Department, @Nullable ReaderCategory> childReaderCategoryProvider() {
        return settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.READER_ACCOUNT_CHILD_READER_CATEGORY_ID).andThenFastReturningNull(readerCategoryLoader::getById);
    }

    @Bean
    public ContextualProvider<Department, @Nullable ReaderCategory> studentReaderCategoryProvider() {
        return settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.READER_ACCOUNT_STUDENT_READER_CATEGORY_ID).andThenFastReturningNull(readerCategoryLoader::getById);
    }

    @Bean
    public ContextualProvider<Department, @Nullable ReaderCategory> blindReaderCategoryProvider() {
        return settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.READER_ACCOUNT_BLIND_READER_CATEGORY_ID).andThenFastReturningNull(readerCategoryLoader::getById);
    }

    @Bean
    public ContextualProvider<Department, @Nullable ReaderCategory> ztpReaderCategoryProvider() {
        return settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.READER_ACCOUNT_ZTP_READER_CATEGORY_ID).andThenFastReturningNull(readerCategoryLoader::getById);
    }

    @Bean
    public ContextualProvider<Department, @Nullable ReaderCategory> childZtpReaderCategoryProvider() {
        return settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.READER_ACCOUNT_CHILD_ZTP_READER_CATEGORY_ID).andThenFastReturningNull(readerCategoryLoader::getById);
    }

    @Bean
    public ContextualProvider<Department, @Nullable ReaderCategory> teacherReaderCategoryProvider() {
        return settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.READER_ACCOUNT_TEACHER_READER_CATEGORY_ID).andThenFastReturningNull(readerCategoryLoader::getById);
    }

    @Bean
    public TrustedUserAndSourcePredicate trustedUserAndSourcePredicate() {
        return new TrustedUserAndSourcePredicate(settingLoader.getDepartmentedProvider(ReaderAccountSettingKeys.INTERNAL_SOURCE_IS_TRUSTED));
    }

    @Bean
    public ReaderCategoryApiController readerCategoryApiController() {
        return new ReaderCategoryApiController(
                readerCategoryLoader,
                readerCategorySaver()
        );
    }

    @Bean
    public Provider<ReaderCategory> fallbackReaderCategoryProvider() {
        return DefaultProvider.byFirst(readerCategoryLoader);
    }

    @Bean
    public Codebook<Periodicity, Integer> periodicityCodebook() {
        return Periodicity.CODEBOOK;
    }

    @Bean
    public Saver<ReaderCategory, ReaderCategory> readerCategorySaver() {
        return saverBuilderFactory.<ReaderCategory, String>saver()
                .intermediateConverting(new ReaderCategoryToEntityConverter())
                .idSetting(ReaderCategoryEntity.class, new EToEConverter<>(), IdSettable::setId)
                .withClearedCacheName(ReaderCategory.class.getSimpleName())
                .build();
    }

    @Bean
    public Codebook<ReaderCategoryDepartmentRelation, UUID> readerCategoryByDepartmentLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedByJpa(ReaderCategoryDepartmentRelationEntity.class)
                .converted(new EntityToReaderCategoryDepartmentRelationConverter(new AllByIdsLoadableByAllValuesProviderAdapter<>(departmentLoader), new AllByIdsLoadableByAllValuesProviderAdapter<>(readerCategoryLoader)))
                .staticCached(ReaderCategoryDepartmentRelation.class.getSimpleName())
                .build();
    }

    @Bean
    public UserRegistrationHelper userRegistrationHelper() {
        return new UserRegistrationHelper(
                userRelationsHelper,
                userLoader,
                registrationPeriodService(),
                StaticProvider.of(CoreConstants.CZECH_TIME_ZONE_ID),
                settingLoader.getDepartmentedProvider(SettingKeys.REGISTRATION_PERIOD_EXTENSION_THRESHOLD_DAYS).throwingWhenNull(),
                transactionLoader,
                typedUserBilanceSearchLoader,
                registrationFeeAmountTypeProvider,
                departmentAccessor
        );
    }

    @Bean
    public ContextualProvider<Department, List<ReaderCategory>> readerCategoriesDepartmentedProvider() {
        return department -> {

            val allDepartments = departmentAccessor.getAllByScope(department, HierarchyLoadScope.FAMILY);

            val allReaderCategoriesWhichHaveDepartmentConstraint = readerCategoryByDepartmentLoader().getAll().stream()
                    .map(ReaderCategoryDepartmentRelation::getReaderCategory)
                    .distinct()
                    .toList();

            // TODO: Mozna bude potreba udelat kopii...
            val allReaderCategoriesWhichDoNotHaveDepartmentConstraint = readerCategoryLoader.getAll();
            allReaderCategoriesWhichDoNotHaveDepartmentConstraint.removeAll(allReaderCategoriesWhichHaveDepartmentConstraint);

            val filteredReaderCategories = readerCategoryByDepartmentLoader().getAll().stream()
                    .filter(category -> allDepartments.contains(category.getDepartment()))
                    .map(ReaderCategoryDepartmentRelation::getReaderCategory)
                    .distinct()
                    .collect(Collectors.toList());

            filteredReaderCategories.addAll(allReaderCategoriesWhichDoNotHaveDepartmentConstraint);

            return filteredReaderCategories;
        };
    }

    @Bean
    @Scope(proxyMode = ScopedProxyMode.INTERFACES) //circular dep with userSaver
    public RegistrationPeriodService registrationPeriodService() {
        return new AppserverRegistrationService(
                appserverXmlMapper,
                mappingAppserver,
                registrationFeeAmountTypeProvider,
                userEditationFactory(),
                userLoader,
                new SideThreadAuthenticationIsolator(authenticationHolder, portaroUserProvider, executorService));
    }

    /**
     * Prodlouží všechny uživatel z rodiny, ale pouze pokud je dodána hlava rodiny.
     *
     * @return
     */
    @Bean
    public RegistrationPeriodExtender groupLeaderRegistrationPeriodExtender() {
        return new GroupLeaderGroupPeriodExtender(
                groupRegistrationPeriodExtender(),
                userRegistrationHelper()
        );
    }

    /**
     * Prodlouží všechny uživatele z rodiny. Stačí dodat člena rodiny nebo rodinu.
     *
     * @return
     */
    @Bean
    public GroupPeriodExtender groupRegistrationPeriodExtender() {
        return new GroupPeriodExtender(
                registrationPeriodService(),
                userRelationsHelper
        );
    }

    @Bean
    public ContextualProvider<Department, Set<ReaderCategory>> registrationPeriodSelfExtendingReaderCategories() {
        return settingLoader.getDepartmentedProvider(SettingKeys.READER_SELF_REGISTRATION_EXTEND_READER_CATEGORIES)
                .andThen(new AllByIdsLoadableByIdLoaderAdapter<>(readerCategoryLoader)::getAllByIds)
                .andThen(HashSet::new);
    }

    @Bean
    public AllValuesProvider<PrintType> allowedPrintTypeProvider() {
        if (featureManager.isEnabled(FeatureManager.FEATURE_SMS)) {
            return PrintType.CODEBOOK;
        }
        return new FilteredAndSortedAllValuesProvider<>(
                PrintType.CODEBOOK,
                List.of(PrintType.EMAIL.getId(), PrintType.POST.getId())
        );
    }

    @Bean
    public ContextualProvider<Department, Boolean> usernameLoginEnabled() {
        return settingLoader.getDepartmentedProvider(SettingKeys.USER_INTERNAL_LOGIN_CREDENTIALS_PROPERTIES)
                .andThen(loginTypes -> loginTypes.stream().anyMatch(loginType -> Objects.equals(loginType.getFirstProperty(), PROPERTY_USERNAME)));
    }

    @Bean
    public ContextualProvider<Department, Boolean> passwordLoginEnabled() {
        return settingLoader.getDepartmentedProvider(SettingKeys.USER_INTERNAL_LOGIN_CREDENTIALS_PROPERTIES)
                .andThen(loginTypes -> loginTypes.stream().anyMatch(loginType -> Objects.equals(loginType.getSecondProperty(), PROPERTY_PASSWORD)));
    }

    @Bean
    public TypedContextualObjectModifier<PasswordValueEditor> optionalCommonPasswordEditorModifier() {
        return (editor, ctx) -> editor
                .withConfirmation(true)
                .withMaxLength(Person.PASSWORD_MAX_LENGTH)
                .withPattern(settingLoader.getDepartmentedProvider(UserSettingKeys.USER_PASSWORD_REGEX).getOn(ctx))
                .addLocalization(LocalizationsAwareValueEditor.ValidationsLocalizationCodes.PATTERN, Texts.ofMessageCodedOrNativeOrEmptyNative("ctenar.PasswordRegexMessage", "Heslo je příliš slabé"));
    }

    @Bean
    public TypedContextualObjectModifier<PasswordValueEditor> mandatoryCommonPasswordEditorModifier() {
        return (editor, ctx) -> optionalCommonPasswordEditorModifier()
                .modify(editor, ctx)
                .withRequired(true);
    }

    @Bean
    public ValueEditorModifier<PasswordValueEditor, PasswordChangeRequest> passwordChangePasswordEditorAnnotationModifier() {
        return (editor, formObject, ctx) -> mandatoryCommonPasswordEditorModifier().modify(editor, ctx);
    }

    @Bean
    public ContextualProvider<Department, ValueEditor<?, ?, ?>> mandatoryUserPasswordEditorDepartmentedProvider() {
        return department -> mandatoryCommonPasswordEditorModifier().modify(PasswordValueEditor.getEmptyEditor(), department);
    }

    @Bean
    public ContextualProvider<Department, ValueEditor<?, ?, ?>> optionalUserPasswordEditorDepartmentedProvider() {
        return department -> optionalCommonPasswordEditorModifier().modify(PasswordValueEditor.getEmptyEditor(), department);
    }

    @Bean
    public UserRelationsValidator userRelationsValidator() {
        return new DefaultUserRelationsValidator(userRelationLoader);
    }

    @Bean
    public ContextualFunction<UserRelationsRequest, Department, List<UserRelation>> userRelationsRequestToFamilyRelationsConverter() {
        return (userRelationsRequest, ctx) -> {
            Locale locale = userLocaleResolver.resolveLocale(authenticationHolder.getCurrentAuth().getActiveUser(), ctx);

            userRelationsValidator().canBeFamily(userRelationsRequest.target()).throwIfInvalid(translator, ctx, locale);
            userRelationsRequest.sources().stream()
                    .map(member -> userRelationsValidator().canBeFamilyMember(member, userRelationsRequest.representative(), userRelationsRequest.target()))
                    .forEach(validationResult -> validationResult.throwIfInvalid(translator, ctx, locale));

            val relations = new ArrayList<>(ListUtil.convert(userRelationsRequest.sources(), user -> UserRelation.createMemberOf(user, userRelationsRequest.target())));

            if (userRelationsRequest.representative() != null) {
                userRelationsValidator().canBeRepresentative(userRelationsRequest.representative()).throwIfInvalid(translator, ctx, locale);

                relations.add(UserRelation.createRepresenterOf(userRelationsRequest.representative(), userRelationsRequest.target()));
                relations.add(UserRelation.createMemberOf(userRelationsRequest.representative(), userRelationsRequest.target()));
            }

            return relations;
        };
    }

    @Bean
    public ContextualFunction<@Uppercase String, Department, BasicUser> newLibraryCreatingContextualLibraryBySiglaLoader() {
        return new NewLibraryCreatingContextualLibraryBySiglaLoader(
                SearchingByIdLoader.ofListSearchProperty(User.class, UserConstants.SearchParams.SIGLA, userSearchLoader),
                librarySyncHelper()
        );
    }

    @Bean
    public LibrarySyncHelper librarySyncHelper() {
        return new LibrarySyncHelper(
                userEditationFactory(),
                new SideThreadAuthenticationIsolator(authenticationHolder, portaroUserProvider, executorService)
        );
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerExports() {
        //all are in ExporterConfig
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerHandlers() {
        compositeAfterSaveHandler().add(new DebtSavingRegistrationPeriodExtenderAfterSaveHandler<>(debtSavingRegistrationExtensionProcessor(), notAutoExtendingCategoriesOnRegistration().andThen(new AllByIdsLoadableByIdLoaderAdapter<>(readerCategoryLoader)::getAllByIds)));
        compositeAfterSaveHandler().ifAdd(noInternetAccessForFullRegistrationUser()::getOn, new FullRegistrationReaderForbiddenInternetServicesAfterSaveHandler<>(defaultByReaderFullRegistrationReaderCategory().andThenFastReturningNull(readerCategoryLoader::getById), userPrefAccessor));
        compositeAfterSaveHandler().add(new GetAndSaveRelatedUsersAfterSaveHandler<>(new RelatedUsersToSaveRequestsConverter<>(userRelationsHelper, userLoader), userSaver()));
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerConverters() {
        IdToObjectConverter<Integer, Person> integerToPersonConverter = new IdToObjectConverter<>(userId -> {
            User user = userLoader.getById(userId);
            if (user instanceof Person) {
                return (Person) user;
            }
            throw new ConversionException("User with given id is not a person");
        });
        IdToObjectConverter<Integer, Institution> integerToInstitutionConverter = new IdToObjectConverter<>(userId -> {
            User user = userLoader.getById(userId);
            if (user instanceof Institution) {
                return (Institution) user;
            }
            throw new ConversionException("User with given id is not a Institution");
        });

        conversionService.addConverter(String.class, Person.class, StringToIntegerToAnyConverter.nullConvertingToNull(integerToPersonConverter));
        conversionService.addConverter(Integer.class, Person.class, integerToPersonConverter);
        conversionService.addConverter(String.class, Institution.class, StringToIntegerToAnyConverter.nullConvertingToNull(integerToInstitutionConverter));
        conversionService.addConverter(Integer.class, Institution.class, integerToInstitutionConverter);
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerPermissions() {
        permissionRegistry.add(SecurityActions.USERS_SHOW, and(
                permissionFactory.currentEvidedAuthentic(),
                permissionFactory.editAction(LibrarianPrivileges.ACTION_READERS_SHOW)
        ));

        permissionRegistry.add(SecurityActions.USERS_SHOW_OF_DEPARTMENT, and(
                withoutSubject(permissionRegistry.getLazy(SecurityActions.USERS_SHOW)),
                permissionFactory.showSubjectWithDepartment()
        ));

        permissionRegistry.add(SecurityActions.USER_SHOW, and(
                permissionFactory.currentEvidedAuthentic(),
                permissionFactory.subjectUserIsEvided(),
                gdprNotSetPermissionResolver,
                or(
                        permissionFactory.canRepresentUser(),
                        and(
                                withoutSubject(permissionRegistry.getLazy(SecurityActions.USERS_SHOW)),
                                permissionFactory.showUserWithDepartment()
                        )
                )
        ));

        permissionRegistry.add(ExemplarSecurityActions.EXEMPLAR_HOLDER_NAME_SHOW, or(
                (auth, ctx, exemplar) -> ifCan(serialCodeProvider.getOn(ctx).equals(CatalogConstants.SERIAL_CODE_KINSTELLAR), auth), //v kinstalaru ctenari vidi ostatni holdery (v seznamu exemplaru)
                adaptingSubject(Exemplar::getDepartment, permissionRegistry.getLazy(SecurityActions.USERS_SHOW_OF_DEPARTMENT)) //zobrazujeme i cizi (z jinych oddeleni) vypujcitele mych (z mych oddeleni) exemplaru
        ));

        permissionRegistry.add(SecurityActions.USER_PRINT, permissionRegistry.getLazy(SecurityActions.USER_SHOW));

        permissionRegistry.add(SecurityActions.USER_CREATE, or(
                permissionFactory.currentNotEvided(),
                permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_READERS_CREATE)
        ));

        permissionRegistry.add(SecurityActions.SOFTWARE_USER_CREATE, permissionFactory.currentEvidedAuthenticActiveWithAllRoles(ROLE_LIBRARIAN, ROLE_ADMIN));

        permissionRegistry.add(SecurityActions.SEND_EMAIL_TO_USER, permissionFactory.currentEvidedAuthenticActiveWithAnyOfRoles(ROLE_LIBRARIAN, ROLE_ADMIN));

        permissionRegistry.add(SecurityActions.SHOW_LIBRARIAN_MESSAGE, permissionFactory.edit());

        permissionRegistry.add(SecurityActions.SEND_SMS_TO_USER, and(
                permissionFactory.feature(FeatureManager.FEATURE_SMS),
                permissionFactory.enabled(settingLoader.getDepartmentedProvider(SettingKeys.SMS_ENABLED), Texts.ofNative("SMS is not enabled")),
                permissionFactory.edit()
        ));

        permissionRegistry.add(SecurityActions.USER_EDIT, or(
                permissionFactory.currentIsSubjectUser(),
                and(
                        permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_READERS_EDIT),
                        permissionFactory.editOnReadableDepartmentsOfUser()
                )
        ));

        permissionRegistry.add(SecurityActions.USER_DELETE, and(
                permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_READERS_DELETE),
                permissionFactory.editOnReadableDepartmentsOfUser()
        ));

        permissionRegistry.add(InternalAuthSecurityActions.USER_PASSWORD_CHANGE, (auth, ctx, user) -> {
            if (StringUtil.isNullOrEmpty(user.getPasswordHash())) {
                return PermissionResult.pointless(Texts.ofNative("User has not any password to change"));
            }
            if (!passwordLoginEnabled().getOn(ctx)) {
                return PermissionResult.disabledFeature(Texts.ofNative("Password authentication is not enabled"));
            }
            PermissionResult authentic = permissionFactory.currentEvidedAuthentic().can(auth, ctx, null);
            if (authentic.forbidden()) {
                return authentic;
            }
            if (!auth.getActiveUser().equals(user)) {
                return PermissionResult.cannot(auth);
            }
            return PermissionResult.allow();
        });

        permissionRegistry.add(InternalAuthSecurityActions.USER_PASSWORD_SET, (auth, ctx, user) -> {
            if (!passwordLoginEnabled().getOn(ctx)) {
                return PermissionResult.disabledFeature(Texts.ofNative("Password authentication is not enabled"));
            }
            if (!auth.getActiveUser().equals(user)) {
                return PermissionResult.cannot(auth);
            }
            if (auth.getAuthenticity().isAtLeast(Authenticity.ONE_CONFIDENTAL_FACTOR)) {
                return PermissionResult.allow();
            }
            if (auth.hasScope(PasswordRecreationLinkService.PASSWORD_RECREATION_SCOPE)) {
                return PermissionResult.allow();
            }
            return PermissionResult.insufficient();
        });

        permissionRegistry.add(CredentialsRegistrationSecurityActions.USER_USERNAME_CREATE, and(
                permissionFactory.enabled(usernameLoginEnabled(), Texts.ofNative("Authentication with username is not enabled")),
                permissionFactory.subjectUserIsEvided(),
                (auth, ctx, user) -> !((auth.getActiveUser() instanceof Person p) && MojeIDUserHelper.isMojeIDUser(p))  ? PermissionResult.allow() : PermissionResult.disabledFeature(Texts.ofNative("K přihlašování prosím použijte MojeID, které již máte nastavené")),
                (auth, ctx, user) -> !((auth.getActiveUser() instanceof Person p) && BankIDUserHelper.isBankIDUser(p))  ? PermissionResult.allow() : PermissionResult.disabledFeature(Texts.ofNative("K přihlašování prosím použijte BankID, které již máte nastavené")),
                (auth, ctx, user) -> PermissionResult.ifCan(user.getUsername() == null, auth, () -> Texts.ofStaticallyLocalized("Username is already set, login via it, please").addCzech("Uživatelské je již nastaveno, přihlaste se prosím pomocí něj.")),
                (auth, ctx, user) -> PermissionResult.ifCan(auth.getAuthenticity().isEqualOrWorseThan(Authenticity.ONE_PUBLIC_FACTOR), auth, () -> Texts.ofNative("Cannot create new username out of first-login process"))
        ));

        permissionRegistry.add(CredentialsRegistrationSecurityActions.USER_PASSWORD_CREATE, and(
                permissionFactory.enabled(passwordLoginEnabled(), Texts.ofNative("Authentication with password is not enabled")),
                permissionFactory.subjectUserIsEvided(),
                permissionFactory.currentIsSubjectUser(),
                (auth, ctx, user) -> !((auth.getActiveUser() instanceof Person p) && MojeIDUserHelper.isMojeIDUser(p)) ? PermissionResult.allow() : PermissionResult.disabledFeature(Texts.ofNative("K přihlašování prosím použijte MojeID, které již máte nastavené")),
                (auth, ctx, user) -> !((auth.getActiveUser() instanceof Person p) && BankIDUserHelper.isBankIDUser(p)) ? PermissionResult.allow() : PermissionResult.disabledFeature(Texts.ofNative("K přihlašování prosím použijte BankID, které již máte nastavené")),
                (auth, ctx, user) -> PermissionResult.ifCan(user.getPasswordHash() == null, auth, () -> Texts.ofStaticallyLocalized("Password is already set, login via it, please").addCzech("Heslo je již nastaveno, přihlaste se prosím pomocí něj.")),
                (auth, ctx, user) -> PermissionResult.ifCan(auth.getAuthenticity().isEqualOrWorseThan(Authenticity.ONE_PUBLIC_FACTOR), auth, () -> Texts.ofNative("Cannot create new pasword out of first-login process"))
        ));

        permissionRegistry.add(CredentialsRegistrationSecurityActions.USER_CREDENTIALS_CREATE, or(
                permissionRegistry.getLazy(CredentialsRegistrationSecurityActions.USER_USERNAME_CREATE),
                permissionRegistry.getLazy(CredentialsRegistrationSecurityActions.USER_PASSWORD_CREATE)
        ));

        permissionRegistry.add(InternalAuthSecurityActions.USER_CREDENTIALS_DELETE, (auth, ctx, user) -> {
            if (user.getUsername() == null && user.getPasswordHash() == null) {
                return PermissionResult.pointless(Texts.ofNative("User has not username or password to delete"));
            }
            if (!passwordLoginEnabled().getOn(ctx)) {
                return PermissionResult.disabledFeature(Texts.ofNative("Password authentication is not enabled"));
            }
            return permissionFactory.editOnReadableDepartmentsOfUser().can(auth, ctx, user);
        });

        permissionRegistry.add(SecurityActions.USER_REGISTRATION_PERIOD_EXTEND, and(
                (auth, ctx, user) -> PermissionResult.ifCan(user.hasRoleOn(ReaderRole.class, ctx), auth, () -> Texts.ofNative("User is not a reader, nothing to extend")),
                or(
                        permissionFactory.edit(),
                        permissionFactory.subjectUserIsEvidedActiveUnblockedReader() // cannot extend by blocked user himself
                ),
                (auth, ctx, user) -> PermissionResult.ifCan(userRegistrationHelper().hasAnyRegistrationExtendableUsers(user, ctx), auth, () -> Texts.ofNative("Subject user does not have any registration extendable users")),
                or(
                        (auth, ctx, user) -> PermissionResult.ifCan(settingLoader.getOn(SettingKeys.READER_SELF_REGISTRATION_EXTEND_FOR_ALL_USERS, ctx) && auth.getActiveUser().equals(user), auth, () -> Texts.ofNative("User cannot extend registration by hiself")),
                        (auth, ctx, user) -> {
                            val selfExtendableReaderCategories = registrationPeriodSelfExtendingReaderCategories().getOn(ctx);
                            boolean canUserExtendSelfByIni = user.roleStreamOn(ReaderRole.class, ctx)
                                    .map(ReaderRole::getReaderCategory)
                                    .anyMatch(selfExtendableReaderCategories::contains);
                            return PermissionResult.ifCan(canUserExtendSelfByIni, auth, () -> Texts.ofNative("User cannot extend registration by hiself (users's reader category is not in whitelist)"));
                        },
                        permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_READERS_EXTEND_REG_PERIOD)
                )
        ));

        permissionRegistry.add(SecurityActions.USER_REGISTRATION_AGREEMENT_PRINT, (auth, ctx, user) -> {
            if (!(user instanceof Person)) {
                return PermissionResult.pointless(Texts.ofNative("User must be a person"));
            }
            if (!user.hasRoleOn(ReaderRole.class, ctx)) {
                return PermissionResult.cannot(auth);
            }
            if (auth.getActiveUser().equals(user)) {
                return PermissionResult.allow();
            }
            PermissionResult action = permissionFactory.currentEvidedAuthenticEditWithLoanLicencedAction(LibrarianPrivileges.ACTION_PRINT_REGISTRATION_AGREEMENT).can(auth, ctx, null);
            if (action.forbidden()) {
                return action;
            }
            return permissionFactory.editOnReadableDepartmentsOfUser().can(auth, ctx, user);
        });

        permissionRegistry.add(SecurityActions.USER_PREFERENCES_EDIT, or(
                permissionFactory.currentIsSubjectUser(),
                permissionFactory.currentEvidedAuthenticActiveWithAllRoles(ROLE_ADMIN)
        ));

        permissionRegistry.add(SecurityActions.USER_PERSONAL_DATA_EVIDENCE_EXPORT, permissionRegistry.getLazy(SecurityActions.USER_SHOW));

        permissionRegistry.add(SecurityActions.FOREIGN_SYSTEM_SYNCHRONIZATION_RUN, (auth, ctx, none) -> {
            if (auth.getActiveUser().hasUsername(Users.VERBIS_USERNAME)) {
                return PermissionResult.allow();
            }
            if (ListUtil.hasAny(auth.getRole(), ROLE_LIBRARIAN, ROLE_ADMIN)) {
                return PermissionResult.allow();
            }
            return PermissionResult.cannot(auth);
        });

        permissionRegistry.add(DiscountSecurityActions.DISCOUNT_APPROVAL_SHOW, PermissionResolver.and(
                permissionFactory.enabled(discountRequestEnabledProvider(), Texts.ofNative("Discount approval is not enabled")),
                permissionFactory.edit()
        ));

        permissionRegistry.add(DiscountSecurityActions.DISCOUNT_APPROVAL_SHOW_AND_EDIT, PermissionResolver.and(
                withoutSubject(permissionRegistry.getLazy(DiscountSecurityActions.DISCOUNT_APPROVAL_SHOW)),
                permissionRegistry.getLazy(SecurityActions.USER_EDIT)
        ));
    }

}
