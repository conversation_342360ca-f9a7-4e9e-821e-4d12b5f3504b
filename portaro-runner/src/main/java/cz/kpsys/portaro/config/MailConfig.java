package cz.kpsys.portaro.config;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.localization.ContextualLocaleLocalizer;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.mail.Mailer;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.loan.LoanSettingKeys;
import cz.kpsys.portaro.mail.MailMessageSendingSavingMailService;
import cz.kpsys.portaro.mail.MailParticipantSpecifier;
import cz.kpsys.portaro.mail.MailService;
import cz.kpsys.portaro.mail.MailerMailService;
import cz.kpsys.portaro.messages.dto.Message;
import cz.kpsys.portaro.messages.dto.MessageSending;
import cz.kpsys.portaro.messages.dto.MessageSendingEmailAddress;
import cz.kpsys.portaro.setting.CoreSettingKeys;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.contact.ContactManager;
import cz.kpsys.portaro.user.locale.UserLocaleResolver;
import cz.kpsys.portaro.view.web.rest.mail.MailApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static cz.kpsys.portaro.config.SettingKeys.SMTP_FROM_LIBRARY_MAILS_REPLY_TO_ADDRESS;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MailConfig {

    @NonNull Mailer<Department> mailer;
    @NonNull ContactManager contactManager;
    @NonNull ContextualLocaleLocalizer<Department> localizer;
    @NonNull FileDataStreamer securedFileDataStreamer;
    @NonNull UserLocaleResolver userLocaleResolver;
    @NonNull Translator<Department> translator;
    @NonNull SettingLoader settingLoader;
    @NonNull Saver<Message, ?> messageSaver;
    @NonNull Saver<MessageSending, ?> messageSendingSaver;
    @NonNull Saver<MessageSendingEmailAddress, ?> messageSendingEmailAddressSaver;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;


    @Bean
    public MailApiController mailApiController() {
        return new MailApiController(mailService());
    }

    @Bean
    public MailService mailService() {
        MailerMailService nonInternalParticipantMailService = new MailerMailService(
                mailer,
                mailParticipantSpecifier(),
                contactManager,
                localizer,
                securedFileDataStreamer
        );
        return new MailMessageSendingSavingMailService(
                mailParticipantSpecifier(),
                nonInternalParticipantMailService,
                messageSaver,
                messageSendingSaver,
                messageSendingEmailAddressSaver,
                contactManager,
                defaultTransactionTemplateFactory.get()
        );
    }

    @Bean
    public MailParticipantSpecifier mailParticipantSpecifier() {
        return new MailParticipantSpecifier(
                settingLoader.getDepartmentedProvider(CoreSettingKeys.DEFAULT_LOCALE),
                userLocaleResolver,
                translator,
                systemEmailProvider(),
                settingLoader.getDepartmentedProvider(LoanSettingKeys.ORDERS_RECIPIENT_EMAIL).throwingWhenNull()
        );
    }

    @Bean
    public ContextualProvider<Department, @NonNull String> systemEmailProvider() {
        return settingLoader.getDepartmentedProvider(SMTP_FROM_LIBRARY_MAILS_REPLY_TO_ADDRESS)
                .fallbacked(settingLoader.getDepartmentedProvider(SettingKeys.SMTP_SENDER_ADDRESS).throwingWhenNull());
    }

}
