Jak funguji lokalizace v JS
===========================

Pokud nekdo potrebuje v javascriptu nejakou lokalizacni hlasku, existuje zde jisty mechanismus nacitani lokalizaci.

V prve rade je potreba pro ziskani lokalizace pouzit sluzbu {@link localizationService} a v komentari ji takzvane oanotovat pomoci `@kp-localization`
a za to dat nazev te lokalizacni hlasky, kterou pozadujeme.

```javascript
// @kp-localization commons.HelloWorld
const message = localizationService.get('commons.HelloWorld')
```

A nejake `MAGIC` zajisti, ze se tam ta lokalizace, pokud bude existovat dostane.

Jak funguje `MAGIC`?
--------------------

Byl vytvoren specialni `webpack` plugin `LocalizationWebpackPlugin`.

Ten udela to, ze pri buildeni skriptu projede vsechny komentare a zjisti, jestli v tom komentari je anotace `@kp-localization`.

Ten plugin po zbuildeni vytvori novy soubor `dist/localizations.ftl` ve kterem je kod pro vyrenderovani najitych lokalizacnich hlasek
do FTL formatu. Je potreba ten soubor nedavat zatim nikem jinam, protoeze jsou tam natvrdo nastavene cesty.

Dale byl do `_layout.ftl` pridan include tohoto souboru:

```injectedfreemarker
<#include "./../resources/dist/localizations.ftl">
```

Diky tomu se vytvori na `window` objekt `localizations` a v nem budou pod klicem nazvu lokalizace prelozene stringy.

Mno a na zaver {@link localizationService} pak uz jenom obaluje ziskavani hodnot z tohoto objektu. 