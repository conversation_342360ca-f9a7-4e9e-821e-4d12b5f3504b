import * as path from 'path';
import * as sveltePreprocessConfig from '../svelte-preprocess.config';


// rename svelte styles files from path/name.svelte to path/name.svelte.css to prevent source maps conflicts
// remove inline source map generated by less during preprocessing to prevent errors when browsing styles source files in browser devtools
export const svelteStylesLoader = {
    loader: path.resolve(__dirname, 'loaders', 'svelte-styles-loader.ts'),
    options: {
        newFilename: '[path][name].[ext].css',
        removePreprocessSourceMap: true
    }
}

export const babelLoader = {
    loader: 'babel-loader',
    options: {
        cacheDirectory: true,
        plugins: [
            'angularjs-annotate', // this plugin uses 'ngInject' directives to annotate constructors and functions to be usable with angularjs DI (for injecting params)
            '@babel/plugin-transform-runtime' // this plugin should reduce babel helpers code duplication (it requires @babel/runtime as runtime dependency in package.json)
        ],
        presets: [
            // this smart preset transpiles code and insert polyfills based on targeted browser defined by the browserslist query in package.json
            ['@babel/preset-env', {useBuiltIns: 'entry', corejs: '3.39'}] // TODO: update used core-js version if newer version is installed
            // option 'entry' requires imported core-js (or its subset) in codebase (here its app.ts file)
        ],
        env: {
            test: { // used only when process.env.NODE_ENV is set to 'test' (during karma testing)
                plugins: [
                    ['babel-plugin-istanbul', {exclude: ['**/*.spec.ts', '**/*.spec.js']}] // dont instrument test files (just project files)
                ]
            }
        }
    }
};

export const tsLoader = {
    loader: 'ts-loader',
    options: {
        experimentalFileCaching: true,
        allowTsInNodeModules: true,
        configFile: path.resolve(__dirname, '..', 'tsconfig.frontend.json')
    }
};

export function svelteLoaderFactory(developEnv: boolean) {
    return {
        loader: 'svelte-loader',
        options: {
            emitCss: true,
            preprocess: sveltePreprocessConfig.preprocess,
            compilerOptions: {
                dev: developEnv,
                accessors: developEnv
            }
        }
    }
}