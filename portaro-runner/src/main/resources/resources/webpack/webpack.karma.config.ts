import * as path from 'path';
import {commonImportRegex} from './webpack.constants';
import {babelLoader, svelteLoaderFactory, tsLoader} from './webpack.loaders';
import * as webpack from 'webpack';
import TerserPlugin from 'terser-webpack-plugin';


export default (dirname: string): webpack.Configuration => ({
    mode: 'development',

    externals: {
        angular: 'angular'
    },

    resolve: {
        extensions: ['.js', '.mjs', '.ts', '.svelte'],
        mainFields: ['svelte', 'browser', 'module', 'main'], // for svelte components import from node_modules, more info: https://github.com/sveltejs/svelte-loader#resolvemainfields
        conditionNames: ['svelte', 'browser', 'import'],
        alias: {
            src: path.resolve(dirname, 'src'),
            core: path.resolve(dirname, 'src', 'core'),
            shared: path.resolve(dirname, 'src', 'shared'),
            typings: path.resolve(dirname, 'src', 'typings'),
            svelte: path.resolve('node_modules', 'svelte/src/runtime')
        }
    },

    module: {
        rules: [
            {
                test: /\.(js|ts)$/,
                include: [commonImportRegex],
                use: [
                    babelLoader,
                    tsLoader
                ]
            },
            {
                // required to prevent errors from Svelte on Webpack 5+, omit on Webpack 4
                test: /\.m?js$/,
                include: /node_modules/,
                resolve: {
                    fullySpecified: false
                }
            },
            {
                test: /\.svelte$/,
                use: [
                    babelLoader,
                    svelteLoaderFactory(true)
                ]
            },
            {
                test: /\.json$/,
                type: 'asset/resource',
                generator: {
                    emit: false
                }
            },
            {
                test: /\.less$/,
                use: [
                    {
                        loader: 'style-loader'
                    },
                    {
                        loader: 'css-loader'
                    },
                    {
                        loader: 'less-loader'
                    }
                ]
            },
            {
                test: /\.css$/, // svelte styles
                use: [
                    {
                        loader: 'style-loader'
                    },
                    {
                        loader: 'css-loader'
                    }
                ]
            },
            {
                test: /(\.sass$)|(\.scss$)/,
                type: 'asset/resource',
                generator: {
                    emit: false
                }
            },
            {
                test: /\.html$/,
                type: 'asset/resource',
                generator: {
                    emit: false
                }
            },
            {
                test: /\.(gif|png|jpg|webp)$/,
                type: 'asset/resource',
                generator: {
                    emit: false
                }
            },
            {
                test: /\.(otf|eot|ttf|woff|woff2)$/,
                type: 'asset/resource',
                generator: {
                    emit: false
                }
            },
            {
                test: /\.svg$/,
                type: 'asset',
                generator: {
                    emit: false
                }
            },
            {
                test: /\.tpl\.ftl$/,
                use: [
                    {
                        loader: 'ngtemplate-loader',
                        options: {
                            relativeTo: path.resolve(dirname, 'src'),
                            prefix: 'ng-templates/'
                        }
                    },
                    {
                        loader: 'regex-replace-loader', // I think it removes ftl macros and directives (`<# ... >`) from angular templates
                        options: {
                            regex: '\\<\\/?#.*?\\>',
                            flags: 'g',
                            value: ''
                        }
                    }
                ]
            }
        ]
    },

    optimization: {
        nodeEnv: 'test'
    },

    devtool: 'inline-source-map'
});
