import * as loaderUtils from 'loader-utils';
import * as path from 'path';

export default function loader(content: string) {
    this.cacheable && this.cacheable();

    const resourcePath: string = this.resourcePath;

    // match only svelte files, ignore css files
    if (resourcePath.match(/.svelte$/)) {

        const options: SvelteStyleLoaderOptions = this.getOptions();

        if (!options.newFilename || typeof options.newFilename !== 'string') {
            throw new Error('Svelte styles loader - missing newFilename option');
        }

        const newName = loaderUtils.interpolateName(this, options.newFilename, {content});

        this.resourcePath = path.normalize(newName)

        if (typeof options.removePreprocessSourceMap === 'boolean' && options.removePreprocessSourceMap) {
            return content.replaceAll(/\/\*# sourceMappingURL.*\*\//g, '');
        }
    }

    return content;
}

interface SvelteStyleLoaderOptions {
    newFilename: string;
    removePreprocessSourceMap: boolean;
}

