import * as path from 'path';
import * as os from 'os';
import WebpackShellPlugin from 'webpack-shell-plugin-next';
import ExtraWatchPlugin from 'extra-watch-webpack-plugin';

import {DEVELOPMENT_WITH_GRADLE_ENV} from './webpack.constants';

export default (dirname: string, env: string) => {

    if (env === DEVELOPMENT_WITH_GRADLE_ENV) {
        console.info('Using `development-with-gradle` switch. Enabling some build optimization and watching mode');
    }

    return {
        mode: 'development',

        watch: env === DEVELOPMENT_WITH_GRADLE_ENV,

        plugins: (() => {
            const plugins = [
                new ExtraWatchPlugin({
                    dirs: [path.resolve(dirname, '..', 'freemarker')]
                })
            ];

            if (env === DEVELOPMENT_WITH_GRADLE_ENV) {
                plugins.push(
                // @ts-ignore
                    new WebpackShellPlugin({
                        safe: isWindow$(),
                        swallowError: true,
                        dev: false,
                        onBuildExit: {
                            scripts: [(() => isWindow$() ? 'npm run gradle-win' : 'npm run gradle')()]
                        }
                    })
                );
            }

            return plugins;
        })()
    };
};

function isWindow$(): boolean {
    return /win/.test(os.platform());
}