import type {WebpackPluginInstance} from 'webpack';
import {Compiler} from 'webpack';
import * as fs from 'fs';
import path from 'path';

const CODE_FRAGMENT_HEADER = `
<#ftl outputFormat="HTML">
<#import "../../freemarker/_localization.ftl" as loc>

<script type="text/javascript">
    window.localizations = {
`;

function keyToEntryCodeFragment(key: string): string {
    return `        '${key}': '\${loc.loc("${key}")?jsString?noEsc}'`;
}

const CODE_FRAGMENT_FOOTER = `
    };
</script>
`;

export interface LocalizationPluginOptions {

    /**
     * Filename with path relative to `output.path`. Default value: `'localizations.ftl'`.
     */
    filename?: string;
}

const DEFAULT_OPTIONS: Required<LocalizationPluginOptions> = {
    filename: 'localizations.ftl'
};

/**
 * Webpack plugin that extracts localization keys from JavaScript comments and Svelte components
 * and generates a FreeMarker template file with all the localization keys.
 *
 * The template will be processed server-side to inject actual localized values.
 */
export default class LocalizationWebpackPlugin implements WebpackPluginInstance {

    private localizations = new Set<string>();
    private name = 'LocalizationWebpackPlugin';
    private options: Required<LocalizationPluginOptions>;

    constructor(options?: LocalizationPluginOptions) {
        this.options = Object.assign({}, DEFAULT_OPTIONS, options);
    }

    apply(compiler: Compiler): void {

        // Extract localization keys from JS comments with @kp-localization annotation
        compiler.hooks.normalModuleFactory.tap(this.name, ({hooks: {parser}}) => {
            parser.for('javascript/auto').tap(this.name, ({hooks: {program}}) => {
                program.tap(this.name, (ast, comments) => {
                    for (const {value} of comments) {
                        if (value && value.match(/@kp-localization/)) {
                            const messageKey = value.match(/@kp-localization ([\S.]*)/);
                            if (messageKey && messageKey[1]) {
                                this.localizations.add(messageKey[1]);
                            }
                        }
                    }
                });
            });
        });

        // Extract localization keys from Svelte components using <Loc code="..."> syntax
        compiler.hooks.normalModuleFactory.tap(this.name, (nmf) => {
            nmf.hooks.afterResolve.tapAsync(this.name, (data, callback) => {
                const resourcePath = data.createData?.resource;

                // Take only Svelte files
                if (resourcePath && resourcePath.endsWith('.svelte')) {
                    try {
                        const source = fs.readFileSync(resourcePath, 'utf-8');

                        // Find all <Loc code="..."> components in Svelte files
                        const matches = [...source.matchAll(/<Loc\s+[^>]*code\s*=\s*"([^"]+)"[^>]*\/?>/g)];

                        for (const match of matches) {
                            const code = match[1];
                            if (code) {
                                this.localizations.add(code);
                            }
                        }
                    } catch (err) {
                        console.warn(`[${this.name}] Failed to parse ${resourcePath}: ${err.message}`);
                    }
                }

                callback(null);
            });
        });

        // Generate and emit the localization FreeMarker template file after compilation
        compiler.hooks.afterEmit.tapAsync(this.name, (compilation, callback) => {
            // Combine all parts to create the final FreeMarker template
            const code =
                CODE_FRAGMENT_HEADER +
                [...this.localizations].map(keyToEntryCodeFragment).join(',\n') +
                CODE_FRAGMENT_FOOTER;

            const outputPath = path.resolve(compiler.options.output.path!, this.options.filename);
            fs.writeFile(outputPath, code, callback);
        });
    }
}