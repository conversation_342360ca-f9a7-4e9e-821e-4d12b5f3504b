import * as webpack from 'webpack';
import CssMinimizerPlugin from 'css-minimizer-webpack-plugin';
import TerserPlugin from 'terser-webpack-plugin';

export default (dirname: string, env: string): webpack.Configuration => ({
    mode: 'production',

    optimization: {
        nodeEnv: 'production', // replacement for DefinePlugin({'process.env.NODE_ENV': '"production"'})
        minimizer: [
            new TerserPlugin({
                parallel: true,
                terserOptions: {
                    sourceMap: true
                }
            }),
            new CssMinimizerPlugin({
                parallel: true,
                minify: CssMinimizerPlugin.cssnanoMinify,
                minimizerOptions: {
                    preset: ['default', {
                        discardComments: {
                            removeAll: true
                        },
                        zindex: false
                    }]
                }
            })
        ]
    }
});
