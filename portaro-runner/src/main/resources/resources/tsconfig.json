{"compilerOptions": {"module": "commonjs", "target": "esnext", "sourceMap": false, "allowJs": true, "experimentalDecorators": true, "resolveJsonModule": true, "esModuleInterop": true, "baseUrl": ".", "removeComments": false, "paths": {"src/*": ["src/*"], "core/*": ["src/core/*"], "shared/*": ["src/shared/*"], "typings/*": ["src/typings/*"], "svelte/*": ["node_modules/svelte/src/runtime/*"]}}, "exclude": ["node_modules", "idea", "docs", "reports", "dist", "**/*.js"]}