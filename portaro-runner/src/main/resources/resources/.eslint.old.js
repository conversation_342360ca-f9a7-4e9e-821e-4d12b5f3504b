
module.exports = {
    "env": {
        "browser": true,
        "es2022": true
    },
    "ignorePatterns": [
        "node_modules",
        "dist",
        "**/*.js",
        "test",
        "webpack",
        "webpack.config.ts"
    ],
    "extends": [
        "plugin:@typescript-eslint/recommended",
        "plugin:@typescript-eslint/recommended-requiring-type-checking",
        "plugin:storybook/recommended",
        "plugin:testing-library/dom",
        "plugin:svelte/recommended"],
    "parser": "@typescript-eslint/parser",
    "parserOptions": {
        "sourceType": "module",
        "project": ["tsconfig.frontend.json", "tsconfig.svelte.json"],
        "extraFileExtensions": [".svelte"]
    },
    "plugins": [
        "eslint-plugin-import",
        "eslint-plugin-jsdoc",
        "eslint-plugin-unicorn",
        "@typescript-eslint",
        "testing-library"
    ],
    "overrides": [
        {
            "files": ["*.svelte"],
            "parser": 'svelte-eslint-parser',
            "parserOptions": {
                "parser": '@typescript-eslint/parser'
            }
        }
    ],
    "settings": {
        "testing-library/utils-module": "off",
        "testing-library/custom-renders": "off",
        "testing-library/custom-queries": "off",
    },
    "rules": {
        "@typescript-eslint/adjacent-overload-signatures": "error",
        "@typescript-eslint/array-type": [
            "off",
            {
                "default": "array-simple"
            }
        ],
        "@typescript-eslint/consistent-type-assertions": "error",
        "@typescript-eslint/dot-notation": "error",
        "@typescript-eslint/member-ordering": "off",
        "@typescript-eslint/naming-convention": "off",
        "@typescript-eslint/no-empty-function": "error",
        "@typescript-eslint/no-empty-interface": "error",
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/no-misused-new": "error",
        "@typescript-eslint/no-namespace": "error",
        "@typescript-eslint/no-parameter-properties": "off",
        "@typescript-eslint/no-shadow": [
            "error",
            {
                "hoist": "all"
            }
        ],
        "@typescript-eslint/no-unused-expressions": "error",
        "@typescript-eslint/no-use-before-define": "off",
        "@typescript-eslint/no-var-requires": "error",
        "@typescript-eslint/prefer-for-of": "error",
        "@typescript-eslint/prefer-function-type": "error",
        "@typescript-eslint/prefer-namespace-keyword": "error",

        "@typescript-eslint/triple-slash-reference": [
            "error",
            {
                "path": "always",
                "types": "prefer-import",
                "lib": "always"
            }
        ],
        "@typescript-eslint/unified-signatures": "error",
        "@typescript-eslint/ban-ts-comment": ["error", {
            "ts-ignore": "allow-with-description"
        }],
        "@typescript-eslint/no-unsafe-member-access": "off",
        "@typescript-eslint/no-unsafe-call": "off",
        "@typescript-eslint/no-unsafe-assignment": "off",
        "@typescript-eslint/no-unsafe-return": "off",
        "@typescript-eslint/unbound-method": "off",
        "@typescript-eslint/no-floating-promises": "off",
        "@typescript-eslint/require-await": "error",
        "@typescript-eslint/restrict-template-expressions": "off",
        "@typescript-eslint/no-misused-promises": "error",
        "@typescript-eslint/explicit-module-boundary-types": "off",
        "@typescript-eslint/no-inferrable-types": [
            "warn",
            {
                "ignoreParameters": true
            }
        ],
        "@typescript-eslint/prefer-regexp-exec": "off",
        "@typescript-eslint/restrict-plus-operands": "warn",
        "@typescript-eslint/await-thenable": "error",
        "@typescript-eslint/consistent-type-imports": [
            "error",
            {
                "prefer": "type-imports",
                "disallowTypeAnnotations": true
            }
        ],
        "@typescript-eslint/no-unsafe-argument": "off",
        "@stylistic/js/arrow-parens": [
            "error",
            "always"
        ],
        "@stylistic/comma-dangle": "off",
        "complexity": "off",
        "constructor-super": "error",
        "dot-notation": "error",
        "eqeqeq": [
            "error",
            "always"
        ],
        "guard-for-in": "error",
        "id-blacklist": [
            "error",
            "any",
            "Number",
            "number",
            "String",
            "string",
            "Boolean",
            "boolean",
            "Undefined",
            "undefined"
        ],
        "id-match": "error",
        "import/order": "off",
        "jsdoc/check-alignment": "error",
        "jsdoc/check-indentation": "error",
        "max-classes-per-file": [
            "error",
            2
        ],
        "@stylistic/max-len": "off",
        "@stylistic/new-parens": "error",
        "no-bitwise": "error",
        "no-caller": "error",
        "no-cond-assign": "error",
        "no-console": "error",
        "no-debugger": "error",
        "no-empty": "error",
        "no-empty-function": "off",
        "no-eval": "error",
        "no-fallthrough": "off",
        "no-invalid-this": "error",
        "no-new-wrappers": "error",
        "no-return-await": "error",
        "no-shadow": "off",
        "no-throw-literal": "error",
        "@stylistic/no-trailing-spaces": "off",
        "no-undef-init": "error",
        "no-underscore-dangle": "off",
        "no-unsafe-finally": "error",
        "no-unused-expressions": "error",
        "no-unused-labels": "error",
        "no-use-before-define": "off",
        "no-var": "error",
        "object-shorthand": "error",
        "one-var": [
            "error",
            "never"
        ],
        "prefer-const": "error",
        "prefer-template": "error",
        "@stylistic/quotes": "off",
        "radix": "error",
        "@stylistic/spaced-comment": ["error", "always", {
            "line": {
                "markers": ["/"],
                "exceptions": ["@ngInject"]
            },
            "block": {
                "markers": ["/"],
                "exceptions": ["@ngInject"],
                "balanced": true
            }
        }],
        "unicorn/filename-case": ["error", {
            "cases": {
                "kebabCase": true,
                "pascalCase": true,
            },
            "ignore": ["<EMAIL>"]
        }],
        "use-isnan": "error",
        "valid-typeof": "off",
        "svelte/valid-compile": "off",
        "svelte/no-unused-svelte-ignore": "off",
        "svelte/no-at-html-tags": "off",
        "testing-library/no-node-access": "off"
    }
};
