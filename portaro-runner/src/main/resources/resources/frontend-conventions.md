
# Jak psát frontend (Angular)

## <PERSON><PERSON><PERSON><PERSON> konvence

### Obecn<PERSON> t<PERSON>ídy, metody, funkce a hlavně proměnné nazýváme celými samopopisnými n<PERSON>zvy, ide<PERSON>lně bez zkratek!

~~link(sc, el, att, con)~~ vs link($scope: IScope, $element: IAugmentedJQuery, $attrs: IAttributes, \[ngModel, kpMyDirective\]: \[INgModelController, KpMyDirectiveController\])

~~\["<PERSON><PERSON>", "<PERSON>", "<PERSON>"\].forEach((e) -> {});~~ vs  \["<PERSON><PERSON>", "<PERSON>", "<PERSON>"\].forEach((name) => {});

Všechny TS soubory se pojmenovávají malými písmeny oddělené pomlčkou.
Pokud se jedná o Anguláří logiku, za tečkou se napíše o co se jedná.

V základu rozlišujeme:

- Direktivy

  To jsou nevizuální GUI komponenty, rozšiřující HTML tagy o nějakou funkčnost.
  Nazýváme je s prefixem `kp-` a postfixem `.directive`, např. `kp-on-scroll.directive.ts`.

- Komponenty

  To jsou vizuální GUI komponenty.
  Nazýváme je s prefixem `kp-` a postfixem `.component`, např. `kp-global-search.component.ts`.

- Servisy

  To je obecně nějaké logika, která nebo kterou využívá Angular a dá se napsat do třídy.
  Nazýváme je s postfixem `.service`, např. `current-auth.service.ts`.

- Data servisy

  To je speciální druh servisy, která se stará jen a pouze o komunikaci s backendem a nejnutnější věci kolem toho.
  Nazýváme je s postfixem `.data-service`, např. `favourites.data-service.ts`.

- Faktorky

  To je obecně nějaké logika, která nebo kterou využívá Angular a nedá se napsat do třídy. Např. funkce.
  Nazýváme je s postfixem `.factory`, např. `favourites.factory.ts`.

- Moduly

  To jsou Anguláří moduly, které soustřeďují výše zmíněné do logických celků. 
  Nazýváme je s postfixem `.module`, např. `services.module.ts`.

## Implementační konvence

### Hlavní pravidlo zní:

**Zanech kód v lepším stavu, než v jakém jsi ho našel.**

Tzn. refactoring JS -> TS, rozdělení do samostatných modulů, refactoring do tříd, refactoring do komponent, pokud možno, vysekat z direktiv `$scope`, refactoring do SPA apod.

### Obecně

Ve třídě se řadí podle následujících pravidel:

1. `static fields`
2. `instance fields`


1. `fields / variables`
2. `methods`


1. `public`
2. `protected`
3. `private`

např.
```
class KpSomeDirective {
    public static directiveName = 'kpSomething';
    private static instanceCounter: number = 0;
    
    public scope = true;
    private $timeout: ITimeoutService;

    /*@ngInject*/
    construstor($timeout: ITimeoutService) {
        this.$timeout = $timeout;
    }

    public link($scope: IScope) {
        KpSomeDirective.instanceCounter++;
        $scope.instances = KpSomeDirective.instanceCounter;
    }

    private resetCounter() {
        KpSomeDirective.instanceCounter = 0;
    }   
}
```

Je snaha vyhnout se utility funkcím, když to podporuje API jazyka.

```
angular.forEach(departments, function(r) {
   r.parent = r.root ? null : Department.get({id: r.parentId});
});
```
vs
```
departments.forEach((department) -> {
   department.parent = department.root ? null : Department.get({id: department.parentId});
});
```

### Dependency Injection
Standardně AngularJS řeší DI tak, že se direktivy (kontrollery, servisy) nadefinují jako pole, jehož obsahem je stringový název pro DI, a jako poslední funkce, 
přijímající injectnuté argumenty, např. `['$timeout', '$compile', function($timeout, $compile) {return {...}}]`.
Jiná možnost je nadefinovat třídu / funkci a vytvořit jí field `$inject` (ano, platí i pro funkce), do kterého ve formě stringového pole napíšu seznam názvů pro DI 
a v konstruktoru jako seznam argumentů se předají.
např. 
```
class KpSomeDirective {
    static $inject = ['$timeout', '$compile'];

    constructor($timeout, $compile) {...};
}
```
To již není třeba dělat manuálně, protože pomocí komentářové anotace `/*@ngInject*/` nad konstruktorem nebo funkcí to za nás udělá plugin babelu.
např.
```
class KpSomeDirective {
    /*@ngInject*/
    constructor(private $timeout: ITimeoutSerice, $compile: ICompileService) {...};
}

/*@ngInject*/
export default function mainMenuConfig(mainMenuServiceProvider: MainMenuServiceProvider) {...} 
```
               
### Asynchronní operace
Je snaha co nejvíce používat `async / await`, místo promise chainů.
Kvůli tomu, že AngularJS používá vlastní implementaci promis pomocí `$q` služby a při každém použití `.then()` / `.catch()` spouští digest cycle, je potřeba 
nativní promisy "obohatit" o tuto funkcionalitu. 
Ve většině případů stačí asynchronní funkci vracející `Promise` oanotovat pomocí anotace `@ngAsync()`, která zajistí spuštění digest cyklu.
Pokud to z nějakého důvodu nelze (cizí knihovna, ...), je třeba digest cyklus spustit ručně `$scope / $rootScope / $apply / $applyAsync`. 
                    
### Direktivy

Direktivu píšeme jako třídu s názvem shodným s názvem souboru v upper camelcase, např. `kp-on-error.directive` -> `KpOnErrorDirective`.
Do `public static directiveName` fieldu napíšeme název direktivy v camelcase stejný jako název souboru BEZ postfixu.
Pokud potřebujeme nějakou DI závislost, vytvoříme konstruktor s komentářem `/*@ngInject*/` s injektovanými argumenty ideálně s typy.
Definice funkce `link` se od standardní definice direktivy liší tím, že se jedná o klasickou metodu s názvem `link`. Argumenty metody link pojmenujeme
s prefixem `$` abychom je odlišili od obyčejných proměnných.
Pokud potřebujeme `preLink` a `postLink`, nadefinujeme je jako metody. Nelze ovšem použít `link` a `postLink` najednou, protože je to to samé.
Pokud pracuji v direktivě s proměnnými na `$scope`, je třeba zdědit z `IScope` nový typ a použít ho v definici metody `link`, jinam nám bude řvát TS kompilátor, např.
   
```
interface OnOverflowDirectiveScope extends IScope {
    isOverflowed: boolean;
}

public link($scope: OnOverflowDirectiveScope) {
    $scope.isOverflowed = true;
}
```

### Komponenty

Snažím se komponentu navrhnout tak, aby nepoužívala ŽÁDNÝ two-way data binding.
Nejdříve si vytvořím interface s definicí rozhraní komponenty, které se bude jmenovat \<*NazevSouboruUpperCamelCase*\>Bindings. Výstupy nadefinuji jako metody, 
pokud má výstup nějaký parametr, naimplementuji ho do argumentu `locals`, typu `{}` a parametry vyspecifikuji s prefixem `$` jako fieldy objektu.
Pokud je to reakce ne nějakou událost, sluší se výstupu dát prefix `on`.
např.
```
interface KpFileEditorComponentBindings {
    file: ViewableFile;
    isFullscreen?: boolean;

    onEditorInit(locals: { $editor: Ace.Editor });
}
```

Definici komponenty píšeme jako třídu s názvem shodným s názvem souboru v upper camelcase, která implementuje rozhraní `Component<BINDINGS>`, 
kde jako generický parametr uvedeme interface s rozhraním komponenty.
Do `public static componentName` fieldu napíšeme název komponenty v camelcase stejný jako název souboru BEZ postfixu.
Pokud bude mít velmi jednoduchou šablonu, můžeme ji napsat přímo jako field `template`, jinak použijeme require, např. `templateUrl = require('template.tpl.ftl')`.
Část bindings nadefinujeme podle interfacu s rozhraním a použijeme klíčové slovo `as const`, např. 
```
class KpFileEditorComponent implements Component<KpFileEditorComponentBindings> {
    public static componentName = 'kpFileEditor';

    public templateUrl = require('kp-file-editor.tpl.ftl');

    public bindings = {
        file: '<',
        isFullscreen: '<',
        onEditorInit: '&'
    } as const;

    public controller = KpFileEditorComponentController;
}
```

Kontroller nadefinujeme jako třídu s názvem stejným jako komponenta a postfixem `Controller`, která implementuje interface s rozhraním, např. `KpFileEditorComponentController`.
Dále je možné, aby implementovala další rozhraní definující metody řízení životního cyklu komponenty, např. `IOnInit, IOnDestroy, ...`.
Pokud komponenta obsahuje výstupní parametry, které volám i z kontroleru, je třeba kontroler nadefinovat jako abstraktní třídu a výstupy implementovat jako abstraktní metody podle
definice z interfacu rozhraní. 
```
abstract class KpFileEditorComponentController implements KpFileEditorComponentBindings {
    public file: ViewableFile;
    public isFullscreen?: boolean;

    public abstract onEditorInit(locals: { $editor: Ace.Editor });
}
```
Pokud potřebuji reagovat na změnu vstupních hodnot, implementuji rozhraní `OnChanges<BINDINGS>` kde jako generický parametr uvedeme interface s rozhraním komponenty.
Díky tomu budeme mít pořešenou typovou korektnost v implementované metodě. 
 
`controllerAs` ve většině případů není nutné používat, spokojíme se s defaultním `$ctrl`.

V případě komponenty exportuji ze souborur třídu definice komponenty jako defaultní export, kontroler a případné další exporty jako jmenný export.
Při vytváření komponenty se snažím vyhnout `$scope`. Pokud potřebuji hlídat změnu nějakého fieldu, nepoužiju `$scope.$watch`, ale implementuji `IDoCheck` a kontrolu provedu manuálně.

### Servisy
Pro implementaci logiky použijeme servisy pokud lze logika napsat pomocí třídy-

### Factory
Pro implementaci logiky, která nelze zapsat pomocí třídy (nějaká funkce) použijeme factory.
např.
```
/*@ngInject*/
export default function favouritesFactory(currentUserFavouritesDataService: CurrentUserFavouritesDataService): FavouritesFactory {
    return new RecordList(currentUserFavouritesDataService);
}
```

### Providery
Providery se používají v případě, že chci nějakou servisu nastavit v konfigurační fázi angularu.
Implementujeme je pomocí třídy a pro vrácení implementace servisy pužijeme metodu `$get()`, která může v případě oanotování pomocí `/*@ngInject*/` přijímat DI jako argumenty.
Dále vytvoříme `interface` s definicí API servisy a použijeme ho jako návratový typ metody `$get()`.

příklad:
```
export interface SearchInputService {
    getSearchValue(): string;
    search(query: string): Promise<any>;
}

export default class SearchInputServiceProvider {
    public static providerName = 'searchInputService';

    private searchValue: string;
    private searchFunction: (query: string) => Promise<any>;

    public setSearchFunction(searchFunction: (query: string) => any) {
        this.searchFunction = searchFunction;
    }

    /*@ngInject*/
    private $get($injector: IInjectorService): SearchInputService {
        this.searchValue = undefined;

        return {
            search(query: string) {
                this.searchValue = query;
                return this.search(query);
            },

            getSearchValue: () => this.searchValue
        };
    }
}
```

### Data Servisy
Data servisy jsou speciálním typem servis, které se starají jen a pouze o komunikaci z backendem.
Pro drtivou většinu komunikace používáme vlastní REST servisu `ajaxService`.
Třída bude ideálně obsahovat mimo privátních metod jen `public async` metody pro volání jednotlivých endpointů.
Dále bude mít třída `private (static) readonly` konstanty s nadefinovanýma základníma routama pro jednotlivé requesty.
Základní URL prefix `/api` není potřeba psát, ten je nakonfigurován pomocí `ajaxServiceProvider` pro všechny instance.
Pokud endpoint vyžaduje URL bez prefixu nebo absolutní URL, tak `AjaxService` má metodu `withoutBaseUrl`, která vrací instanci `AjaxService` bez prefixu.
Ukázkový příklad data servisy:
```
export default class SdiRequestsDataService {
    public static serviceName = 'sdiRequestsDataService';

    private readonly SDI_REQUESTS_ROUTE = 'sdi-requests';
    private readonly SDI_REQUESTS_CREATE_ROUTE = 'sdi-requests/create';
    private readonly SDI_REQUESTS_EDIT_ROUTE = 'sdi-requests/edit';
    private readonly SDI_SENDINGS_ROUTE = 'sdi-sendings';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async query(userId?: number): Promise<SdiRequest[]> {
        const params = userId ? {user: userId} : undefined;

        return this.ajaxService
            .createRequest(this.SDI_REQUESTS_ROUTE)
            .get(params);
    }

    @ngAsync()
    public async createSdiRequest(sdiRequestCreationRequest: SdiRequestCreationRequest): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${this.SDI_REQUESTS_CREATE_ROUTE}`)
            .post(transferify(sdiRequestCreationRequest));
    }

    @ngAsync()
    public async editSdiRequest(sdiRequestEditRequest: SdiRequestEditRequest): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${this.SDI_REQUESTS_EDIT_ROUTE}`)
            .post(transferify(sdiRequestEditRequest));
    }

    @ngAsync()
    public async removeSdiRequest(sdiRequestId: number): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${this.SDI_REQUESTS_ROUTE}/${sdiRequestId}`)
            .delete();
    }
}
```
Pokud je potřeba reaktivní request (při použití v RxJS), je třeba použít servisu `ObservableAjaxService`. 
Příklad:
```
export default class ObservableCaptureDataService {

    public static serviceName = 'observableCaptureDataService';
    private baseUri: string;

    /*@ngInject*/
    constructor(private observableAjaxService: ObservableAjaxService) {
        this.baseUri = 'captures';
    }

    public putCapture(capture: Capture): Observable<ActionResponseWithText> {
        return this.observableAjaxService
            .forUri(this.baseUri)
            .withBody(transferify(capture))
            .put<ActionResponseWithText>();
    }
}
```
### Moduly
Pro nadefinování modulu používáme knihovnu `angularjs-register`, která řeší některé záležitosti s definováním komponent jako tříd.
Narozdíl od standardní definice modulu není potřeba v případě žádných závislostí psát `[]` jako druhý argument.
Modul by měl vždy defaultně exportovat svůj název.
Pokud budu zadávat závislosti do modulu, je snaha vyhnout se obyčejným stringovým hodnotám a místo toho použít import jména modulu.
Jednotlivé komponenty modulu zaregistuji pomocí jejich názvů ve statickém fieldu.
příklad: 
```
import pdfViewerModule from '../pdf-viewer/pdf-viewer.module';

export default register('portaro.media-viewer', [pdfViewerModule])
    .service(RepositoryService.serviceName, RepositoryService)
    .service(MediaViewerService.serviceName, MediaViewerService)
    .controller(MediaViewerController.controllerName, MediaViewerController)
    .component(KpDirectoryTreeComponent.componentName, KpDirectoryTreeComponent)
    .component(KpMediaViewerDirectoriesComponent.componentName, KpMediaViewerDirectoriesComponent)
    .name();
```

## Dokumentace
Vždy, když vznikne nová komponenta je třeba ji doplnit o dokumentaci.
K dokumentaci se používají ngDoc anotace.
Vždy když vznikne nový modul, je třeba vytvořit modul i pro dokumentaci.
Tedy např.:
```
/**
 * @ngdoc module
 * @name portaro.media-viewer
 * @module portaro.media-viewer
 */
```
Dokumentovat je třeba veřejné API servis, minimálně alespoň argumenty vizuálních komponent a stručný popis.
Dobré je do dokumentace vytvořit i fungující příklad. Nástroje generátoru dokumentace z toho posléze vytvoří fungující příklad na webu.
Když bude komponenta dobře zdokumentovaná, nebude potřeba poté procházet její zdrojové kódy pro pochopení funkčnosti a tím se ušetří čas.
Důležitá je také dokumentace pro nově příchozí vývojáře. Stejně by to mělo být i na backendu.

## Testy
Bylo by dobré psát testy a mít co nejvyšší pokrytí testama. Ideálně by bylo dobré mít testy pro veškerou logiku. Složité vizuální komponenty se špatně testují.
Testy se píšou prozatím do adresáře `test`, kde se vytvoří stejná adresářová struktura, jako k testovanému souboru ve složce `src`. 
Název souboru je stejný jako název testovaného souboru s spříponou `.spec.ts`.

## HTML
Protože AngularJS je rozšířením HTML, bylo by dobré pro snadnou orientaci v kódu dodržovat nějako štábní kulturu.
Tedy, rovnat atributy v HTML tagu tak, aby jako první byly nativní atributy a až po nich atributy angularu. 
Dále pro snažší orientaci jako první psát `id`, dále třídu `class` a až po nich abytek.
např:
```
<button class="btn-next btn btn-default glyphicon glyphicon-chevron-right" type="button" ng-show="$ctrl.hasNext()" ng-click="$ctrl.onNextDeferred.resolve()"></button>
```

## CSS
Názvy tříd a IDček kebab-casem.