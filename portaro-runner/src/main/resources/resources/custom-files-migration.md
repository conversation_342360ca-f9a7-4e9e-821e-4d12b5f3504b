
# Custom files migration

veci pouzivane v custom filech a sablonach
(artoteka, moravska galerie, TUL, VSB, KFBZ, demo.kpsys.cz, stare tisky, provenio, <PERSON><PERSON> roznov)

## direktivy
- ~~clamp (v kp-carousel) (z balicku ng-clamp1) (clamp="3") -> slo by nahradit css styly~~ (https://albertwalicki.com/learn/solutions/how-to-limit-text-to-n-lines)
- ~~kp-scroll-up (artoteka - html/all.html) class="scroll-button scroll-button-hidden" -> nova komponenat obalena v custom elementu~~

## skripty
- tul - spusta
- tul - custom login component template - WTF?!
- ~~google map provider (provenio tusim)~~

## komponenty
- kp-carousel
- kp-collapser - !ng-transclude!
- jp-slider - !ng-transclude! -> asi zakazat
- kp-pdf-viewer
- jp-news-slider - stare jmeno (angular komponent name) implementovano komponenentou kpNewDocuments
- kp-documents-slider
- kp-main-menu (jako css selektor)
- jp-documents-slider (jako css selektor)


## custom css
- artoteka - spusta

## css id
- detail-main-nazev
- detail-main-autor
- detail-main-name
- user-detail-additional-content-1
- user-detail-additional-content-container
- return-to-top
- searchStringInputVIndexu
- searchStringInputVLogu
- detail
- detail-main-autor
- authority-detail
- konto

## css class
- panel-body
- carousel-title
- carousel-description
- series
- series-title
- part-title
- part-number
- part-separator
- navesti
- 'staveci_znak_l1%s'
- sourceDocument
- printTable
- print-receipt-of-payment
- print-registration-agreement
- artoteka-*
- embed-responsive-*
- show-more-content (kp-collapser)
- unavailable
- locales
- container-fluid
- nav
- nav-pills
- galerie-*
- scroll-button
- scroll-button-hidden
- text-center
- btn-borrow
- collapsible-content
- table
- table-bordered
- table-condensed
- indent-top
- big
- custom-page
- img-responsive
- indent-left
- portaroSearch
- index-search
- input-group
- index-search-submit
- pull-right
- cover-container
- permalink-value
- form-control
- index-search-input
- input-group-btn
- list-group
- list-group-item
- searchStringInput
- novinkyVKataloguDiv
- ng-pristine, ng-valid (vsb - index.html)
- infobox
- oh
- alert
- alert-warning
- alert-dismissable
- close
- greeting
- ss-icon
- margin-top-1em
- news-slider-header
- carousel
- icon-chevron-up
- searchbox-header
- custom-footer
- search
- type-catalog
- list-item-document-main
- document-unavailable-badge
- list-item-document-paragraph-template
- list-item-document
- pretty
- p-*
- scroll-button
- scroll-button-hidden
- greenHeaderTable
- nav-*
- nav-tabs
- nav-tabs-*
- tab-content
- upperFooter
- jp-slider
- arrow
- jp-documents-slider
- upperFooter
- btn-slider-*
- kp-main-menu
- onhover
- kp-mm-main-menu-container
- menu-top-wrapper
- kp-mm-user-avatar
- locale-change
- logo-content-container
- logo-stripe
- logo-search
- extended-search
- input-group
- locales
- searchStringSubmit
- record-heading
- main-panel-pravy
- main-panel-hlavni
- cover-container-document-detail
- hodnoceni
- document-detail-blok-main
- detail-blok
- authority-name
- zaznamListItem
- nazevZaznamu
- list-item-document-autor
- search-facet-panel
- facets-panel-body
- facet-name
- facet-keys
- buttonsPanel
- dostupnost
- availability-exemplars-info
- availability-exemplars-departments-info
- availability-button
- print-btn-group
- record-name
- news-slider-header
- lang-list
- footer-container
- footer
- custom-page
- footer
- rss
- container
- index
- main-panel-hlavni
- row
- kp-panels-wrapper
- kp-panel-container
- panel
- panel-default
- kp-panel-content
- panel-body
- panel-heading
- panel-body
- pull-left
- bg-warning
- glyphicon
- glyphicon-exclamation-sign
- range-value-editor-direct-input - vsb