<script lang="ts">
    import type {ServerFolder} from 'typings/portaro.be.types';
    import {getInjector} from 'core/svelte-context/context';
    import {createReportServerContext} from './report-server-context';
    import ReportServerService from './services/report-server.service';
    import KpLoadablePageContainer from 'shared/layouts/containers/KpLoadablePageContainer.svelte';
    import ReportServerHeadingPart from './parts/ReportServerHeadingPart.svelte';
    import ReportServerFoldersPart from './parts/ReportServerFoldersPart.svelte';
    import ReportServerFormPart from './parts/ReportServerFormPart.svelte';

    export let serverFolder: ServerFolder;

    const service = getInjector().getByToken<ReportServerService>(ReportServerService.serviceName);

    createReportServerContext(service, serverFolder);
</script>

<KpLoadablePageContainer pageClass="kp-report-server-page">
    <ReportServerHeadingPart/>

    <div class="folders-and-form-container">
        <ReportServerFoldersPart/>
        <ReportServerFormPart/>
    </div>
</KpLoadablePageContainer>

<style lang="less">
    @import (reference) "styles/portaro.media-queries.less";
    @import (reference) "styles/portaro.themes.less";

    .folders-and-form-container {
        width: 100%;
        height: 100%;
        display: flex;
        border-top: 1px solid @themed-border-default;

        @media (max-width: @screen-sm-max) {
            flex-direction: column;
        }
    }
</style>