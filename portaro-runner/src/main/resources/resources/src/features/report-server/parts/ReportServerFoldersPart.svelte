<script lang="ts">
    import type {ServerFolder} from 'typings/portaro.be.types';
    import {getReportServerContext} from '../report-server-context';
    import {getLocalization} from 'core/svelte-context/context';
    import {onD<PERSON>roy} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import ReportServerFolder from '../components/ReportServerFolder.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';

    const context = getReportServerContext();
    const localize = getLocalization();

    let serverFolder: ServerFolder | null;
    const serverFolderUnsubscribe = context.serverFolder.subscribe((currentServerFolder) => serverFolder = currentServerFolder);

    onDestroy(() => {
        serverFolderUnsubscribe();
    });
</script>

<div class="report-server-folders-part">
    <h2>{localize(/* @kp-localization reportServer.page.foldersHeading */ 'reportServer.page.foldersHeading')}</h2>
    {#if exists(serverFolder)}
        <ReportServerFolder {serverFolder}/>
    {:else}
        <KpLoadingBlock size="sm"/>
    {/if}
</div>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";
    @import (reference) "styles/portaro.variables.less";

    .report-server-folders-part {
        display: flex;
        flex-direction: column;
        gap: @spacing-l;
        padding-top: @spacing-l;
        padding-right: @spacing-l;
        border-right: 1px solid @panel-default-border;

        @media (max-width: @screen-sm-max) {
            border-right: none;
            padding-right: 0;
        }

        h2 {
            font-size: @font-size-large;
            margin: @spacing-xs 0;
        }
    }
</style>