<script lang="ts">
    import type {ServerFolder} from 'typings/portaro.be.types';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {getReportServerContext} from '../report-server-context';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import ReportServerTemplateItem from './ReportServerTemplateItem.svelte';

    export let serverFolder: ServerFolder;

    const context = getReportServerContext();
</script>

<div class="report-server-folder">
    <div class="folder-heading">
        <UIcon icon="folder"/>
        <span>{pipe(serverFolder, loc())}</span>
    </div>

    {#if serverFolder.reportTemplates.length > 0 || serverFolder.folderSubItems.length > 0}
        <ul class="directory-contents">
            {#each serverFolder.reportTemplates as reportTemplate}
                <li>
                    <ReportServerTemplateItem {reportTemplate}
                                              on:click={() => context.setSelectedTemplate(reportTemplate)}/>
                </li>
            {/each}

            {#each serverFolder.folderSubItems as subFolder}
                <li>
                    <svelte:self serverFolder="{subFolder}" on:template-selected/>
                </li>
            {/each}
        </ul>
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .report-server-folder {
        display: flex;
        flex-direction: column;
        width: 100%;

        .folder-heading {
            display: flex;
            align-items: center;
            gap: @spacing-s;
        }

        .directory-contents {
            display: flex;
            flex-direction: column;
            gap: @spacing-ml;
            margin-top: @spacing-ml;
            margin-left: @spacing-s;
            padding-left: @spacing-m;
            border-left: 2px solid var(--accent-blue-new);
        }
    }
</style>