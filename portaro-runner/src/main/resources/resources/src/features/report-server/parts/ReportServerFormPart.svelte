<script lang="ts">
    import type {Form, ReportTemplate} from 'typings/portaro.be.types';
    import type {FormGroup} from 'shared/value-editors/internal/forms/form-group';
    import type {Subscription} from 'rxjs';
    import {getReportServerContext} from '../report-server-context';
    import {getLocalization} from 'core/svelte-context/context';
    import {onDestroy} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {fly} from 'svelte/transition';
    import {get} from 'svelte/store';
    import KpUniversalForm from 'shared/value-editors/kp-universal-form/KpUniversalForm.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';

    const context = getReportServerContext();
    const localize = getLocalization();

    let lastReportTemplateId: string | null = null;
    let selectedReportTemplate: ReportTemplate | null = null;
    let reportTemplateForm: Form | null = null;

    let universalForm; // KpUniversalForm component binding
    let fieldStateSubscription: Subscription;

    let formData: Record<string, any>; // Binding of model data from universal form
    let isFormValid = false;
    let loadingForm = false;
    let reportProcessing = false;

    $: if (exists(universalForm)) mountFormValidityCheck(universalForm?.getFormController());

    const reportTemplateUnsubscribe = context.selectedTemplate.subscribe((reportTemplate) => {
        selectedReportTemplate = reportTemplate;

        if (!exists(reportTemplate)) {
            lastReportTemplateId = null;
            reportTemplateForm = null;
            return;
        }

        if (lastReportTemplateId !== reportTemplate.id) {
            loadReportTemplateForm(reportTemplate);
        }
    });

    onDestroy(() => {
        reportTemplateUnsubscribe();
    });

    async function loadReportTemplateForm(reportTemplate: ReportTemplate) {
        loadingForm = true;
        reportTemplateForm = null;
        lastReportTemplateId = reportTemplate.id;
        formData = get(context.prefillTemplateProps);
        reportTemplateForm = await context.service.getTemplateForm(reportTemplate);
        loadingForm = false;
    }

    function mountFormValidityCheck(formController: FormGroup<any>) {
        if (!exists(formController)) {
            return;
        }

        if (exists(fieldStateSubscription)) {
            fieldStateSubscription.unsubscribe();
        }

        fieldStateSubscription = formController.getFieldState$().subscribe((state) => isFormValid = state.valid);
    }

    const handleDownloadReportClick = async () => {
        reportProcessing = true;
        await context.service.downloadExport(selectedReportTemplate, formData);
        reportProcessing = false;
    };

    const handleDisplayReportClick = () => {
        const url = context.service.getExportUrl(selectedReportTemplate, formData);
        window.open(url, '_blank');
    };
</script>

<div class="report-server-form-part">
    {#if loadingForm}
        <div class="loading-container">
            <KpLoadingBlock size="sm"/>
        </div>
    {:else}
        {#if exists(selectedReportTemplate) && exists(reportTemplateForm)}
            <div class="form-container" in:fly={{y: 10, duration: 250}}>
                <h2>{pipe(selectedReportTemplate, loc())}</h2>
                <KpUniversalForm bind:this={universalForm}
                                 bind:model="{formData}"
                                 formSettings="{reportTemplateForm}"/>

                <small class="buttons-label text-muted text-center">
                    {localize(/* @kp-localization reportServer.page.formInfoLabel */ 'reportServer.page.formInfoLabel')}
                </small>

                <div class="buttons-container">
                    <KpButton buttonStyle="accent-blue-new"
                              isDisabled="{!isFormValid || reportProcessing}"
                              on:click={handleDownloadReportClick}>

                        <IconedContent icon="download">
                            {localize(/* @kp-localization reportServer.page.downloadReportBtn */ 'reportServer.page.downloadReportBtn')}
                        </IconedContent>
                    </KpButton>

                    <KpButton buttonStyle="brand-orange-new"
                              isDisabled="{!isFormValid || reportProcessing}"
                              on:click={handleDisplayReportClick}>

                        <IconedContent icon="eye">
                            {localize(/* @kp-localization reportServer.page.showReportBtn */ 'reportServer.page.showReportBtn')}
                        </IconedContent>
                    </KpButton>
                </div>
            </div>
        {/if}

        {#if !exists(selectedReportTemplate) || !exists(reportTemplateForm)}
            <div class="loading-container">
                <IconedContent icon="info" orientation="vertical" align="center" justify="center">
                    <span class="text-muted">
                        {localize(/* @kp-localization reportServer.page.formPlaceholder */ 'reportServer.page.formPlaceholder')}
                    </span>
                </IconedContent>
            </div>
        {/if}
    {/if}
</div>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";
    @import (reference) "styles/portaro.variables.less";

    .report-server-form-part {
        position: relative;
        flex: 1;
        display: flex;
        flex-direction: column;
        max-width: 60vw;
        padding-top: @spacing-l;
        padding-left: @spacing-l;

        @media (max-width: @screen-sm-max) {
            padding-left: 0;
            min-height: 100px;
            margin-top: @spacing-l;
            border-top: 1px solid @panel-default-border;
        }

        .loading-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .form-container {
            display: flex;
            flex-direction: column;
            gap: @spacing-l;

            h2 {
                font-size: @font-size-large;
                margin: @spacing-xs 0;
            }

            .buttons-label {
                margin-bottom: -10px;
            }

            .buttons-container {
                display: flex;
                gap: @spacing-m;
            }
        }
    }

    :global {
        .report-server-form-part .form-container {
            .form-group .control-label {
                text-align: start;
            }

            .buttons-container > button {
                flex: 1;
            }
        }
    }
</style>