import type {Readable} from 'svelte/store';
import type {ReportTemplate, ServerFolder} from 'typings/portaro.be.types';
import type ReportServerService from './services/report-server.service';
import {writable} from 'svelte/store';
import {getContext, hasContext, setContext} from 'svelte';

const reportServerContext = 'report-server-ctx';

interface ReportServerContext {
    service: ReportServerService,
    serverFolder: Readable<ServerFolder | null>;
    prefillTemplateProps: Readable<Record<string, any> | null>;
    selectedTemplate: Readable<ReportTemplate>;
    setServerFolder: (serverFolder: ServerFolder | null) => void;
    setSelectedTemplate: (reportTemplate: ReportTemplate) => void;
    setPrefillTemplateProps: (props: Record<string, any>) => void;
}

export function createReportServerContext(service: ReportServerService, serverFolder: ServerFolder | null): ReportServerContext {
    const selectedTemplate = writable<ReportTemplate | null>(null);
    const reportServerWritable = writable(serverFolder);
    const prefillTemplatePropsWritable = writable<Record<string, any> | null>(null);

    return setContext<ReportServerContext>(reportServerContext, {
        service,
        selectedTemplate,
        serverFolder: reportServerWritable,
        prefillTemplateProps: prefillTemplatePropsWritable,
        setServerFolder: (folder: ServerFolder | null) => reportServerWritable.set(folder),
        setSelectedTemplate: (reportTemplate) => selectedTemplate.set(reportTemplate),
        setPrefillTemplateProps: (props: Record<string, any>) => prefillTemplatePropsWritable.set(props)
    });
}

export function getReportServerContext(): ReportServerContext {
    if (!hasContext(reportServerContext)) {
        throw new Error('Report server context does not exist! Use `createReportServerContext` function to create it in a parent component.');
    }

    return getContext<ReportServerContext>(reportServerContext);
}