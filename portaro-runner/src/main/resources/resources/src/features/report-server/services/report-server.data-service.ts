import type {Fond, Form, ReportTemplate, ServerFolder} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';
import type {FileDownloadService} from 'core/data-services/file-download.service';
import {transferify} from 'shared/utils/data-service-utils';

/**
 * @ngdoc service
 * @name reportServerDataService
 * @module use-cases.report-server
 *
 * @description
 * Data service for providing the report-server
 */
export default class ReportServerDataService {
    public static serviceName = 'reportServerDataService';

    private static readonly SERVER_FOLDER_ROUTE = 'report-server/server-folder';
    private static readonly FOND_REPORT_FOLDER_ROUTE = 'report-server/fond-report-folder';
    private static readonly TEMPLATE_ROUTE = 'report-server/templates';
    private static readonly FORM_ROUTE = 'form';
    public static readonly EXPORT_ROUTE_PREFIX = 'report-server/reports';
    public static readonly EXPORT_ROUTE_SUFFIX = 'export';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService, private fileDownloadService: FileDownloadService) {
    }

    public async getServerFolder(): Promise<ServerFolder> {
        return this.ajaxService
            .createRequest(`${ReportServerDataService.SERVER_FOLDER_ROUTE}`)
            .get();
    }

    public async getFondReportFolder(fond: Fond): Promise<ServerFolder> {
        return this.ajaxService
            .createRequest(`${ReportServerDataService.FOND_REPORT_FOLDER_ROUTE}/${fond.id}`)
            .get();
    }

    public async getTemplateForm(report: ReportTemplate): Promise<Form> {
        return this.ajaxService
            .createRequest(`${ReportServerDataService.TEMPLATE_ROUTE}/${report.id}/${ReportServerDataService.FORM_ROUTE}`)
            .get();
    }

    public async downloadExportFile(report: ReportTemplate, props: Record<string, any>): Promise<void> {
        await this.fileDownloadService
            .createRequest(`${ReportServerDataService.EXPORT_ROUTE_PREFIX}/${report.id}/${ReportServerDataService.EXPORT_ROUTE_SUFFIX}`)
            .withParams(transferify(props))
            .download();
    }
}