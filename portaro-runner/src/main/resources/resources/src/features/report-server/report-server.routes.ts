import type {StateProvider} from '@uirouter/angularjs';
import type ReportServerService from './services/report-server.service';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function reportServerRoutes($stateProvider: StateProvider) {
    let reportServerModule: { default: any; };
    $stateProvider
        .state({
            name: 'report-server',
            url: '/report-server',
            component:  KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => reportServerModule.default,
                props: async (reportServerService: ReportServerService) => {
                    const serverFolder = await reportServerService.getServerFolder();
                    return {serverFolder};
                }
            },
            lazyLoad: async () => {
                reportServerModule = await import(/* webpackChunkName: "reportServer" */ './KpReportServerPage.svelte');
                return null;
            }
        });
}