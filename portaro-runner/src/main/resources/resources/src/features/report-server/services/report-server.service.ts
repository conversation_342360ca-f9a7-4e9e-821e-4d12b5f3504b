import type {Form, Rec, ReportTemplate, ServerFolder} from 'typings/portaro.be.types';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
import {serializeUrlParams, transferify} from 'shared/utils/data-service-utils';
import ReportServerDataService from './report-server.data-service';

/**
 * @ngdoc service
 * @name reportServerService
 * @module use-cases.report-server
 *
 * @description
 * Service for interaction with report-server
 */
export default class ReportServerService {
    public static serviceName = 'reportServerService';

    /*@ngInject*/
    constructor(private toastMessageService: ToastMessageService,
                private reportServerDataService: ReportServerDataService,
                private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    public async getServerFolder(): Promise<ServerFolder> {
        try {
            return await this.reportServerDataService.getServerFolder();
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async getRecordReportServerFolder(record: Rec): Promise<ServerFolder> {
        try {
            return await this.reportServerDataService.getFondReportFolder(record.fond);
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async getTemplateForm(report: ReportTemplate): Promise<Form> {
        return this.reportServerDataService.getTemplateForm(report);
    }

    public async downloadExport(report: ReportTemplate, props: Record<string, any>): Promise<void> {
        try {
            const waitToast = this.toastMessageService.showWait('Probíhá generování přehledu...');

            await this.reportServerDataService.downloadExportFile(report, props);

            this.toastMessageService.dismissToast(waitToast);
            this.toastMessageService.showSuccess('Přehled byl úspěšně vygenerován');
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
        }
    }

    public getExportUrl(report: ReportTemplate, props: Record<string, any>): string {
        const serializedUrlParams = serializeUrlParams(transferify(props));
        return `/api/${ReportServerDataService.EXPORT_ROUTE_PREFIX}/${report.id}/${ReportServerDataService.EXPORT_ROUTE_SUFFIX}?${serializedUrlParams}`;
    }
}