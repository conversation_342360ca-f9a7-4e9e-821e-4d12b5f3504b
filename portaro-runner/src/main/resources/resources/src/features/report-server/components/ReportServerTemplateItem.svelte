<script lang="ts">
    import type {ReportTemplate} from 'typings/portaro.be.types';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {getReportServerContext} from '../report-server-context';
    import {onDestroy} from 'svelte';

    export let reportTemplate: ReportTemplate;
    const context = getReportServerContext();

    let selectedReportTemplate: ReportTemplate | null = null;
    const reportTemplateUnsubscribe = context.selectedTemplate.subscribe((currentTemplate) => selectedReportTemplate = currentTemplate);
    $: isSelected = reportTemplate.id === selectedReportTemplate?.id;

    onDestroy(() => {
        reportTemplateUnsubscribe();
    });
</script>

<button class="list-group-item report-server-template-item"
        class:is-selected={isSelected}
        on:click>

    <strong>
        {pipe(reportTemplate, loc())}
    </strong>

    {#if reportTemplate.description}
        <p class="text-muted">{reportTemplate.description}</p>
    {/if}
</button>

<style lang="less">
    .report-server-template-item {
        width: fit-content;
        text-align: start;
        transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;

        &.is-selected {
            background-color: #F4E1DC;
            border-color: var(--brand-orange-new);
        }

        p {
            margin: 0;
        }
    }
</style>