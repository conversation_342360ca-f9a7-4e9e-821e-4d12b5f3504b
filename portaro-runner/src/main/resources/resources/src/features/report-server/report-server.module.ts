import register from '@kpsys/angularjs-register';
import ReportServerService from './services/report-server.service';
import ReportServerDataService from './services/report-server.data-service';
import reportServerRoutes from './report-server.routes';

/**
 * @ngdoc module
 * @name portaro.features.report-server
 * @module portaro.features.report-server
 *
 *
 * @description
 * Data service for providing the report-server
 */
export default register('portaro.features.report-server')
    .service(ReportServerService.serviceName, ReportServerService)
    .service(ReportServerDataService.serviceName, ReportServerDataService)
    .config(reportServerRoutes)
    .name();