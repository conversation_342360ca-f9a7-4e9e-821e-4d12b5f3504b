<script lang="ts">
    import type {RecordCollectionViewConfiguration} from '../record-collections/types';
    import type {RecordCollectionManager} from '../record-collections/record-collection-manager';
    import {getLocalization} from 'core/svelte-context/context';
    import KpRecordCollection from '../record-collections/kp-record-collection/KpRecordCollection.svelte';
    import KpPluralizeText from 'shared/components/kp-pluralize-text/KpPluralizeText.svelte';
    import KpVisitedPanel from '../record/kp-visited-documents-panel/KpVisitedPanel.svelte';
    import KpPageContainer from 'shared/layouts/containers/KpPageContainer.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    export let recordCollectionManager: RecordCollectionManager;
    export let viewConfiguration: RecordCollectionViewConfiguration;

    const localize = getLocalization();

    let collectionSize = recordCollectionManager.getSize();

    function updateCollectionSize() {
        collectionSize = recordCollectionManager.getSize();
    }
</script>

<KpPageContainer id="favourites" additionalClasses="kp-favourites-page">
    <Flex direction="column" gap="s">
        <KpHeading type="h1">
            {localize(/* @kp-localization recordCollections.favourites.Favourites */ 'recordCollections.favourites.Favourites')}
        </KpHeading>

        <p class="subtitle">{localize(/* @kp-localization recordCollections.favourites.Subtitle */ 'recordCollections.favourites.Subtitle')}</p>
    </Flex>

    <div class="row">
        <div class="col-sm-8 main-panel-hlavni">
            <div class="record-collection-size">
                <span class="collection-size-counter">
                    <KpPluralizeText
                        count={collectionSize}
                        zeroCountText={localize(/* @kp-localization recordCollections.favourites.vOblibenychNejsouZadneZaznamy */ 'recordCollections.favourites.vOblibenychNejsouZadneZaznamy')}
                        oneCountText={localize(/* @kp-localization recordCollections.favourites.1ZaznamVOblibenych */ 'recordCollections.favourites.1ZaznamVOblibenych')}
                        twoThreeFourCountText={localize(/* @kp-localization recordCollections.favourites.xZaznamyVOblibenych */ 'recordCollections.favourites.xZaznamyVOblibenych')}
                        moreText={localize(/* @kp-localization recordCollections.favourites.xZaznamuVOblibenych */ 'recordCollections.favourites.xZaznamuVOblibenych')}/>
                </span>
            </div>

            <KpRecordCollection {recordCollectionManager} {viewConfiguration} on:update={updateCollectionSize}/>
        </div>

        <!-- pravy sloupec -->
        <aside class="col-sm-4 main-panel-pravy">
            <KpVisitedPanel/>
        </aside>
    </div>
</KpPageContainer>