<script lang="ts">
    import type {Rec} from 'typings/portaro.be.types';
    import type {State} from 'shared/constants/portaro.constants';
    import {KpFavouritesPanelPresenter} from './kp-favourites-panel.presenter';
    import {onDestroy, onMount,} from 'svelte';
    import {fade} from 'svelte/transition';
    import {skipWhile} from 'rxjs/operators';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {exists} from 'shared/utils/custom-utils';
    import {states} from 'shared/constants/portaro.constants';
    import KpFavouritesButton from '../kp-favourites-button/KpFavouritesButton.svelte';
    import KpCover from '../../record/kp-cover/KpCover.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import KpGenericPanel from 'shared/ui-widgets/panel/KpGenericPanel.svelte';
    import KpButtonStyleAnchor from 'shared/ui-widgets/button/KpButtonStyleAnchor.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';

    export let record: Rec = null;

    const localize = getLocalization();
    const presenter = getInjector().getByToken<KpFavouritesPanelPresenter>(KpFavouritesPanelPresenter.presenterName);

    const limit = presenter.getLimit();

    let isInitialized = false;
    let size = 0;
    let showAddAll = false;
    let isLoading = true;
    let isEmpty = true;
    let records: Rec[] = [];

    // subscribe for updates after initial load in case already loaded items are not valid (e.g. incorrect sorting order)
    const favouriteRecordsSubscription = presenter.getFavouriteRecord$().pipe(skipWhile(() => !isInitialized)).subscribe((updatedRecordList) => updateRecordList(updatedRecordList));
    const statesSubscription = presenter.getFavouritesState$().subscribe((state) => updateLoadingState(state));
    const showAddAllVisibilitySubscription = presenter.getAddAllCapabilityChange$().subscribe(() => updateAddAllButtonVisibility());

    onMount(async () => {
        records = await presenter.loadFavourites();
        isInitialized = true;
    });

    onDestroy(() => {
        favouriteRecordsSubscription.unsubscribe();
        statesSubscription.unsubscribe();
        showAddAllVisibilitySubscription.unsubscribe();
    });

    function updateLoadingState(state: State) {
        isLoading = state === states.PROCESSING || state === states.INITIAL;
        if (state === states.NORMAL) {
            size = presenter.getSize();
            isEmpty = presenter.isEmpty();
        }
    }

    function updateRecordList(updatedRecordList: Rec[]) {
        records = updatedRecordList;
    }

    function updateAddAllButtonVisibility() {
        showAddAll = presenter.canAddPotentials();
    }

    function addAll() {
        presenter.addAll();
    }

    function removeAll() {
        presenter.removeAll();
    }
</script>

<!-- id serves as scrollTo target for $anchorScroll -->
<section class="oblibene-container" id="favourites-panel" aria-labelledby="favourites-panel-label">
    <KpGenericPanel hasBodyPadding="{isLoading || isEmpty}">
        <svelte:fragment slot="heading">
            {#if exists(record)}
                <div class="pull-right">
                    <KpFavouritesButton {record}/>
                </div>
            {/if}

            {#if showAddAll}
                <div class="pull-right">
                    <button type="button"
                            disabled="{isLoading}"
                            on:click={addAll}
                            class="btn btn-xs btn-default">
                        {localize(/* @kp-localization recordCollections.favourites.addAllFromSearch */ 'recordCollections.favourites.addAllFromSearch')}
                    </button>
                </div>
            {/if}

            <h2 id="favourites-panel-label" class="unset-style">
                {localize(/* @kp-localization recordCollections.favourites.Favourites */ 'recordCollections.favourites.Favourites')}
            </h2>

            <span class="badge" class:hidden={isLoading}>{size}</span>
        </svelte:fragment>

        {#if isLoading}
            <KpLoadingBlock size="sm"/>
        {/if}

        {#if !isLoading}
            {#if isEmpty}
                <span class="text-center text-muted">
                    {localize(/* @kp-localization commons.ZadnePolozky */ 'commons.ZadnePolozky')}
                </span>
            {:else}
                <div class="covers-list">
                    {#each records as item (item.id)}
                        <div class="cover-container-in-mini-list" transition:fade={{duration:200}}>
                            <KpCover record={item} fillTo="width"/>
                        </div>
                    {:else}
                        <KpLoadingBlock size="sm"/>
                    {/each}

                    {#if (size > limit)}
                        <div class="and-more-message-text">&hellip;{localize(/* @kp-localization commons.aDalsi */ 'commons.aDalsi')}</div>
                    {/if}
                </div>

                <div class="action-buttons-container">
                    <KpButtonStyleAnchor href="/#!/favourites" buttonSize="xs">
                        {localize(/* @kp-localization commons.ZobrazitVse */ 'commons.ZobrazitVse')}
                    </KpButtonStyleAnchor>

                    <KpButton buttonSize="xs" on:click={removeAll}>
                        {localize(/* @kp-localization recordCollections.removeAll */ 'recordCollections.removeAll')}
                    </KpButton>
                </div>
            {/if}
        {/if}
    </KpGenericPanel>
</section>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "bootstrap-less/bootstrap/variables";

    .covers-list {
        display: flex;
        flex-wrap: wrap;
        gap: @spacing-xs;
        padding: @panel-body-padding;
    }

    .cover-container-in-mini-list {
        width: 23px;
        margin-right: 2px;
    }

    .action-buttons-container {
        display: flex;
        align-items: center;
        gap: @spacing-s;
        padding: @panel-body-padding;
        border-top: 1px solid @panel-default-border;
    }

    .and-more-message-text {
        display: flex;
        flex-direction: row;
        align-items: center;
    }
</style>