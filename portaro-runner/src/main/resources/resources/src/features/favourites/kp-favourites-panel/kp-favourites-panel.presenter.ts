import type {RecordCollectionManager} from '../../record-collections/record-collection-manager';
import type {FavouritesCollectionService} from '../favourites-collection.service';
import type {Rec} from 'typings/portaro.be.types';
import type {Observable, Subject} from 'rxjs';
import {BehaviorSubject, combineLatest, ReplaySubject} from 'rxjs';
import {debounceTime, map} from 'rxjs/operators';
import type {State} from 'shared/constants/portaro.constants';
import {states} from 'shared/constants/portaro.constants';
import type {SortablePageableList} from 'shared/utils/sortable-reactive-pageable-list';
import type {RecordCollectionItem} from '../../record-collections/types';

export class KpFavouritesPanelPresenter {
    public static presenterName = 'kpFavouritesPanelPresenter';

    private readonly FAVOURITES_PANEL_ITEMS_LIMIT = 8;

    private favouritesManager: RecordCollectionManager;
    private pageableList: SortablePageableList<RecordCollectionItem, string>
    private readonly favouriteRecords$: Subject<Rec[]> =  new ReplaySubject<Rec[]>(1);
    private readonly favouritesState$: Subject<State> = new BehaviorSubject<State>(states.INITIAL);
    private readonly isInitialized: Promise<void>;

    /*@ngInject*/
    constructor(private favouritesCollectionService: FavouritesCollectionService) {
        this.isInitialized = favouritesCollectionService
            .getFavouritesCollectionManager()
            .then((favouritesManager) => this.init(favouritesManager));
    }

    private init(favouritesManager: RecordCollectionManager) {
        this.favouritesManager = favouritesManager;
        this.favouritesManager.getState$().subscribe(this.favouritesState$);
        this.pageableList = this.favouritesManager.getPageableList();

        this.pageableList.getState$()
            .pipe(
                map((state) => state.loadedItems ?? []),
                debounceTime(100),
                map((collectionItems) => this.getRecordsForPanel(collectionItems))
            ).subscribe(this.favouriteRecords$);
    }

    public getLimit(): number {
        return this.FAVOURITES_PANEL_ITEMS_LIMIT;
    }

    public isEmpty(): boolean {
        return this.favouritesManager.isEmpty();
    }

    public getSize(): number {
        return this.favouritesManager.getSize();
    }

    public getFavouriteRecord$(): Observable<Rec[]> {
        return this.favouriteRecords$.asObservable();
    }

    public getFavouritesState$() {
        return this.favouritesState$.asObservable();
    }

    public getAddAllCapabilityChange$(): Observable<[Rec[], void]> {
        return combineLatest([this.favouriteRecords$, this.favouritesCollectionService.getPotentialsListUpdate$()])
    }

    public canAddPotentials(): boolean {
        return this.favouritesCollectionService.canAddSomePotentials();
    }

    public async removeAll(): Promise<void> {
        await this.favouritesManager.removeAll();
    }

    public async addAll(): Promise<void> {
        await this.favouritesCollectionService.addPotentials();
    }

    public async loadFavourites() {
        await this.isInitialized;
        const items = await this.pageableList.loadFirstPage();
        return this.getRecordsForPanel(items);
    }

    private getRecordsForPanel(collectionItems: RecordCollectionItem[]): Rec[] {
        return collectionItems
            .slice(0, this.FAVOURITES_PANEL_ITEMS_LIMIT)
            .map((item) => item.record);
    }
}