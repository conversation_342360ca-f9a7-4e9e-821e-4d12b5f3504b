<script lang="ts">
    import type {State} from 'shared/constants/portaro.constants';
    import type {Rec} from 'typings/portaro.be.types';
    import {onDestroy} from 'svelte';
    import {KpFavouritesButtonPresenter} from './kp-favourites-button.presenter';
    import {states} from 'shared/constants/portaro.constants';
    import {getInjector, getLocalization} from 'core/svelte-context/context';

    export let record: Rec;

    const localize = getLocalization();
    const presenter = getInjector().getByToken<KpFavouritesButtonPresenter>(KpFavouritesButtonPresenter.presenterName);

    let showAddButton = false;
    let showRemoveButton = false;
    let isDisabled = true;

    const statesSubscription = presenter.getFavouritesState$().subscribe((state) => updateButtonsDisabledState(state));

    onDestroy(() => statesSubscription.unsubscribe());

    function updateButtonsDisabledState(state: State) {
        isDisabled = state === states.PROCESSING || state === states.INITIAL;
        if (state === states.NORMAL) {
            showAddButton = presenter.canAddToFavourites(record);
            showRemoveButton = presenter.canRemoveFromFavourites(record);
        }
    }

    function addToFavourites() {
        presenter.addToFavourites(record);
    }

    function removeFromFavourites() {
        presenter.removeFromFavourites(record);
    }
</script>

{#if showAddButton}
    <button type="button"
            class="btn btn-default btn-xs btn-block add-to-favourites-button"
            disabled="{isDisabled}"
            on:click={() => addToFavourites()}>

        <slot name="add">
            <span class="glyphicon glyphicon-heart-empty"></span>
            {localize(/* @kp-localization recordCollections.favourites.AddToFavourites */ 'recordCollections.favourites.AddToFavourites')}
        </slot>
    </button>
{/if}

{#if showRemoveButton}
    <button type="button"
            class="btn btn-default btn-xs btn-block remove-from-favourites-button"
            disabled="{isDisabled}"
            on:click={() => removeFromFavourites()}>

        <slot name="remove">
            <span class="glyphicon glyphicon-heart"></span>
            {localize(/* @kp-localization recordCollections.favourites.RemoveFromFavourites */ 'recordCollections.favourites.RemoveFromFavourites')}
        </slot>
    </button>
{/if}

<style lang="less">
    .remove-from-favourites-button {
        visibility: visible; /* tlacitko odebrat je viditelne porad (ne jen kdyz se najede na nejaky jeho kontejner) */
    }
</style>