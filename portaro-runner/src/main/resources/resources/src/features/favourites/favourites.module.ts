import register from '@kpsys/angularjs-register';
import favouritesRoutes from './favourites.routes';
import {FavouritesCollectionService} from './favourites-collection.service';
import recordCollectionsModule from '../record-collections/record-collections.module';
import {KpFavouritesPanelPresenter} from './kp-favourites-panel/kp-favourites-panel.presenter';
import {KpFavouritesButtonPresenter} from './kp-favourites-button/kp-favourites-button.presenter';

export default register('portaro.features.favourites', [recordCollectionsModule])
    .service(FavouritesCollectionService.serviceName, FavouritesCollectionService)
    .service(KpFavouritesPanelPresenter.presenterName, KpFavouritesPanelPresenter)
    .service(KpFavouritesButtonPresenter.presenterName, KpFavouritesButtonPresenter)
    .config(favouritesRoutes)
    .name();
