import type {FavouritesCollectionService} from '../favourites-collection.service';
import type {Rec} from 'typings/portaro.be.types';
import type {RecordCollectionManager} from '../../record-collections/record-collection-manager';
import type {Subject} from 'rxjs';
import {BehaviorSubject} from 'rxjs';
import type {State} from 'shared/constants/portaro.constants';
import {states} from 'shared/constants/portaro.constants';

export class KpFavouritesButtonPresenter {
    public static presenterName = 'kpFavouritesButtonPresenter';

    private favouritesManager: RecordCollectionManager;
    private readonly favouritesState$: Subject<State> = new BehaviorSubject<State>(states.INITIAL);

    /*@ngInject*/
    constructor(private favouritesCollectionService: FavouritesCollectionService) {
        this.favouritesCollectionService
            .getFavouritesCollectionManager()
            .then((favouritesManager) => this.init(favouritesManager));
    }

    private init(favouritesManager: RecordCollectionManager) {
        this.favouritesManager = favouritesManager;
        this.favouritesManager.getState$().subscribe(this.favouritesState$);
    }

    public getFavouritesState$() {
        return this.favouritesState$.asObservable();
    }

    public canAddToFavourites(record: Rec): boolean {
        return !this.favouritesManager.contains(record);
    }

    public canRemoveFromFavourites(record: Rec): boolean {
        return this.favouritesManager.contains(record);
    }

    public async addToFavourites(record: Rec): Promise<void> {
        await this.favouritesManager.insertRecord(record);
    }

    public async removeFromFavourites(record: Rec): Promise<void> {
        await this.favouritesManager.removeFirstMatchingRecord(record);
    }
}