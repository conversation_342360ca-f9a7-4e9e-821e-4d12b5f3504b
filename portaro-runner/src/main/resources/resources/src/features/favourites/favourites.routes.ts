import type {StateProvider} from '@uirouter/angularjs';
import type {FavouritesCollectionService} from './favourites-collection.service';
import type {RecordCollectionsDataService} from '../record-collections/record-collections.data-service';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function favouritesRoutes($stateProvider: StateProvider) {
    let favouritesModule: { default: any; };
    $stateProvider
        .state({
            name: 'favourites',
            url: '/favourites',
            component:  KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => favouritesModule.default,

                /*@ngInject*/
                props: async (favouritesCollectionService: FavouritesCollectionService, recordCollectionsDataService: RecordCollectionsDataService) => {
                    const manager = await favouritesCollectionService.getFavouritesCollectionManager();
                    const viewConfiguration = await recordCollectionsDataService.getPageSettings(manager.getRecordCollection().id);
                    const recordCollectionManager = await favouritesCollectionService.getFavouritesCollectionManager();
                    return {viewConfiguration, recordCollectionManager};
                }
            },
            lazyLoad: async () => {
                favouritesModule = await import(/* webpackChunkName: "favourites" */ './KpFavouritesPage.svelte');
                return null;
            }
        });
}
