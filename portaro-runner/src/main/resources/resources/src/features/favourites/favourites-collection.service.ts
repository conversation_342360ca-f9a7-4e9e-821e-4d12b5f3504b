import type {RecordCollectionsDataService} from '../record-collections/record-collections.data-service';
import type {Auth, FinishedSaveResponse, Rec} from 'typings/portaro.be.types';
import type {RecordCollectionManager} from '../record-collections/record-collection-manager';
import type {Observable} from 'rxjs';
import {Subject} from 'rxjs';
import {debounceTime} from 'rxjs/operators';
import {isFavouritesCollection} from '../record-collections/util';
import type {RecordCollectionManagerFactory} from '../record-collections/record-collection-manager.factory';
import {RECORD_COLLECTION_CATEGORY_ID} from '../record-collections/constants';
import type {RecordCollectionItem} from '../record-collections/types';


export class FavouritesCollectionService {
    public static serviceName = 'favouritesCollectionService';

    private favouritesCollectionManager: RecordCollectionManager;
    private potentials: Rec[] = [];
    private readonly initializedPromise: Promise<any>;
    private potentialsListUpdate$: Subject<void> = new Subject<void>();

    /*@ngInject*/
    constructor(private recordCollectionsDataService: RecordCollectionsDataService, private currentAuth: Auth, private recordCollectionManagerFactory: RecordCollectionManagerFactory) {
        this.initializedPromise = this.initializeCollectionManager();
    }

    public async getFavouritesCollectionManager(): Promise<RecordCollectionManager> {
        await this.initializedPromise;
        return this.favouritesCollectionManager;
    }

    public getPotentialsListUpdate$(): Observable<void> {
        return this.potentialsListUpdate$.pipe(debounceTime(100));
    }

    public clearPotentials() {
        this.potentials = [];
        this.potentialsListUpdate$.next();
    }

    public registerPotential(item: Rec) {
        this.potentials.push(item);
        this.potentialsListUpdate$.next();
    }

    public canAddSomePotentials(): boolean {
        return this.potentials.some((potential) => !this.favouritesCollectionManager.contains(potential));
    }

    public addPotentials(): Promise<FinishedSaveResponse<RecordCollectionItem[]>> {
        const validPotentials = this.potentials.filter((potential) => !this.favouritesCollectionManager.contains(potential));
        return this.favouritesCollectionManager.insertRecords(validPotentials);
    }

    private async initializeCollectionManager() {
        this.favouritesCollectionManager = await this.recordCollectionManagerFactory.createAndInitialize(await this.getUsersFavouritesCollection());
    }

    private async getUsersFavouritesCollection() {
        const collections = await this.recordCollectionsDataService.queryCollections(this.currentAuth.activeUser, [RECORD_COLLECTION_CATEGORY_ID.FAVOURITES], true);
        if (collections.length < 1) {
            throw new Error('User does not have any favourites collection');
        }
        if (collections.length > 1) {
            throw new Error('User has more then one favourites collection');
        }
        const collection = collections[0];
        if (!isFavouritesCollection(collection)) {
            throw new Error('Favourites collection does not have favourites category');
        }
        return collection;
    }
}