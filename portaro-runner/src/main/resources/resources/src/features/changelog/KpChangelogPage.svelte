<script lang="ts">
    import type {AjaxService} from 'core/data-services/ajax.service';
    import {getInjector, getSanitize} from 'core/svelte-context/context';
    import {AjaxServiceProvider} from 'core/data-services/ajax.service';
    import {onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import KpLoadablePageContainer from 'shared/layouts/containers/KpLoadablePageContainer.svelte';

    const sanitizeHtml = getSanitize();
    const ajaxService = getInjector().getByToken<AjaxService>(AjaxServiceProvider.providerName);

    let changelogHtml: string | undefined;

    onMount(async () => {
        changelogHtml = await ajaxService.withoutBaseUrl().createRequest('/docs/changelog/changelog.html')
            .withResponseType('text')
            .get();
    });
</script>

<KpLoadablePageContainer pageClass="kp-changelog-page" loading="{!exists(changelogHtml)}">
    {@html sanitizeHtml(changelogHtml)}
</KpLoadablePageContainer>

<style lang="less">
    @code-highlight: #f5e7e2;
    @code-padding: 3px 5px;

    :global {
        .kp-changelog-page > * {
            margin: 0;
        }

        .kp-changelog-page > h2 {
            margin-bottom: -14px;
        }

        .kp-changelog-page code {
            color: var(--brand-orange-new);
            background-color: @code-highlight;
            padding: @code-padding;
        }
    }
</style>