import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function changelogRoutes($stateProvider: StateProvider) {
    let changelogModule: { default: any; };
    $stateProvider
        .state({
            name: 'changelog',
            url: '/changelog',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => changelogModule.default,
            },
            lazyLoad: async () => {
                changelogModule = await import(/* webpackChunkName: "changelog" */ './KpChangelogPage.svelte');
                return null;
            }
        });
}
