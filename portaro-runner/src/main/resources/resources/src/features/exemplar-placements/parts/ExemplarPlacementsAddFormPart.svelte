<script lang="ts">
    import type {ExemplarPlacementsData, RegalMap} from '../types';
    import {slide} from 'svelte/transition';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {createEventDispatcher} from 'svelte';
    import {KpExemplarPlacementsService} from '../kp-exemplar-placements.service';
    import {exists} from 'shared/utils/custom-utils';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let pageData: ExemplarPlacementsData;
    export let addingNewMap: boolean;

    const localize = getLocalization();
    const dispatch = createEventDispatcher<{ 'map-add': RegalMap, 'cancel': void }>();
    const service = getInjector().getByToken<KpExemplarPlacementsService>(KpExemplarPlacementsService.serviceName);
    const slideAnimParams = {duration: 250};

    const handleFormSubmit = async (event: SubmitEvent) => {
        const formData = new FormData(event.target as HTMLFormElement);
        const newRegalMap = await service.addNewExemplarPlacementMap(formData);

        if (!exists(newRegalMap)) {
            dispatch('cancel');
            return;
        }

        dispatch('map-add', newRegalMap);
    }

    const handleCancelClick = () => {
        dispatch('cancel');
    }
</script>

{#if addingNewMap}
    <form class="exemplar-placements-add-form-part"
          transition:slide="{slideAnimParams}"
          on:submit|preventDefault="{(event) => handleFormSubmit(event)}">

        <div class="form-group">
            <label for="map-id-input" class="col-sm-2 control-label">
                {`${localize(/* @kp-localization exemplarPlacements.MapIdentifier */ 'exemplarPlacements.MapIdentifier')} (${pageData.regalMapProperty})`}
            </label>

            <div class="col-sm-10">
                <input id="map-id-input" type="text" name="mapId" required class="form-control"/>
            </div>
        </div>

        <div class="form-group">
            <label for="map-info-input" class="col-sm-2 control-label">
                {localize(/* @kp-localization exemplarPlacements.MoreInformation */ 'exemplarPlacements.MoreInformation')}
            </label>

            <div class="col-sm-10">
                <textarea id="map-info-input" name="information" cols="50" rows="4" class="form-control"></textarea>
            </div>
        </div>

        <div class="form-group">
            <label for="map-image-input" class="col-sm-2 control-label">
                {localize(/* @kp-localization exemplarPlacements.Map */ 'exemplarPlacements.Map')}
            </label>

            <div class="col-sm-10">
                <input id="map-image-input"
                       type="file"
                       name="mapImage"
                       required accept="image/*"
                       class="form-control"/>
            </div>
        </div>

        <div class="buttons-container col-sm-10">
            <KpButton buttonStyle="danger-new" buttonType="button" on:click={handleCancelClick}>
                <IconedContent icon="cross">
                    {localize(/* @kp-localization commons.zrusit */ 'commons.zrusit')}
                </IconedContent>
            </KpButton>

            <KpButton buttonStyle="success-new" buttonType="submit">
                <IconedContent icon="check">
                    {localize(/* @kp-localization commons.Ulozit */ 'commons.Ulozit')}
                </IconedContent>
            </KpButton>
        </div>
    </form>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "bootstrap-less/bootstrap/variables";

    .exemplar-placements-add-form-part {
        position: relative;
        width: 100%;
        margin-bottom: 30px;
        display: flex;
        flex-direction: column;
        gap: @spacing-ml;
        padding: @spacing-ml 0;
        border: 1px solid @table-border-color;
        border-radius: @border-radius-default;

        .form-group {
            padding: 0;
            margin: 0;
        }

        .buttons-container {
            display: flex;
            align-items: center;
            gap: @spacing-ml;
            flex-wrap: wrap;
            justify-content: space-between;
            align-self: end;
        }
    }
</style>