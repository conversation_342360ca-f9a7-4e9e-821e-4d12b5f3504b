<script lang="ts">
    import type {RegalMap} from '../types';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {KpExemplarPlacementsService} from '../kp-exemplar-placements.service';
    import {createEventDispatcher} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {fade} from 'svelte/transition';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let map: RegalMap;

    const localize = getLocalization();
    const dispatch = createEventDispatcher<{ 'map-remove': RegalMap, 'map-edit': RegalMap }>();
    const service = getInjector().getByToken<KpExemplarPlacementsService>(KpExemplarPlacementsService.serviceName);
    const fadeInAnimParams = {duration: 250};

    let newInformation = map.information;
    $: isInformationChanged = map.information !== newInformation;

    const handleFormSubmit = async () => {
        const editedMap = await service.updateExemplarPlacementMapInformation(map, newInformation);
        if (!exists(editedMap)) {
            return;
        }

        dispatch('map-edit', editedMap);
    }

    const handleRemoveClick = async () => {
        const successful = await service.removeExemplarPlacementMap(map);

        if (successful) {
            dispatch('map-remove', map);
        }
    }
</script>

<tr class="kp-exemplar-placement-map" in:fade={fadeInAnimParams}>
    <td>{map.id}</td>
    <td>
        <form class="edit-form" on:submit|preventDefault="{() => handleFormSubmit()}">
            <textarea name="information" rows="1" cols="50" bind:value={newInformation}></textarea>
            <KpButton buttonStyle="accent-blue-new" buttonType="submit" isDisabled="{!isInformationChanged}">
                <IconedContent icon="disk">
                    {localize(/* @kp-localization commons.Ulozit */ 'commons.Ulozit')}
                </IconedContent>
            </KpButton>
        </form>
    </td>
    <td class="map-image-container">
        {#if exists(map.map)}
            <img class="map-image"
                 alt="map"
                 src="data:image/jpeg;base64,{map.map}"/>
        {/if}
    </td>
    <td>
        <KpButton buttonStyle="danger-new" on:click={() => handleRemoveClick()}>
            <IconedContent icon="trash">
                {localize(/* @kp-localization commons.Smazat */ 'commons.Smazat')}
            </IconedContent>
        </KpButton>
    </td>
</tr>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "bootstrap-less/bootstrap/variables";

    .kp-exemplar-placement-map {
        .edit-form {
            display: flex;
            align-items: center;
            gap: @spacing-ml;
        }

        .map-image-container {
            text-align: center;

            .map-image {
                border: 1px solid @table-border-color;
                border-radius: @border-radius-small;
                width: 100px;
                height: 80px;
                object-fit: contain;
            }
        }
    }
</style>