import register from '@kpsys/angularjs-register';
import {exemplarPlacementsRoutes} from './exemplar-placements.routes';
import {ExemplarPlacementsDataService} from './exemplar-placements.data-service';
import {KpExemplarPlacementsService} from './kp-exemplar-placements.service';

export default register('portaro.features.exemplarPlacements')
    .config(exemplarPlacementsRoutes)
    .service(ExemplarPlacementsDataService.serviceName, ExemplarPlacementsDataService)
    .service(KpExemplarPlacementsService.serviceName, KpExemplarPlacementsService)
    .name();