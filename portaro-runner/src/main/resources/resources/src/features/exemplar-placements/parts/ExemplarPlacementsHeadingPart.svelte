<script lang="ts">
    import {getLocalization} from 'core/svelte-context/context';
    import {createEventDispatcher} from 'svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    const localize = getLocalization();
    const dispatch = createEventDispatcher<{'add-new': void}>();

    const handleAddMapClick = () => {
        dispatch('add-new');
    };
</script>

<div class="exemplar-placements-heading-part">
    <KpHeading type="h1">
        {localize(/* @kp-localization exemplarPlacements.Heading */ 'exemplarPlacements.Heading')}
    </KpHeading>

    <KpButton buttonStyle="success-new" on:click={handleAddMapClick}>
        <IconedContent icon="add">
            {localize(/* @kp-localization exemplarPlacements.NewMap */ 'exemplarPlacements.NewMap')}
        </IconedContent>
    </KpButton>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .exemplar-placements-heading-part {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: @spacing-ml;
        width: 100%;
        margin-bottom: 30px;
    }
</style>