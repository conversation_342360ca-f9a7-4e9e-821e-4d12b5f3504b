import type {ExemplarPlacementsData, RegalMap} from './types';
import type {ExemplarPlacementsDataService} from './exemplar-placements.data-service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';

export class KpExemplarPlacementsService {
    public static serviceName = 'kpExemplarPlacementsService';

    /*@ngInject*/
    constructor(private exemplarPlacementsDataService: ExemplarPlacementsDataService,
                private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    public async loadExemplarPlacementsPageData(): Promise<ExemplarPlacementsData | null> {
        try {
            return await this.exemplarPlacementsDataService.getPageData();
        } catch {
            return null;
        }
    }

    public async addNewExemplarPlacementMap(newMapData: FormData): Promise<RegalMap | null> {
        try {
            const actionResponse = await this.exemplarPlacementsDataService.createNewRegalMap(newMapData);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
            return actionResponse.savedObject;
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            return null;
        }
    }

    public async updateExemplarPlacementMapInformation(map: RegalMap, newInformation: string): Promise<RegalMap> {
        try {
            const actionResponse = await this.exemplarPlacementsDataService.editRegalMapInformation(map, newInformation);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
            return actionResponse.savedObject;
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            return null;
        }
    }

    public async removeExemplarPlacementMap(map: RegalMap): Promise<boolean> {
        try {
            const actionResponse = await this.exemplarPlacementsDataService.removeRegalMap(map);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
            return true;
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            return false;
        }
    }
}