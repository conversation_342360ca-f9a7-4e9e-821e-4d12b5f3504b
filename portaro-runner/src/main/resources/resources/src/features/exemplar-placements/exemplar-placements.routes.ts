import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export function exemplarPlacementsRoutes($stateProvider: StateProvider) {
    let exemplarPlacementsModule: { default: any; };
    $stateProvider
        .state({
            name: 'exemplar-placements',
            url: '/exemplar-placements',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => exemplarPlacementsModule.default,
            },
            lazyLoad: async () => {
                exemplarPlacementsModule = await import(/* webpackChunkName: "exemplarPlacements" */ './KpExemplarPlacementsPage.svelte');
                return null;
            }
        });
}