import type {ExemplarPlacementsData, RegalMap} from './types';
import type {FinishedActionResponse, FinishedSaveResponse} from 'typings/portaro.be.types';
import {ngAsync} from 'shared/utils/ng-@decorators';
import type {AjaxService} from 'core/data-services/ajax.service';

export class ExemplarPlacementsDataService {
    public static serviceName = 'exemplarPlacementsDataService';

    public static readonly EXEMPLAR_PLACEMENTS_ROUTE = 'exemplar-placements';
    public static readonly PAGE_DATA_ROUTE = 'exemplar-placements/page-data';


    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async getPageData(): Promise<ExemplarPlacementsData> {
        return this.ajaxService
            .createRequest(`${ExemplarPlacementsDataService.PAGE_DATA_ROUTE}`)
            .get();
    }

    @ngAsync()
    public async createNewRegalMap(newMapData: FormData): Promise<FinishedSaveResponse<RegalMap>> {
        return this.ajaxService
            .createRequest(`${ExemplarPlacementsDataService.EXEMPLAR_PLACEMENTS_ROUTE}`)
            .withHeaders({'Content-Type': undefined}) // remove default Content-Type and let the browser set the Content-Type for FormData
            .post(newMapData);
    }

    @ngAsync()
    public async editRegalMapInformation(map: RegalMap, newInformation: string): Promise<FinishedSaveResponse<RegalMap>> {
        return this.ajaxService
            .createRequest(`${ExemplarPlacementsDataService.EXEMPLAR_PLACEMENTS_ROUTE}/${map.id}`)
            .post({information: newInformation});
    }

    @ngAsync()
    public async removeRegalMap(map: RegalMap): Promise<FinishedActionResponse> {
        return this.ajaxService
            .createRequest(`${ExemplarPlacementsDataService.EXEMPLAR_PLACEMENTS_ROUTE}/${map.id}`)
            .delete();
    }
}