<script lang="ts">
    import type {ExemplarPlacementsData} from '../types';
    import {getLocalization} from 'core/svelte-context/context';
    import KpClassicTable from 'shared/ui-widgets/table/classic/KpClassicTable.svelte';
    import KpExemplarPlacementMap from '../components/KpExemplarPlacementMap.svelte';

    export let pageData: ExemplarPlacementsData;

    const localize = getLocalization();
</script>

<div class="exemplar-placements-table-part">
    <KpClassicTable additionalClasses="exemplar-placements-table"
                    responsive
                    colorAccented
                    columnHeadersCentered
                    horizontallyDivided
                    verticallyCentered
                    bordered
                    hoverRows>

        <tr slot="header">
            <th class="id-table-header">{localize(/* @kp-localization exemplarPlacements.MapIdentifier */ 'exemplarPlacements.MapIdentifier')} ({pageData.regalMapProperty})</th>
            <th>{localize(/* @kp-localization exemplarPlacements.MoreInformation */ 'exemplarPlacements.MoreInformation')}</th>
            <th>{localize(/* @kp-localization exemplarPlacements.Map */ 'exemplarPlacements.Map')}</th>
            <th>{localize(/* @kp-localization commons.editovat */ 'commons.editovat')}</th>
        </tr>

        <svelte:fragment slot="body">
            {#each pageData.maps as map(map.id)}
                <KpExemplarPlacementMap {map} on:map-remove on:map-edit/>
            {/each}
        </svelte:fragment>
    </KpClassicTable>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "bootstrap-less/bootstrap/variables";

    .exemplar-placements-table-part {
        width: 100%;
        margin-bottom: 30px;

        .id-table-header {
            width: 150px;
        }
    }

    :global {
        .exemplar-placements-table-part .exemplar-placements-table {
            tr > td:last-child {
                text-align: end;
            }

            tr > td:first-child {
                text-align: center;
            }
        }
    }
</style>