import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function recordExportRoutes($stateProvider: StateProvider) {
    let recordExportsModule: { default: any; };
    $stateProvider
        .state({
            name: 'record-exports',
            url: '/record-exports',
            component:  KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => recordExportsModule.default,
            },
            lazyLoad: async () => {
                recordExportsModule = await import(/* webpackChunkName: "record-exports" */ './KpRecordExportsPage.svelte');
                return null;
            }
        });
}