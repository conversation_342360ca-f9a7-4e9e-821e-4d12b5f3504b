<script lang="ts">
    import type {DataDisplayerSettings} from 'typings/portaro.fe.types';
    import {DataDisplayerOptions} from 'typings/portaro.fe.types';
    import type {Department, Fond, RecordExport, RecordOperationType, RecordStatus} from 'typings/portaro.be.types';
    import {RecordExportService} from './record-export.service';
    import {uuid} from 'shared/utils/custom-utils';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import KpDataDisplayer from 'shared/ui-widgets/table/KpDataDisplayer.svelte';
    import KpPageContainer from 'shared/layouts/containers/KpPageContainer.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    const localize = getLocalization();
    const service = getInjector().getByToken<RecordExportService>(RecordExportService.serviceName);

    let dataDisplayer: KpDataDisplayer;

    const handleEdit = async (event: CustomEvent<RecordExport>) => {
        await service.edit(event.detail);
        await dataDisplayer.loadAll();
    };

    const create = async () => {
        await service.create({id: uuid(), confirmed: false});
        await dataDisplayer.loadAll();
    };

    const settings = {
        options: [DataDisplayerOptions.Edit],
        loadAll: () => service.getAll(),
        columnsSetting: [
            {title: '#', key: 'id'},
            {title: localize( /* @kp-localization export.request.oaiSetId */ 'export.request.oaiSetId'), key: 'oaiSetId'},
            {title: localize( /* @kp-localization export.request.oaiSetName */ 'export.request.oaiSetName'), key: 'oaiSetName'},
            {
                title: 'pouze operace',
                key: 'recordOperationTypes',
                valueModifier: (value: RecordOperationType[]) => value.map((v) => v.id).join(',')
            },
            {
                title: localize( /* @kp-localization commons.Fondy */ 'commons.Fondy'),
                key: 'fonds',
                valueModifier: (value: Fond[]) => value.map((v) => v.id).join(',')
            },
            {
                title: 'pouze statusy',
                key: 'recordStatuses',
                valueModifier: (value: RecordStatus[]) => value.map((v) => v.id).join(',')
            },
            {
                title: localize( /* @kp-localization commons.Departments */ 'commons.Departments'),
                key: 'rootDepartments',
                valueModifier: (value: Department[]) => value.map((v) => v.id).join(',')
            },
            {title: 'vč. periodik', key: 'withPerio'},
            {title: 'vč. neperiodik', key: 'withNonperio'},
            {title: 'vč. bez exemplářů', key: 'withNonexemplared'},
            {title: 'vyloučené záznamy', key: 'forbiddenRecordIds'},
            {title: 'custom sql', key: 'sqlClause'}
        ]
    } as DataDisplayerSettings;
</script>

<KpPageContainer id="record-exports" additionalClasses="kp-record-exports-page">
    <Flex alignItems="center" gap="m" justifyContent="space-between">
        <KpHeading type="h1">Exporty (OAI, NCIP, ...)</KpHeading>

        <KpButton buttonStyle="success-new" on:click={create}>
            <IconedContent icon="add">
                Vytvořit nový export
            </IconedContent>
        </KpButton>
    </Flex>

    <KpDataDisplayer {settings} on:edit={handleEdit} bind:this={dataDisplayer}/>
</KpPageContainer>