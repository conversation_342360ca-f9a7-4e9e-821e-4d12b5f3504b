import type {ConfirmableRequest, RecordExport} from 'typings/portaro.be.types';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type {RecordExportDataService} from './record-export-data.service';

export class RecordExportService {
    public static serviceName = 'recordExportService';

    /*@ngInject*/
    constructor(private recordExportDataService: RecordExportDataService, private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    public async getAll(): Promise<RecordExport[]> {
        return this.recordExportDataService.getAll();
    }

    public async create(recordExportRequest: Partial<RecordExport & ConfirmableRequest>) {
        try {
            const response = await this.recordExportDataService.create(recordExportRequest);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(response)
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e)
        }
    }

    public async edit(recordExportRequest: RecordExport) {
        try {
            const response = await this.recordExportDataService.edit(recordExportRequest);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(response);
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e)
        }
    }
}