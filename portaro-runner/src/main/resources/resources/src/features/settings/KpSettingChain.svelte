<script lang="ts">
    import type {ViewableSettingChain} from 'typings/portaro.be.types';
    import type {SettingsValueEventData} from './types';
    import {onDestroy, onMount} from 'svelte';
    import {getSettingsContext} from './context';
    import {exists} from 'shared/utils/custom-utils';
    import KpSettingChainItem from './KpSettingChainItem.svelte';
    import {SETTINGS_VALUE_DELETED_EVENT, SETTINGS_OVERRIDE_EVENT} from './constants';

    export let setting: ViewableSettingChain;
    export let displaySectionLabels: boolean;

    const context = getSettingsContext();

    onMount(() => {
        context.eventBus.addEventListener(SETTINGS_OVERRIDE_EVENT, handleSettingsValueOverride);
        context.eventBus.addEventListener(SETTINGS_VALUE_DELETED_EVENT, handleSettingsValueDeleted);
    });

    onDestroy(() => {
        context.eventBus.removeEventListener(SETTINGS_OVERRIDE_EVENT, handleSettingsValueOverride);
        context.eventBus.removeEventListener(SETTINGS_VALUE_DELETED_EVENT, handleSettingsValueDeleted);
    });

    const handleSettingsValueOverride = (event: CustomEvent<SettingsValueEventData>) => {
        if (event.detail.parentSettingsId !== setting.id) {
            return;
        }

        const overridingValue = setting.chain.find((value) => !exists(value.id));
        if (exists(overridingValue)) {
            setting.chain = setting.chain.filter((value) => value.id !== overridingValue.id);
            setting = setting; // Trigger reactivity
            return;
        }

        const overrideChainItem = context.service.overrideChainItem(setting, event.detail.item);
        setting.chain.push(overrideChainItem);
        setting = setting; // Trigger reactivity
    };

    const handleSettingsValueDeleted = (event: CustomEvent<SettingsValueEventData>) => {
        if (event.detail.parentSettingsId !== setting.id) {
            return;
        }

        setting.chain = setting.chain.filter((value) => value.id !== event.detail.item.id);
        setting = setting; // Trigger reactivity
    };
</script>

<div class="kp-setting-item" title="{setting.description}" data-id="{setting.id}">
    <div class="name-container">
        {setting.name}

        {#if displaySectionLabels}
            <small class="text-muted">{setting.section}</small>
        {/if}
    </div>

    <ul class="setting-chain-list">
        {#each setting.chain as chainItem, index (chainItem.id)}
            <KpSettingChainItem {setting}
                                {chainItem}
                                overridden="{index === 0 && setting.chain.length >= 2}"
                                topMargin="{index > 0}"/>
        {/each}
    </ul>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .kp-setting-item {
        display: flex;
        flex-direction: column;
        border-radius: @border-radius-default;
        border: 1px solid @themed-border-default;

        .name-container {
            display: flex;
            flex-direction: column;
            padding: @spacing-m @spacing-ml;
            font-weight: 500;
            border-bottom: 1px solid @themed-border-default;
        }

        .setting-chain-list {
            display: flex;
            flex-direction: column;
            padding: @spacing-m @spacing-ml;
        }
    }
</style>