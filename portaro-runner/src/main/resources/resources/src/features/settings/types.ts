import type {LabeledIdentified, PortaroState, ViewableSettingValue} from 'typings/portaro.be.types';

export type SettingSection = LabeledIdentified<string>;

export interface KpSettingsData {
    serverState: PortaroState;
    settingSections: SettingSection[];
    activeSettingSection: SettingSection;
}

export interface KpSettingsUrlParams {
    q?: string;
    section?: string;
}

export interface SettingsValueEventData {
    parentSettingsId: string;
    item: ViewableSettingValue;
}