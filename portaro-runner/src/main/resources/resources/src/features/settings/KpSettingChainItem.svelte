<script lang="ts">
    import type {ViewableSettingChain, ViewableSettingValue} from 'typings/portaro.be.types';
    import type {SettingsValueEventData} from './types';
    import {exists} from 'shared/utils/custom-utils';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {slide} from 'svelte/transition';
    import {getLocalization} from 'core/svelte-context/context';
    import {getSettingsContext} from './context';
    import {onMount} from 'svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpDatatypedValue from 'shared/components/kp-datatyped/KpDatatypedValue.svelte';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import KpLoadingInline from 'shared/components/kp-loading/KpLoadingInline.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import {
        SETTINGS_CHAIN_EDITED_EVENT,
        SETTINGS_OVERRIDE_EVENT,
        SETTINGS_VALUE_DELETED_EVENT
    } from './constants';

    export let setting: ViewableSettingChain;
    export let chainItem: ViewableSettingValue;
    export let overridden: boolean;
    export let topMargin: boolean;

    const localize = getLocalization();
    const context = getSettingsContext();
    const slideAnimParams = {duration: 250};

    let htmlElement: HTMLElement;
    let value: any = chainItem.value;
    let editing = chainItem.newlyCreated === true;
    let processing = false;

    onMount(() => {
        if (chainItem.newlyCreated) {
            htmlElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'center'
            });
        }
    });

    const handleToggleEditOpenClick = () => {
        editing = !editing;
    };

    const handleCancelEdit = () => {
        if (chainItem.newlyCreated) {
            context.eventBus.dispatchEvent(new CustomEvent<SettingsValueEventData>(SETTINGS_VALUE_DELETED_EVENT, {
                detail: {
                    item: chainItem,
                    parentSettingsId: setting.id
                }
            }));
        }

        editing = false;
    };

    const handleEditorFormSubmit = () => {
        processChainChange(async () => {
            const updatedChain = await context.service.saveChainItemSettings(setting, chainItem, value);
            context.eventBus.dispatchEvent(new CustomEvent<ViewableSettingChain>(SETTINGS_CHAIN_EDITED_EVENT, {detail: updatedChain}));
        });
    };

    const handleDeleteClick = () => {
        processChainChange(async () => {
            const updatedChain = await context.service.deleteChainItemSettings(chainItem);
            context.eventBus.dispatchEvent(new CustomEvent<ViewableSettingChain>(SETTINGS_CHAIN_EDITED_EVENT, {detail: updatedChain}));
        });
    };

    const handleRedefineClick = () => {
        context.eventBus.dispatchEvent(new CustomEvent<SettingsValueEventData>(SETTINGS_OVERRIDE_EVENT, {
            detail: {
                item: chainItem,
                parentSettingsId: setting.id
            }
        }));
    };

    function processChainChange(action: () => Promise<void>) {
        handleCancelEdit();
        processing = true;
        action().then(() => processing = false);
    }
</script>

<li class="kp-setting-chain-item"
    class:top-margined={topMargin}
    class:errored-setting={exists(chainItem.errorText)}
    bind:this={htmlElement}
    transition:slide={slideAnimParams}>

    <div class="item-row">
        <div class="texts-container">
            <span class:text-muted={!chainItem.effective && !editing}
                  class:overwritten-value={!chainItem.effective && !editing}
                  class:edit-open={editing}>

                {#if chainItem.default}
                    <span class="name">Default</span>
                {/if}

                {#if exists(chainItem.department)}
                    <span class="name">{pipe(chainItem.department, loc())}</span>
                {/if}

                {#if exists(chainItem.fond)}
                    <span class="name">Fond {pipe(chainItem.fond, loc())}</span>
                {/if}

                {#if !chainItem.newlyCreated}
                    <span>: </span>

                    <KpDatatypedValue value="{chainItem.value}" datatype="{setting.datatype}"/>

                    {#if chainItem.note}
                        <small class="text-muted">({chainItem.note})</small>
                    {/if}
                {/if}
            </span>
        </div>

        {#if processing}
            <KpLoadingInline size="xs"/>
        {/if}

        {#if !chainItem.newlyCreated}
            <div class="action-buttons-container">
                {#if chainItem.editable}
                    <KpButton buttonSize="xs"
                              title="Upraví nastavení daného oddělení"
                              on:click={handleToggleEditOpenClick}>

                        <IconedContent icon="edit">Edit</IconedContent>
                    </KpButton>
                {/if}

                {#if chainItem.overridable}
                    <KpButton buttonSize="xs"
                              title="Předefinuje nastavení nastavením na tomto oddělení"
                              on:click={handleRedefineClick}>

                        <IconedContent icon="refresh">Předefinovat</IconedContent>
                    </KpButton>
                {/if}

                {#if !chainItem.default}
                    <KpButton buttonSize="xs"
                              on:click={handleDeleteClick}>

                        <IconedContent icon="trash">Smazat</IconedContent>
                    </KpButton>
                {/if}
            </div>
        {/if}
    </div>

    {#if exists(chainItem.errorText)}
        <small class="error-text">{chainItem.errorText}</small>
    {/if}

    {#if editing}
        <form class="form-vertical editor-container"
              on:submit|preventDefault={handleEditorFormSubmit}
              transition:slide={slideAnimParams}>

            <div class="form-group">
                <KpValueEditor type="{setting.editor.type}"
                               options="{setting.editor.options}"
                               validations="{setting.editor.validations}"
                               bind:model="{value}"/>
            </div>

            {#if !chainItem.newlyCreated}
                <KpButton buttonStyle="accent-blue-new" buttonType="submit">
                    <IconedContent icon="disk">Uložit změny</IconedContent>
                </KpButton>
            {:else}
                <KpButton buttonStyle="success-new" buttonType="submit">
                    <IconedContent icon="disk">Uložit novou redefinici</IconedContent>
                </KpButton>
            {/if}

            <KpButton buttonStyle="danger-new"
                      buttonType="button"
                      on:click={handleCancelEdit}>

                <IconedContent icon="cross">
                    {localize(/* @kp-localization commons.zrusit */ 'commons.zrusit')}
                </IconedContent>
            </KpButton>
        </form>
    {/if}

    {#if overridden}
        <UIcon icon="arrow-down"/>
    {/if}
</li>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.media-queries.less";

    .kp-setting-chain-item {
        display: flex;
        flex-direction: column;

        &.top-margined {
            margin-top: @spacing-s;
        }

        &.errored-setting {
            color: var(--danger-red);
            background-color: @themed-body-bg-red-highlighted;
            padding: @spacing-s @spacing-sm;
            border-radius: @border-radius-default;
        }

        .item-row {
            display: flex;
            gap: @spacing-s;
            align-items: center;

            .texts-container {
                flex: 1;
                word-break: break-all;

                span:not(.overwritten-value) .name {
                    font-weight: 500;
                }
            }

            .edit-open {
                font-weight: 500;
            }

            .action-buttons-container {
                display: flex;
                gap: 4px;
                margin-left: @spacing-s;
            }

            @media screen and (max-width: @screen-xs) {
                margin-bottom: @spacing-s;

                .action-buttons-container {
                    flex-direction: column;
                }
            }
        }

        .editor-container {
            margin: @spacing-s 0;

            .form-group {
                margin-bottom: @spacing-sm;
            }
        }
    }
</style>