import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export function settingsRoutes($stateProvider: StateProvider) {
    let settingsModule: { default: any; };
    $stateProvider
        .state({
            name: 'settings',
            url: '/settings?q&section',
            params: {q: {dynamic: true}, section: {dynamic: true}},
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => settingsModule.default,
            },
            lazyLoad: async () => {
                settingsModule = await import(/* webpackChunkName: "settings" */ './KpSettingsPage.svelte');
                return null;
            }
        });
}