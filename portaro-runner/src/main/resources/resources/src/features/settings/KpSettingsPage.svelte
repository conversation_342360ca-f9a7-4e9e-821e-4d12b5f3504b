<script lang="ts">
    import type {PortaroState, UUID, ViewableSettingChain} from 'typings/portaro.be.types';
    import type {KpSettingsUrlParams, SettingSection} from './types';
    import type {Subscription} from 'rxjs';
    import {onDestroy, onMount} from 'svelte';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {KpSettingsService} from './kp-settings.service';
    import {cleanup, exists, uuid} from 'shared/utils/custom-utils';
    import {fade} from 'svelte/transition';
    import {createSettingsContext} from './context';
    import {replaceAll, byId} from 'shared/utils/array-utils';
    import {SETTINGS_CHAIN_EDITED_EVENT} from './constants';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpActionRequestButton from 'shared/components/kp-action-request-button/KpActionRequestButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpSearchBar from 'shared/ui-widgets/search/KpSearchBar.svelte';
    import KpSettingChain from './KpSettingChain.svelte';
    import KpLoadablePageContainer from 'shared/layouts/containers/KpLoadablePageContainer.svelte';
    import KpHorizontalSeparator from 'shared/ui-widgets/separator/KpHorizontalSeparator.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    const context = createSettingsContext(getInjector().getByToken<KpSettingsService>(KpSettingsService.serviceName));
    const localize = getLocalization();
    const sectionFadeInAnimParams = {duration: 250};

    let initiallyLoaded = false;
    let loading = true;
    let sectionSettingsLoading = false;
    let showingSearchResults = false;
    let subscription: Subscription;
    let portaroState: PortaroState;
    let settingSections: SettingSection[];
    let searchHighlightedSections: string[];
    let activeSettingSection: SettingSection;
    let settings: ViewableSettingChain[];
    let settingsSectionReactivityTriggerKey: UUID;
    let currentSearchQuery = '';

    onMount(() => {
        context.eventBus.addEventListener(SETTINGS_CHAIN_EDITED_EVENT, handleSettingsChainEdited);
        // eslint-disable-next-line @typescript-eslint/no-misused-promises
        subscription = context.service.getUrlState$().subscribe(handleUrlStateChange);
    });

    onDestroy(() => {
        context.eventBus.removeEventListener(SETTINGS_CHAIN_EDITED_EVENT, handleSettingsChainEdited);

        cleanup(subscription);
    });

    const handleSettingsChainEdited = (event: CustomEvent<ViewableSettingChain>) => {
        settings = replaceAll(settings, byId(event.detail.id), event.detail);
    };

    const handleSearch = async (event: CustomEvent<string>) => {
        currentSearchQuery = event.detail;
        await loadSearchData();
        context.service.setUrlSearchParam(currentSearchQuery);
    };

    const handleSearchReset = async () => {
        currentSearchQuery = '';
        await handleLoadSettingsSection(activeSettingSection);
    };

    const handleLoadSettingsSection = async (section: SettingSection) => {
        showingSearchResults = false;
        sectionSettingsLoading = true;
        activeSettingSection = section;
        settingsSectionReactivityTriggerKey = uuid();
        searchHighlightedSections = [];
        settings = await context.service.getSettings(section.id);
        sectionSettingsLoading = false;
        context.service.setUrlSectionParam(section);
    };

    const handleUrlStateChange = async (params: KpSettingsUrlParams) => {
        if (exists(params.q) && currentSearchQuery !== params.q) {
            settings = undefined;
            sectionSettingsLoading = true;
            currentSearchQuery = params.q ?? '';

            if (!initiallyLoaded) {
                await loadSettingsData(params.section, false);
            }

            await loadSearchData();
            return;
        }

        if ((!exists(params.section) && !initiallyLoaded) || (exists(params.section) && activeSettingSection?.id !== params.section)) {
            await loadSettingsData(params.section);
        }
    };

    async function loadSearchData() {
        searchHighlightedSections = [];
        sectionSettingsLoading = true;
        showingSearchResults = true;
        settingsSectionReactivityTriggerKey = uuid();
        settings = await context.service.searchForSettings(currentSearchQuery);
        searchHighlightedSections = settings.map((value) => value.section);
        sectionSettingsLoading = false;
    }

    async function loadSettingsData(section: string | null, loadActiveSection: boolean = true) {
        const settingsData = await context.service.loadSettingsData(section);
        portaroState = settingsData.serverState;
        settingSections = settingsData.settingSections;
        activeSettingSection = settingsData.activeSettingSection;
        settingsSectionReactivityTriggerKey = uuid();
        loading = false;
        showingSearchResults = false;
        initiallyLoaded = true;
        searchHighlightedSections = [];

        if (loadActiveSection) {
            await handleLoadSettingsSection(settingsData.activeSettingSection);
        }
    }
</script>

<KpLoadablePageContainer pageClass="kp-settings">
    <div class="title-row">
        <KpHeading type="h1">Nastavení (Ini)</KpHeading>

        <KpActionRequestButton buttonStyle="danger-new"
                               buttonSize="md"
                               path="/api/cache/invalidate-all">

            <IconedContent icon="trash">
                {localize(/* @kp-localization util.smazCache */ 'util.smazCache')}
            </IconedContent>
        </KpActionRequestButton>
    </div>

    {#if loading}
        <KpLoadingBlock size="sm"/>
    {/if}

    {#if !loading}
        <div class="info-container">
            <span>
                <b>{localize(/* @kp-localization util.VerzePortara */ 'util.VerzePortara')}:</b>
                {portaroState.version}
            </span>

            <span>
                <b>{localize(/* @kp-localization util.VerzeAplikacnihoServeru */ 'util.VerzeAplikacnihoServeru')}:</b>
                {portaroState.appserverVersion}
            </span>
        </div>

        <KpHorizontalSeparator/>

        <KpSearchBar placeholder="{localize(/* @kp-localization commons.hledat */ 'commons.hledat')}"
                     on:search={handleSearch}
                     on:reset={handleSearchReset}
                     minlength="{2}"
                     searchQuery="{currentSearchQuery}"
                     enableReset/>

        <div class="setting-sections-list" in:fade={sectionFadeInAnimParams}>
            {#each settingSections as settingSection(settingSection.id)}
                <KpButton buttonSize="sm"
                          buttonStyle="{(settingSection.id === activeSettingSection.id && !showingSearchResults) || (showingSearchResults && searchHighlightedSections.includes(settingSection.id))? 'brand-orange-new' : 'default'}"
                          on:click={() => handleLoadSettingsSection(settingSection)}>

                    {settingSection.id}
                </KpButton>
            {/each}
        </div>

        <KpHorizontalSeparator/>

        {#key settingsSectionReactivityTriggerKey}
            <div class="section-items-container" in:fade={sectionFadeInAnimParams}>
                {#if !showingSearchResults}
                    <h2>{activeSettingSection.id}</h2>
                {:else}
                    <KpHeading type="h2">Výsledky pro hledání '{currentSearchQuery}'</KpHeading>
                {/if}

                {#if sectionSettingsLoading || !exists(settings)}
                    <KpLoadingBlock size="xs"/>
                {/if}

                {#if !sectionSettingsLoading && exists(settings)}
                    <div class="setting-chain-items-container" in:fade={sectionFadeInAnimParams}>
                        {#each settings as setting(setting.id)}
                            <KpSettingChain {setting} displaySectionLabels="{showingSearchResults}"/>
                        {/each}

                        {#if settings.length === 0}
                            <IconedContent icon="search" align="center" justify="center">
                                {localize( /* @kp-localization vysledky.nebylyNalezenyZadneZaznamy */ 'vysledky.nebylyNalezenyZadneZaznamy')}
                            </IconedContent>
                        {/if}
                    </div>
                {/if}
            </div>
        {/key}
    {/if}
</KpLoadablePageContainer>

<style lang="less">
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.variables.less";

    .title-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .info-container {
        display: flex;
        flex-direction: column;
    }

    .setting-sections-list {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
    }

    .section-items-container {
        display: flex;
        flex-direction: column;
        gap: @spacing-ml;

        .setting-chain-items-container {
            display: flex;
            flex-direction: column;
            gap: @spacing-m;
        }
    }
</style>