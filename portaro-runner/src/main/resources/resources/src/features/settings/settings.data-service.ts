import type {FinishedSaveResponse, TransferableSettingsValue, ViewableSettingChain} from 'typings/portaro.be.types';
import type {SettingSection} from './types';
import {ngAsync} from 'shared/utils/ng-@decorators';
import type {AjaxService} from 'core/data-services/ajax.service';

export class SettingsDataService {
    public static readonly serviceName = 'settingsDataService';

    private static readonly SETTINGS_ROUTE = 'settings';
    private static readonly SETTINGS_SECTIONS_ROUTE = 'settings/sections';
    private static readonly SETTINGS_QUERY_ROUTE = 'settings/query';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async getSettingsByIds(settingIds: string[] = []): Promise<ViewableSettingChain[]> {
        return this.ajaxService
            .createRequest(SettingsDataService.SETTINGS_ROUTE)
            .get({setting: settingIds});
    }

    @ngAsync()
    public async getSettings(sectionId: string): Promise<ViewableSettingChain[]> {
        return this.ajaxService
            .createRequest(SettingsDataService.SETTINGS_ROUTE)
            .get({section: sectionId});
    }

    @ngAsync()
    public async querySettings(query: string): Promise<ViewableSettingChain[]> {
        return this.ajaxService
            .createRequest(SettingsDataService.SETTINGS_QUERY_ROUTE)
            .get({q: query});
    }

    @ngAsync()
    public async saveSettings(transferableSettings: TransferableSettingsValue): Promise<FinishedSaveResponse<ViewableSettingChain>> {
        return this.ajaxService
            .createRequest(SettingsDataService.SETTINGS_ROUTE)
            .post(transferableSettings);
    }

    @ngAsync()
    public async deleteSettings(settingsId: string): Promise<FinishedSaveResponse<ViewableSettingChain>> {
        return this.ajaxService
            .createRequest(`${SettingsDataService.SETTINGS_ROUTE}/${settingsId}`)
            .delete();
    }

    @ngAsync()
    public async getAllSettingSections(): Promise<SettingSection[]> {
        return this.ajaxService
            .createRequest(SettingsDataService.SETTINGS_SECTIONS_ROUTE)
            .get();
    }
}