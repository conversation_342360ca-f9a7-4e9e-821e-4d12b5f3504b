import type {KpSettingsService} from './kp-settings.service';
import {getContext, setContext} from 'svelte';

const settingsContextKey = 'kp-settings-ctx';

export interface KpSettingsContext {
    eventBus: EventTarget;
    service: KpSettingsService;
}

export function createSettingsContext(service: KpSettingsService): KpSettingsContext {
    const eventBus = new EventTarget();

    return setContext<KpSettingsContext>(settingsContextKey, {
        eventBus,
        service
    });
}

export function getSettingsContext(): KpSettingsContext {
    return getContext<KpSettingsContext>(settingsContextKey);
}