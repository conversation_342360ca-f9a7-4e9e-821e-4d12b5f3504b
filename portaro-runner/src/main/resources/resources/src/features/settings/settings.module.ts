import register from '@kpsys/angularjs-register';
import {SettingsDataService} from './settings.data-service';
import {KpSettingsService} from './kp-settings.service';
import {settingsRoutes} from './settings.routes';

export default register('portaro.features.settings')
    .config(settingsRoutes)
    .service(SettingsDataService.serviceName, SettingsDataService)
    .service(KpSettingsService.serviceName, KpSettingsService)
    .name();