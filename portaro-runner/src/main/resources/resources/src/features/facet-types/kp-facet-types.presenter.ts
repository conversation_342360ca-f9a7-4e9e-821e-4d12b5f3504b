import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type {FacetTypeDataService} from './facet-type.data-service';
import type {FacetType} from 'typings/portaro.be.types';

export class KpFacetTypesPresenter {
    public static presenterName = 'kpFacetTypesPresenter';

    /*@ngInject*/
    constructor(private facetTypeDataService: FacetTypeDataService, private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    public async getAll(): Promise<FacetType[]> {
        return this.facetTypeDataService.getAll();
    }

    public async create() {
        try {
            const response = await this.facetTypeDataService.createFacetType();
            await this.finishedResponseInteractionService.showSuccessResponseInModalWindow(response)
        } catch (e) {
            await this.finishedResponseInteractionService.showFailedInModalWindow(e)
        }
    }

    public async edit(facetType: FacetType) {
        try {
            const response = await this.facetTypeDataService.editFacetType(facetType)
            await this.finishedResponseInteractionService.showSuccessResponseInModalWindow(response)
        } catch (e) {
            await this.finishedResponseInteractionService.showFailedInModalWindow(e)
        }
    }
}