<script lang="ts">
    import type {FacetType} from 'typings/portaro.be.types';
    import {onMount} from 'svelte';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {KpFacetTypesPresenter} from './kp-facet-types.presenter';
    import KpPageContainer from 'shared/layouts/containers/KpPageContainer.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpBarebonesTable from 'shared/ui-widgets/table/barebones/KpBarebonesTable.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    const localize = getLocalization();
    const presenter = getInjector().getByToken<KpFacetTypesPresenter>(KpFacetTypesPresenter.presenterName);

    let facetTypes = [] as FacetType[];

    onMount(async () => {
        facetTypes = await presenter.getAll();
    });

    async function create() {
        await presenter.create();
        facetTypes = await presenter.getAll();
    }

    async function edit(facetType: FacetType) {
        await presenter.edit(facetType);
        facetTypes = await presenter.getAll();
    }
</script>

<KpPageContainer id="facet" additionalClasses="kp-facet-types-page">
    <Flex alignItems="center" gap="m" justifyContent="space-between">
        <KpHeading type="h1">
            {localize(/* @kp-localization util.EditaceRezu */ 'util.EditaceRezu')}
        </KpHeading>

        <KpButton buttonStyle="success-new" on:click={() => create()}>
            <IconedContent icon="add">
                {localize(/* @kp-localization commons.Novy */ 'commons.Novy')}
            </IconedContent>
        </KpButton>
    </Flex>

    <KpBarebonesTable headerFooterDivided rowsTopBordered colorAccented responsive>
        <tr slot="header">
            <th>#</th>
            <th>{localize(/* @kp-localization commons.nazev */ 'commons.nazev')}</th>
            <th>{localize(/* @kp-localization commons.Poradi */ 'commons.Poradi')}</th>
            <th>Rozsah</th>
            <th>{localize(/* @kp-localization util.TypDefinice */ 'util.TypDefinice')}</th>
            <th>{localize(/* @kp-localization util.Definice */ 'util.Definice')}</th>
            <th>{localize(/* @kp-localization util.ExemplarovyTyp */ 'util.ExemplarovyTyp')}</th>
            <th>{localize(/* @kp-localization commons.Zapnuto */ 'commons.Zapnuto')}</th>
            <th>Dat.typ</th>
            <th>Řazení</th>
            <th>{localize(/* @kp-localization commons.editovat */ 'commons.editovat')}</th>
        </tr>

        <svelte:fragment slot="body">
            {#each facetTypes as facetType (facetType.id)}
                <tr>
                    <td>{facetType.id}</td>
                    <td>{facetType.name}</td>
                    <td>{facetType.order}</td>
                    <td>{pipe(facetType.scope, loc())}</td>
                    <td>{pipe(facetType.definitionType, loc())}</td>
                    <td>{facetType.definition ?? ''}</td>
                    <td>
                        {#if facetType.exemplarType}
                            <UIcon icon="check" color="var(--success-green)"/>
                        {/if}
                    </td>
                    <td>
                        {#if facetType.enabled}
                            <UIcon icon="check" color="var(--success-green)"/>
                        {/if}
                    </td>
                    <td>{pipe(facetType.datatype, loc())}</td>
                    <td>{pipe(facetType.sorting, loc())}</td>
                    <td>
                        <KpButton buttonSize="xs" on:click={() => edit(facetType)}>
                            <IconedContent icon="edit">
                                {localize(/* @kp-localization commons.edit */ 'commons.edit')}
                            </IconedContent>
                        </KpButton>
                    </td>
                </tr>
            {/each}
        </svelte:fragment>
    </KpBarebonesTable>
</KpPageContainer>