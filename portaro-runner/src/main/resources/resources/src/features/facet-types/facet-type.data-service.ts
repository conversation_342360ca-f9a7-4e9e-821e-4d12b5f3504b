import {ngAsync} from 'shared/utils/ng-@decorators';
import {transferify} from 'shared/utils/data-service-utils';
import type {ActionResponse, FacetType} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';

export class FacetTypeDataService {
    public static serviceName = 'facetTypeDataService';

    public static readonly ROUTE = 'facet-types';
    public static readonly CREATE_ROUTE = 'facet-types/create';
    public static readonly EDIT_ROUTE = 'facet-types/edit';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async getAll(): Promise<FacetType[]> {
        return this.ajaxService
            .createRequest(`${FacetTypeDataService.ROUTE}`)
            .get();
    }

    @ngAsync()
    public async editFacetType(facetType: FacetType): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${FacetTypeDataService.EDIT_ROUTE}`)
            .post(transferify({...facetType, confirmed: false}));
    }

    @ngAsync()
    public async createFacetType(): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${FacetTypeDataService.CREATE_ROUTE}`)
            .post({});
    }
}