import register from '@kpsys/angularjs-register';
import facetTypesRoutes from './facet-types.routes';
import {KpFacetTypesPresenter} from './kp-facet-types.presenter';
import {FacetTypeDataService} from './facet-type.data-service';

export default register('portaro.features.facet-types')
    .config(facetTypesRoutes)
    .service(FacetTypeDataService.serviceName, FacetTypeDataService)
    .service(KpFacetTypesPresenter.presenterName, KpFacetTypesPresenter)
    .name();