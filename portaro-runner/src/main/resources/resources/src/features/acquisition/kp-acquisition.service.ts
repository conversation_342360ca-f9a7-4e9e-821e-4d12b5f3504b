import type {ActionResponse, Auth} from 'typings/portaro.be.types';
import type {RecordDemand} from './types';
import type {AjaxService} from 'core/data-services/ajax.service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import {transferify} from 'shared/utils/data-service-utils';

export class KpAcquisitionService {
    public static serviceName = 'kpAcquisitionService';

    private static RECORD_DEMANDS_ROUTE = 'record-demands';

    /*@ngInject*/
    constructor(private currentAuth: Auth,
                private ajaxService: AjaxService,
                private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    public async createRecordDemand() {
        try {
            const response = await this.saveRecordDemand({
                id: null,
                record: null,
                creator: this.currentAuth.activeUser,
                createDate: new Date(),
                department: null
            });

            return this.finishedResponseInteractionService.showSuccessResponseInModalWindow(response);
        } catch (error) {
            return this.finishedResponseInteractionService.showFailedResponseInModalWindow(error);
        }
    }

    private async saveRecordDemand(recordDemand: RecordDemand): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(KpAcquisitionService.RECORD_DEMANDS_ROUTE)
            .post(transferify(recordDemand));
    }
}