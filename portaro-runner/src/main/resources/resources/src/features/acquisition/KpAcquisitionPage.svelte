<script lang="ts">
    import {getInjector} from 'core/svelte-context/context';
    import {KpAcquisitionService} from './kp-acquisition.service';
    import {Kind, SearchType} from 'shared/constants/portaro.constants';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpSearchPageContainerWithContextActions from 'src/features/search/search-page-container-with-context-actions/KpSearchPageContainerWithContextActions.svelte';

    const service = getInjector().getByToken<KpAcquisitionService>(KpAcquisitionService.serviceName);
    const staticParams = {type: SearchType.TYPE_RECORD_REQUEST_SEARCH, kind: [Kind.KIND_RECORD_REQUEST]};
</script>

<KpSearchPageContainerWithContextActions {staticParams} pageClass="kp-acquisition-page">
    <svelte:fragment slot="context-actions">
        <KpButton on:click={() => service.createRecordDemand()}>Nakoupit knížku</KpButton>
    </svelte:fragment>
</KpSearchPageContainerWithContextActions>