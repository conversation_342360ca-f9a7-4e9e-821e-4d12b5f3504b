import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function acquisitionRoutes($stateProvider: StateProvider) {
    let acquisitionModule: { default: any; };
    $stateProvider
        .state({
            name: 'acquisition',
            url: '/acquisition',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => acquisitionModule.default,
            },
            lazyLoad: async () => {
                acquisitionModule = await import(/* webpackChunkName: "acquisition" */ './KpAcquisitionPage.svelte');
                return null;
            }
        });
}
