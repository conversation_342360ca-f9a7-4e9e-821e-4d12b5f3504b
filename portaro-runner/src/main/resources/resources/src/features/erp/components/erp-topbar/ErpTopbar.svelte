<script lang="ts">
    import type {Auth} from 'typings/portaro.be.types';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {onDestroy} from 'svelte';
    import {cleanup} from 'shared/utils/custom-utils';
    import {ErpTopbarService} from './erp-topbar.service';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import KpGlobalSearchInput from 'shared/global-search/KpGlobalSearchInput.svelte';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';
    import KpDropdownMenuItem from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuItem.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpDropdownMenuWrapper from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuWrapper.svelte';
    import KpThreadsPopover from 'src/features/threads/popover/KpThreadsPopover.svelte';
    import rainbowSearchLogo from '../../assets/rainbow-search-logo.svg?assets';
    import DepartmentSelector from 'src/features/erp/components/erp-topbar/DepartmentSelector.svelte';

    const localize = getLocalization();
    const service = getInjector().getByClass(ErpTopbarService);

    let currentAuth: Auth;
    const currentAuthSubscription = service.getCurrentAuth$().subscribe((auth) => currentAuth = auth);

    onDestroy(() => {
        cleanup(currentAuthSubscription);
    });
</script>

<div class="erp-topbar">
    <div class="content-left">
        {#if currentAuth.evided}
            <KpGlobalSearchInput inputId="erp-topbar-search-input"
                                 additionalInputClasses="form-control erp-topbar-search-input"
                                 placeholder="{localize(/* @kp-localization hledani.hledatKnihyAutoryTemata */ 'hledani.hledatKnihyAutoryTemata')}"
                                 additionalButtonClasses="erp-topbar-search-button">

                <img src={rainbowSearchLogo} alt="Search logo"/>
            </KpGlobalSearchInput>
        {/if}
    </div>

    <div class="erp-logo">
        <img src="/erp/assets/text-logo.svg" class="erp-logo-img" alt="Sutin logo"/>
        <DepartmentSelector/>
    </div>

    <div class="content-right">
        {#if currentAuth.evided}
            <KpThreadsPopover/>

            <KpDropdownMenuWrapper noHorizontalPadding>
                <div slot="button" class="user-container">
                    <KpUserAvatar userRecordId="{currentAuth.activeUser.rid}" sizePx="{30}"/>

                    <div class="texts-container">
                        <small class="text-muted">Přihlášený jako</small>
                        <span class="username">{pipe(currentAuth.activeUser, loc())}</span>
                    </div>
                </div>

                <svelte:fragment slot="menu">
                    <KpDropdownMenuItem href="/#!/users/{currentAuth.activeUser.id}">
                        <IconedContent icon="user">Uživatelský profil</IconedContent>
                    </KpDropdownMenuItem>

                    <KpDropdownMenuItem on:click={() => service.logout()}>
                        <IconedContent icon="sign-out-alt">Odhlásit se</IconedContent>
                    </KpDropdownMenuItem>
                </svelte:fragment>
            </KpDropdownMenuWrapper>
        {/if}

        {#if !currentAuth.evided}
            <KpIconButton icon="sign-in-alt"
                          tooltipLabel="Přihlásit se"
                          buttonSize="md"
                          on:click={() => service.login()}/>
        {/if}
    </div>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro-erp.less";

    @search-height: 34px;

    .erp-topbar {
        position: relative;
        display: flex;
        width: 100%;
        align-items: center;
        height: @erp-topbar-height;
        padding-left: @spacing-xxl;
        padding-right: @spacing-xxl;
        animation: 0.4s ease-in-out 0s 1 topbar-slide-from-top;

        .content-right,
        .content-left {
            display: flex;
            height: 100%;
            flex: 1;
            gap: @spacing-ml;
            align-items: center;
        }

        .erp-logo {
            display: flex;
            align-items: center;
            gap: @spacing-m;
            position: absolute;
            left: 50%;
            top: calc(@erp-topbar-height / 2);
            transform: translate(-50%, -50%);
            animation: 0.4s ease-in-out 0s 1 logo-fade-in;

            .erp-logo-img {
                height: 10px;
            }
        }

        .content-left {
            justify-content: start;
        }

        .content-right {
            justify-content: end;

            .user-container {
                display: flex;
                align-items: center;
                gap: @spacing-sm;

                .texts-container {
                    display: flex;
                    flex-direction: column;
                    align-items: start;

                    span, small {
                        line-height: 1.15;
                    }
                }
            }
        }
    }

    :global {
        .erp-topbar {
            .kp-global-search-input {
                width: 250px;

                &:focus-within {
                    .rainbow-effect(0);
                }

                .erp-topbar-search-input {
                    height: @search-height;
                    border-right: none;
                    box-shadow: none;
                    border-radius: @border-radius-large 0 0 @border-radius-large;

                    &:focus {
                        border-color: @themed-border-default !important;
                    }
                }

                .erp-topbar-search-button {
                    height: @search-height;
                    width: calc(@search-height + @spacing-s);
                    font-size: @font-size-large;
                    display: flex;
                    align-items: center;
                    background-color: @themed-body-bg;
                    color: @themed-text-default;
                    border: 1px solid @themed-border-default;
                    border-left: none;
                    justify-content: center;
                    border-radius: 0 @border-radius-large @border-radius-large 0;
                }
            }
        }
    }

    @keyframes topbar-slide-from-top {
        0% {
            transform: translateY(-25%);
            opacity: 0;
        }
        100% {
            transform: translateY(0);
            opacity: 100%;
        }
    }

    @keyframes logo-fade-in {
        0% {
            opacity: 0;
        }
        100% {
            opacity: 100%;
        }
    }
</style>