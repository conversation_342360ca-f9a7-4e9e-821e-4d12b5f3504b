<script lang="ts">
    import type {HistoricalPlaceMapMarker, PlaceMapMarkerComponentProps} from '../types';
    import type {SvelteComponent} from 'svelte';
    import {Loader} from '@googlemaps/js-api-loader'
    import {onDestroy, onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {fade} from 'svelte/transition';
    import {getLogger} from 'core/svelte-context/context';
    import {createFloatingActions} from 'svelte-floating-ui';
    import {floatingUiCommonSettings} from 'shared/utils/floating-ui-common-settings';
    import {MarkerClusterer} from '@googlemaps/markerclusterer';
    import {HISTORICAL_PLACE_INFO_EVENT, mapOptions} from '../constants';
    import GroupedPlaceMapMarker from './GroupedPlaceMapMarker.svelte';

    export let markers: HistoricalPlaceMapMarker[];

    const logger = getLogger();
    const eventTarget = new EventTarget();
    const [floatingRef, floatingContent] = createFloatingActions(floatingUiCommonSettings('bottom'));

    const googleMapsLoader = new Loader({
        apiKey: 'AIzaSyB_SbxB0hm1rR0PhAvYlOEwgrx5_McEwlE',
        version: 'weekly'
    });

    let loadError = false;
    let map: google.maps.Map;
    let mapContainer: HTMLDivElement;
    let shownHistoricalPlaces: HistoricalPlaceMapMarker[] | null = null;

    const markerComponents: SvelteComponent[] = [];

    onMount(async () => {
        eventTarget.addEventListener(HISTORICAL_PLACE_INFO_EVENT, handleHistoricalPlaceInfo);

        try {
            await initializeGoogleMaps();
        } catch (e) {
            logger.error(e)
            loadError = true;
        }
    });

    onDestroy(() => {
        eventTarget.removeEventListener(HISTORICAL_PLACE_INFO_EVENT, handleHistoricalPlaceInfo);

        markerComponents.forEach((component) => {
            component.$destroy();
        });

        googleMapsLoader.deleteScript();
    });

    async function initializeGoogleMaps() {
        await googleMapsLoader.importLibrary('maps');
        await googleMapsLoader.importLibrary('marker');

        map = new google.maps.Map(mapContainer, mapOptions);

        const mapsMarkers: google.maps.marker.AdvancedMarkerElement[] = [];
        const groupedMarkers = groupMarkersBySameCoordinates(markers);

        Object.keys(groupedMarkers).forEach((coordinates) => {
            const coordinateMarkers = groupedMarkers[coordinates] ?? [];

            if (coordinateMarkers.length > 0) {
                const markerTitle = coordinateMarkers.length === 1 ? coordinateMarkers[0].title : `${coordinateMarkers.length} ${coordinateMarkers.length >= 5 ? 'knihoven' : 'knihovny'}`;
                const markerPosition = coordinateMarkers[0].position;

                const advancedMarker = new google.maps.marker.AdvancedMarkerElement({
                    map,
                    position: markerPosition,
                    title: markerTitle
                });

                mapsMarkers.push(advancedMarker);
                advancedMarker.content = createMarkerElement({
                    groupedMarkers: coordinateMarkers,
                    advancedMarker,
                    floatingRef,
                    eventTarget,
                    markerTitle
                });
            }
        });

        new MarkerClusterer({map, markers: mapsMarkers});
    }

    const handleHistoricalPlaceInfo = (event: CustomEvent<HistoricalPlaceMapMarker[] | null>) => {
        shownHistoricalPlaces = event.detail;
    }

    function createMarkerElement(props: PlaceMapMarkerComponentProps): HTMLElement {
        const target = document.createElement('div');
        markerComponents.push(new GroupedPlaceMapMarker({target, props}));

        return target;
    }

    function groupMarkersBySameCoordinates(allMarkers: HistoricalPlaceMapMarker[]): Record<string, HistoricalPlaceMapMarker[]> {
        return allMarkers.reduce((acc, marker) => {
            const key = `${marker.position.lat},${marker.position.lng}`;
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(marker);
            return acc;
        }, {});
    }
</script>

{#if !loadError}
    <div class="places-map" bind:this={mapContainer}></div>
{:else}
    <div class="text-center">Nastala chyba při inicializaci Google Maps!</div>
{/if}

{#if exists(shownHistoricalPlaces)}
    <ul class="historical-places-info"
        in:fade={{duration: 250}}
        use:floatingContent>

        {#each shownHistoricalPlaces as historicalPlace}
            <li class="historical-place">
                <a href="{historicalPlace.recordUrl}" class="place-title">{historicalPlace.title}</a>

                {#if exists(historicalPlace.fromYear) && exists(historicalPlace.toYear)}
                    <span class="text-muted">Mezi lety {historicalPlace.fromYear} - {historicalPlace.toYear}</span>
                {:else if exists(historicalPlace.fromYear)}
                    <span class="text-muted">Od roku {historicalPlace.fromYear}</span>
                {:else if exists(historicalPlace.toYear)}
                    <span class="text-muted">Do roku {historicalPlace.toYear}</span>
                {/if}
            </li>
        {/each}
    </ul>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.media-queries.less";

    .places-map {
        width: 100%;
        height: 60dvh;
        background-color: #F5F5F5;

        @media (max-width: @screen-sm) {
            height: 45dvh;
        }
    }

    .historical-places-info {
        position: fixed;
        top: -200px;
        left: -200px;
        z-index: 100;
        width: 160px;
        background-color: @themed-body-bg;
        border-radius: @border-radius-default;
        border: 1px solid @themed-border-default;
        display: flex;
        flex-direction: column;
        font-size: @font-size-small;

        .historical-place {
            display: flex;
            flex-direction: column;
            gap: @spacing-s;
            padding: @spacing-sm @spacing-m;
            border-bottom: 1px solid @themed-border-default;

            &:last-child {
                border-bottom: none;
            }

            .place-title {
                font-size: @font-size-default;
                font-weight: 500;
                text-decoration: underline;
            }
        }
    }
</style>