import register from '@kpsys/angularjs-register';
import {historicalPlacesRoutes} from './historical-places.routes';
import {HistoricalPlacesDataService} from './historical-places.data-service';
import {KpHistoricalPlacesService} from './kp-historical-places.service';

export default register('portaro.features.historicalPlaces')
    .config(historicalPlacesRoutes)
    .service(HistoricalPlacesDataService.serviceName, HistoricalPlacesDataService)
    .service(KpHistoricalPlacesService.serviceName, KpHistoricalPlacesService)
    .name();