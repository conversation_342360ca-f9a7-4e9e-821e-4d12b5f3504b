import type {HistoricalPlace, HistoricalPlacesRequest} from './types';
import {ngAsync} from 'shared/utils/ng-@decorators';
import {transferify} from 'shared/utils/data-service-utils';
import type {AjaxService} from 'core/data-services/ajax.service';

export class HistoricalPlacesDataService {
    public static serviceName = 'historicalPlacesDataService';

    private static readonly ROUTE = 'places-history';
    private static readonly FORM_ROUTE = 'places-history/form';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async query(query: HistoricalPlacesRequest = {}): Promise<HistoricalPlace[]> {
        return this.ajaxService
            .createRequest(`${HistoricalPlacesDataService.ROUTE}`)
            .post(transferify(query));
    }

    @ngAsync()
    public async getFormSettings(): Promise<any> {
        return this.ajaxService
            .createRequest(`${HistoricalPlacesDataService.FORM_ROUTE}`)
            .get();
    }
}