<script lang="ts">
    import type {HistoricalPlaceMapMarker} from '../types';
    import {exists, isTouchDevice} from 'shared/utils/custom-utils';
    import {clickOutside} from 'shared/svelte-actions/use.click-outside';
    import {onDestroy, onMount} from 'svelte';
    import {HISTORICAL_PLACE_INFO_EVENT} from '../constants';

    export let advancedMarker: google.maps.marker.AdvancedMarkerElement;
    export let groupedMarkers: HistoricalPlaceMapMarker[];
    export let eventTarget: EventTarget;
    export let markerTitle: string;
    export let floatingRef: any;

    const touchDevice = isTouchDevice();

    let hoveredOver = false;
    let infoClickedVisible = false;
    let clickListener: google.maps.MapsEventListener;

    $: displayingInfo = hoveredOver || infoClickedVisible;
    $: iconUrl = groupedMarkers.length === 1 ? groupedMarkers[0].iconUrl : '/resources/ext/proveniences-map/marker_cluster_1.png';
    $: eventTarget.dispatchEvent(new CustomEvent<HistoricalPlaceMapMarker[] | null>(HISTORICAL_PLACE_INFO_EVENT, {detail: displayingInfo ? groupedMarkers : null}));
    $: isProfessionalType = groupedMarkers.some((value) => value.isProfessionalType)

    onMount(() => {
        clickListener = advancedMarker.addListener('click', handleMarkerClick);
    });

    onDestroy(() => {
        if (exists(clickListener)) {
            google.maps.event.removeListener(clickListener);
        }
    });

    const handleMarkerClick = () => {
        infoClickedVisible = !infoClickedVisible;
    }

    const handleMouseEnter = () => {
        if (!touchDevice) {
            hoveredOver = true;
        }
    }

    const handleMouseLeave = () => {
        hoveredOver = false;
    }

    const handleClickOutside = () => {
        infoClickedVisible = false;
    }
</script>

<div class="historical-place-map-marker"
     role="application"
     on:mouseenter={handleMouseEnter}
     on:mouseleave={handleMouseLeave}
     use:clickOutside
     on:click-outside={handleClickOutside}>

    <img class="marker-img"
         class:info-shown={displayingInfo}
         class:red-marker={isProfessionalType}
         src="{iconUrl}"
         alt="{markerTitle}"/>

    {#if displayingInfo}
        <div use:floatingRef></div>
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .historical-place-map-marker {
        color: inherit;

        &:hover {
            text-decoration: none;
        }

        .marker-img {
            width: 42px;
            height: 42px;
            outline-offset: 2px;
            border-radius: @border-radius-default;
            outline: 2px solid transparent;
            transition: outline-color 0.15s ease-in-out;

            &.info-shown {
                outline-color: var(--accent-blue-new);
            }
            &.red-marker {
                filter: brightness(0) saturate(100%) invert(14%) sepia(100%) saturate(7470%) hue-rotate(0deg) brightness(98%) contrast(101%)
            }
        }
    }
</style>