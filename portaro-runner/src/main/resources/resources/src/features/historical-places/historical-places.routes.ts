import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export function historicalPlacesRoutes($stateProvider: StateProvider) {
    let historicalPlacesModule: { default: any; };
    $stateProvider
        .state({
            name: 'places-history',
            url: '/places-history',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => historicalPlacesModule.default,
            },
            lazyLoad: async () => {
                historicalPlacesModule = await import(/* webpackChunkName: "historicalPlaces" */ './KpHistoricalPlaces.svelte');
                return null;
            }
        });
}