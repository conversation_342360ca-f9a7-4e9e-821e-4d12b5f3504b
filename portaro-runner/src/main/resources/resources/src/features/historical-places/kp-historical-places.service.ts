import type {HistoricalPlace, HistoricalPlaceMapMarker, HistoricalPlacesRequest} from './types';
import type {HistoricalPlacesDataService} from './historical-places.data-service';
import {provenienceTypeToStringMap} from './constants';

export class KpHistoricalPlacesService {
    public static serviceName = 'kpHistoricalPlacesService';

    /*@ngInject*/
    constructor(private historicalPlacesDataService: HistoricalPlacesDataService) {
    }

    public async getSearchFormSettings(): Promise<any> {
        return this.historicalPlacesDataService.getFormSettings();
    }

    public async getHistoricalPlaces(query?: HistoricalPlacesRequest): Promise<HistoricalPlace[] | null> {
        try {
            return await this.historicalPlacesDataService.query(query);
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (e) {
            return null;
        }
    }

    public createMapMarker(place: HistoricalPlace): HistoricalPlaceMapMarker {
        return {
            id: place.id,
            position: place.coordinates,
            title: place.name,
            iconUrl: `/resources/ext/proveniences-map/${provenienceTypeToStringMap[place.type.id]}.png`,
            recordUrl: `/#!/records/${place.recordId}`,
            fromYear: place.fromYear,
            toYear: place.toYear,
            isProfessionalType: place.professionalType.length > 0
        }
    }
}