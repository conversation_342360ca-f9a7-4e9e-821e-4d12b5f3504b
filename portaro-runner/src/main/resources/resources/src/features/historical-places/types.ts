import type {Identified, LabeledIdentified} from 'typings/portaro.be.types';

export interface HistoricalPlacesRequest extends Record<string, any> {
    name?: string;
    place?: string;
    type?: ProvenienceType;
    yearRange?: {
        from: number,
        to: number
    };
    profession?: string;
    sex?: string;
}

export interface HistoricalPlace extends LabeledIdentified<string> {
    recordId: string;
    name: string;
    type: ProvenienceType;
    professionalType: ProfessionalType[];
    fromYear: number;
    toYear: number;
    coordinates: Coordinates;
    place: string;
    profession: string;
    sex: string;
}

export interface Coordinates {
    lat: number;
    lng: number;
}

export interface HistoricalPlaceMapMarker extends Identified<string> {
    position: Coordinates;
    title: string;
    iconUrl: string;
    recordUrl: string;
    fromYear: number;
    toYear: number;
    isProfessionalType: boolean;
}

export interface PlaceMapMarkerComponentProps {
    advancedMarker: google.maps.marker.AdvancedMarkerElement;
    groupedMarkers: HistoricalPlaceMapMarker[];
    eventTarget: EventTarget;
    markerTitle: string;
    floatingRef: any;
}

export type ProvenienceType = LabeledIdentified<string>;
export type ProfessionalType = LabeledIdentified<string>;