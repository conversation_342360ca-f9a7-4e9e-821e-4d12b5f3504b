export const provenienceTypeToStringMap: Record<string, string> = {
    'db34514b-998c-4dab-9787-b585fb55ac4e': 'marker_knihovna_jina',
    'b40a36a3-d2f1-4e6d-9872-4d8b586c22d1': 'marker_knihovna_slechticka',
    '90a16232-0c84-4a5f-ac82-d45300c20be5': 'marker_knihovna_mestanska',
    'a6b88df3-eaad-40ff-8e96-0619d3f92fe9': 'marker_knihovna_cirkevni'
};

export const mapOptions = {
    mapId: 'provenio-historical-places-map',
    center: {lat: 50.087743, lng: 14.478982},
    zoom: 8
}

export const HISTORICAL_PLACE_INFO_EVENT = 'historical-place-info-event';