<script lang="ts">
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    export let heading: string;
    export let printButtonLabel: string;
    export let printDisabled = false;
</script>

<Flex class="optimisation-overview-heading" justifyContent="space-between" alignItems="flex-end">
    <span class="overview-heading">{heading}</span>

    <KpButton isDisabled={printDisabled} buttonStyle="accent-blue-new" on:click>
        <IconedContent icon="print">{printButtonLabel}</IconedContent>
    </KpButton>
</Flex>

<style>
    .overview-heading {
        font-weight: 500;
    }
</style>