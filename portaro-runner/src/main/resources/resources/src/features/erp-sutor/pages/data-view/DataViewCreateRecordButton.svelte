<script lang="ts">
    import type {Fond, Rec} from 'typings/portaro.be.types';
    import type {FondPermissions} from 'src/features/erp/services/fond-permissions.data-service';
    import type {ButtonStyle, ComponentSize} from 'shared/ui-widgets/types';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {getInjector} from 'core/svelte-context/context';
    import {ErpFondPermissionsContextService} from 'src/features/erp/services/erp-fond-permissions-context.service';
    import {createEventDispatcher, onDestroy, onMount} from 'svelte';
    import {cleanup, exists} from 'shared/utils/custom-utils';
    import {Subkind} from 'shared/constants/portaro.constants';
    import {RecordGridDataService} from 'src/features/record-grid/services/record-grid.data-service';
    import RecordService from 'src/features/record/record.service';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpDropdownMenuButton from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuButton.svelte';
    import KpDropdownMenuItem from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuItem.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';

    export let rootFondId: number;
    export let buttonLabel: string;
    export let buttonStyle: ButtonStyle = 'success-new';
    export let buttonSize: ComponentSize = 'md';
    export let isBlock = false;

    const fondPermissionsService = getInjector().getByClass(ErpFondPermissionsContextService);
    const recordGridDataService = getInjector().getByClass(RecordGridDataService);
    const recordService = getInjector().getByClass(RecordService);
    const dispatch = createEventDispatcher<{'record-created': Rec}>();

    let creatableFonds: Fond[];
    const fondPermissionsSubscription = fondPermissionsService.fondPermissions$.subscribe((fondPermissions) => {
        loadCreatableFonds(fondPermissions);
    });

    onMount(async () => {
        const fondPermissions = fondPermissionsService.getFondPermissionsValue();
        await loadCreatableFonds(fondPermissions);
    });

    onDestroy(() => {
        cleanup(fondPermissionsSubscription);
    });

    async function loadCreatableFonds(fondPermissions: FondPermissions) {
        const loadedCompatibleFonds = await recordGridDataService.loadInsertableFonds(rootFondId);

        creatableFonds = loadedCompatibleFonds.filter((fond) => {
            return fondPermissionsService.hasEditFondPermissions(fondPermissions, fond.id);
        });
    }

    const handleCreateRecord = async (fond: Fond) => {
        const createdRecord = await recordService.createRecordEditation({
            subkind: Subkind.SUBKIND_DOCUMENT,
            rootFond: fond,
            fond
        });

        if (!exists(createdRecord)) {
            return;
        }

        dispatch('record-created', createdRecord);
    };
</script>

{#if exists(creatableFonds) && creatableFonds.length > 0}
    {#if creatableFonds.length === 1}
        <KpButton {buttonStyle} {buttonSize} {isBlock} on:click={() => handleCreateRecord(creatableFonds[0])}>
            <IconedContent icon="add">{buttonLabel}</IconedContent>
        </KpButton>
    {:else}
        <KpDropdownMenuButton {buttonStyle} {buttonSize} {isBlock}>
            <svelte:fragment slot="button">
                <IconedContent icon="add" trailingIcon="angle-small-down">{buttonLabel}</IconedContent>
            </svelte:fragment>

            <svelte:fragment slot="menu">
                {#each creatableFonds as fond (fond.id)}
                    <KpDropdownMenuItem on:click={() => handleCreateRecord(fond)}>
                        {pipe(fond, loc())}
                    </KpDropdownMenuItem>
                {/each}
            </svelte:fragment>
        </KpDropdownMenuButton>
    {/if}
{/if}