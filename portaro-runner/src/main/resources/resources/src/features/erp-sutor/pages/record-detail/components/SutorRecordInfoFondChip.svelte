<script lang="ts">
    import type {ComponentSize} from 'shared/ui-widgets/types';
    import {getSanitize} from 'core/svelte-context/context';
    import SutorFondChip from './SutorFondChip.svelte';

    export let chipSize: ComponentSize = 'md';
    export let fondId: number;
    export let fondName: string | null = null;
    export let value: string;

    const sanitize = getSanitize();
    const split = value.split('<');
    const tagValue = split.slice(1).join('<');
</script>

<div class="sutor-record-info-fond-chip-container">
    {split[0]}

    <SutorFondChip {fondId} {fondName} {chipSize}>
        {@html sanitize(`<${tagValue}`)}
    </SutorFondChip>
</div>

<style lang="less">
    .sutor-record-info-fond-chip-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
</style>