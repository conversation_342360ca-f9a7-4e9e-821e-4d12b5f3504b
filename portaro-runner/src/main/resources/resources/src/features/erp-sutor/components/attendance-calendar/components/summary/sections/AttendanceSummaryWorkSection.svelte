<script lang="ts">
    import type {DescriptedDurationItem, WorkSummary} from 'src/features/erp-sutor/components/attendance-calendar/types';
    import {SummarySectionType} from 'src/features/erp-sutor/components/attendance-calendar/types';
    import AttendanceSummarySection from '../AttendanceSummarySection.svelte';
    import AttendanceSummaryColumn from '../AttendanceSummaryColumn.svelte';
    import AttendanceCell from '../../AttendanceCell.svelte';

    export let work: WorkSummary;
    export let commitment: DescriptedDurationItem;
    export let holiday: DescriptedDurationItem;
</script>

<AttendanceSummarySection type="{SummarySectionType.WORK}">
    <AttendanceSummaryColumn>
        <AttendanceCell header>Fond</AttendanceCell>
        <AttendanceCell durationItem="{commitment}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>Odpr.</AttendanceCell>
        <AttendanceCell durationItem="{work.costsSourcePresence}"/>
        <AttendanceCell durationItem="{work.attendanceSourcePresence}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>Sv.</AttendanceCell>
        <AttendanceCell durationItem="{holiday}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>Plán</AttendanceCell>
        <AttendanceCell durationItem="{work.costsSourcePlan}"/>
        <AttendanceCell durationItem="{work.attendanceSourcePlan}"/>
    </AttendanceSummaryColumn>
</AttendanceSummarySection>
