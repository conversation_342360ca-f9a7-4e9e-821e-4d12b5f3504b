<script lang="ts">
    import type {DraggableDashboardCard} from 'src/features/erp/components/erp-draggable-dashboard/types';
    import type {SutorHomePageData} from 'src/features/erp-sutor/pages/homepage/types';
    import ErpDraggableDashboard from 'src/features/erp/components/erp-draggable-dashboard/ErpDraggableDashboard.svelte';
    import EmptyCardContent from 'src/features/erp-sutor/pages/homepage/parts/cards/EmptyCardContent.svelte';
    import LinksCard from 'src/features/erp-sutor/pages/homepage/parts/cards/LinksCard.svelte';

    export let homepageData: SutorHomePageData;

    const cards: DraggableDashboardCard[] = [
        {
            id: 1,
            title: 'Rychlé odkazy',
            colSpan: 3,
            cardContentComponent: LinksCard,
            componentProps: {homepageData}
        },
        {
            id: 2,
            title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
            colSpan: 2,
            cardContentComponent: EmptyCardContent
        },
        {
            id: 3,
            title: '<PERSON>sobn<PERSON> kalend<PERSON>',
            colSpan: 1,
            cardContentComponent: EmptyCardContent
        },
        {
            id: 4,
            title: 'Uživatelé Sutin 2.0',
            colSpan: 1,
            cardContentComponent: EmptyCardContent
        },
        {
            id: 5,
            title: 'Finanční toky',
            colSpan: 2,
            cardContentComponent: EmptyCardContent
        }
    ];
</script>

<ErpDraggableDashboard {cards}/>