<script lang="ts">
    import type {Tab<PERSON><PERSON>on, TabId, TabPageDefinitionsMap} from 'shared/ui-widgets/tabset/types';
    import type {RelatedRecordsTable} from '../../../record/kp-related-records-table/types';
    import type {User} from 'typings/portaro.be.types';
    import type {UserDetailView} from 'src/features/user/types';
    import UserDataService from 'src/features/user/user.data-service';
    import {getPageContext} from 'shared/layouts/page-context';
    import {getInjector} from 'core/svelte-context/context';
    import {onMount} from 'svelte';
    import {FOND_PERSON, FOND_PROJECT} from 'src/features/erp-sutor/sutor-fonds';
    import KpRelatedRecordsTable from '../../../record/kp-related-records-table/KpRelatedRecordsTable.svelte';
    import SutorDocumentDetailTab from 'src/features/erp-sutor/pages/record-detail/tab-pages/SutorDocumentDetailTab.svelte';
    import ErpTabbedSubpagesContainer from 'src/features/erp/components/erp-tabbed-subpages/ErpTabbedSubpagesContainer.svelte';
    import KpMediaViewerInline from '../../../media-viewer/KpMediaViewerInline.svelte';
    import SutorDocumentExportTab from './tab-pages/SutorDocumentExportTab.svelte';
    import SutorProjectReportsOptimisationTab from 'src/features/erp-sutor/pages/project-detail/reports-optimisation/SutorProjectReportsOptimisationTab.svelte';
    import ErpPageLayout from 'src/features/erp/components/ErpPageLayout.svelte';
    import SutorDocumentThreadTab from 'src/features/erp-sutor/pages/record-detail/tab-pages/SutorDocumentThreadTab.svelte';
    import SutorDocumentDetailTopBar from 'src/features/erp-sutor/pages/record-detail/components/SutorDocumentDetailTopBar.svelte';
    import SutorUserAccountAttendanceTab from 'src/features/user/kp-user-account-page/sutor-parts/tab-pages/SutorUserAccountAttendanceTab.svelte';
    import SutorProjectDetail from 'src/features/erp-sutor/pages/project-detail/SutorProjectDetail.svelte';
    import TabbedSubpagesRouter from 'src/features/erp/components/erp-tabbed-subpages/TabbedSubpagesRouter.svelte';
    import type {
        DocumentDetailReactivePageData,
        DocumentDetailStaticPageData
    } from '../../../record/kp-document-detail-page/types';

    export let relatedTables: RelatedRecordsTable[] = [];

    const userDataService = getInjector().getByClass(UserDataService);

    const pageContext = getPageContext<DocumentDetailStaticPageData, DocumentDetailReactivePageData>();
    const model = pageContext.staticData.model;
    const record = model.record;
    const isRecordProject = FOND_PROJECT.subfonds.includes(record.fond.id);

    let loading = true;
    let loadedUser: User | null = null;
    let loadedUserDetail: UserDetailView | null = null;

    const isUserRecord = FOND_PERSON.fond === record.fond.id;

    const tabButtons: TabButton[] = [
        {
            id: 'tab-detail',
            label: isRecordProject ? 'Přehledy' : 'Přehled',
            icon: 'info',
            tabPageWithoutPadding: isRecordProject
        }
    ];

    let tabs = createTabs(null, null);

    // Add related tables tabs
    relatedTables.forEach((relatedTable) => {
        tabButtons.push({
            id: `tab-relatedtable-${relatedTable.tableId}`,
            label: relatedTable.title,
            icon: 'grid-alt',
            tabPageWithoutPadding: true,
            tabPageContainerClass: 'record-grid-tab-page'
        });
    });

    // Add an optimisation tab for project fonds
    if (isRecordProject) {
        tabButtons.push({
            id: 'tab-optimisation',
            label: 'Optimalizace nákladů',
            icon: 'money-check-edit',
            tabPageWithoutPadding: true
        });
    }

    // Add a user attendance tab for user records
    if (isUserRecord) {
        tabButtons.push({
            id: 'tab-user-attendance',
            label: 'Docházka',
            icon: 'calendar'
        });
    }

    // Add a thread tab if applicable
    if (record.fond.threadable) {
        tabButtons.push({
            id: 'tab-thread',
            label: 'Konverzace',
            icon: 'messages',
            tabPageWithoutPadding: true
        });
    }

    // Add common tabs
    tabButtons.push(
        {
            id: 'tab-files',
            label: 'Soubory',
            icon: 'file',
            tabPageWithoutPadding: true
        },
        {
            id: 'tab-export',
            label: 'Tisk',
            icon: 'print'
        }
    );

    onMount(async () => {
        if (!isUserRecord) {
            loading = false;
            return;
        }

        try {
            loadedUser = await userDataService.getUser(record.id);
            loadedUserDetail = await userDataService.getUserDetailView(record.id);
        } catch {
            loadedUser = null;
            loadedUserDetail = null;
        }

        tabs = createTabs(loadedUser, loadedUserDetail);

        loading = false;
    });

    function isTableTab(tabId: TabId): boolean {
        return tabId.includes('tab-relatedtable-');
    }

    function createTabs(user: User | null, userDetailView: UserDetailView | null): TabPageDefinitionsMap {
        return {
            'tab-detail': {
                component: isRecordProject ? SutorProjectDetail : SutorDocumentDetailTab,
                props: isUserRecord ? {
                    user,
                    userDetailView
                } : undefined
            },
            'tab-export': {component: SutorDocumentExportTab},
            'tab-thread': {component: SutorDocumentThreadTab},
            'tab-optimisation': {
                component: SutorProjectReportsOptimisationTab,
                props: {
                    record
                }
            },
            'tab-files': {
                component: KpMediaViewerInline,
                props: {record}
            },
            'tab-user-attendance': {
                component: SutorUserAccountAttendanceTab,
                props: {injectedUserRecord: record}
            }
        };
    }
</script>

<ErpPageLayout pageClass="sutor-document-detail-tabbed-page" gap="0px" withoutContentPadding {loading}>
    <SutorDocumentDetailTopBar user="{loadedUser}" userDetailView="{loadedUserDetail}"/>

    <ErpTabbedSubpagesContainer {tabButtons} additionalClasses="document-detail-tabbed-subpages" let:activeTab>
        {#if isTableTab(activeTab)}
            {@const relatedTable = relatedTables.find((table) => `tab-relatedtable-${table.tableId}` === activeTab)}

            <KpRelatedRecordsTable record="{model.record}"
                                   fondId="{relatedTable.fondId}"
                                   relations="{relatedTable.relations}"
                                   referenceFieldTypeId="{relatedTable.referenceFieldTypeId}"/>
        {:else}
            <TabbedSubpagesRouter {tabs} {activeTab}/>
        {/if}
    </ErpTabbedSubpagesContainer>
</ErpPageLayout>