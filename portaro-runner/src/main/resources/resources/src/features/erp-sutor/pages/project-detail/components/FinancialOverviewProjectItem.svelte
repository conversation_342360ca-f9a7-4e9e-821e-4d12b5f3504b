<script lang="ts">
    import type {Document} from 'typings/portaro.be.types';
    import {ignoreUnusedProperties} from 'shared/utils/custom-utils';
    import KpChipTag from 'shared/ui-widgets/chip/KpChipTag.svelte';

    export let projectRecord: Document;
    export let index: number;

    ignoreUnusedProperties(projectRecord);

    const rootProject = index === 0;
</script>

<tr class="financial-overview-project-item">
    <td>
        <div class="project-column">
            <span class="project-index">{index + 1}</span>
            <div class="project-info" class:root-project={rootProject}>
                <div class="name-container">
                    <span class="name">3738439304E</span>
                    <KpChipTag chipSize="xs" chipStyle="success-new">Zahájeno</KpChipTag>
                </div>
                <span class="description">Testovací popis</span>
                <span class="price">Smluvní cena: <span class="price-value">10 000 CZK</span></span>
            </div>
        </div>
    </td>

    <td>
        <div class="data-column headings">
            <div class="data-cell">Celkem</div>
            <div class="data-cell">Vlastní</div>
            <div class="data-cell">Pendleři</div>
            <div class="data-cell">Cizí</div>
        </div>
    </td>

    <!-- Work -->
    <td>
        <div class="data-column green-highlight">
            <div class="data-cell">100 000</div>
            <div class="data-cell">-</div>
            <div class="data-cell">-</div>
            <div class="data-cell">-</div>
        </div>
    </td>

    <!-- Machine -->
    <td>
        <div class="data-column green-highlight">
            <div class="data-cell">10 000</div>
            <div class="data-cell">-</div>
            <div class="data-cell">-</div>
            <div class="data-cell">-</div>
        </div>
    </td>

    <!-- Material -->
    <td>
        <div class="data-column blue-highlight">
            <div class="data-cell">1 000</div>
            <div class="data-cell">-</div>
            <div class="data-cell">-</div>
            <div class="data-cell">-</div>
        </div>
    </td>

    <!-- Subproject -->
    <td>
        <div class="data-column blue-highlight">
            <div class="data-cell">1 000</div>
            <div class="data-cell">-</div>
            <div class="data-cell">-</div>
            <div class="data-cell">-</div>
        </div>
    </td>

    <!-- Other -->
    <td>
        <div class="data-column blue-highlight">
            <div class="data-cell">1 000</div>
            <div class="data-cell">-</div>
            <div class="data-cell">-</div>
            <div class="data-cell">-</div>
        </div>
    </td>

    <!-- Total -->
    <td>
        <div class="data-column">
            <div class="data-cell">113 000</div>
            <div class="data-cell">-</div>
            <div class="data-cell">-</div>
            <div class="data-cell">-</div>
        </div>
    </td>

    <!-- Profit -->
    <td>
        <div class="data-column all-highlighted">
            <div class="data-cell yellow-highlight">113 000</div>
            <div class="data-cell red-highlight">1000</div>
            <div class="data-cell">-</div>
            <div class="data-cell">-</div>
        </div>
    </td>
</tr>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .data-column {
        display: inline-flex;
        flex-direction: column;
        width: 100%;
        gap: @spacing-xs;

        .data-cell {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: @spacing-sm;
            height: 24px;
        }

        &:not(.headings) {
            .data-cell:first-child {
                font-weight: 500;
            }
        }

        &.headings {
            color: @themed-text-muted;
        }

        &.all-highlighted .data-cell {
            font-weight: 500;
        }
    }

    .project-column {
        display: inline-flex;
        align-items: center;
        gap: @spacing-l;

        .project-index {
            width: 28px;
            height: 28px;
            border-radius: @border-radius-default;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid @themed-border-default;
            background-color: @themed-panel-bg;
            font-size: @font-size-default;
        }

        .project-info {
            display: flex;
            flex-direction: column;
            gap: @spacing-xs;

            --project-color: var(--accent-blue-new);

            &.root-project {
                --project-color: var(--brand-orange-new);
            }

            .name-container {
                display: flex;
                align-items: center;
                gap: @spacing-s;

                .name {
                    font-size: @font-size-default;
                    font-weight: 500;
                    color: var(--project-color);
                }
            }

            .description {
                color: @themed-text-muted;
            }

            .price {
                color: @themed-text-muted;

                .price-value {
                    font-weight: 500;
                    color: @themed-text-default;
                }
            }
        }
    }

    .green-highlight {
        background-color: #ddf3e3;
        border-radius: @border-radius-default;
    }

    .blue-highlight {
        background-color: @themed-body-bg-blue-highlighted;
        border-radius: @border-radius-default;
    }

    .yellow-highlight {
        background-color: #FFF2D3;
        border-radius: @border-radius-default;
    }

    .red-highlight {
        background-color: #f6dcdf;
        border-radius: @border-radius-default;
    }
</style>