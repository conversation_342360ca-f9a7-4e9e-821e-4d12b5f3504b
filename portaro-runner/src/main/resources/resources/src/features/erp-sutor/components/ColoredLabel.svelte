<script lang="ts">
    import {getColorFromString} from 'shared/utils/color-util';

    export let label: string;
    export let colorKey: string | null = null;
    export let color: string | null = null;
</script>

<div class="colored-label">
    <div class="color-box" style:background-color="{color ?? getColorFromString(colorKey ?? label, 70)}"></div>
    <span class="job-name">{label}</span>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .colored-label {
        display: flex;
        align-items: center;
        gap: @spacing-sm;

        .color-box {
            width: 16px;
            height: 16px;
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: @border-radius-sm;
        }
    }
</style>