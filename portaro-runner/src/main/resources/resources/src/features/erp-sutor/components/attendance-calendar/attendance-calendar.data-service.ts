import type {DayAttendance, WorkAttendanceOverview} from './types';
import type {UUID} from 'typings/portaro.be.types';
import type {YearMonth} from 'shared/components/kp-calendar/types';
import type {AjaxService} from 'core/data-services/ajax.service';
import {ngAsync} from 'shared/utils/ng-@decorators';
import {exists} from 'shared/utils/custom-utils';

export class AttendanceCalendarDataService {
    public static serviceName = 'attendanceCalendarDataService';

    private static readonly WORK_ATTENDANCE_OVERVIEW_ROUTE = 'work-attendance/overview';
    private static readonly WORK_ATTENDANCE_DAYS_ROUTE = 'work-attendance/days';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async getWorkAttendanceOverview(userRecordId: UUID, yearMonth?: YearMonth | null): Promise<WorkAttendanceOverview> {
        return this.ajaxService
            .createRequest(`${AttendanceCalendarDataService.WORK_ATTENDANCE_OVERVIEW_ROUTE}`)
            .get({
                month: exists(yearMonth) ? yearMonth : undefined,
                userRecordId
            });
    }

    @ngAsync()
    public async getWorkAttendanceDays(userRecordId: UUID, fromDate: Date, toDate: Date): Promise<DayAttendance[]> {
        return this.ajaxService
            .createRequest(`${AttendanceCalendarDataService.WORK_ATTENDANCE_DAYS_ROUTE}`)
            .get({
                userRecordId,
                fromDate: fromDate.toISOString(),
                toDate: toDate.toISOString()
            });
    }
}