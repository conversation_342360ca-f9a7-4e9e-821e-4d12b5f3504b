<script lang="ts">
    import type {DiagramNodeDetailsModalSettings} from 'shared/components/kp-hierarchy-diagram/types';
    import type {Department} from 'typings/portaro.be.types';
    import {onDestroy, onMount} from 'svelte';
    import {getInjector} from 'core/svelte-context/context';
    import {KpDepartmentsService} from 'src/features/departments/services/kp-departments.service';
    import {getDataViewEventContext} from 'src/features/erp-sutor/pages/data-view/data-view.event-context';
    import {REFRESH_DATA_VIEW_GRID_EVENT} from 'src/features/erp-sutor/pages/data-view/types';
    import KpHierarchyDiagram from 'shared/components/kp-hierarchy-diagram/KpHierarchyDiagram.svelte';
    import DepartmentDiagramNodeDetailsContent from 'src/features/departments/components/DepartmentDiagramNodeDetailsContent.svelte';
    import KpLoadableContainer from 'shared/layouts/containers/KpLoadableContainer.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    const departmentsService = getInjector().getByClass(KpDepartmentsService);
    const eventContext = getDataViewEventContext();

    const nodeDetailsModalSettings: DiagramNodeDetailsModalSettings = {
        headingGetter: (item) => item.text,
        component: DepartmentDiagramNodeDetailsContent
    };

    let loading = true;
    let departments: Department[] = [];

    onMount(async () => {
        await loadDepartments();
        loading = false;

        eventContext.addEventListener(REFRESH_DATA_VIEW_GRID_EVENT, handleRefresh);
    });

    onDestroy(() => {
        eventContext.removeEventListener(REFRESH_DATA_VIEW_GRID_EVENT, handleRefresh);
    });

    const handleRefresh = () => {
        loading = true;
        loadDepartments().then(() => loading = false);
    };

    async function loadDepartments() {
        departments = await departmentsService.getDepartments();
    }
</script>

<KpLoadableContainer fillAvailableSpace {loading}>
    <Flex class="sutor-divisions-diagram" direction="column" fillAvailableSpace>
        <KpHierarchyDiagram items="{departments}"
                            heading="Diagramová struktura divizí"
                            {nodeDetailsModalSettings}/>
    </Flex>
</KpLoadableContainer>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro-erp.less";

    :global {
        .sutor-divisions-diagram {
            .kp-hierarchy-diagram {
                .flex-grow();

                .heading-container {
                    padding: 0 @spacing-xl;
                    margin-top: 18px;
                }

                .svelte-flow-container {
                    .flex-grow();
                    height: 100% !important;
                    border-left: none !important;
                    border-right: none !important;
                    border-bottom: none !important;
                    border-radius: 0 !important;
                }
            }
        }
    }
</style>