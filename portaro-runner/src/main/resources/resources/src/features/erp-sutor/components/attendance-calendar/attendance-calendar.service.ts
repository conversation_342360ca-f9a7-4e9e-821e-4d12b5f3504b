import type {DayAttendance, WorkAttendanceOverview} from './types';
import type {UUID} from 'typings/portaro.be.types';
import type {AttendanceCalendarDataService} from './attendance-calendar.data-service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';

export class AttendanceCalendarService {
    public static serviceName = 'attendanceCalendarService';

    /*@ngInject*/
    constructor(private attendanceCalendarDataService: AttendanceCalendarDataService,
                private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    public async getWorkAttendanceOverview(userRecordId: UUID, year: number, month: number): Promise<WorkAttendanceOverview | null> {
        try {
            return await this.attendanceCalendarDataService.getWorkAttendanceOverview(userRecordId, `${year}-${month < 10 ? '0' : ''}${month}`);
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            return null;
        }
    }

    public async getWorkAttendanceDays(userRecordId: UUID, fromDate: Date, toDate: Date): Promise<DayAttendance[]> {
        try {
            return await this.attendanceCalendarDataService.getWorkAttendanceDays(userRecordId, fromDate, toDate);
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            return [];
        }
    }
}