<script lang="ts">
    import type {OvertimeWorkSummary} from 'src/features/erp-sutor/components/attendance-calendar/types';
    import {SummarySectionType} from 'src/features/erp-sutor/components/attendance-calendar/types';
    import AttendanceSummarySection from '../AttendanceSummarySection.svelte';
    import AttendanceCell from '../../AttendanceCell.svelte';
    import AttendanceSummaryColumn from '../AttendanceSummaryColumn.svelte';

    export let overtime: OvertimeWorkSummary;
</script>

<AttendanceSummarySection type="{SummarySectionType.OVERTIME}">
    <AttendanceSummaryColumn>
        <AttendanceCell header>Pr.d.</AttendanceCell>
        <AttendanceCell durationItem="{overtime.costsSourceWorkday}"/>
        <AttendanceCell durationItem="{overtime.attendanceSourceWorkday}"/>
        <AttendanceCell durationItem="{overtime.approvedWorkday}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>V.S.</AttendanceCell>
        <AttendanceCell durationItem="{overtime.costsSourceWeekendAndHoliday}"/>
        <AttendanceCell durationItem="{overtime.attendanceSourceWeekendAndHoliday}"/>
        <AttendanceCell durationItem="{overtime.approvedWeekend}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>Celk.</AttendanceCell>
        <AttendanceCell durationItem="{overtime.costsSourceSum}"/>
        <AttendanceCell durationItem="{overtime.attendanceSourceSum}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>Bank</AttendanceCell>
        <AttendanceCell durationItem="{overtime.costsSourceBank}"/>
        <AttendanceCell durationItem="{overtime.attendanceSourceBank}"/>
        <AttendanceCell durationItem="{overtime.approvedBank}"/>
    </AttendanceSummaryColumn>
</AttendanceSummarySection>