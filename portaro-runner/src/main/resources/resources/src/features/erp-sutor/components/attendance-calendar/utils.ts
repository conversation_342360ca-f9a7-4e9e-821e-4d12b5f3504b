import {Duration} from 'luxon';
import {AbsenceReason, SummarySectionType} from './types';

export function formatDurationStringToHours(durationString: string): string {
    const duration = Duration.fromISO(durationString);

    const hours = duration.as('hours');

    if (hours === 0) {
        return '-';
    }

    return hours.toFixed(2);
}

export function formatAbsenceReasons(reasons: AbsenceReason[]) {
    return reasons.map((value) => absenceReasonToAbbr(value)).join(',');
}

export function absenceReasonToAbbr(value: AbsenceReason) {
    switch (value) {
        case AbsenceReason.DOCTOR_VISIT:
            return 'L';
        case AbsenceReason.VACATION:
            return 'D';
        case AbsenceReason.ILLNESS:
            return 'N';
        case AbsenceReason.OCR:
            return 'Ocr';
        case AbsenceReason.PARAGRAPH:
            return 'P';
        case AbsenceReason.COMPENSATION:
            return 'Na';
        case AbsenceReason.UNPAID:
            return 'Ne';
        case AbsenceReason.UNEXCUSED:
            return 'A';
        case AbsenceReason.MATERNITY:
            return 'MD';
        case AbsenceReason.PATERNITY:
            return 'OtD';
        case AbsenceReason.EMPLOYER_OBSTACLES:
            return 'Pz';
        case AbsenceReason.CHILD_CAMP:
            return 'Adm';
    }
}

export function summaryTypeLabel(value: SummarySectionType) {
    switch (value) {
        case SummarySectionType.WORK:
            return 'Práce';
        case SummarySectionType.OVERTIME:
            return 'Přesčas';
        case SummarySectionType.ABSENCE:
            return 'Nepřítomnost';
        case SummarySectionType.ON_CALL_DUTY:
            return 'Pohotovost';
        case SummarySectionType.SECTION_LEGEND:
            return '';
    }
}