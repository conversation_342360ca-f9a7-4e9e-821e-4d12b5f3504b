import type {UIcons} from 'shared/ui-widgets/uicons/types';

export interface SutorHomePageData {
    linkCards: ErpHomepageLinkCardsConfiguration;
}

export interface ErpHomepageLinkCardsConfiguration {
    cardSections: HomepageLinkCardSection[];
}

export interface HomepageLinkCardSection {
    color: string;
    cards: HomepageLinkCard[];
}

export interface HomepageLinkCard {
    label: string;
    icon: UIcons;
    href: string;
    value?: string | number;
    showPermissionFond?: number;
    editPermissionFond?: number;
}