<script lang="ts">
    import type {OnCallDutySummary} from 'src/features/erp-sutor/components/attendance-calendar/types';
    import {SummarySectionType} from 'src/features/erp-sutor/components/attendance-calendar/types';
    import AttendanceSummarySection from '../AttendanceSummarySection.svelte';
    import AttendanceCell from '../../AttendanceCell.svelte';
    import AttendanceSummaryColumn from '../AttendanceSummaryColumn.svelte';

    export let onCallDuty: OnCallDutySummary;
</script>

<AttendanceSummarySection type="{SummarySectionType.ON_CALL_DUTY}">
    <AttendanceSummaryColumn>
        <AttendanceCell header>Celkový čas</AttendanceCell>
        <AttendanceCell durationItem="{onCallDuty}"/>
    </AttendanceSummaryColumn>
</AttendanceSummarySection>
