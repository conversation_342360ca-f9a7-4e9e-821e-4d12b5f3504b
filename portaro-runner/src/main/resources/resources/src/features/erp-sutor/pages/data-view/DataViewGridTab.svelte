<script lang="ts">
    import type {FondId} from 'src/features/erp-sutor/pages/data-view/types';
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import type {FieldTypeId, UUID} from 'typings/portaro.be.types';
    import type {SearchManager} from 'src/features/search/search-manager/search-manager';
    import {getDataViewEventContext} from 'src/features/erp-sutor/pages/data-view/data-view.event-context';
    import {onDestroy, onMount} from 'svelte';
    import {isFunction} from 'shared/utils/custom-utils';
    import {REFRESH_DATA_VIEW_GRID_EVENT} from 'src/features/erp-sutor/pages/data-view/types';
    import KpRecordGrid from 'src/features/record-grid/KpRecordGrid.svelte';

    export let fondId: FondId;
    export let referenceRecordId: UUID;
    export let referenceFieldTypeId: FieldTypeId;

    let getSearchManager: (() => SearchManager<RecordRow>) | null = null;

    const eventContext = getDataViewEventContext();

    onMount(() => {
        eventContext.addEventListener(REFRESH_DATA_VIEW_GRID_EVENT, handleRefresh);
    });

    onDestroy(() => {
        eventContext.removeEventListener(REFRESH_DATA_VIEW_GRID_EVENT, handleRefresh);
    });

    const handleRefresh = () => {
        if (!isFunction(getSearchManager)) {
            return;
        }

        const searchManager = getSearchManager();
        searchManager.refreshSearch();
    };
</script>

<KpRecordGrid fondOrFondId="{fondId}"
              searchType="record-name"
              {referenceRecordId}
              {referenceFieldTypeId}
              bind:getSearchManager/>