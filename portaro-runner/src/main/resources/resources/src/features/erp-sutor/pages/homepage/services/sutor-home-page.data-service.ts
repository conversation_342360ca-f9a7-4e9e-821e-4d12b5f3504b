import type {SutorHomePageData} from '../types';
import type {AjaxService} from 'core/data-services/ajax.service';
import {ngAsync} from 'shared/utils/ng-@decorators';

export class SutorHomePageDataService {
    public static serviceName = 'sutorHomePageDataService';

    public static readonly ROUTE = 'page-data/sutor-homepage';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async getHomePageData(): Promise<SutorHomePageData> {
        return this.ajaxService
            .createRequest(SutorHomePageDataService.ROUTE)
            .get();
    }
}