<script lang="ts">
    import type {DocumentDetailReactivePageData, DocumentDetailStaticPageData} from 'src/features/record/kp-document-detail-page/types';
    import type {ServerFolder} from 'typings/portaro.be.types';
    import {getInjector} from 'core/svelte-context/context';
    import {onDestroy, onMount} from 'svelte';
    import {createReportServerContext} from '../../../../report-server/report-server-context';
    import {exists} from 'shared/utils/custom-utils';
    import {fly} from 'svelte/transition';
    import {isString} from 'shared/utils/string-utils';
    import {getPageContext} from 'shared/layouts/page-context';
    import ReportServerService from '../../../../report-server/services/report-server.service';
    import ReportServerFoldersPart from '../../../../report-server/parts/ReportServerFoldersPart.svelte';
    import ReportServerFormPart from '../../../../report-server/parts/ReportServerFormPart.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    const pageContext = getPageContext<DocumentDetailStaticPageData, DocumentDetailReactivePageData>();
    const record = pageContext.staticData.model.record;
    const reportServerService = getInjector().getByClass(ReportServerService);
    const reportServerContext = createReportServerContext(reportServerService, null);
    const flyInAnimParams = {duration: 250, y: 10};

    reportServerContext.setPrefillTemplateProps({
        recordId: record.id
    });

    let loading = true;
    let serverFolder: ServerFolder | null;
    const serverFolderUnsubscribe = reportServerContext.serverFolder.subscribe((currentServerFolder) => serverFolder = currentServerFolder);

    onMount(async () => {
        try {
            const folder = await reportServerService.getRecordReportServerFolder(record);
            loading = false;

            if (isString(folder)) {
                return;
            }

            reportServerContext.setServerFolder(folder);
        } catch {
            reportServerContext.setServerFolder(null);
            loading = false;
        }
    });

    onDestroy(() => {
        serverFolderUnsubscribe();
    });
</script>

{#if loading}
    <KpLoadingBlock fillAvailableSpace/>
{/if}

{#if exists(serverFolder)}
    <div class="sutor-report-server-exports" in:fly={flyInAnimParams}>
        <ReportServerFoldersPart/>
        <ReportServerFormPart/>
    </div>
{/if}

{#if !exists(serverFolder) && !loading}
    <div class="no-exports-container">
        <IconedContent orientation="vertical" align="center" justify="center" icon="info">
            Nejsou k dispozici žádné šablony k exportu
        </IconedContent>
    </div>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.media-queries.less";
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro-erp.less";

    .sutor-report-server-exports {
        width: 100%;
        display: flex;
        min-height: 600px;

        @media (max-width: @screen-sm-max) {
            flex-direction: column;
        }
    }

    .no-exports-container {
        .flex-grow();
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    :global {
        .sutor-report-server-exports {
            .report-server-folders-part,
            .report-server-form-part {
                flex: 1;
                padding-top: 0 !important;
            }

            .report-server-folders-part h2 {
                display: none;
            }
        }
    }
</style>