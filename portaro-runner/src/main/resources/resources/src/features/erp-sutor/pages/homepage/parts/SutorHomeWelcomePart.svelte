<script lang="ts">
    import type {BasicUser} from 'typings/portaro.be.types';
    import {onDestroy, onMount} from 'svelte';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {getCurrentLanguage} from 'core/svelte-context/context';
    import {range} from 'shared/utils/array-utils';
    import ErpHeadingBar from '../../../../erp/components/ErpHeadingBar.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';
    import peacefulAfternoon from '../../../assets/bgs/peaceful-afternoon.png';
    import quietNight from '../../../assets/bgs/quiet-night.png';
    import springDay from '../../../assets/bgs/spring-day.png';
    import sunsetMountains from '../../../assets/bgs/sunset-mountains.png';

    export let user: BasicUser;

    let currentTime = new Date();
    let clockInterval: number;

    onMount(() => {
        clockInterval = window.setInterval(() => {
            currentTime = new Date();
        }, 1000);
    });

    onDestroy(() => {
        if (clockInterval) {
            clearInterval(clockInterval);
        }
    });

    function formatTime(date: Date): string {
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        return `${hours}:${minutes}:${seconds}`;
    }

    function formatDate(date: Date): string {
        const locale = getCurrentLanguage();
        return new Intl.DateTimeFormat(locale, {
            weekday: 'long',
            day: 'numeric',
            month: 'long'
        }).format(date);
    }

    const hourGreetings = [
        {
            greeting: 'Dobrý den',
            background: springDay,
            range: [-1]
        },
        {
            greeting: 'Dobré ráno',
            background: springDay,
            range: range(5, 10)
        },
        {
            greeting: 'Dobré poledne',
            background: springDay,
            range: [11]
        },
        {
            greeting: 'Dobré odpoledne',
            background: springDay,
            range: range(12, 14)
        },
        {
            greeting: 'Dobré odpoledne',
            background: peacefulAfternoon,
            range: range(15, 17)
        },
        {
            greeting: 'Dobrý večer',
            background: sunsetMountains,
            range: range(18, 19)
        },
        {
            greeting: 'Dobrý večer',
            background: quietNight,
            range: range(20, 23)
        }
    ];

    function getGreeting(date: Date): {greeting: string, background: string} {
        const hours = date.getHours();
        return hourGreetings.find((greeting) => greeting.range.includes(hours)) || hourGreetings[0];
    }
</script>

<ErpHeadingBar additionalClasses="sutor-home-welcome-part"
               backgroundImageUrl="{getGreeting(currentTime).background}">

    <KpHeading type="h2">
        <span class="greeting-part">{getGreeting(currentTime).greeting},</span>
        <br>{pipe(user, loc())}
    </KpHeading>

    <div class="clock-container">
        <div class="clock">{formatTime(currentTime)}</div>
        <div class="date">{formatDate(currentTime)}</div>
    </div>
</ErpHeadingBar>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    :global {
        .sutor-home-welcome-part {
            max-width: min(95%, 1360px);
            align-self: center;
            padding-top: @spacing-ml !important;
            padding-bottom: @spacing-ml !important;
            border-left: 1px solid @themed-border-default !important;
            border-right: 1px solid @themed-border-default !important;
            border-bottom: 1px solid @themed-border-default !important;
            border-bottom-left-radius: @border-radius-xl;
            border-bottom-right-radius: @border-radius-xl;
            position: relative;

            h2 {
                color: white;

                .greeting-part {
                    font-size: @font-size-large;
                    color: rgba(255, 255, 255, 0.75);
                    font-weight: 400;
                }
            }
        }
    }

    .clock-container {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;

        .clock {
            color: white;
            font-size: 20px;
            font-weight: 500;
        }

        .date {
            color: rgba(255, 255, 255, 0.75);
            text-align: center;
        }
    }
</style>