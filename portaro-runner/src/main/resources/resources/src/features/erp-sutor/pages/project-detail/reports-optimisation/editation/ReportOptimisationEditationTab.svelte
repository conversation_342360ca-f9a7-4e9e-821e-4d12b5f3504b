<script lang="ts">
    import type {FieldTypeId, Fond} from 'typings/portaro.be.types';
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import {getSutorReportsOptimisationContext} from '../sutor-reports-optimisation-context';
    import {popInAnim, popOutAnim} from 'shared/animations/pop-animations';
    import {onDestroy} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import KpRecordGrid from 'src/features/record-grid/KpRecordGrid.svelte';
    import ReportOptimisationControlCell from 'src/features/erp-sutor/pages/project-detail/reports-optimisation/editation/ReportOptimisationControlCell.svelte';
    import ReportOptimisationUsers from 'src/features/erp-sutor/pages/project-detail/reports-optimisation/editation/users/ReportOptimisationUsers.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    export let fond: Fond;

    const context = getSutorReportsOptimisationContext();

    let optimisedInstallationLogbook: RecordRow | null = null;
    const optimisedInstallationLogbookUnsubscribe = context.optimisedInstallationLogbookRecord$.subscribe((currentOptimisedInstallationLogbook) => optimisedInstallationLogbook = currentOptimisedInstallationLogbook);

    const referenceFieldTypeId = 'd1002.main' as FieldTypeId;

    onDestroy(() => {
        optimisedInstallationLogbookUnsubscribe();
    });
</script>

<Flex class="report-optimisation-editation-tab" direction="column" fillAvailableSpace>
    <div class="overlay-container">
        {#if !exists(optimisedInstallationLogbook)}
            <div class="grid-container record-grid-tab-page" in:popInAnim={{key: 'grid'}} out:popOutAnim={{key: 'grid'}}>
                <KpRecordGrid controlCellComponent="{ReportOptimisationControlCell}"
                              fondOrFondId="{fond.id}"
                              referenceRecordId="{context.projectRecord.id}"
                              expandedFirstLevel
                              {referenceFieldTypeId}/>
            </div>
        {:else}
            <div class="users-container" in:popInAnim={{key: 'users'}} out:popOutAnim={{key: 'users'}}>
                <ReportOptimisationUsers {optimisedInstallationLogbook}/>
            </div>
        {/if}
    </div>
</Flex>

<style lang="less">
    @import (reference) "styles/portaro-erp.less";

    .overlay-container {
        .flex-grow();
        position: relative;

        .grid-container,
        .users-container {
            .flex-grow();
            align-items: start;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    }
</style>