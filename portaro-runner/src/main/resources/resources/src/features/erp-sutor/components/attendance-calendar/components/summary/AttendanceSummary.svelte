<script lang="ts">
    import type {AttendanceSummary} from '../../types';
    import AttendanceSummaryWorkSection from './sections/AttendanceSummaryWorkSection.svelte';
    import AttendanceSummaryOvertimeSection from './sections/AttendanceSummaryOvertimeSection.svelte';
    import AttendanceSummaryAbsenceSection from './sections/AttendanceSummaryAbsenceSection.svelte';
    import AttendanceSummaryOnCallDutySection from './sections/AttendanceSummaryOnCallDutySection.svelte';
    import AttendanceSummaryLegendSection from './sections/AttendanceSummaryLegendSection.svelte';

    export let summary: AttendanceSummary;
</script>

<div class="attendance-summary-container">
    <AttendanceSummaryLegendSection/>
    <AttendanceSummaryWorkSection work="{summary.work}" commitment="{summary.commitment}" holiday="{summary.holiday}"/>
    <AttendanceSummaryOvertimeSection overtime="{summary.overtime}"/>
    <AttendanceSummaryAbsenceSection absence="{summary.absence}"/>
    <AttendanceSummaryOnCallDutySection onCallDuty="{summary.onCallDuty}"/>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .attendance-summary-container {
        width: 100%;
        display: flex;
        gap: @spacing-ml;
        align-items: flex-start;
    }
</style>