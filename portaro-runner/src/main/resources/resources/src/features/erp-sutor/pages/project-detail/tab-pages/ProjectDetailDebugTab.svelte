<script lang="ts">
    import type {DocumentDetailReactivePageData, DocumentDetailStaticPageData} from 'src/features/record/kp-document-detail-page/types';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {DocumentDetailTabsetService} from 'src/features/record/kp-document-detail-tabset/document-detail-tabset.service';
    import {getPageContext} from 'shared/layouts/page-context';
    import {onMount} from 'svelte';
    import KpMasonryGridItem from 'shared/layouts/masonry-grid/KpMasonryGridItem.svelte';
    import KpGenericPanel from 'shared/ui-widgets/panel/KpGenericPanel.svelte';
    import KpRecordMarcTable from '../../../../record/kp-record-marc-table/KpRecordMarcTable.svelte';
    import KpRecordOperationMini from 'src/features/record/operation/KpRecordOperationMini.svelte';
    import KpPaginatedPanelList from 'shared/components/kp-paginated-panel-list/KpPaginatedPanelList.svelte';
    import KpMasonryGrid from 'shared/layouts/masonry-grid/KpMasonryGrid.svelte';

    const service = getInjector().getByClass(DocumentDetailTabsetService);
    const pageContext = getPageContext<DocumentDetailStaticPageData, DocumentDetailReactivePageData>();
    const model = pageContext.staticData.model;
    const localize = getLocalization();

    const operationPageableList = service.createOperationPageableList(model.record);

    onMount(() => {
        operationPageableList.loadFirstPage();
    });
</script>

<KpMasonryGrid gridTemplateColumns="repeat(auto-fill, minmax(600px, 1fr))"
               additionalClasses="project-detail-grid">

    <KpMasonryGridItem animate="{false}">
        <KpGenericPanel hasBodyPadding="{false}">
            <span slot="heading">Systemist debug - MARC informace</span>
            <KpRecordMarcTable fields="{model.record.fields}"/>
        </KpGenericPanel>
    </KpMasonryGridItem>

    <KpMasonryGridItem animate="{false}">
        <KpGenericPanel>
            <span slot="heading">{localize(/* @kp-localization record.ProcessingHistory */ 'record.ProcessingHistory')}</span>
            <KpPaginatedPanelList pageableList="{operationPageableList}"
                                  isInPaddedPanel="{true}"
                                  let:item>
                <KpRecordOperationMini operation="{item}"/>
            </KpPaginatedPanelList>
        </KpGenericPanel>
    </KpMasonryGridItem>
</KpMasonryGrid>