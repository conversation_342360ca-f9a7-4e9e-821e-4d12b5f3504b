<script lang="ts">
    import type {CalendarView} from 'shared/components/kp-calendar/types';
    import type {DayAttendance, WorkAttendanceOverview} from './types';
    import type {UUID} from 'typings/portaro.be.types';
    import {getCurrentLanguage, getInjector} from 'core/svelte-context/context';
    import {AttendanceCalendarService} from './attendance-calendar.service';
    import {KpCalendarManager} from 'shared/components/kp-calendar/kp-calendar-manager';
    import {onDestroy, onMount} from 'svelte';
    import {DateTime} from 'luxon';
    import {calendarDateFromLuxon} from 'shared/components/kp-calendar/utils';
    import {cleanup, exists} from 'shared/utils/custom-utils';
    import {fade} from 'svelte/transition';
    import AttendanceCalendar from './components/AttendanceCalendar.svelte';
    import AttendanceSummary from './components/summary/AttendanceSummary.svelte';
    import KpLoadableContainer from 'shared/layouts/containers/KpLoadableContainer.svelte';
    import KpVerticalSeparator from 'shared/ui-widgets/separator/KpVerticalSeparator.svelte';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';

    export let userRecordId: UUID;
    export let type: 'last-week' | 'month-with-summary';
    export let withoutLoadAnimations = false;
    export let highlightedDate: DateTime | null = null;

    const service = getInjector().getByClass(AttendanceCalendarService);
    const calendarManager = KpCalendarManager.createNavigableCalendar({
        type: 'monthView',
        startDate: calendarDateFromLuxon(exists(highlightedDate) ? highlightedDate : DateTime.now())
    });

    let workAttendance: WorkAttendanceOverview | DayAttendance[];
    let loading = true;
    $: loadError = !loading && !exists(workAttendance);

    let calendarView: CalendarView;
    const calendarViewSubscription = calendarManager.getView$().subscribe((currentCalendarView) => calendarView = currentCalendarView);

    onMount(async () => {
        await loadWorkAttendance();
    });

    onDestroy(() => {
        cleanup(calendarViewSubscription);
    });

    async function loadWorkAttendance() {
        loading = true;

        if (type === 'last-week') {
            const today = (exists(highlightedDate) ? highlightedDate : DateTime.now()).plus({days: 1});
            const oneWeekBefore = today.minus({weeks: 1});
            workAttendance = await service.getWorkAttendanceDays(userRecordId, oneWeekBefore.toJSDate(), today.toJSDate());
        }

        if (type === 'month-with-summary') {
            workAttendance = await service.getWorkAttendanceOverview(userRecordId, calendarView.year, calendarView.month ?? 1);
        }

        loading = false;
    }

    async function navigateToPrevious() {
        calendarManager.navigate(-1);
        await loadWorkAttendance();
    }

    async function navigateToNext() {
        calendarManager.navigate(1);
        await loadWorkAttendance();
    }

    function isMonthAttendanceWithSummary(attendance: WorkAttendanceOverview | DayAttendance[]): attendance is WorkAttendanceOverview {
        return 'summary' in attendance && 'days' in attendance;
    }

    function getCalendarLabel(view: CalendarView): string {
        if (exists(view.month)) {
            const formatter = new Intl.DateTimeFormat(getCurrentLanguage(), {month: 'long'});
            const monthDate = new Date(view.year, view.month - 1, 1);
            return `${view.year} - ${formatter.format(monthDate)}`;
        }

        if (exists(view.week)) {
            return `${view.week}. týden ${view.year}`;
        }

        return '';
    }
</script>

<div class="sutor-attendance-calendar-wrapper">
    {#if type === 'month-with-summary'}
        <div class="toolbar-row">
            <KpIconButton buttonSize="md" on:click={navigateToPrevious} icon="angle-small-left"/>
            <KpIconButton buttonSize="md" on:click={navigateToNext} icon="angle-small-right"/>
            <KpVerticalSeparator height="16px"/>
            <span>{getCalendarLabel(calendarView)}</span>
        </div>
    {/if}

    <KpLoadableContainer {loading} {loadError} withoutAnimations="{withoutLoadAnimations}">
        <div class="sutor-attendance-calendar" in:fade={{duration: withoutLoadAnimations ? 300 : 0}}>
            {#if isMonthAttendanceWithSummary(workAttendance)}
                <AttendanceSummary summary="{workAttendance.summary}"/>
                <AttendanceCalendar attendanceDays="{workAttendance.days}"/>
            {:else}
                <AttendanceCalendar {highlightedDate} attendanceDays="{workAttendance}"/>
            {/if}
        </div>
    </KpLoadableContainer>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .sutor-attendance-calendar-wrapper {
        display: flex;
        flex-direction: column;
        gap: @spacing-m;

        .toolbar-row {
            display: flex;
            align-items: center;
            gap: @spacing-m;

            span {
                font-weight: 500;
                font-size: @font-size-large;
            }
        }

        .sutor-attendance-calendar {
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: @spacing-ml;
        }
    }
</style>