<script lang="ts">
    import type {DayOptimisationJobTypeSummary} from 'src/features/erp-sutor/pages/project-detail/reports-optimisation/types';
    import {priceFormatter} from 'shared/utils/pipes';
    import {getCurrentLanguage} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import ErpCard from 'src/features/erp/components/erp-card/ErpCard.svelte';
    import KpBarebonesTable from 'shared/ui-widgets/table/barebones/KpBarebonesTable.svelte';
    import ColoredLabel from '../../../../../components/ColoredLabel.svelte';

    export let jobTypeSummary: DayOptimisationJobTypeSummary;

    const priceFormat = priceFormatter(getCurrentLanguage());

    function getDifference(reported: number, charged: number): string {
        const difference = charged - reported;
        return `${difference !== 0 ? (difference > 0 ? '+' : '') : ''}${difference}`;
    }

    function getSummedUpReportedHours() {
        return jobTypeSummary.reportedStandardHours + jobTypeSummary.reportedOvertimeHours;
    }

    function getSummedUpChargedHours() {
        return jobTypeSummary.chargedStandardHours + jobTypeSummary.chargedOvertimeHours;
    }
</script>

<ErpCard>
    <svelte:fragment slot="header">
        <ColoredLabel label="{jobTypeSummary.name}"/>
    </svelte:fragment>

    <svelte:fragment slot="body">
        <KpBarebonesTable headerFooterDivided fontSize="12px">
            <tr slot="header">
                <th>Typ</th>
                <th>Vykázaná data</th>
                <th>Účtovaná data</th>
                <th>Rozdíl</th>
            </tr>

            <svelte:fragment slot="body">
                <tr>
                    <td>Počet pracovníků</td>
                    <td>{jobTypeSummary.reportedWorkersCount}</td>
                    <td>{jobTypeSummary.chargedWorkersCount}</td>
                    <td class:difference-value-positive={jobTypeSummary.chargedWorkersCount - jobTypeSummary.reportedWorkersCount > 0}
                        class:difference-value-negative={jobTypeSummary.chargedWorkersCount - jobTypeSummary.reportedWorkersCount < 0}>

                        {getDifference(jobTypeSummary.reportedWorkersCount, jobTypeSummary.chargedWorkersCount)}
                    </td>
                </tr>

                <tr>
                    <td>Hodiny</td>
                    <td>{jobTypeSummary.reportedStandardHours} + {jobTypeSummary.reportedOvertimeHours} hodin</td>
                    <td>{jobTypeSummary.chargedStandardHours} + {jobTypeSummary.chargedOvertimeHours} hodin</td>
                    <td class:difference-value-positive={getSummedUpChargedHours() - getSummedUpReportedHours() > 0}
                        class:difference-value-negative={getSummedUpChargedHours() - getSummedUpReportedHours() < 0}>

                        {getDifference(getSummedUpReportedHours(), getSummedUpChargedHours())} hodin
                    </td>
                </tr>
            </svelte:fragment>

            <tr slot="footer">
                <td>Celková cena</td>
                <td class="price-value">{pipe(jobTypeSummary.reportedPrice, priceFormat)}</td>
                <td class="price-value">{pipe(jobTypeSummary.chargedPrice, priceFormat)}</td>

                <td class="price-value"
                    class:difference-value-positive={jobTypeSummary.chargedPrice.amount - jobTypeSummary.reportedPrice.amount > 0}
                    class:difference-value-negative={jobTypeSummary.chargedPrice.amount - jobTypeSummary.reportedPrice.amount < 0}>

                    {getDifference(jobTypeSummary.reportedPrice.amount, jobTypeSummary.chargedPrice.amount)} Kč
                </td>
            </tr>
        </KpBarebonesTable>
    </svelte:fragment>
</ErpCard>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .difference-value-positive {
        color: var(--success-green);
    }

    .difference-value-negative {
        color: var(--danger-red);
    }

    .price-value {
        font-weight: 500;
        font-size: @font-size-default;
    }
</style>