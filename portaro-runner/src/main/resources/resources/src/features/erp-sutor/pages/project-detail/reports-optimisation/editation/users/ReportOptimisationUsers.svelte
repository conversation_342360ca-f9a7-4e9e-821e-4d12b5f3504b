<script lang="ts">
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import {getSutorReportsOptimisationContext} from '../../sutor-reports-optimisation-context';
    import {onDestroy} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {fly} from 'svelte/transition';
    import ReportOptimisationUsersTable from './ReportOptimisationUsersTable.svelte';
    import ReportOptimisationUsersUserDetail from './ReportOptimisationUsersUserDetail.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import ErpHorizontalTwoSplitLayout from 'src/features/erp/components/erp-split-layouts/ErpHorizontalTwoSplitLayout.svelte';

    export let optimisedInstallationLogbook: RecordRow;

    const context = getSutorReportsOptimisationContext();

    let openedUserReportRecord: RecordRow | null;
    const openedUserUnsubscribe = context.openedUser$.subscribe((currentOpenedUser) => openedUserReportRecord = currentOpenedUser);

    onDestroy(() => {
        openedUserUnsubscribe();
    });
</script>

<ErpHorizontalTwoSplitLayout pageClass="sutor-reports-optimisation-users-page" resizeDisabled>
    <svelte:fragment slot="left-panel">
        <ReportOptimisationUsersTable {optimisedInstallationLogbook}/>
    </svelte:fragment>

    <svelte:fragment slot="right-panel">
        {#key openedUserReportRecord}
            <div class="optimisation-user-detail"
                 class:no-user-report={!exists(openedUserReportRecord)}
                 in:fly={{y: 10, duration: 250}}>

                {#if !exists(openedUserReportRecord)}
                    <IconedContent align="center" justify="center" orientation="vertical" icon="info">
                        <span class="text-muted">Zde budou zobrazeny podrobnosti výkazu po rozkliknutí</span>
                    </IconedContent>
                {:else}
                    <ReportOptimisationUsersUserDetail reportRecord="{openedUserReportRecord}" {optimisedInstallationLogbook}/>
                {/if}
            </div>
        {/key}
    </svelte:fragment>
</ErpHorizontalTwoSplitLayout>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .optimisation-user-detail {
        display: flex;
        width: 100%;
        height: 100%;
        flex-direction: column;

        &.no-user-report {
            align-items: center;
            justify-content: center;
        }
    }
</style>