<script lang="ts">
    import type {HomepageLinkCardSection, SutorHomePageData} from 'src/features/erp-sutor/pages/homepage/types';
    import type {FondPermissions} from 'src/features/erp/services/fond-permissions.data-service';
    import {getInjector} from 'core/svelte-context/context';
    import {ErpFondPermissionsContextService} from 'src/features/erp/services/erp-fond-permissions-context.service';
    import {map} from 'rxjs/operators';
    import SutorHomePageIconCard from 'src/features/erp-sutor/pages/homepage/components/SutorHomePageIconCard.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let homepageData: SutorHomePageData;

    const fondPermissionsService = getInjector().getByClass(ErpFondPermissionsContextService);
    const filteredSections$ = fondPermissionsService.fondPermissions$.pipe(map((permissions) => filterSectionsByPermission(permissions, homepageData.linkCards.cardSections)));

    function filterSectionsByPermission(permissions: FondPermissions, cardSections: HomepageLinkCardSection[]): HomepageLinkCardSection[] {
        return cardSections.filter((cardSection) => {
            return cardSection.cards.some((card) => {
                return fondPermissionsService.hasFondPermissions(permissions, card.showPermissionFond, card.editPermissionFond);
            });
        }).map((cardSection) => {
            return {
                ...cardSection,
                cards: cardSection.cards.filter((card) => {
                    return fondPermissionsService.hasFondPermissions(permissions, card.showPermissionFond, card.editPermissionFond);
                })
            };
        });
    }
</script>

<div class="links-card-content">
    {#each $filteredSections$ as cardSection}
        <ul class="cards-group-container">
            {#each cardSection.cards as card}
                <li class="info-card-item">
                    <SutorHomePageIconCard href="{card.href}"
                                           icon="{card.icon}"
                                           label="{card.label}"
                                           value="{card.value}"
                                           iconBackground="{cardSection.color}"/>
                </li>
            {/each}
        </ul>
    {/each}

    {#if $filteredSections$.length === 0}
        <IconedContent icon="exclamation" orientation="vertical" align="center" justify="center" fillAvailableSpace>
            Nejsou zde žádné odkazy. Pravděpodobně nemáte dostatečná oprávnění.
        </IconedContent>
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .links-card-content {
        display: flex;
        flex-direction: column;
        gap: @spacing-l;
        flex-wrap: wrap;

        .cards-group-container {
            display: flex;
            flex-wrap: wrap;
            gap: @spacing-ml;
        }
    }
</style>