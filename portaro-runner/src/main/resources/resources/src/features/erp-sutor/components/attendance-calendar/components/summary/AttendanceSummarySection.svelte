<script lang="ts">
    import type {SummarySectionType} from 'src/features/erp-sutor/components/attendance-calendar/types';
    import {summaryTypeLabel} from 'src/features/erp-sutor/components/attendance-calendar/utils';
    import AttendanceCell from '../AttendanceCell.svelte';

    export let type: SummarySectionType;
</script>

<div class="attendance-summary-section {type}">
    <AttendanceCell header>{summaryTypeLabel(type)}</AttendanceCell>

    <div class="columns-container">
        <slot/>
    </div>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    @work-type-highlight: #EAEEFF;
    @overtime-type-highlight: #FFF4ED;
    @absence-type-highlight: #F3F1FF;
    @oncallduty-type-highlight: #F8F8F8;

    .attendance-summary-section {
        display: flex;
        flex-direction: column;
        border-radius: @border-radius-large;
        border: 1px solid @themed-border-default;
        flex: 1;

        .columns-container {
            display: flex;
        }

        &.work {
            background-color: @work-type-highlight;
        }

        &.overtime {
            background-color: @overtime-type-highlight;
        }

        &.absence {
            background-color: @absence-type-highlight;
            flex: 2;
        }

        &.on-call-duty {
            background-color: @oncallduty-type-highlight;
            flex: 0;
        }

        &.section-legend {
            border: none;
            background: none;
            flex: 0;
        }
    }

    :global {
        .attendance-summary-section {
            &.section-legend .attendance-calendar-cell {
                border-bottom: none;
                background: none;
                padding: 0;
                justify-content: start;

                &.header-cell {
                    border-bottom: none;
                }

                &:hover {
                    background: none;
                }
            }
        }
    }
</style>