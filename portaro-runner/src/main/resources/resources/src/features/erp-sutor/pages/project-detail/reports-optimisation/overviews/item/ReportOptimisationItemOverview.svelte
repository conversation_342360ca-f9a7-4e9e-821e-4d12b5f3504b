<script lang="ts">
    import type {CalendarDate} from 'shared/components/kp-calendar/types';
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import type {DayOptimisationOverviewResponse, GroupedProjectItemsResponse} from 'src/features/erp-sutor/pages/project-detail/reports-optimisation/types';
    import type {SimpleFond} from 'typings/portaro.be.types';
    import {calendarDateFromISODate, calendarDateFromLuxon, getTodayCalendarDate} from 'shared/components/kp-calendar/utils';
    import {getSutorReportsOptimisationContext} from 'src/features/erp-sutor/pages/project-detail/reports-optimisation/sutor-reports-optimisation-context';
    import {getFirstFieldByFieldTypeIdRecursive} from 'src/features/record-grid/lib/grid-fields';
    import {getDateFromGridFieldValue} from 'src/features/erp-sutor/sutor-utils';
    import {get} from 'svelte/store';
    import {onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {FOND_PROJECT_BUSINESS_ITEM} from 'src/features/erp-sutor/sutor-fonds';
    import OptimisationOverviewContainer from '../components/OptimisationOverviewContainer.svelte';
    import OptimisationOverviewHeading from '../components/OptimisationOverviewHeading.svelte';
    import DayOverviewJobTypeCard from '../components/DayOverviewJobTypeCard.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpLoadableContainer from 'shared/layouts/containers/KpLoadableContainer.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import KpStyledSelect from 'shared/ui-widgets/select/KpStyledSelect.svelte';
    import ErpRowCalendar from 'src/features/erp/components/erp-row-calendar/ErpRowCalendar.svelte';
    import InstallationLogbookSelector from 'src/features/erp-sutor/pages/project-detail/reports-optimisation/overviews/components/InstallationLogbookSelector.svelte';
    import ProjectItemSelector from 'src/features/erp-sutor/pages/project-detail/reports-optimisation/overviews/components/ProjectItemSelector.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';

    const context = getSutorReportsOptimisationContext();
    const openOverviewProjectItem = get(context.openOverviewSignal$);

    let loadingProjectItems = true;
    let groupedProjectItems: GroupedProjectItemsResponse | null = null;

    let selectedFond: SimpleFond | null = null;
    let selectedDate = getSelectedDate(openOverviewProjectItem);
    let selectedProjectItem = openOverviewProjectItem;

    let loadingOptimisationOverview = true;
    let dayOptimisationOverviewResponse: DayOptimisationOverviewResponse | null;

    onMount(async () => {
        if (!exists(openOverviewProjectItem)) {
            const loadedGroupedProjectItems = await context.service.getGroupedProjectItems(context.projectRecord);

            if (exists(loadedGroupedProjectItems)) {
                groupedProjectItems = loadedGroupedProjectItems;
                selectedFond = groupedProjectItems.uniqueItemFonds.find((fond) => fond.id === FOND_PROJECT_BUSINESS_ITEM.INSTALLATION_ITEM.fond) ?? groupedProjectItems.uniqueItemFonds[0];
            }
        }

        if (exists(openOverviewProjectItem)) {
            selectedFond = openOverviewProjectItem.fond;
        }

        loadingProjectItems = false;

        if (!exists(selectedProjectItem)) {
            loadingOptimisationOverview = false;
            return;
        }

        await loadItemOptimisationOverview(selectedProjectItem);
    });

    function getSelectedDate(installationLogbook: RecordRow): CalendarDate {
        const dateField = exists(installationLogbook) ? getFirstFieldByFieldTypeIdRecursive(installationLogbook, FOND_PROJECT_BUSINESS_ITEM.INSTALLATION_ITEM.dateFieldId) : null;
        const date = getDateFromGridFieldValue(dateField);

        if (!exists(date)) {
            return getTodayCalendarDate();
        }

        return calendarDateFromLuxon(date);
    }

    async function loadItemOptimisationOverview(projectItem: RecordRow) {
        if (!exists(projectItem)) {
            return;
        }

        loadingOptimisationOverview = true;
        const projectRecord = context.projectRecord;
        dayOptimisationOverviewResponse = await context.service.getItemOverview(projectRecord, projectItem);
        loadingOptimisationOverview = false;
    }

    function getHeadingText(installationLogbook: RecordRow): string {
        if (!exists(installationLogbook)) {
            return 'Přehled';
        }

        return `Přehled pro ${installationLogbook.name}`;
    }

    const handleDateSelected = (event: CustomEvent<CalendarDate>) => {
        selectedDate = event.detail;
        selectedProjectItem = null;
        dayOptimisationOverviewResponse = null;
    };

    const handleProjectItemSelected = async (event: CustomEvent<RecordRow | null>) => {
        selectedProjectItem = event.detail;
        dayOptimisationOverviewResponse = null;
        await loadItemOptimisationOverview(selectedProjectItem);
    };

    const handleFondChange = () => {
        selectedProjectItem = null;
        dayOptimisationOverviewResponse = null;
    };

    function getHighlightedDates(grouped: Record<string, RecordRow[]>): CalendarDate[] {
        return Object.keys(grouped).map((date) => calendarDateFromISODate(date));
    }

    function getStartDate(grouped: Record<string, RecordRow[]>): CalendarDate {
        const dates = Object.keys(grouped).map((date) => calendarDateFromISODate(date));
        return dates.toSorted((a, b) => a.year - b.year || a.month - b.month || a.day - b.day)[0] ?? getTodayCalendarDate();
    }

    function filterItemsByFond(items: RecordRow[], fond: SimpleFond): RecordRow[] {
        return items.filter((item) => item.fond.id === fond.id);
    }

    function getPrintButtonLabel(item: RecordRow): string {
        if (item.fond.id === FOND_PROJECT_BUSINESS_ITEM.INSTALLATION_ITEM.fond) {
            return 'Tisk montážního deníku';
        }

        return 'Tisk položky';
    }
</script>

<OptimisationOverviewContainer>
    {#if !exists(openOverviewProjectItem)}
        <KpLoadableContainer fillAvailableSpace="{loadingProjectItems || !exists(selectedFond)}"
                             loading="{loadingProjectItems}"
                             loadError="{!loadingProjectItems && !exists(groupedProjectItems)}">

            <Flex direction="column" gap="sm" alignItems="flex-start">
                <label class="text-muted" for="project-item-selector">
                    <small>Fond položek</small>
                </label>

                <KpStyledSelect id="project-item-selector" bind:value={selectedFond} on:change={handleFondChange}>
                    {#each groupedProjectItems.uniqueItemFonds as fond(fond.id)}
                        <option value={fond}>
                            {pipe(fond, loc())}
                        </option>
                    {/each}

                    {#if groupedProjectItems.uniqueItemFonds.length === 0}
                        <option value={null} selected>
                            Neexistují žádné položky
                        </option>
                    {/if}
                </KpStyledSelect>
            </Flex>

            <Spacer direction="vertical" size="xl"/>

            {#if exists(selectedFond)}
                <Flex direction="column" gap="xl">
                    {#if selectedFond.id === FOND_PROJECT_BUSINESS_ITEM.INSTALLATION_ITEM.fond}
                        <ErpRowCalendar startDate="{getStartDate(groupedProjectItems.groupedInstallationLogbooks)}"
                                        selectedDate="{selectedDate}"
                                        highlightedDates="{getHighlightedDates(groupedProjectItems.groupedInstallationLogbooks)}"
                                        on:date-selected={handleDateSelected}/>

                        <InstallationLogbookSelector date="{selectedDate}"
                                                     selectedInstallationLogbook="{selectedProjectItem}"
                                                     groupedInstallationLogbooks="{groupedProjectItems.groupedInstallationLogbooks}"
                                                     on:logbook-selected={handleProjectItemSelected}/>
                    {:else}
                        <ProjectItemSelector projectItems="{filterItemsByFond(groupedProjectItems.itemsWithoutDate, selectedFond)}"
                                             selectedProjectItem="{selectedProjectItem}"
                                             fond="{selectedFond}"
                                             on:project-item-selected={handleProjectItemSelected}/>
                    {/if}
                </Flex>
            {:else}
                <IconedContent icon="info" orientation="vertical" fillAvailableSpace>
                    <span class="load-error-label">Nejdříve vyberte fond položek</span>
                </IconedContent>
            {/if}
        </KpLoadableContainer>
    {/if}

    {#if !exists(selectedProjectItem)}
        {#if !loadingProjectItems && exists(selectedFond)}
            <IconedContent icon="info" orientation="vertical" fillAvailableSpace>
                <span class="load-error-label">Nejdříve vyberte {selectedFond?.id === FOND_PROJECT_BUSINESS_ITEM.INSTALLATION_ITEM.fond ? 'montážní deník' : 'položku'}</span>
            </IconedContent>
        {/if}
    {:else}
        <OptimisationOverviewHeading heading="{getHeadingText(selectedProjectItem)}"
                                     printDisabled="{!exists(selectedProjectItem)}"
                                     printButtonLabel="{getPrintButtonLabel(selectedProjectItem)}"/>

        <KpLoadableContainer fillAvailableSpace loading="{loadingOptimisationOverview}" loadError="{!loadingOptimisationOverview && !exists(dayOptimisationOverviewResponse)}">
            <Flex direction="column" gap="xxl" fillAvailableSpace>
                {#if dayOptimisationOverviewResponse.jobTypeSummaries.length === 0}
                    <IconedContent icon="info" orientation="vertical" fillAvailableSpace>
                        <span class="load-error-label">Nejsou k dispozici žádná data</span>
                    </IconedContent>
                {:else}
                    <Flex class="job-type-summaries-container" direction="column" gap="xl">
                        {#each dayOptimisationOverviewResponse.jobTypeSummaries as jobTypeSummary}
                            <DayOverviewJobTypeCard {jobTypeSummary}/>
                        {/each}
                    </Flex>
                {/if}
            </Flex>
        </KpLoadableContainer>
    {/if}
</OptimisationOverviewContainer>