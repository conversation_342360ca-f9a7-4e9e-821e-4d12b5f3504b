<script lang="ts">
    import type {ChipSize} from 'shared/ui-widgets/chip/types';
    import {getFondColor} from 'shared/utils/color-util';
    import KpChipTag from 'shared/ui-widgets/chip/KpChipTag.svelte';

    export let chipSize: ChipSize = 'md';
    export let fondId: number;
    export let fondName: string | null = null;

    const textColor = getFondColor(fondId, 12);
    const backgroundColor = getFondColor(fondId, 85);
</script>

<KpChipTag {chipSize}
           chipTitle={fondName}
           {textColor}
           {backgroundColor}
           additionalClasses="sutor-fond-tag">

    <slot/>
</KpChipTag>

<style lang="less">
    :global {
        .sutor-fond-tag {
            display: inline-flex;
            align-items: center;
            border: 1px solid rgba(0, 0, 0, 0.125);
            outline: 1px solid transparent;
            outline-offset: 2px;
            transition: opacity 0.3s ease-in-out, outline-color 0.3s ease-in-out;

            a {
                color: var(--chip-text-color);
            }
        }
    }
</style>