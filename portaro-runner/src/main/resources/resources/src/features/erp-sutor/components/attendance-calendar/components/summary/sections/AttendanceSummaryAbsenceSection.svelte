<script lang="ts">
    import type {AbsenceSummary} from 'src/features/erp-sutor/components/attendance-calendar/types';
    import {AbsenceReason} from 'src/features/erp-sutor/components/attendance-calendar/types';
    import {SummarySectionType} from 'src/features/erp-sutor/components/attendance-calendar/types';
    import {absenceReasonToAbbr} from 'src/features/erp-sutor/components/attendance-calendar/utils';
    import AttendanceSummarySection from '../AttendanceSummarySection.svelte';
    import AttendanceCell from '../../AttendanceCell.svelte';
    import AttendanceSummaryColumn from '../AttendanceSummaryColumn.svelte';

    export let absence: AbsenceSummary;
</script>

<AttendanceSummarySection type="{SummarySectionType.ABSENCE}">
    <AttendanceSummaryColumn>
        <AttendanceCell header>{absenceReasonToAbbr(AbsenceReason.DOCTOR_VISIT)}</AttendanceCell>
        <AttendanceCell durationItem="{absence.costsSourceDoctor}"/>
        <AttendanceCell durationItem="{absence.attendanceSourceDoctor}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>{absenceReasonToAbbr(AbsenceReason.VACATION)}</AttendanceCell>
        <AttendanceCell durationItem="{absence.costsSourceVacation}"/>
        <AttendanceCell durationItem="{absence.attendanceSourceVacation}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>{absenceReasonToAbbr(AbsenceReason.ILLNESS)}</AttendanceCell>
        <AttendanceCell durationItem="{absence.costsSourceIllness}"/>
        <AttendanceCell durationItem="{absence.attendanceSourceIllness}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>{absenceReasonToAbbr(AbsenceReason.OCR)}</AttendanceCell>
        <AttendanceCell durationItem="{absence.costsSourceOcr}"/>
        <AttendanceCell durationItem="{absence.attendanceSourceOcr}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>{absenceReasonToAbbr(AbsenceReason.PARAGRAPH)}</AttendanceCell>
        <AttendanceCell durationItem="{absence.costsSourceParagraph}"/>
        <AttendanceCell durationItem="{absence.attendanceSourceParagraph}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>{absenceReasonToAbbr(AbsenceReason.COMPENSATION)}</AttendanceCell>
        <AttendanceCell durationItem="{absence.costsSourceCompensatory}"/>
        <AttendanceCell durationItem="{absence.attendanceSourceCompensatory}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>{absenceReasonToAbbr(AbsenceReason.UNPAID)}</AttendanceCell>
        <AttendanceCell durationItem="{absence.costsSourceUnpaid}"/>
        <AttendanceCell durationItem="{absence.attendanceSourceUnpaid}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>{absenceReasonToAbbr(AbsenceReason.UNEXCUSED)}</AttendanceCell>
        <AttendanceCell durationItem="{absence.costsSourceUnexcused}"/>
        <AttendanceCell durationItem="{absence.attendanceSourceUnexcused}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>{absenceReasonToAbbr(AbsenceReason.MATERNITY)}</AttendanceCell>
        <AttendanceCell durationItem="{absence.costsSourceMaternity}"/>
        <AttendanceCell durationItem="{absence.attendanceSourceMaternity}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>{absenceReasonToAbbr(AbsenceReason.PATERNITY)}</AttendanceCell>
        <AttendanceCell durationItem="{absence.costsSourcePaternity}"/>
        <AttendanceCell durationItem="{absence.attendanceSourcePaternity}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>{absenceReasonToAbbr(AbsenceReason.EMPLOYER_OBSTACLES)}</AttendanceCell>
        <AttendanceCell durationItem="{absence.costsSourceObstacles}"/>
        <AttendanceCell durationItem="{absence.attendanceSourceObstacles}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>{absenceReasonToAbbr(AbsenceReason.CHILD_CAMP)}</AttendanceCell>
        <AttendanceCell durationItem="{absence.costsSourceChildrenAndYouth}"/>
        <AttendanceCell durationItem="{absence.attendanceSourceChildrenAndYouth}"/>
    </AttendanceSummaryColumn>

    <AttendanceSummaryColumn>
        <AttendanceCell header>Celk.</AttendanceCell>
        <AttendanceCell durationItem="{absence.costsSourceSum}"/>
        <AttendanceCell durationItem="{absence.attendanceSourceSum}"/>
    </AttendanceSummaryColumn>
</AttendanceSummarySection>