<script lang="ts">
    import {getPageContext} from 'shared/layouts/page-context';
    import KpMasonryGridItem from 'shared/layouts/masonry-grid/KpMasonryGridItem.svelte';
    import KpMasonryGrid from 'shared/layouts/masonry-grid/KpMasonryGrid.svelte';
    import KpGenericPanel from 'shared/ui-widgets/panel/KpGenericPanel.svelte';
    import KpCustomParagraph from 'shared/value-editors/internal/editors/search-or-edit/templates/KpCustomParagraph.svelte';
    import type {
        DocumentDetailReactivePageData,
        DocumentDetailStaticPageData
    } from 'src/features/record/kp-document-detail-page/types';

    const pageContext = getPageContext<DocumentDetailStaticPageData, DocumentDetailReactivePageData>();
    const model = pageContext.staticData.model;
</script>

<KpMasonryGrid gridTemplateColumns="repeat(auto-fill, minmax(600px, 1fr))"
               additionalClasses="project-detail-grid">

    <KpMasonryGridItem animate="{false}">
        <KpGenericPanel hasBodyPadding="{false}">
            <KpCustomParagraph content="{model.nextToCoverParagraph}"
                               customClass="paragraph-next-to-cover sutor-record-info-paragraph"/>
        </KpGenericPanel>
    </KpMasonryGridItem>
</KpMasonryGrid>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    :global {
        .project-detail-grid .sutor-record-info-paragraph {
            display: flex;
            flex-direction: column;

            .record-info-row {
                padding: 8px @spacing-m;
                display: flex;
                align-items: center;
                justify-content: space-between;
                border-bottom: 1px solid @themed-border-default;

                &.title {
                    font-weight: 500;
                }

                &:empty {
                    display: none;
                }

                &:last-child {
                    border-bottom: none;
                }
            }
        }
    }

    :global {
        .project-detail-grid .marc-table {
            tr > th {
                padding: @spacing-sm @spacing-ml;
                font-weight: 500;
            }

            &.table-color-accented > thead > tr > th {
                color: @themed-text-default;
                border-bottom: 1px solid @themed-border-default;
            }
        }
    }
</style>