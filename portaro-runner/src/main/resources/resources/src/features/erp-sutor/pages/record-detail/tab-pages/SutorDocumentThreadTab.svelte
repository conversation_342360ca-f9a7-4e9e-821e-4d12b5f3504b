<script lang="ts">
    import type {DocumentDetailReactivePageData, DocumentDetailStaticPageData} from 'src/features/record/kp-document-detail-page/types';
    import type {Thread} from 'src/features/threads/threads.types';
    import {getInjector} from 'core/svelte-context/context';
    import {ThreadsService} from 'src/features/threads/services/threads.service';
    import {onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {getPageContext} from 'shared/layouts/page-context';
    import ThreadContent from 'src/features/threads/thread-content/ThreadContent.svelte';
    import KpLoadableContainer from 'shared/layouts/containers/KpLoadableContainer.svelte';

    const threadService = getInjector().getByClass(ThreadsService);
    const pageContext = getPageContext<DocumentDetailStaticPageData, DocumentDetailReactivePageData>();
    const record = pageContext.staticData.model.record;

    let thread: Thread;
    let loading = true;
    let loadError = false;

    onMount(async () => {
        let loadedThread = await threadService.getThreadByRecord(record);

        if (!exists(loadedThread)) {
            loadedThread = await threadService.createRecordThread(record);
            loading = false;
        }

        if (!exists(loadedThread)) {
            loading = false;
            loadError = true;
        }

        thread = loadedThread;
        loading = false;
    });
</script>

<KpLoadableContainer fillAvailableSpace {loading} {loadError}>
    <ThreadContent {thread} placement="fullscreen-page"/>
</KpLoadableContainer>