import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export function dataViewRoutes($stateProvider: StateProvider): void {
    let dataViewModule: { default: any; };

    window.customDataViewPagesConfiguration.definedDataViewPages.forEach((definedDataViewPage) => {
        $stateProvider
            .state({
                name: `data-view-${definedDataViewPage.id}`,
                url: `/data-view/${definedDataViewPage.route}?activeTab`,
                params: {activeTab: {dynamic: true}},
                component: KpSvelteComponentWrapperComponent.componentName,
                resolve: {
                    component: () => dataViewModule.default,
                    props: () => ({configuration: definedDataViewPage})
                },
                lazyLoad: async () => {
                    dataViewModule = await import(/* webpackChunkName: "data-view" */ './DataViewPage.svelte');
                    return null;
                }
            });
    });
}