<script lang="ts">
    import {getInjector} from 'core/svelte-context/context';
    import {KpDepartmentsService} from 'src/features/departments/services/kp-departments.service';
    import {getDataViewEventContext} from 'src/features/erp-sutor/pages/data-view/data-view.event-context';
    import {REFRESH_DATA_VIEW_GRID_EVENT} from 'src/features/erp-sutor/pages/data-view/types';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    const departmentsService = getInjector().getByClass(KpDepartmentsService);
    const eventContext = getDataViewEventContext();

    const handleCreateDivision = async () => {
        await departmentsService.createDepartment();
        eventContext.dispatchEvent(new CustomEvent(REFRESH_DATA_VIEW_GRID_EVENT));
    };
</script>

<KpButton buttonStyle="success-new" on:click={handleCreateDivision}>
    <IconedContent icon="add">
        Vytvořit novou divizi
    </IconedContent>
</KpButton>