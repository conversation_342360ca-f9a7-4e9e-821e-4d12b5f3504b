import type {Rec} from 'typings/portaro.be.types';
import type {RecordRow} from 'src/features/record-grid/lib/types';
import type {AjaxService} from 'core/data-services/ajax.service';
import {transferify} from 'shared/utils/data-service-utils';
import type {
    DayOptimisationOverviewResponse,
    OptimisationSumsResponse,
    OverallOptimisationOverviewResponse,
    GroupedProjectItemsResponse
} from 'src/features/erp-sutor/pages/project-detail/reports-optimisation/types';

export class SutorReportsOptimisationDataService {
    public static serviceName = 'sutorReportsOptimisationDataService';

    public static readonly REPORT_FINANCIAL_SUMS_ROUTE = 'report-optimisation/financial-sums';
    public static readonly OVERALL_OVERVIEW_ROUTE = 'report-optimisation/overview/overall';
    public static readonly ITEM_OVERVIEW_ROUTE = 'report-optimisation/overview/item';
    public static readonly GROUPED_PROJECT_ITEMS_ROUTE = 'report-optimisation/grouped-project-items';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    public async getReportSums(project: Rec): Promise<OptimisationSumsResponse> {
        return this.ajaxService
            .createRequest(SutorReportsOptimisationDataService.REPORT_FINANCIAL_SUMS_ROUTE)
            .get(transferify({project}));
    }

    public async getOverallOverview(project: Rec): Promise<OverallOptimisationOverviewResponse> {
        return this.ajaxService
            .createRequest(SutorReportsOptimisationDataService.OVERALL_OVERVIEW_ROUTE)
            .get(transferify({project}));
    }

    public async getItemOverview(project: Rec, projectItem: RecordRow): Promise<DayOptimisationOverviewResponse> {
        return this.ajaxService
            .createRequest(SutorReportsOptimisationDataService.ITEM_OVERVIEW_ROUTE)
            .get(transferify({project, item: projectItem}));
    }

    public async getGroupedProjectItems(project: Rec): Promise<GroupedProjectItemsResponse> {
        return this.ajaxService
            .createRequest(SutorReportsOptimisationDataService.GROUPED_PROJECT_ITEMS_ROUTE)
            .get(transferify({project}));
    }
}