<script lang="ts">
    import type {Row} from '@tanstack/svelte-table';
    import type {HierarchicalRecordRow} from 'src/features/record-grid/lib/types';
    import type {RecordGridDataManager} from 'src/features/record-grid/lib/record-grid-data-manager';
    import type {Readable} from 'svelte/store';
    import type {UUID} from 'typings/portaro.be.types';
    import {getSutorReportsOptimisationContext} from 'src/features/erp-sutor/pages/project-detail/reports-optimisation/sutor-reports-optimisation-context';
    import {readable} from 'svelte/store';
    import {FOND_PROJECT_BUSINESS_ITEM} from 'src/features/erp-sutor/sutor-fonds';
    import ControlCell from 'src/features/record-grid/components/cells/ControlCell.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';

    export let recordGridDataManager: RecordGridDataManager;
    export let row: Row<HierarchicalRecordRow>;
    export let rowExpansionEnabled = false;
    export let expandButtonHidden = true;
    export let rowsWithSubrowsLoading$: Readable<UUID[]> = readable([]);

    const context = getSutorReportsOptimisationContext();

    const handleOptimiseClick = () => {
        context.setOptimisedInstallationLogbookRecord(row.original);
    };

    const handleOpenOverviewClick = () => {
        context.setOpenOverviewSignal(row.original);
    };
</script>

<ControlCell {recordGridDataManager} {row} {rowExpansionEnabled} {expandButtonHidden} {rowsWithSubrowsLoading$}>
    <Spacer flex="1"/>

    {#if FOND_PROJECT_BUSINESS_ITEM.subfonds.includes(row.original.fond.id)}
        {#if context.projectType === 'reported' && FOND_PROJECT_BUSINESS_ITEM.INSTALLATION_ITEM.fond === row.original.fond.id}
            <KpButton buttonSize="xs" buttonStyle="brand-orange-new" on:click={handleOptimiseClick}>
                <IconedContent icon="user-add">Optimalizovat</IconedContent>
            </KpButton>
        {/if}

        <KpButton buttonSize="xs" on:click={handleOpenOverviewClick}>
            <IconedContent icon="chart-histogram">Přehled</IconedContent>
        </KpButton>
    {/if}
</ControlCell>