<script lang="ts">
    import type {DayAttendance} from '../types';
    import {DateTime} from 'luxon';
    import {exists} from 'shared/utils/custom-utils';
    import AttendanceCell from './AttendanceCell.svelte';

    export let attendanceDays: DayAttendance[];
    export let highlightedDate: DateTime | null = null;

    const today = DateTime.now();
</script>

<div class="attendance-calendar-container">
    <div class="calendar-column">
        <AttendanceCell header>Typ</AttendanceCell>
        <AttendanceCell>Výkaz</AttendanceCell>
        <AttendanceCell>Výkaz</AttendanceCell>
        <AttendanceCell>Výkaz</AttendanceCell>
        <AttendanceCell>Výkaz</AttendanceCell>
        <AttendanceCell>Výkaz</AttendanceCell>
        <AttendanceCell topBordered>PwK</AttendanceCell>
        <AttendanceCell>PwK</AttendanceCell>
        <AttendanceCell>PwK</AttendanceCell>
    </div>

    <div class="calendar-column">
        <AttendanceCell header>Den</AttendanceCell>
        <AttendanceCell>Práce</AttendanceCell>
        <AttendanceCell>Přesčas</AttendanceCell>
        <AttendanceCell>Absence</AttendanceCell>
        <AttendanceCell>Pohotovost</AttendanceCell>
        <AttendanceCell>Suma</AttendanceCell>
        <AttendanceCell topBordered>Práce</AttendanceCell>
        <AttendanceCell>Přesčas</AttendanceCell>
        <AttendanceCell>Absence</AttendanceCell>
    </div>

    {#each attendanceDays as day}
        {@const dayDateTime = DateTime.fromISO(day.date.value)}

        <div class="calendar-column flex-grow"
             class:day-saturday={day.date.labels.includes('SATURDAY')}
             class:day-sunday={day.date.labels.includes('SUNDAY')}
             class:day-holiday={day.date.labels.includes('HOLIDAY')}
             class:day-highlighted={exists(highlightedDate) && dayDateTime.hasSame(highlightedDate, 'day')}>

            <AttendanceCell header>
                <div class="date-container">
                    {dayDateTime.day}.{dayDateTime.month}.
                    {#if !dayDateTime.hasSame(today, 'year')}
                        <span class="year">{dayDateTime.year}</span>
                    {/if}
                </div>
            </AttendanceCell>
            <AttendanceCell durationItem={day.costsSourceRegularWork}/>
            <AttendanceCell durationItem={day.costsSourceOvertimeWork}/>
            <AttendanceCell durationItem={day.costsSourceAbsence}/>
            <AttendanceCell durationItem={day.costsSourceOnCallDuty}/>
            <AttendanceCell durationItem={day.costsSourceSum}/>
            <AttendanceCell topBordered durationItem={day.attendanceSourceRegularWork}/>
            <AttendanceCell durationItem={day.attendanceSourceOvertimeWork}/>
            <AttendanceCell durationItem={day.attendanceSourceAbsence}/>
        </div>
    {/each}
</div>

<style lang="less">
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.variables.less";

    @saturday-highlight: #FFF7E2;
    @sunday-highlight: #FFECEF;
    @holiday-highlight: #F3F1FF;
    @current-highlighted: @themed-body-bg-orange-highlighted;

    .attendance-calendar-container {
        width: 100%;
        border-radius: @border-radius-large;
        border: 1px solid @themed-border-default;
        background-color: @themed-border-default;
        display: flex;
        gap: 1px;

        .calendar-column {
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 1px;
            background-color: @themed-body-bg;

            &:first-child {
                border-top-left-radius: @border-radius-large;
                border-bottom-left-radius: @border-radius-large;
                overflow: hidden;
            }

            &:last-child {
                border-top-right-radius: @border-radius-large;
                border-bottom-right-radius: @border-radius-large;
                overflow: hidden;
            }

            &.flex-grow {
                flex-grow: 1;
            }

            &.day-saturday {
                background-color: @saturday-highlight;
            }

            &.day-sunday {
                background-color: @sunday-highlight;
            }

            &.day-holiday {
                background-color: @holiday-highlight;
            }

            &.day-highlighted {
                background-color: @current-highlighted;
                outline: 2px solid var(--brand-orange-new);
                outline-offset: 2px;
            }

            .date-container {
                display: flex;
                flex-direction: column;
                align-items: center;

                .year {
                    color: @themed-text-muted;
                    font-size: @font-size-xs;
                    font-weight: 400;
                }
            }
        }
    }

    :global {
        .attendance-calendar-container > .calendar-column > .attendance-calendar-cell.header-cell {
            background-color: @themed-body-bg-blue-highlighted;
        }
    }
</style>