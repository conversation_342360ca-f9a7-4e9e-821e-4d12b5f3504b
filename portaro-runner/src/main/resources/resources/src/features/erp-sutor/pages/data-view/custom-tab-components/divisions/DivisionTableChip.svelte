<script lang="ts">
    import type {ViewableDepartment} from 'typings/portaro.fe.types';
    import type {Rec} from 'typings/portaro.be.types';
    import {onMount} from 'svelte';
    import {getInjector} from 'core/svelte-context/context';
    import {exists} from 'shared/utils/custom-utils';
    import RecordService from 'src/features/record/record.service';
    import GridFieldValue from 'shared/ui-widgets/grid/GridFieldValue.svelte';
    import KpLoadingInline from 'shared/components/kp-loading/KpLoadingInline.svelte';

    export let department: ViewableDepartment;

    const recordService = getInjector().getByClass(RecordService);

    let loading = true;
    let record: Rec | null = null;

    onMount(async () => {
        if (!exists(department.rid)) {
            loading = false;
            return;
        }

        record = await recordService.getById(department.rid);
        loading = false;
    });
</script>

{#if loading}
    <KpLoadingInline size="xs"/>
{/if}

{#if exists(record) && !loading}
    <GridFieldValue fondId="{record.fond.id}"
                    href="/#!/records/{record.id}"
                    value="{department.name} ({department.text})"/>
{/if}

{#if !exists(record) && !loading}
    <span>{department.name} ({department.text})</span>
{/if}
