<script lang="ts">
    import type {CalendarDate} from 'shared/components/kp-calendar/types';
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import {createEventDispatcher} from 'svelte';
    import {getDateFormatter} from 'core/svelte-context/context';
    import {luxonDateFromCalendarDate} from 'shared/components/kp-calendar/utils';
    import {exists} from 'shared/utils/custom-utils';
    import {pipe} from 'core/utils';
    import {fade} from 'svelte/transition';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import KpLoadableContainer from 'shared/layouts/containers/KpLoadableContainer.svelte';

    export let date: CalendarDate;
    export let groupedInstallationLogbooks: Record<string, RecordRow[]>;
    export let selectedInstallationLogbook: RecordRow | null = null;

    const dateFormatter = getDateFormatter();

    let loadedInstallationLogbooks: RecordRow[] | null = null;
    let lastDate: CalendarDate | null = null;
    $: if (lastDate !== date) {
        dispatch('logbook-selected', null);
        lastDate = date;
        loadedInstallationLogbooks = null;
        getInstallationLogbooksForDate(date);
    }

    const dispatch = createEventDispatcher<{'logbook-selected': RecordRow | null}>();

    function getInstallationLogbooksForDate(currentDate: CalendarDate) {
        loadedInstallationLogbooks = groupedInstallationLogbooks[luxonDateFromCalendarDate(currentDate).toISODate()] ?? [];
    }
</script>

{#if exists(loadedInstallationLogbooks)}
    <Flex direction="column" gap="sm">
        <small class="text-muted">Montážní deníky pro {pipe(luxonDateFromCalendarDate(date).toString(), dateFormatter('d.M.yyyy'))}</small>

        <KpLoadableContainer loading="{!exists(loadedInstallationLogbooks)}">
            {#key loadedInstallationLogbooks}
                <div class="anim-container" in:fade={{duration: 250}}>
                    <Flex alignItems="center" gap="s" wrap="wrap">
                        {#each loadedInstallationLogbooks as installationLogbook(installationLogbook.id)}
                            <KpButton buttonStyle={selectedInstallationLogbook?.id === installationLogbook.id ? 'brand-orange-new' : 'default'}
                                      on:click={() => dispatch('logbook-selected', installationLogbook)}>

                                {installationLogbook.name}
                            </KpButton>
                        {/each}
                    </Flex>
                </div>
            {/key}

            {#if loadedInstallationLogbooks.length === 0}
                <span class="text-muted">V tento den neexistuje žádný montážní deník</span>
            {/if}
        </KpLoadableContainer>
    </Flex>
{/if}