<script lang="ts">
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import type {SimpleFond} from 'typings/portaro.be.types';
    import {createEventDispatcher} from 'svelte';
    import {fade} from 'svelte/transition';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    export let projectItems: RecordRow[];
    export let selectedProjectItem: RecordRow | null = null;
    export let fond: SimpleFond;

    const dispatch = createEventDispatcher<{'project-item-selected': RecordRow | null}>();
</script>

<Flex direction="column" gap="sm">
    <small class="text-muted">{pipe(fond, loc())}</small>

    {#key projectItems}
        <div class="anim-container" in:fade={{duration: 250}}>
            <Flex alignItems="center" gap="s" wrap="wrap">
                {#each projectItems as projectItem(projectItem.id)}
                    <KpButton buttonStyle={selectedProjectItem?.id === projectItem.id ? 'brand-orange-new' : 'default'}
                              on:click={() => dispatch('project-item-selected', projectItem)}>

                        {projectItem.name}
                    </KpButton>
                {/each}
            </Flex>
        </div>
    {/key}

    {#if projectItems.length === 0}
        <span class="text-muted">V tento den neexistují žádné položky</span>
    {/if}
</Flex>