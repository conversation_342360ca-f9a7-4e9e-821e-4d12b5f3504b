<script lang="ts">
    import type {TabButton, TabPageDefinitionsMap} from 'shared/ui-widgets/tabset/types';
    import ErpTabbedSubpagesContainer from 'src/features/erp/components/erp-tabbed-subpages/ErpTabbedSubpagesContainer.svelte';
    import ProjectDetailFinancialOverviewTab from './tab-pages/ProjectDetailFinancialOverviewTab.svelte';
    import ProjectDetailInfoTab from 'src/features/erp-sutor/pages/project-detail/tab-pages/ProjectDetailInfoTab.svelte';
    import ProjectDetailDebugTab from 'src/features/erp-sutor/pages/project-detail/tab-pages/ProjectDetailDebugTab.svelte';
    import TabbedSubpagesRouter from 'src/features/erp/components/erp-tabbed-subpages/TabbedSubpagesRouter.svelte';

    const tabButtons: TabButton[] = [
        {
            id: 'tab-info',
            label: 'Základní informace',
            icon: 'info'
        },
        {
            id: 'tab-debug',
            label: 'Debug',
            icon: 'bug'
        },
        {
            id: 'tab-reported-work',
            label: 'Hospodařen<PERSON> zakázky',
            icon: 'edit'
        }
    ];

    const tabs: TabPageDefinitionsMap = {
        'tab-info': {component: ProjectDetailInfoTab},
        'tab-reported-work': {component: ProjectDetailFinancialOverviewTab},
        'tab-debug': {component: ProjectDetailDebugTab}
    };
</script>

<ErpTabbedSubpagesContainer {tabButtons}
                            withoutUrlManagement
                            additionalClasses="sutor-project-detail"
                            let:activeTab>

    <TabbedSubpagesRouter {tabs} {activeTab}/>
</ErpTabbedSubpagesContainer>