<script lang="ts">
    import type {UIcons} from 'shared/ui-widgets/uicons/types';
    import {exists} from 'shared/utils/custom-utils';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let icon: UIcons;
    export let label: string;
    export let value: string | number | null = null;
    export let iconBackground: string;
    export let href: string | null = null;

    function getElementProps(isAnchor: boolean): Record<string, any> {
        if (isAnchor) {
            return {href};
        }

        return {type: 'button'};
    }
</script>

<svelte:element this="{exists(href) ? 'a' : 'button'}"
                {...getElementProps(exists(href))}
                role="button"
                tabindex="0"
                class="sutor-landing-icon-card"
                style:--icon-bg="{iconBackground}"
                on:click>

    <div class="icon-label-container">
        <div class="icon-container">
            <UIcon {icon}/>
        </div>

        <span class="card-label">{label}</span>
    </div>

    <span class="card-value">{value ?? ''}</span>
</svelte:element>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .sutor-landing-icon-card {
        border-radius: @border-radius-large;
        padding: @spacing-m @spacing-ml @spacing-m @spacing-m;
        display: flex;
        align-items: center;
        border: 1px solid @themed-border-muted;
        background-color: @themed-panel-bg;
        color: @themed-text-default;
        outline: 2px solid transparent;
        outline-offset: 2px;
        gap: @spacing-ml;
        transition: outline-color 0.3s ease-in-out, opacity 0.3s ease-in-out;

        &:hover {
            opacity: 0.75;
            outline-color: var(--accent-blue-new);
            text-decoration: none;
        }

        &:focus,
        &:active {
            text-decoration: none;
        }

        .icon-label-container {
            display: flex;
            align-items: center;
            gap: @spacing-m;

            .icon-container {
                border-radius: @border-radius-large;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 28px;
                height: 28px;
                font-size: 16px;
                color: white;
                border: 1px solid rgba(0, 0, 0, 0.2);
                background-color: var(--icon-bg);
            }

            .card-label {
                font-weight: 500;
            }
        }

        .card-value {
            color: @themed-text-muted;
        }
    }
</style>