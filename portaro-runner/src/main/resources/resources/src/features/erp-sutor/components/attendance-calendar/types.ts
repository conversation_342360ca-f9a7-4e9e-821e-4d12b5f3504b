export interface WorkAttendanceOverview {
    summary: AttendanceSummary;
    days: DayAttendance[];
}

export interface DayAttendance {
    date: DateInfo;
    costsSourceRegularWork: DescriptedDurationItem;
    costsSourceOvertimeWork: DescriptedDurationItem;
    costsSourceOnCallDuty: DescriptedDurationItem;
    costsSourceAbsence: AbsenceDurationItem;
    costsSourceSum: DescriptedDurationItem;
    attendanceSourceRegularWork: DescriptedDurationItem;
    attendanceSourceOvertimeWork: DescriptedDurationItem;
    attendanceSourceAbsence: AbsenceDurationItem;
}

export interface AttendanceSummary {
    commitment: DescriptedDurationItem;
    holiday: DescriptedDurationItem;
    work: WorkSummary;
    overtime: OvertimeWorkSummary;
    absence: AbsenceSummary;
    onCallDuty: OnCallDutySummary;
}

type DurationString = string;
type DateLabel = 'SATURDAY' | 'SUNDAY' | 'HOLIDAY';

export enum AbsenceReason {
    DOCTOR_VISIT = 'DOCTOR_VISIT',
    VACATION = 'VACATION',
    ILLNESS = 'ILLNESS',
    OCR = 'OCR',
    PARAGRAPH = 'PARAGRAPH',
    COMPENSATION = 'COMPENSATION',
    UNPAID = 'UNPAID',
    UNEXCUSED = 'UNEXCUSED',
    MATERNITY = 'MATERNITY',
    PATERNITY = 'PATERNITY',
    EMPLOYER_OBSTACLES = 'EMPLOYER_OBSTACLES',
    CHILD_CAMP = 'CHILD_CAMP',
}

export enum SummarySectionType {
    WORK = 'work',
    OVERTIME = 'overtime',
    ABSENCE = 'absence',
    ON_CALL_DUTY = 'on-call-duty',
    SECTION_LEGEND = 'section-legend'
}

export interface WorkSummary {
    costsSourcePresence: DescriptedDurationItem;
    attendanceSourcePresence: DescriptedDurationItem;
    costsSourcePlan: DescriptedDurationItem;
    attendanceSourcePlan: DescriptedDurationItem;
}

export type OnCallDutySummary = DescriptedDurationItem;

export interface OvertimeWorkSummary {
    costsSourceWorkday: DescriptedDurationItem;
    attendanceSourceWorkday: DescriptedDurationItem;
    approvedWorkday: DescriptedDurationItem;
    costsSourceWeekendAndHoliday: DescriptedDurationItem;
    attendanceSourceWeekendAndHoliday: DescriptedDurationItem;
    approvedWeekend: DescriptedDurationItem;
    costsSourceSum: DescriptedDurationItem;
    attendanceSourceSum: DescriptedDurationItem;
    costsSourceBank: DescriptedDurationItem;
    attendanceSourceBank: DescriptedDurationItem;
    approvedBank: DescriptedDurationItem;
}

export interface AbsenceSummary {
    costsSourceDoctor: DescriptedDurationItem;
    attendanceSourceDoctor: DescriptedDurationItem;
    costsSourceVacation: DescriptedDurationItem;
    attendanceSourceVacation: DescriptedDurationItem;
    costsSourceIllness: DescriptedDurationItem;
    attendanceSourceIllness: DescriptedDurationItem;
    costsSourceOcr: DescriptedDurationItem;
    attendanceSourceOcr: DescriptedDurationItem;
    costsSourceParagraph: DescriptedDurationItem;
    attendanceSourceParagraph: DescriptedDurationItem;
    costsSourceCompensatory: DescriptedDurationItem;
    attendanceSourceCompensatory: DescriptedDurationItem;
    costsSourceUnpaid: DescriptedDurationItem;
    attendanceSourceUnpaid: DescriptedDurationItem;
    costsSourceUnexcused: DescriptedDurationItem;
    attendanceSourceUnexcused: DescriptedDurationItem;
    costsSourceMaternity: DescriptedDurationItem;
    attendanceSourceMaternity: DescriptedDurationItem;
    costsSourcePaternity: DescriptedDurationItem;
    attendanceSourcePaternity: DescriptedDurationItem;
    costsSourceObstacles: DescriptedDurationItem;
    attendanceSourceObstacles: DescriptedDurationItem;
    costsSourceChildrenAndYouth: DescriptedDurationItem;
    attendanceSourceChildrenAndYouth: DescriptedDurationItem;
    costsSourceSum: DescriptedDurationItem;
    attendanceSourceSum: DescriptedDurationItem;
}

interface DateInfo {
    value: string;
    labels: DateLabel[];
}

export interface DurationItem {
    duration: DurationString;
}

export interface DescriptedDurationItem extends DurationItem {
    description: string;
    errorDescription?: string;
}

export interface AbsenceDurationItem extends DurationItem {
    reasons: AbsenceReason[];
}