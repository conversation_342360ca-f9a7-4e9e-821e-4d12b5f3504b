import type {FieldTypeId, Rec, SetFieldValueRequest, ViewableField} from 'typings/portaro.be.types';
import type {GridFieldValue} from '../record-grid/lib/types';
import {exists} from 'shared/utils/custom-utils';
import {isString} from 'shared/utils/string-utils';
import {splitFieldTypeId} from 'shared/utils/record-field-utils';
import {DateTime} from 'luxon';
import {hasRecordReference} from 'src/features/record-grid/lib/types-utils';

export function getTextFromGridFieldValue(field: GridFieldValue | null, defaultPlaceholder: string = '-'): string {
    if (!exists(field)) {
        return defaultPlaceholder;
    }

    if (!field.text) {
        return defaultPlaceholder;
    }

    return field.text ?? defaultPlaceholder;
}

export function getNumberFromGridFieldValue(field: GridFieldValue | null, defaultValue: number | null = 0): number {
    if (!exists(field) || !('value' in field) || typeof field.value !== 'number') {
        return defaultValue;
    }

    return field.value;
}

export function isGridFieldValueEmpty(field: GridFieldValue | null): boolean {
    return !exists(field) || !field.text;
}

export function getDateFromGridFieldValue(field: GridFieldValue | null): DateTime | null {
    if (!exists(field) || !('value' in field) || typeof field.value !== 'string') {
        return null;
    }

    return DateTime.fromISO(field.value);
}

export function getTextFromViewableField(field: ViewableField | null, defaultPlaceholder: string = '-'): string {
    if (!exists(field)) {
        return defaultPlaceholder;
    }

    return field.text ?? defaultPlaceholder;
}

export function getIdFromGridFieldValue(field: GridFieldValue | null): string | null {
    if (!exists(field) || !('id' in field) || !isString(field.id)) {
        return null;
    }

    return field.id;
}

export function getRawFromViewableField(field: (ViewableField) | null): string | null {
    if (!exists(field)) {
        return null;
    }

    return field?.raw;
}

export function getFirstViewableFieldByFieldTypeIdRecursive(record: Rec, fieldTypeId: FieldTypeId): ViewableField | null {
    const segmentedFieldTypeId = splitFieldTypeId(fieldTypeId);

    let foundField: ViewableField | null = null;
    let searchedFields = record.fields;

    while (segmentedFieldTypeId.length > 0) {
        const idToFind = segmentedFieldTypeId.splice(0, 1)[0];
        const subField = searchedFields.find((field) => field.code === idToFind);

        if (!exists(subField)) {
            return null;
        }

        foundField = subField;
        searchedFields = foundField.fields;
    }

    return foundField;
}

export function createSetFieldRequestFromGridFieldValue(field: GridFieldValue, isRecordReference: boolean): SetFieldValueRequest {
    if (isRecordReference && hasRecordReference(field)) {
        return {
            recordId: field.recordReference.id
        };
    }

    if ('id' in field) {
        return {
            value: field.id
        };
    }

    return {
        value: field
    };
}