import type {GridFieldValue, RecordRow} from 'src/features/record-grid/lib/types';
import type {Rec, UUID, ViewableFile} from 'typings/portaro.be.types';
import type {FileService} from 'src/features/file/file.service';
import type {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
import type {RecordEditationDataService} from 'src/features/record-editation/services/record-editation.data-service';
import type {SutorReportsOptimisationDataService} from './sutor-reports-optimisation.data-service';
import type RecordDataService from 'src/features/record/record.data-service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import {exists} from 'shared/utils/custom-utils';
import {Subkind} from 'shared/constants/portaro.constants';
import {hasRecordReference} from 'src/features/record-grid/lib/types-utils';
import {getFirstFieldByFieldTypeIdRecursive} from 'src/features/record-grid/lib/grid-fields';
import {createSetFieldRequestFromGridFieldValue} from 'src/features/erp-sutor/sutor-utils';
import {FOND_REPORT} from 'src/features/erp-sutor/sutor-fonds';
import type {
    DayOptimisationOverviewResponse,
    GroupedProjectItemsResponse,
    OptimisationSumsResponse,
    OverallOptimisationOverviewResponse
} from '../types';

export class SutorReportsOptimisationService {
    public static serviceName = 'sutorReportsOptimisationService';

    /*@ngInject*/
    constructor(private recordDataService: RecordDataService,
                private recordEditationDataService: RecordEditationDataService,
                private toastMessageService: ToastMessageService,
                private finishedResponseInteractionService: FinishedResponseInteractionService,
                private fileService: FileService,
                private sutorReportsOptimisationDataService: SutorReportsOptimisationDataService) {
    }

    public async getReportReferencePdfFile(field: GridFieldValue | null): Promise<ViewableFile> {
        if (!exists(field) || !hasRecordReference(field)) {
            return null;
        }

        const referenceRecord = await this.recordDataService.getById(field.recordReference.id);
        if (!exists(referenceRecord)) {
            return null;
        }

        const allFiles = await this.fileService.getFilesByDirectory(referenceRecord.directoryId);
        return allFiles[0] ?? null;
    }

    public async useForOptimisationBulk(projectRecord: Rec, installationLogbookRecord: RecordRow, reportsForOptimisation: RecordRow[]): Promise<UUID[]> {
        const results = await Promise.all(reportsForOptimisation.map((report: RecordRow) => this.useForOptimisation(projectRecord, installationLogbookRecord, report, false)));
        this.toastMessageService.showSuccess('Výkazy byly použity k optimalizaci!');

        return results;
    }

    public async useForOptimisation(projectRecord: Rec, installationLogbookRecord: RecordRow, reportForOptimisation: RecordRow, showToastMessage: boolean = true): Promise<UUID | null> {
        const dateField = getFirstFieldByFieldTypeIdRecursive(reportForOptimisation, FOND_REPORT.dateFieldId);
        const typeField = getFirstFieldByFieldTypeIdRecursive(reportForOptimisation, FOND_REPORT.OPTIMISATION.typeFieldId);
        const workerField = getFirstFieldByFieldTypeIdRecursive(reportForOptimisation, FOND_REPORT.workerFieldId);

        if (!exists(dateField) || !exists(typeField) || !exists(workerField)) {
            this.toastMessageService.showError('Tento výkaz nemůžete použít k optimalizaci, je totiž nevalidní!');
            return null;
        }

        const recordEditation = await this.recordEditationDataService.createNewEditation({
            subkind: Subkind.SUBKIND_DOCUMENT,
            fond: {id: FOND_REPORT.OPTIMISATION.fond},
            rootFond: {id: FOND_REPORT.OPTIMISATION.fond},
            initialValues: {
                [FOND_REPORT.projectFieldId]: {
                    recordId: projectRecord.id
                },
                [FOND_REPORT.dateFieldId]: createSetFieldRequestFromGridFieldValue(dateField, true),
                [FOND_REPORT.OPTIMISATION.typeFieldId]: createSetFieldRequestFromGridFieldValue(typeField, false),
                [FOND_REPORT.workerFieldId]: createSetFieldRequestFromGridFieldValue(workerField, true),
                [FOND_REPORT.installationLogbookFieldId]: {
                    recordId: installationLogbookRecord.id
                },
                [FOND_REPORT.descriptionFieldId]: {
                    value: 'Optimalizace'
                }
            }
        });

        try {
            // TODO: In future, it will be as a draft
            await this.recordEditationDataService.publish(recordEditation.id);

            if (showToastMessage) {
                this.toastMessageService.showSuccess('Výkaz byl použit pro optimalizaci!');
            }

            return reportForOptimisation.id;
        } catch (errorException) {
            if (showToastMessage) {
                this.finishedResponseInteractionService.showFailedResponseInToast(errorException);
            }

            return null;
        }
    }

    public async getReportSums(project: Rec): Promise<OptimisationSumsResponse | null> {
        try {
            return await this.sutorReportsOptimisationDataService.getReportSums(project);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            return null;
        }
    }

    public async getOverallOverview(project: Rec): Promise<OverallOptimisationOverviewResponse | null> {
        try {
            return await this.sutorReportsOptimisationDataService.getOverallOverview(project);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            return null;
        }
    }

    public async getItemOverview(project: Rec, projectItem: RecordRow): Promise<DayOptimisationOverviewResponse | null> {
        try {
            return await this.sutorReportsOptimisationDataService.getItemOverview(project, projectItem);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            return null;
        }
    }

    public async getGroupedProjectItems(project: Rec): Promise<GroupedProjectItemsResponse | null> {
        try {
            return await this.sutorReportsOptimisationDataService.getGroupedProjectItems(project);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            return null;
        }
    }
}