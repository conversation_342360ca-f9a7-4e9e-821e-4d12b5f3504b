<script lang="ts">
    import {getInjector} from 'core/svelte-context/context';
    import {getDataViewEventContext} from 'src/features/erp-sutor/pages/data-view/data-view.event-context';
    import UserService from 'src/features/user/user.service';
    import {REFRESH_DATA_VIEW_GRID_EVENT} from 'src/features/erp-sutor/pages/data-view/types';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';

    const userService = getInjector().getByClass(UserService);
    const eventContext = getDataViewEventContext();

    let loading = false;

    const handleCreateEmployee = async () => {
        loading = true;
        await userService.createNewEditorPerson().finally(() => loading = false);
        eventContext.dispatchEvent(new CustomEvent(REFRESH_DATA_VIEW_GRID_EVENT));
    };
</script>

<KpButton isLoading="{loading}" buttonStyle="success-new" on:click={handleCreateEmployee}>
    <IconedContent icon="add">
        Vytvořit nového pracovníka
    </IconedContent>
</KpButton>