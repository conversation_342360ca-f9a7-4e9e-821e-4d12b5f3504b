import type {SutorHomePageData} from '../types';
import type {Auth} from 'typings/portaro.be.types';
import type {SutorHomePageDataService} from './sutor-home-page.data-service';
import type LoginService from 'shared/login/login.service';

export class SutorHomePageService {
    public static serviceName = 'sutorLandingPageService';

    /*@ngInject*/
    constructor(private sutorHomePageDataService: SutorHomePageDataService,
                private loginService: LoginService) {
    }

    public async getHomePageData(): Promise<SutorHomePageData> {
        return this.sutorHomePageDataService.getHomePageData();
    }

    public async login(): Promise<Auth> {
        return this.loginService.login();
    }
}