<script lang="ts">
    import type {RecordSearchParams} from 'typings/portaro.be.types';
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import type {SearchManager} from 'src/features/search/search-manager/search-manager';
    import {asRecordRow} from 'src/features/record-grid/lib/types-utils';
    import {Kind, SearchType} from 'shared/constants/portaro.constants';
    import {onDestroy, onMount} from 'svelte';
    import {getDataViewEventContext} from 'src/features/erp-sutor/pages/data-view/data-view.event-context';
    import {isFunction} from 'shared/utils/custom-utils';
    import {FOND_MONTHLY_ATTENDANCE} from 'src/features/erp-sutor/sutor-fonds';
    import {REFRESH_DATA_VIEW_GRID_EVENT} from 'src/features/erp-sutor/pages/data-view/types';
    import KpPageableSearchResults from 'src/features/search/kp-search-results/KpPageableSearchResults.svelte';
    import KpSearchToolbar from 'src/features/search/kp-search-toolbar/KpSearchToolbar.svelte';
    import KpSearchContext from 'src/features/search/kp-search-context/KpSearchContext.svelte';
    import KpSearchFieldTypeFiltersContainer from 'src/features/search/kp-search-field-type-filter/KpSearchFieldTypeFiltersContainer.svelte';
    import KpBarebonesTable from 'shared/ui-widgets/table/barebones/KpBarebonesTable.svelte';
    import AttendanceEmployeeItem from 'src/features/erp-sutor/pages/data-view/custom-tab-components/employees/AttendanceEmployeeItem.svelte';

    const eventContext = getDataViewEventContext();
    let getSearchManager: (() => SearchManager<RecordRow>) | null = null;

    onMount(() => {
        eventContext.addEventListener(REFRESH_DATA_VIEW_GRID_EVENT, handleRefresh);
    });

    onDestroy(() => {
        eventContext.removeEventListener(REFRESH_DATA_VIEW_GRID_EVENT, handleRefresh);
    });

    function createStaticParams(): RecordSearchParams {
        return {
            kind: [Kind.KIND_RECORD],
            type: SearchType.TYPE_TABLE,
            rootFond: [{id: FOND_MONTHLY_ATTENDANCE.fond}],
            pageSize: 25,
            [FOND_MONTHLY_ATTENDANCE.yearMonthFieldFilterSearchParam]: '2025-08'
        };
    }

    const handleRefresh = () => {
        if (!isFunction(getSearchManager)) {
            return;
        }

        const searchManager = getSearchManager();
        searchManager.refreshSearch();
    };
</script>

<KpSearchContext staticParams="{createStaticParams()}" let:searchManager bind:getSearchManager>
    <KpSearchFieldTypeFiltersContainer fondId="{FOND_MONTHLY_ATTENDANCE.fond}">
        <div class="sutor-employees-attendance-container">
            <KpSearchToolbar searchType="record-name"/>

            <KpPageableSearchResults noRefreshLoading pagination="{searchManager.getPagination()}" let:paginationData>
                <KpBarebonesTable fontSize="13px">
                    <tr slot="header">
                        <th>Osobní číslo</th>
                        <th>Pracovník</th>
                        <th>Typ smlouvy</th>
                        <th>Divize</th>
                        <th>Nadřízený</th>
                        <th>Stav schválení</th>
                        <th>Období</th>
                    </tr>

                    <svelte:fragment slot="body">
                        {#each paginationData.items as itemRow(itemRow.id)}
                            {@const record = asRecordRow(itemRow)}

                            <AttendanceEmployeeItem {record}/>
                        {/each}
                    </svelte:fragment>
                </KpBarebonesTable>
            </KpPageableSearchResults>
        </div>
    </KpSearchFieldTypeFiltersContainer>
</KpSearchContext>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    :global {
        .sutor-employees-attendance-container .kp-pageable-search-results {
            margin-top: @spacing-ml;
        }
    }
</style>