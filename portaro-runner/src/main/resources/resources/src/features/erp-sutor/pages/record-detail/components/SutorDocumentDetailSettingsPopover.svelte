<script lang="ts">
    import type {DocumentDetailReactivePageData, DocumentDetailStaticPageData} from '../../../../record/kp-document-detail-page/types';
    import type {RecordHolding, User} from 'typings/portaro.be.types';
    import type {UserDetailView} from 'src/features/user/types';
    import {getLocalization} from 'core/svelte-context/context';
    import {getPageContext} from 'shared/layouts/page-context';
    import {onDestroy} from 'svelte';
    import KpPopover from 'shared/ui-widgets/popover/KpPopover.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import DocumentDetailEditButtons from '../../../../record/kp-document-detail-page/components/DocumentDetailEditButtons.svelte';

    export let user: User | null = null;
    export let userDetailView: UserDetailView | null = null;

    const localize = getLocalization();
    const pageContext = getPageContext<DocumentDetailStaticPageData, DocumentDetailReactivePageData>();

    const model = pageContext.staticData.model;
    let recordHoldings: RecordHolding[] | undefined;
    const reactiveDataUnsubscribe = pageContext.reactiveData.subscribe((data) => recordHoldings = data.recordHoldings);
    onDestroy(reactiveDataUnsubscribe);
</script>

<KpPopover buttonSize="md"
           placement="bottom-end"
           buttonStyle="default"
           additionalPopoverPanelClasses="sutor-document-detail-settings-popover-panel"
           additionalPopoverButtonClasses="sutor-document-detail-settings-button">

    <UIcon slot="button" icon="settings"/>
    <span slot="popover-title">
        {localize(/* @kp-localization record.RecordManagement */ 'record.RecordManagement')}
    </span>

    <div slot="popover-content" class="sutor-document-detail-settings-popover-content">
        <DocumentDetailEditButtons {recordHoldings} {model} {user} {userDetailView} isSutor/>
    </div>
</KpPopover>

<style lang="less">
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.variables.less";

    .sutor-document-detail-settings-popover-content {
        padding: @spacing-s 0;
        display: flex;
        flex-direction: column;
        gap: @spacing-m;
    }

    :global {
        .sutor-document-detail-settings-popover-panel {
            --popover-bg-color: @themed-body-bg;
            --popover-border-color: @themed-border-default;
            --popover-border-radius: @border-radius-large;
            --popover-title-bg-color: @themed-body-bg;
            --popover-title-border-color: @themed-border-default;
            --popover-content-padding: @spacing-m @spacing-ml;

            width: 300px;
            margin-top: @spacing-s;
            margin-left: calc(@spacing-m * -1);

            .title {
                display: flex;
                align-items: center;
                height: 42px;
                font-weight: 500;
            }
        }
    }
</style>