import type {Readable} from 'svelte/store';
import type {RecordRow} from 'src/features/record-grid/lib/types';
import type {Rec, UUID} from 'typings/portaro.be.types';
import type {ProjectType} from 'src/features/erp-sutor/pages/project-detail/reports-optimisation/types';
import type {SutorReportsOptimisationService} from './services/sutor-reports-optimisation.service';
import {getContext, hasContext, setContext} from 'svelte';
import {writable} from 'svelte/store';
import {FOND_PROJECT} from 'src/features/erp-sutor/sutor-fonds';

const sutorReportsOptimisationContextKey = 'sutor-reports-optimisation-ctx';

interface SutorReportsOptimisationContext {
    service: SutorReportsOptimisationService;
    projectRecord: Rec;
    projectType: ProjectType;
    openedUser$: Readable<RecordRow | null>;
    optimisedInstallationLogbookRecord$: Readable<RecordRow | null>;
    usedReportsForOptimisation$: Readable<UUID[]>;
    openOverviewSignal$: Readable<RecordRow | null>;
    setOpenedUser: (workReport: RecordRow | null) => void;
    addUsedReportForOptimisation: (reportId: UUID) => void;
    setOptimisedInstallationLogbookRecord: (installationLogbookRecord: RecordRow | null) => void;
    setOpenOverviewSignal: (record: RecordRow | null) => void;
}

export function createSutorReportsOptimisationContext(projectRecord: Rec, service: SutorReportsOptimisationService): SutorReportsOptimisationContext {
    const projectType = projectRecord.fond.id === FOND_PROJECT.REPORTED.fond ? 'reported' : 'contracted';
    const openedUser$ = writable<RecordRow | null>(null);
    const usedReportsForOptimisation$ = writable<UUID[]>([]);
    const openOverviewSignal$ = writable<RecordRow | null>(null);
    const optimisedInstallationLogbookRecord$ = writable<RecordRow | null>(null);

    return setContext<SutorReportsOptimisationContext>(sutorReportsOptimisationContextKey, {
        projectRecord,
        projectType,
        service,
        openedUser$,
        openOverviewSignal$,
        usedReportsForOptimisation$,
        optimisedInstallationLogbookRecord$,
        setOpenedUser: (workReport) => openedUser$.set(workReport),
        addUsedReportForOptimisation: (reportId) => usedReportsForOptimisation$.update((usedReports) => [...usedReports, reportId]),
        setOptimisedInstallationLogbookRecord: (installationLogbookRecord) => optimisedInstallationLogbookRecord$.set(installationLogbookRecord),
        setOpenOverviewSignal: (record) => openOverviewSignal$.set(record)
    });
}

export function getSutorReportsOptimisationContext(): SutorReportsOptimisationContext {
    if (!hasContext(sutorReportsOptimisationContextKey)) {
        throw new Error('Sutor work reports finalisation context does not exist! Use `createSutorReportsFinalisationContext` function to create it in a parent component.');
    }

    return getContext<SutorReportsOptimisationContext>(sutorReportsOptimisationContextKey);
}