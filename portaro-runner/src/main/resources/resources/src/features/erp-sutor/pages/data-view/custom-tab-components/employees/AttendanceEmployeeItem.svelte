<script lang="ts">
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import {getFirstFieldByFieldTypeIdRecursive} from 'src/features/record-grid/lib/grid-fields';
    import {getTextFromGridFieldValue} from 'src/features/erp-sutor/sutor-utils';
    import {FOND_MONTHLY_ATTENDANCE} from 'src/features/erp-sutor/sutor-fonds';
    import GridFieldValue from 'shared/ui-widgets/grid/GridFieldValue.svelte';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';

    export let record: RecordRow;

    const employeeField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.employeeFieldId);
    const yearMonthField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.yearMonthFieldId);
    const stateField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.stateFieldId);
</script>

<tr>
    <td>123</td>

    <td>
        <GridFieldValue field="{employeeField}">
            <KpUserAvatar userRecordId="{employeeField?.recordReference?.id}" sizePx="{48}"/>
            {getTextFromGridFieldValue(employeeField)}
        </GridFieldValue>
    </td>

    <td></td>

    <td></td>

    <td></td>

    <td>
        <GridFieldValue field="{stateField}"/>
    </td>

    <td>
        <GridFieldValue field="{yearMonthField}"/>
    </td>
</tr>

<tr>
    <td colspan="5">
        <!-- <SutorAttendanceCalendar userRecordId="{record.id}"
                                 type="month-with-summary"
                                 withoutLoadAnimations="{true}"/> -->
    </td>
</tr>