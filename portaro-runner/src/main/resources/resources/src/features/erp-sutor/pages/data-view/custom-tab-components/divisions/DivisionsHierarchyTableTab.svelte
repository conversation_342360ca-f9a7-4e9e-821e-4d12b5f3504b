<script lang="ts">
    import type {LabeledIdentified} from 'typings/portaro.be.types';
    import type {ViewableDepartment} from 'typings/portaro.fe.types';
    import type {ColumnDef, TableOptions} from '@tanstack/svelte-table';
    import type {ViewableDepartmentsHierarchy} from 'src/features/departments/types';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {createRender} from 'svelte-render';
    import {writable} from 'svelte/store';
    import {defaultCellContent, defaultGlobalFilter, defaultRowId} from 'shared/ui-widgets/grid/utils';
    import {getCoreRowModel, getExpandedRowModel, getFilteredRowModel} from '@tanstack/svelte-table';
    import {KpDepartmentsService} from 'src/features/departments/services/kp-departments.service';
    import {getDataViewEventContext} from 'src/features/erp-sutor/pages/data-view/data-view.event-context';
    import {onDestroy, onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {REFRESH_DATA_VIEW_GRID_EVENT} from 'src/features/erp-sutor/pages/data-view/types';
    import KpGenericGrid from 'shared/ui-widgets/grid/KpGenericGrid.svelte';
    import ExpandableValue from 'shared/ui-widgets/grid/grid-cell/grid-cell-content-components/ExpandableValue.svelte';
    import LabeledIdentifiedList from 'shared/ui-widgets/grid/grid-cell/grid-cell-content-components/LabeledIdentifiedList.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import TextValue from 'shared/ui-widgets/grid/grid-cell/grid-cell-content-components/TextValue.svelte';
    import KpGenericGridContextProvider from 'shared/ui-widgets/grid/KpGenericGridContextProvider.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import DepartmentsOptions from 'src/features/departments/components/DepartmentsOptions.svelte';
    import DepartmentsToolbarPart from 'src/features/departments/parts/DepartmentsToolbarPart.svelte';
    import DivisionTableChip from './DivisionTableChip.svelte';
    import KpLoadableContainer from 'shared/layouts/containers/KpLoadableContainer.svelte';

    const localize = getLocalization();
    const departmentsService = getInjector().getByClass(KpDepartmentsService);
    const eventContext = getDataViewEventContext();

    let loading = true;
    let hierarchicalDepartments: ViewableDepartmentsHierarchy[] = [];

    onMount(async () => {
        const loadedDepartments = await departmentsService.getDepartments();
        hierarchicalDepartments = departmentsService.transformIntoHierarchicalData(loadedDepartments);
        loading = false;

        eventContext.addEventListener(REFRESH_DATA_VIEW_GRID_EVENT, handleRefresh);
    });

    onDestroy(() => {
        eventContext.removeEventListener(REFRESH_DATA_VIEW_GRID_EVENT, handleRefresh);
    });

    let gridContainerElement: HTMLDivElement;

    function createTableOptions() {
        const defaultColumns: ColumnDef<ViewableDepartmentsHierarchy>[] = [
            {
                id: 'id',
                size: 50,
                accessorFn: (row) => row.id,
                header: () => 'ID',
                cell: ({cell}) => createRender(TextValue, {value: cell.getValue<number>()})
            },
            {
                id: 'order',
                size: 100,
                accessorFn: (row) => row.order,
                header: () => localize(/* @kp-localization commons.Poradi */ 'commons.Poradi'),
                cell: ({cell}) => createRender(TextValue, {value: cell.getValue<number>()})
            },
            {
                id: 'name',
                size: 300,
                accessorFn: (row) => row,
                header: () => localize(/* @kp-localization commons.nazev */ 'commons.nazev'),
                cell: ({cell, row}) => createRender(ExpandableValue, {
                    isExpanded: row.getIsExpanded(),
                    canExpand: row.getCanExpand(),
                    depth: row.depth
                })
                    .on('click', row.getToggleExpandedHandler())
                    .slot(createRender(DivisionTableChip, {department: cell.getValue<ViewableDepartment>()}))
            },
            {
                id: 'syncId',
                accessorFn: (row) => row.syncId,
                header: () => 'Sync ID',
                cell: ({cell}) => createRender(TextValue, {value: cell.getValue<string>()})
            },
            {
                id: 'locations',
                size: 300,
                accessorFn: (row) => row.locations,
                header: () => localize(/* @kp-localization commons.Locations */ 'commons.Locations'),
                cell: ({cell}) => createRender(LabeledIdentifiedList, {values: cell.getValue<LabeledIdentified<number>[]>()})
            }
        ];

        const conditionalColumns: ColumnDef<ViewableDepartmentsHierarchy>[] = [
            {
                id: 'options',
                header: () => localize(/* @kp-localization commons.Options */ 'commons.Options'),
                cell: ({row}) => createRender(DepartmentsOptions, {
                    canEdit: departmentsService.canEditDepartment(),
                    canRemove: departmentsService.canDeleteDepartment() && !row.original.root
                })
                    // eslint-disable-next-line @typescript-eslint/no-misused-promises
                    .on('edit', () => edit(row.original))
                    // eslint-disable-next-line @typescript-eslint/no-misused-promises
                    .on('remove', () => remove(row.original))
            }
        ];

        const columns = departmentsService.canEditDepartment() || departmentsService.canDeleteDepartment() ? [...defaultColumns, ...conditionalColumns] : defaultColumns;
        return writable<TableOptions<ViewableDepartmentsHierarchy>>({
            data: [],
            columns,
            defaultColumn: {
                cell: defaultCellContent()
            },
            getSubRows: (row) => row.subdepartments,
            getRowId: defaultRowId(),
            globalFilterFn: defaultGlobalFilter((row, searchPhrase) => Boolean(row.original?.text?.toLowerCase()?.includes(searchPhrase))),
            getCoreRowModel: getCoreRowModel(),
            getFilteredRowModel: getFilteredRowModel(),
            getExpandedRowModel: getExpandedRowModel()
        });
    }

    async function remove(department: ViewableDepartment) {
        await departmentsService.deleteDepartment(department);
        await loadDepartments();
        departmentsService.goToHomepageIfCurrentDepartmentGotRemoved(department);
    }

    async function edit(department: ViewableDepartment) {
        await departmentsService.editDepartment(department);
        await loadDepartments();
    }

    async function loadDepartments() {
        const loadedDepartments = await departmentsService.getDepartments();
        hierarchicalDepartments = departmentsService.transformIntoHierarchicalData(loadedDepartments);
    }

    const handleRefresh = () => {
        loading = true;
        loadDepartments().then(() => loading = false);
    };
</script>

<KpLoadableContainer fillAvailableSpace {loading}>
    <Flex class="sutor-divisions-table" direction="column" width="100%" gap="ml" fillAvailableSpace>
        <KpGenericGridContextProvider tableData="{hierarchicalDepartments}"
                                      options="{createTableOptions()}"
                                      hoverRows
                                      stickyHeader
                                      colorAccented
                                      columnResizingEnabled
                                      columnPinningEnabled
                                      selectionEnabled
                                      rowClassTemplate="{(row) => `row-department-${row.original.id}`}"
                                      let:gridContext>

            <div class="toolbar-container">
                <DepartmentsToolbarPart table$={gridContext.table$} withoutAddButton/>
            </div>

            {#if !exists(hierarchicalDepartments)}
                <KpLoadingBlock size="xs"/>
            {:else}
                <Flex class="departments-grid-container" width="100%" bind:element={gridContainerElement}>
                    <KpGenericGrid table$={gridContext.table$} expandedFirstLevel/>
                </Flex>
            {/if}
        </KpGenericGridContextProvider>
    </Flex>
</KpLoadableContainer>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro-erp.less";

    .toolbar-container {
        padding: 0 @spacing-xl;
        margin-top: 18px;
    }

    :global {
        .sutor-divisions-table {
            .departments-grid-container {
                .flex-grow();

                .kp-generic-grid-container {
                    .flex-grow();
                    border-left: none !important;
                    border-right: none !important;
                    border-bottom: none !important;
                    border-radius: 0 !important;

                    .grid {
                        .flex-grow();
                    }
                }
            }
        }
    }
</style>