<script lang="ts">
    import type {DocumentDetailReactivePageData, DocumentDetailStaticPageData} from 'src/features/record/kp-document-detail-page/types';
    import {getPageContext} from 'shared/layouts/page-context';
    import ProjectFinancialOverviewTable from 'src/features/erp-sutor/pages/project-detail/components/ProjectFinancialOverviewTable.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    const pageContext = getPageContext<DocumentDetailStaticPageData, DocumentDetailReactivePageData>();
    const projectRecord = pageContext.staticData.model.record;
</script>

<KpHeading type="h2">Hospodařen<PERSON></KpHeading>
<ProjectFinancialOverviewTable {projectRecord}/>