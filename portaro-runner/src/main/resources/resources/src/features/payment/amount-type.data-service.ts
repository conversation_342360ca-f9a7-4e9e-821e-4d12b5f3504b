import {ngAsync} from 'shared/utils/ng-@decorators';
import type {AmountType} from './types';
import type {AjaxService} from 'core/data-services/ajax.service';

export class AmountTypeDataService {
    public static serviceName = 'amountTypeDataService';
    public static readonly ROUTE = 'amount-types';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async getAll(): Promise<AmountType[]> {
        return this.ajaxService
            .createRequest(`${AmountTypeDataService.ROUTE}`)
            .get();
    }
}