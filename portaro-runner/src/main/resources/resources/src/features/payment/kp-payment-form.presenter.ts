import type {AmountTypeDataService} from './amount-type.data-service';
import type {ContraDebtablePaymentItemRequest, PayRequest, ViewablePaymentRequestItem, AmountType} from './types';
import type PaymentDataService from './payment.data-service';
import type {ActionResponse} from 'typings/portaro.be.types';

export class KpPaymentFormPresenter {
    public static presenterName = 'KpPaymentFormPresenter';

    /*@ngInject*/
    constructor(private amountTypeDataService: AmountTypeDataService, private paymentDataService: PaymentDataService) {
    }

    public createAmounts(formObject: PayRequest): ViewablePaymentRequestItem[] {
        return formObject.amounts.map((amount) => this.createPaymentRequestItem(amount, false, false));
    }

    public getAmountTypes(): Promise<AmountType[]> {
        return this.amountTypeDataService.getAll()
    }

    public createPaymentRequestItem(amount: ContraDebtablePaymentItemRequest, direct: boolean, withContraDebt: boolean): ViewablePaymentRequestItem {
        return {
            ...amount,
            enabled: true,
            direct,
            withContraDebt,
            maxSum: direct || amount.type.creditable ? 999999 : amount.sum
        } as ViewablePaymentRequestItem;
    }

    public pay(payRequest: PayRequest): Promise<ActionResponse> {
        return this.paymentDataService.pay(payRequest);
    }
}