<script lang="ts">
    import type {Payment} from './types';
    import {getDateFormatter, getLocalization} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {exists} from 'shared/utils/custom-utils';
    import {Kind} from 'shared/constants/portaro.constants';
    import Label from 'shared/components/kp-label/Label.svelte';

    export let payment: Payment;

    const dateFormatter = getDateFormatter();
    const localize = getLocalization();
</script>

<div class="list-item-payment">
    <div>
        <h2 class="unset-style payment-sum">{payment.state.text} {payment.sumToPay}Kč</h2>
        <div class="payment-dates">
            <span class="text-muted">
                {localize(/* @kp-localization payment.CreationDate */ 'payment.CreationDate')}:
                {pipe(payment.createDate, dateFormatter('d.M.yyyy HH:mm'))}
            </span>
            {#if exists(payment.payDate)}
                <span class="text-muted">
                    {localize(/* @kp-localization payment.PaymentDate */ 'payment.PaymentDate')}:
                    {pipe(payment.payDate, dateFormatter('d.M.yyyy HH:mm'))}
                </span>
            {/if}
            {#if exists(payment.cancelDate)}
                <span class="text-muted">
                    {localize(/* @kp-localization payment.CancellationDate */ 'payment.CancellationDate')}:
                    {pipe(payment.cancelDate, dateFormatter('d.M.yyyy HH:mm'))}
                </span>
            {/if}
        </div>
    </div>

    <div class="payment-info">
        <span>
            {localize(/* @kp-localization payment.Payer */ 'payment.Payer')}:
            <Label labeled={payment.payer} explicitKind={Kind.KIND_USER}/>
        </span>
        {#if exists(payment.cashier)}
            <span>
                {localize(/* @kp-localization payment.Cashier */ 'payment.Cashier')}:
                <Label labeled={payment.cashier} explicitKind={Kind.KIND_USER}/>
            </span>
        {/if}
        <span>
            {localize(/* @kp-localization payment.Provider */ 'payment.Provider')}:
            {payment.provider}
        </span>
        <span>
            {localize(/* @kp-localization commons.Department */ 'commons.Department')}:
            {pipe(payment.department, loc())}
        </span>
    </div>
</div>

<style lang="less">
    .list-item-payment {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1em;
    }

    .payment-sum {
        font-weight: bold;
    }

    .payment-dates {
        display: flex;
        flex-direction: column;
    }

    .payment-info {
        display: flex;
        flex-direction: column;
    }
</style>