import {transferify} from 'shared/utils/data-service-utils';
import {ngAsync} from 'shared/utils/ng-@decorators';
import type {CreditTransfer, PayRequest} from './types';
import type {ActionResponse} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';

export default class PaymentDataService {
    public static serviceName = 'paymentDataService';

    public static readonly ROUTE = 'payments/pay';
    public static readonly PAY_DEBT_FROM_CREDIT_ROUTE = 'payments/pay-debt-from-credit';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async pay(payRequest: PayRequest): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${PaymentDataService.ROUTE}`)
            .post(transferify(payRequest));
    }

    @ngAsync()
    public async payDebtFromCredit(creditTransfer: CreditTransfer): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${PaymentDataService.PAY_DEBT_FROM_CREDIT_ROUTE}`)
            .post(transferify(creditTransfer));
    }
}
