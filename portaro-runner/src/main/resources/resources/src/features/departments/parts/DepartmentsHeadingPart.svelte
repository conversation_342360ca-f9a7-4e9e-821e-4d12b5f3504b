<script lang="ts">
    import {getLocalization} from 'core/svelte-context/context';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    const localize = getLocalization();
</script>

<Flex class="departments-heading-part" width="100%">
    <KpHeading type="h1">{localize(/* @kp-localization commons.Rentals */ 'commons.Rentals')}</KpHeading>
</Flex>