import register from '@kpsys/angularjs-register';
import departmentsRoutes from './departments.routes';
import {KpDepartmentsService} from './services/kp-departments.service';
import {registerInterceptors} from './register-interceptors';
import DepartmentDataService from './services/department.data-service';


export default register('portaro.features.departments')
    .config(departmentsRoutes)
    .run(registerInterceptors)
    .service(DepartmentDataService.serviceName, DepartmentDataService)
    .service(KpDepartmentsService.serviceName, KpDepartmentsService)
    .name();