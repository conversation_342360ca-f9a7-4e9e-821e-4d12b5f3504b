<script lang="ts">
    import type {DiagramNodeDetailsModalSettings} from 'shared/components/kp-hierarchy-diagram/types';
    import type {DepartmentsReactivePageData} from '../types';
    import type {ViewableDepartment} from 'typings/portaro.fe.types';
    import type {KpDepartmentsService} from '../services/kp-departments.service';
    import {getPageContext} from 'shared/layouts/page-context';
    import {onDestroy} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import KpHierarchyDiagram from 'shared/components/kp-hierarchy-diagram/KpHierarchyDiagram.svelte';
    import DepartmentDiagramNodeDetailsContent from '../components/DepartmentDiagramNodeDetailsContent.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    let departments: ViewableDepartment[] = null;
    const pageContext = getPageContext<KpDepartmentsService, DepartmentsReactivePageData>();
    const reactiveDataUnsubscribe = pageContext.reactiveData.subscribe((currentData) => departments = currentData?.departments);

    const nodeDetailsModalSettings: DiagramNodeDetailsModalSettings = {
        headingGetter: (item) => item.text,
        component: DepartmentDiagramNodeDetailsContent
    };

    onDestroy(() => {
        reactiveDataUnsubscribe();
    });
</script>

{#if exists(departments)}
    <Flex class="departments-diagram-part" direction="column" width="100%">
        <KpHierarchyDiagram items="{departments}"
                            heading="Diagramová struktura půjčoven"
                            {nodeDetailsModalSettings}/>
    </Flex>
{/if}