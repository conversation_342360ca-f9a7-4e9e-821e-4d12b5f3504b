<script lang="ts">
    import {createEventDispatcher} from 'svelte';
    import {getLocalization} from 'core/svelte-context/context';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    export let canEdit: boolean;
    export let canRemove: boolean;

    const localize = getLocalization();
    const dispatch = createEventDispatcher<{edit: void; remove: void}>();

    const edit = () => dispatch('edit');
    const remove = () => dispatch('remove');
</script>

<Flex class="edit-buttons" gap="xs">
    {#if canEdit}
        <KpButton buttonSize="xs" on:click={edit}>
            <IconedContent icon="edit">
                {localize( /* @kp-localization commons.edit */ 'commons.edit')}
            </IconedContent>
        </KpButton>
    {/if}

    {#if canRemove}
        <KpButton buttonSize="xs" on:click={remove}>
            <IconedContent icon="trash">
                {localize( /* @kp-localization commons.Smazat */ 'commons.Smazat')}
            </IconedContent>
        </KpButton>
    {/if}
</Flex>