import type {ViewableDepartment} from 'typings/portaro.fe.types';
import type {ViewableDepartmentsHierarchy} from '../types';
import type {Department} from 'typings/portaro.be.types';
import type DepartmentDataService from './department.data-service';
import type LocationDataService from '../../locations/location.data-service';
import type CurrentAuthService from 'shared/services/current-auth.service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type WalkerService from 'shared/services/walker.service';
import {transformIntoHierarchyNodes} from 'shared/utils/hierarchical-data-utils';
import {findFirst, byIdOf} from 'shared/utils/array-utils';
import {isNullOrUndefined} from 'shared/utils/custom-utils';

export class KpDepartmentsService {
    public static serviceName = 'kpDepartmentsPresenter';

    /*@ngInject*/
    constructor(private departmentDataService: DepartmentDataService,
                private locationDataService: LocationDataService,
                private currentAuthService: CurrentAuthService,
                private finishedResponseInteractionService: FinishedResponseInteractionService,
                private currentDepartment: Department,
                private walker: WalkerService) {
    }

    public async getDepartments(): Promise<ViewableDepartment[]> {
        const departments: Department[] = await this.departmentDataService.getAll();
        return Promise.all(departments.map(async (department) => {
            return {
                ...department,
                parent: department.root ? null : departments.find((value) => value.id === department.parentId),
                locations: await this.locationDataService.getAllByDepartment(department),
            }
        }));
    }

    public canDeleteDepartment(): boolean {
        return this.currentAuthService.hasAnyRole('ROLE_LIBRARIAN', 'ROLE_ADMIN', 'ROLE_SERVICEMAN');
    }

    public canEditDepartment(): boolean {
        return this.currentAuthService.hasAnyRole('ROLE_LIBRARIAN', 'ROLE_ADMIN', 'ROLE_SERVICEMAN');
    }

    public canCreateDepartment(): boolean {
        return this.currentAuthService.hasAnyRole('ROLE_LIBRARIAN', 'ROLE_ADMIN', 'ROLE_SERVICEMAN');
    }

    public async deleteDepartment(department: Department): Promise<void> {
        try {
            const response = await this.departmentDataService.delete(department);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(response);
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e);
            throw e;
        }
    }

    public async createDepartment(): Promise<Department> {
        try {
            const response = await this.departmentDataService.create();
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(response);
            return response.savedObject;
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e);
            throw e;
        }
    }

    public async editDepartment(department: Department): Promise<Department> {
        try {
            const response = await this.departmentDataService.edit({
                department,
                parentDepartment: department.parentId,
                name: department.name,
                order: department.order,
                exemplarable: department.exemplarable,
                central: department.central,
                online: department.online,
                sigla: department.sigla
            });
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(response);
            return response.savedObject;
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e);
            throw e;
        }
    }

    public goToHomepageIfCurrentDepartmentGotRemoved(department: Department) {
        if (this.currentDepartment.id === department.id) {
            this.walker.newPage('/');
        }
    }

    public transformIntoHierarchicalData(departments: ViewableDepartment[]): ViewableDepartmentsHierarchy[] {
        if (departments.length === 0) {
            return [];
        }

        let rootNodes = [];
        if (!this.currentDepartment.root) {
            const viewableCurrentDepartment = findFirst(departments, byIdOf(this.currentDepartment));
            if (isNullOrUndefined(viewableCurrentDepartment)) {
                throw new Error(`Current department: ${this.currentDepartment.id} not found in viewable departments`);
            }
            rootNodes = [viewableCurrentDepartment];
        }

        return transformIntoHierarchyNodes(departments, (department, subdepartments) => ({...department, subdepartments}), rootNodes);
    }
}