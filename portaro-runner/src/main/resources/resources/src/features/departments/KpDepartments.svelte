<script lang="ts">
    import type {ViewLayout} from 'shared/layouts/grid-system/built-layout/types';
    import type {SvelteComponentConstructor} from 'core/types';
    import type {DepartmentsReactivePageData} from './types';
    import {KpDepartmentsService} from './services/kp-departments.service';
    import {LayoutBuilderUtil} from 'shared/layouts/grid-system/built-layout/layout-builder-util';
    import {getInjector} from 'core/svelte-context/context';
    import {onMount} from 'svelte';
    import {createPageContext} from 'shared/layouts/page-context';
    import {defaultViewLayoutSettings} from 'shared/layouts/grid-system/built-layout/constants';
    import DepartmentsHeadingPart from './parts/DepartmentsHeadingPart.svelte';
    import DepartmentsDiagramPart from './parts/DepartmentsDiagramPart.svelte';
    import DepartmentsGridPart from './parts/DepartmentsGridPart.svelte';
    import KpLayoutGridContainer from 'shared/layouts/grid-system/KpLayoutGridContainer.svelte';
    import KpLayoutBuilder from 'shared/layouts/grid-system/built-layout/KpLayoutBuilder.svelte';

    const service = getInjector().getByToken<KpDepartmentsService>(KpDepartmentsService.serviceName);
    const pageContext = createPageContext<KpDepartmentsService, DepartmentsReactivePageData>(service);

    const pageLayout: ViewLayout = {
        settings: defaultViewLayoutSettings,
        parts: [
            LayoutBuilderUtil.createInRowComponent('default', 'heading'),
            LayoutBuilderUtil.createInRowComponent('default', 'grid'),
            LayoutBuilderUtil.createInRowComponent('wide', 'diagram')
        ]
    };

    const supportedDepartmentsComponents: Record<string, SvelteComponentConstructor> = {
        heading: DepartmentsHeadingPart,
        grid: DepartmentsGridPart,
        diagram: DepartmentsDiagramPart
    };

    onMount(async () => {
        const loadedDepartments = await service.getDepartments();
        pageContext.updateReactiveData({
            departments: loadedDepartments,
            hierarchicalDepartments: service.transformIntoHierarchicalData(loadedDepartments)
        });
    });
</script>

<KpLayoutGridContainer additionalClasses="kp-departments-page" rowsWidth="{pageLayout.settings.defaultRowWidth}">
    <KpLayoutBuilder layout="{pageLayout}" supportedComponents="{supportedDepartmentsComponents}"/>
</KpLayoutGridContainer>