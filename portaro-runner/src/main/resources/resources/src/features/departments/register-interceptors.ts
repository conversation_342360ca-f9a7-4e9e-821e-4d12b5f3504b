import type ModalConversationInterceptorService from '../../modals/modal-conversation-interceptor.service';

/*@ngInject*/
export function registerInterceptors(modalConversationInterceptor: ModalConversationInterceptorService) {
    modalConversationInterceptor.registerFieldEnablingConfirmationDialogFromExceptionHandler('DepartmentHasHoldingsException');
    modalConversationInterceptor.registerFieldEnablingConfirmationDialogFromExceptionHandler('DepartmentHasUsersException');
}