import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function departmentsRoutes($stateProvider: StateProvider) {
    let departmentsModule: { default: any; };
    $stateProvider
        .state({
            name: 'departments',
            url: '/departments',
            component:  KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => departmentsModule.default,
            },
            lazyLoad: async () => {
                departmentsModule = await import(/* webpackChunkName: "departments" */ './KpDepartments.svelte');
                return null;
            }
        });
}