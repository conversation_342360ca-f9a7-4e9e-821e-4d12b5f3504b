<script lang="ts">
    import type {Readable} from 'svelte/store';
    import type {Table} from '@tanstack/svelte-table';
    import type {ViewableDepartmentsHierarchy} from '../types';
    import type {Department} from 'typings/portaro.be.types';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {createEventDispatcher} from 'svelte';
    import {KpDepartmentsService} from '../services/kp-departments.service';
    import KpSearchBar from 'shared/ui-widgets/search/KpSearchBar.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';

    export let table$: Readable<Table<ViewableDepartmentsHierarchy>>;
    export let withoutAddButton = false;

    const localize = getLocalization();
    const presenter = getInjector().getByToken<KpDepartmentsService>(KpDepartmentsService.serviceName);
    const dispatch = createEventDispatcher<{'department-add': Department}>();

    async function create() {
        const department = await presenter.createDepartment();
        dispatch('department-add', department);
    }
</script>

<Flex class="departments-toolbar-part" alignItems="center" gap="ml">
    <KpSearchBar placeholder="{localize(/* @kp-localization commons.hledat */ 'commons.hledat')}"
                 enableReset
                 on:search={(event) => $table$.setGlobalFilter(event.detail)}
                 on:reset={() => $table$.resetGlobalFilter()}/>

    {#if presenter.canCreateDepartment() && !withoutAddButton}
        <Spacer flex="1"/>

        <KpButton buttonStyle="success-new" on:click={create}>
            <IconedContent icon="add">
                &nbsp;{localize( /* @kp-localization department.creation.title */ 'department.creation.title')}
            </IconedContent>
        </KpButton>
    {/if}
</Flex>