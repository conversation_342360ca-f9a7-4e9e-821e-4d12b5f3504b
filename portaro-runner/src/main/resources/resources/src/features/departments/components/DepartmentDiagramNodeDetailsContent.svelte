<script lang="ts">
    import type {ViewableDepartment} from 'typings/portaro.fe.types';
    import {getLocalization} from 'core/svelte-context/context';
    import NodeDetailsInfoRow from '../../../modals/diagram-node-details-modal/components/NodeDetailsInfoRow.svelte';
    import BooleanValue from 'shared/ui-widgets/grid/grid-cell/grid-cell-content-components/BooleanValue.svelte';
    import KpTitledSection from 'shared/layouts/containers/KpTitledSection.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    export let item: ViewableDepartment;

    const localize = getLocalization();
</script>

<Flex class="department-diagram-node-details-content" direction="column" gap="xl">
    <KpTitledSection title="Základní informace">
        <NodeDetailsInfoRow heading="ID">{item.id}</NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="{localize(/* @kp-localization commons.Poradi */ 'commons.Poradi')}">{item.order}</NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="{localize(/* @kp-localization commons.nazev */ 'commons.nazev')}">{item.text}</NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="{localize(/* @kp-localization department.Central */ 'department.Central')}">
            <BooleanValue value="{item.central}"/>
        </NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="{localize(/* @kp-localization department.Online */ 'department.Online')}">
            <BooleanValue value="{item.online}"/>
        </NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="{localize(/* @kp-localization department.Exemplarable */ 'department.Exemplarable')}">
            <BooleanValue value="{item.exemplarable}"/>
        </NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="{localize(/* @kp-localization department.Sigla */ 'department.Sigla')}">{item.sigla}</NodeDetailsInfoRow>
        <NodeDetailsInfoRow heading="{localize(/* @kp-localization commons.Locations */ 'commons.Locations')}">{item.locations.map((location) => location.text).join(', ')}</NodeDetailsInfoRow>
    </KpTitledSection>
</Flex>