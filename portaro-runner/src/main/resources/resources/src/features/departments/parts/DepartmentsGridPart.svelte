<script lang="ts">
    import type {Department, LabeledIdentified, NamedLabeled} from 'typings/portaro.be.types';
    import type {ViewableDepartment} from 'typings/portaro.fe.types';
    import type {DepartmentsReactivePageData, ViewableDepartmentsHierarchy} from '../types';
    import type {ColumnDef, TableOptions} from '@tanstack/svelte-table';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {KpDepartmentsService} from '../services/kp-departments.service';
    import {createRender} from 'svelte-render';
    import {writable} from 'svelte/store';
    import {defaultCellContent, defaultGlobalFilter, defaultRowId} from 'shared/ui-widgets/grid/utils';
    import {getCoreRowModel, getExpandedRowModel, getFilteredRowModel} from '@tanstack/svelte-table';
    import {onDestroy, tick} from 'svelte';
    import {getPageContext} from 'shared/layouts/page-context';
    import {exists} from 'shared/utils/custom-utils';
    import KpGenericGrid from 'shared/ui-widgets/grid/KpGenericGrid.svelte';
    import ExpandableValue from 'shared/ui-widgets/grid/grid-cell/grid-cell-content-components/ExpandableValue.svelte';
    import NamedLabeledText from 'shared/ui-widgets/grid/grid-cell/grid-cell-content-components/NamedLabeledText.svelte';
    import BooleanValue from 'shared/ui-widgets/grid/grid-cell/grid-cell-content-components/BooleanValue.svelte';
    import LabeledIdentifiedList from 'shared/ui-widgets/grid/grid-cell/grid-cell-content-components/LabeledIdentifiedList.svelte';
    import DepartmentsOptions from '../components/DepartmentsOptions.svelte';
    import DepartmentsToolbarPart from './DepartmentsToolbarPart.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import TextValue from 'shared/ui-widgets/grid/grid-cell/grid-cell-content-components/TextValue.svelte';
    import KpGenericGridContextProvider from 'shared/ui-widgets/grid/KpGenericGridContextProvider.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    const localize = getLocalization();
    const presenter = getInjector().getByToken<KpDepartmentsService>(KpDepartmentsService.serviceName);

    let hierarchicalDepartments: ViewableDepartmentsHierarchy[] = null;
    const pageContext = getPageContext<KpDepartmentsService, DepartmentsReactivePageData>();
    const reactiveDataUnsubscribe = pageContext.reactiveData.subscribe((currentData) => hierarchicalDepartments = currentData?.hierarchicalDepartments);

    let gridContainerElement: HTMLDivElement;

    onDestroy(() => {
        reactiveDataUnsubscribe();
    });

    function createTableOptions() {
        const defaultColumns: ColumnDef<ViewableDepartmentsHierarchy>[] = [
            {
                id: 'id',
                size: 50,
                accessorFn: (row) => row.id,
                header: () => 'ID',
                cell: ({cell}) => createRender(TextValue, {value: cell.getValue<number>()})
            },
            {
                id: 'order',
                size: 100,
                accessorFn: (row) => row.order,
                header: () => localize(/* @kp-localization commons.Poradi */ 'commons.Poradi'),
                cell: ({cell}) => createRender(TextValue, {value: cell.getValue<number>()})
            },
            {
                id: 'name',
                size: 300,
                accessorFn: (row) => ({name: row.name, text: row.text}),
                header: () => localize(/* @kp-localization commons.nazev */ 'commons.nazev'),
                cell: ({cell, row}) => createRender(ExpandableValue, {
                    isExpanded: row.getIsExpanded(),
                    canExpand: row.getCanExpand(),
                    depth: row.depth
                })
                    .on('click', row.getToggleExpandedHandler())
                    .slot(createRender(NamedLabeledText, {value: cell.getValue<NamedLabeled>()}))
            },
            {
                id: 'central',
                accessorFn: (row) => row.central,
                header: () => localize(/* @kp-localization department.Central */ 'department.Central'),
                cell: ({cell}) => createRender(BooleanValue, {value: cell.getValue<boolean>()})
            },
            {
                id: 'online',
                accessorFn: (row) => row.online,
                header: () => localize(/* @kp-localization department.Online */ 'department.Online'),
                cell: ({cell}) => createRender(BooleanValue, {value: cell.getValue<boolean>()})
            },
            {
                id: 'exemplarable',
                accessorFn: (row) => row.exemplarable,
                header: () => localize(/* @kp-localization department.Exemplarable */ 'department.Exemplarable'),
                cell: ({cell}) => createRender(BooleanValue, {value: cell.getValue<boolean>()})
            },
            {
                id: 'sigla',
                accessorFn: (row) => row.sigla,
                header: () => localize(/* @kp-localization department.Sigla */ 'department.Sigla'),
                cell: ({cell}) => createRender(TextValue, {value: cell.getValue<string>()})
            },
            {
                id: 'locations',
                size: 300,
                accessorFn: (row) => row.locations,
                header: () => localize(/* @kp-localization commons.Locations */ 'commons.Locations'),
                cell: ({cell}) => createRender(LabeledIdentifiedList, {values: cell.getValue<LabeledIdentified<number>[]>()})
            }
        ];

        const conditionalColumns: ColumnDef<ViewableDepartmentsHierarchy>[] = [
            {
                id: 'options',
                header: () => localize(/* @kp-localization commons.Options */ 'commons.Options'),
                cell: ({row}) => createRender(DepartmentsOptions, {
                    canEdit: presenter.canEditDepartment(),
                    canRemove: presenter.canDeleteDepartment() && !row.original.root
                })
                    // eslint-disable-next-line @typescript-eslint/no-misused-promises
                    .on('edit', () => edit(row.original))
                    // eslint-disable-next-line @typescript-eslint/no-misused-promises
                    .on('remove', () => remove(row.original))
            }
        ];

        const columns = presenter.canEditDepartment() || presenter.canDeleteDepartment() ? [...defaultColumns, ...conditionalColumns] : defaultColumns;
        return writable<TableOptions<ViewableDepartmentsHierarchy>>({
            data: [],
            columns,
            defaultColumn: {
                cell: defaultCellContent()
            },
            getSubRows: (row) => row.subdepartments,
            getRowId: defaultRowId(),
            globalFilterFn: defaultGlobalFilter((row, searchPhrase) => Boolean(row.original?.text?.toLowerCase()?.includes(searchPhrase))),
            getCoreRowModel: getCoreRowModel(),
            getFilteredRowModel: getFilteredRowModel(),
            getExpandedRowModel: getExpandedRowModel()
        });
    }

    async function remove(department: ViewableDepartment) {
        await presenter.deleteDepartment(department);
        await loadDepartments();
        presenter.goToHomepageIfCurrentDepartmentGotRemoved(department);
    }

    async function edit(department: ViewableDepartment) {
        await presenter.editDepartment(department);
        await loadDepartments();
    }

    async function scrollToDepartmentRowAfterLoad(department: Department) {
        await tick(); // wait for new data rerender
        gridContainerElement.querySelector(`.row-department-${department.id}`)?.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });
    }

    const handleDepartmentAdded = async (department: Department) => {
        await loadDepartments();
        await scrollToDepartmentRowAfterLoad(department);
    };

    async function loadDepartments() {
        const loadedDepartments = await pageContext.staticData.getDepartments();
        pageContext.updateReactiveData({
            departments: loadedDepartments,
            hierarchicalDepartments: pageContext.staticData.transformIntoHierarchicalData(loadedDepartments)
        });
    }
</script>

<Flex class="departments-table" direction="column" width="100%" gap="ml">
    <KpGenericGridContextProvider tableData="{hierarchicalDepartments}"
                                  options="{createTableOptions()}"
                                  hoverRows
                                  stickyHeader
                                  colorAccented
                                  columnResizingEnabled
                                  columnPinningEnabled
                                  selectionEnabled
                                  rowClassTemplate="{(row) => `row-department-${row.original.id}`}"
                                  let:gridContext>

        <DepartmentsToolbarPart table$={gridContext.table$} on:department-add={(event) => handleDepartmentAdded(event.detail)}/>

        {#if !exists(hierarchicalDepartments)}
            <KpLoadingBlock size="xs"/>
        {:else}
            <Flex class="departments-grid-container" width="100%" bind:element={gridContainerElement}>
                <KpGenericGrid table$={gridContext.table$} expandedFirstLevel/>
            </Flex>
        {/if}
    </KpGenericGridContextProvider>
</Flex>