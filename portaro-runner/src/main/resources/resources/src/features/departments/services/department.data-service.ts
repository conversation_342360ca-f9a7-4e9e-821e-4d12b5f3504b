import type {ActionResponse, BasicUser, Department, FinishedSaveResponse, Ordered} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';
import {transferify} from 'shared/utils/data-service-utils';
import {ngAsync} from 'shared/utils/ng-@decorators';

export default class DepartmentDataService {
    public static serviceName = 'departmentDataService';

    public static readonly ROUTE = 'departments';
    public static readonly ACTIVATE_ROUTE = 'activate';
    public static readonly DELETE_ROUTE = 'delete';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async activate(department: Department, initiator: BasicUser): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${DepartmentDataService.ROUTE}/${department.id}/${DepartmentDataService.ACTIVATE_ROUTE}`)
            .post(transferify({initiator}));
    }

    @ngAsync()
    public async getAll(): Promise<Department[]> {
        return this.ajaxService
            .createRequest(`${DepartmentDataService.ROUTE}`)
            .get();
    }

    @ngAsync()
    public async getByDepartmentId(departmentId: number): Promise<Department> {
        return this.ajaxService
            .createRequest(`${DepartmentDataService.ROUTE}/${departmentId}`)
            .get();
    }

    @ngAsync()
    public async delete(department: Department): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${DepartmentDataService.ROUTE}/${department.id}/${DepartmentDataService.DELETE_ROUTE}`)
            .post( transferify({department, confirmed: false}));
    }

    @ngAsync()
    public async create(): Promise<FinishedSaveResponse<Department>> {
        return this.ajaxService
            .createRequest(`${DepartmentDataService.ROUTE}`)
            .post({});
    }

    @ngAsync()
    public async edit(request: DepartmentEditationRequest): Promise<FinishedSaveResponse<Department>> {
        return this.ajaxService
            .createRequest(`${DepartmentDataService.ROUTE}/${request.department.id}`)
            .post(transferify({...request, confirmed: false}));
    }
}

interface DepartmentEditationRequest extends Ordered {
    department: Department;
    parentDepartment: number;
    name: string;
    exemplarable: boolean;
    online: boolean;
    central: boolean;
    sigla?: string;
}
