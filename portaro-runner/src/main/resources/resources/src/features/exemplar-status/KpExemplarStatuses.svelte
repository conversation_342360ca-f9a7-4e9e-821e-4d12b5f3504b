<script lang="ts">
    import type {DataDisplayerSettings} from 'typings/portaro.fe.types';
    import {ExemplarStatusDataService} from './exemplar-status.data-service';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import KpDataDisplayer from 'shared/ui-widgets/table/KpDataDisplayer.svelte';
    import KpPageContainer from 'shared/layouts/containers/KpPageContainer.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    const localize = getLocalization();
    const dataService = getInjector().getByToken<ExemplarStatusDataService>(ExemplarStatusDataService.serviceName);

    const settings = {
        loadAll: () => dataService.getAll(),
        columnsSetting: [
            {title: '#', key: 'id'},
            {title: localize( /* @kp-localization commons.popis */ 'commons.popis'), key: 'text'},
            {title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n<PERSON>', key: 'lendable'},
            {title: 'Rezervovatelný', key: 'reservable'}
        ]
    } as unknown as DataDisplayerSettings;
</script>

<KpPageContainer id="exemplar-statuses" additionalClasses="kp-exemplar-statuses-page">
    <KpHeading type="h1">
        {localize(/* @kp-localization commons.ExemplaroveStatusy */ 'commons.ExemplaroveStatusy')}
    </KpHeading>

    <KpDataDisplayer {settings}/>
</KpPageContainer>