import {ngAsync} from 'shared/utils/ng-@decorators';
import type {ExemplarStatus} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';

export class ExemplarStatusDataService {
    public static serviceName = 'exemplarStatusDataService';

    public static readonly ROUTE = 'exemplar-statuses';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async getAll(): Promise<ExemplarStatus[]> {
        return this.ajaxService
            .createRequest(`${ExemplarStatusDataService.ROUTE}`)
            .get();
    }
}