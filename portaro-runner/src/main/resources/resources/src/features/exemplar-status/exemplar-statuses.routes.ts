import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function exemplarStatusesRoutes($stateProvider: StateProvider) {
    let exemplarStatusesModule: { default: any; };
    $stateProvider
        .state({
            name: 'exemplar-statuses',
            url: '/exemplar-statuses',
            component:  KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => exemplarStatusesModule.default,
            },
            lazyLoad: async () => {
                exemplarStatusesModule = await import(/* webpackChunkName: "exemplar-statuses" */ './KpExemplarStatuses.svelte');
                return null;
            }
        });
}