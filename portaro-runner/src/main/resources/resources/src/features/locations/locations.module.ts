import register from '@kpsys/angularjs-register';
import locationsRoutes from './locations.routes';
import {KpLocationsPresenter} from './kp-locations.presenter';
import LocationDataService from './location.data-service';


export default register('portaro.features.locations')
    .config(locationsRoutes)
    .service(LocationDataService.serviceName, LocationDataService)
    .service(KpLocationsPresenter.presenterName, KpLocationsPresenter)
    .name();