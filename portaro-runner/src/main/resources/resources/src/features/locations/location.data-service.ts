import {transferify} from 'shared/utils/data-service-utils';
import {ngAsync} from 'shared/utils/ng-@decorators';
import type {Ordered, Department, Location, ActionResponse, LocationRequest} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';

export default class LocationDataService {
    public static serviceName = 'locationDataService';

    public static readonly ROUTE = 'locations';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async getAllByDepartment(department: Department): Promise<Location[]> {
        return this.ajaxService
            .createRequest(`${LocationDataService.ROUTE}`)
            .get({department: department.id});
    }

    @ngAsync()
    public async getAll(queryParams: {viewable?: boolean}): Promise<Location[]> {
        return this.ajaxService
            .createRequest(`${LocationDataService.ROUTE}`)
            .get(queryParams);
    }

    @ngAsync()
    public async save(locationRequest: LocationRequest | Ordered): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${LocationDataService.ROUTE}`)
            .post(transferify(locationRequest));
    }

    @ngAsync()
    public async remove(location: Location): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${LocationDataService.ROUTE}/${location.id}`)
            .delete();
    }
}
