import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function locationsRoutes($stateProvider: StateProvider) {
    let locationsModule: { default: any; };
    $stateProvider
        .state({
            name: 'locations',
            url: '/locations',
            component:  KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => locationsModule.default,
            },
            lazyLoad: async () => {
                locationsModule = await import(/* webpackChunkName: "locations" */ './KpLocations.svelte');
                return null;
            }
        });
}