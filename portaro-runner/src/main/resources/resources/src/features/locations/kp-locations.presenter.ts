import type LocationDataService from './location.data-service';
import type {Location} from 'typings/portaro.be.types';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type CurrentAuthService from 'shared/services/current-auth.service';

export class KpLocationsPresenter {
    public static presenterName = 'kpLocationsPresenter';

    /*@ngInject*/
    constructor(private locationDataService: LocationDataService,
                private finishedResponseInteractionService: FinishedResponseInteractionService,
                private currentAuthService: CurrentAuthService) {
    }

    public async getAll(): Promise<Location[]> {
        return this.locationDataService.getAll({viewable: true});
    }

    public canCreateLocation(): boolean {
        return this.currentAuthService.hasAnyRole('ROLE_LIBRARIAN', 'ROLE_ADMIN', 'ROLE_SERVICEMAN');
    }

    public async switch(location1: Location, location2: Location) {
        await Promise.all([this.locationDataService.save({...location1, order: location2.order}), this.locationDataService.save({...location2, order: location1.order})]);
    }

    public async create(locations: Location[]) {
        const maxOrder = Math.max(...locations.map((o) => o.order));
        try {
            const response = await this.locationDataService.save({order: maxOrder + 1});
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(response)
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e)
        }
    }

    public async remove(location: Location) {
        try {
            const response = await this.locationDataService.remove(location);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(response)
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e)
        }
    }

    public async edit(location: Location) {
        try {
            const response = await this.locationDataService.save({...location, confirmed: false});
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(response)
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e)
        }
    }
}