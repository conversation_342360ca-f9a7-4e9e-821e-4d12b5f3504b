<script lang="ts">
    import type {Location} from 'typings/portaro.be.types';
    import {KpLocationsPresenter} from './kp-locations.presenter';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {onMount} from 'svelte';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import KpBarebonesTable from 'shared/ui-widgets/table/barebones/KpBarebonesTable.svelte';
    import KpPageContainer from 'shared/layouts/containers/KpPageContainer.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    const localize = getLocalization();
    const presenter: KpLocationsPresenter = getInjector().getByToken(KpLocationsPresenter.presenterName);

    let locations = [] as Location[];

    onMount(async () => {
        locations = await presenter.getAll();
    });

    async function switchLocation(location1: Location, location2: Location) {
        await presenter.switch(location1, location2);
        locations = await presenter.getAll();
    }

    async function create() {
        await presenter.create(locations);
        locations = await presenter.getAll();
    }

    async function remove(location: Location) {
        await presenter.remove(location);
        locations = await presenter.getAll();
    }

    async function edit(location: Location) {
        await presenter.edit(location);
        locations = await presenter.getAll();
    }
</script>

<KpPageContainer id="locations" additionalClasses="kp-locations-page">
    <Flex alignItems="center" gap="m" justifyContent="space-between">
        <KpHeading type="h1">
            {localize(/* @kp-localization commons.Locations */ 'commons.Locations')}
        </KpHeading>

        {#if presenter.canCreateLocation()}
            <KpButton buttonStyle="success-new" on:click={create}>
                <IconedContent icon="add">
                    {localize(/* @kp-localization location.CreateNewLocation */ 'location.CreateNewLocation')}
                </IconedContent>
            </KpButton>
        {/if}
    </Flex>

    <KpBarebonesTable responsive colorAccented rowsTopBordered headerFooterDivided>
        <tr slot="header">
            <th>#</th>
            <th>{localize( /* @kp-localization commons.Poradi */ 'commons.Poradi')}</th>
            <th>{localize( /* @kp-localization commons.nazev */ 'commons.nazev')}</th>
            <th>{localize( /* @kp-localization commons.Rentals */ 'commons.Rentals')}</th>
            <th>{localize( /* @kp-localization commons.Options */ 'commons.Options')}</th>
        </tr>

        <svelte:fragment slot="body">
            {#each locations as location,index (location.id)}
                <tr>
                    <td>{location.id}</td>

                    <td>{location.order}</td>

                    <td>
                        <span>{location.name}</span>
                        <br/>
                        <span class="text-muted">({location.text})</span>
                    </td>

                    <td>
                        <ul>
                            {#each location.departments as department}
                                <li>{department.id} - {pipe(department, loc())}</li>
                            {/each}
                        </ul>
                    </td>

                    <td>
                        {#if location.editable || location.deletable}
                            {#if location.editable && index !== 0}
                                <KpButton buttonSize="xs" on:click={() => switchLocation(location, locations[index - 1])}>
                                    <UIcon icon="arrow-small-up"/>
                                </KpButton>
                            {/if}

                            {#if location.editable && index !== locations.length - 1 }
                                <KpButton buttonSize="xs" on:click={() => switchLocation(location, locations[index + 1])}>
                                    <UIcon icon="arrow-small-down"/>
                                </KpButton>
                            {/if}

                            {#if location.editable}
                                <KpButton buttonSize="xs" on:click={() => edit(location)}>
                                    <IconedContent icon="edit">
                                        {localize( /* @kp-localization commons.edit */ 'commons.edit')}
                                    </IconedContent>
                                </KpButton>
                            {/if}

                            {#if location.deletable}
                                <KpButton buttonSize="xs" buttonStyle="danger-new" on:click={() => remove(location)}>
                                    <IconedContent icon="trash">
                                        {localize( /* @kp-localization commons.Smazat */ 'commons.Smazat')}
                                    </IconedContent>
                                </KpButton>
                            {/if}
                        {/if}
                    </td>
                </tr>
            {/each}
        </svelte:fragment>
    </KpBarebonesTable>
</KpPageContainer>