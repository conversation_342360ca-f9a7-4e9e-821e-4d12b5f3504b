<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import type {ViewedContentPageData} from '../../../lib/mv-viewed-content';
    import {fade} from 'svelte/transition';
    import {getMediaViewerContext} from '../../../lib/mv-context';
    import {viewableFileContainsViewForm} from '../../../lib/mv-utils';
    import {FileViewForm} from 'shared/constants/portaro.constants';
    import {fly} from 'svelte/transition';
    import {onDestroy, onMount} from 'svelte';
    import {clickOutside} from 'shared/svelte-actions/use.click-outside';
    import {isCurrentLayout, isCurrentLayoutOrSmaller, MediaViewerLayoutSize, mediaViewerLayoutWidth} from '../../../lib/mv-layout';
    import {getViewedContentContext} from '../../../lib/mv-viewed-content';
    import {exists} from 'shared/utils/custom-utils';
    import {getLocalization} from 'core/svelte-context/context';
    import {CONTENTS_SEARCH_TOGGLE_EVENT} from '../../../lib/mv-constants';
    import MvTooltippedIconButton from '../../mv-misc/MvTooltippedIconButton.svelte';
    import MvFileContentsSearch from './MvFileContentsSearch.svelte';

    export let file: ViewableFile;

    const context = getMediaViewerContext();
    const localize = getLocalization();
    const viewedContent = getViewedContentContext();
    const searchPopoverFlyAnimParams = {y: 4, duration: 250};
    let searchOpen = false;
    let pageData: ViewedContentPageData;
    $: mobileLayout = isCurrentLayout(MediaViewerLayoutSize.MOBILE, $mediaViewerLayoutWidth);
    const pageDataUnsubscribe = viewedContent.pageData.subscribe((currentPageData) => pageData = currentPageData);

    onMount(() => {
        context.eventBus.addEventListener(CONTENTS_SEARCH_TOGGLE_EVENT, handleSearchToggle);
    });

    onDestroy(() => {
        pageDataUnsubscribe();

        context.eventBus.removeEventListener(CONTENTS_SEARCH_TOGGLE_EVENT, handleSearchToggle);
    });

    const handleSearchToggle = () => {
        searchOpen = !searchOpen;
    };

    const handleSearchClick = () => {
        searchOpen = !searchOpen;
    };

    const handleFileDownloadClick = async () => {
        await context.service.downloadFile(file);
    };

    const handleTogglePagesClick = () => {
        if (!exists(pageData)) {
            return;
        }

        viewedContent.setPageData({...pageData, pagesShown: !pageData.pagesShown});
    };
</script>

<div class="mv-file-action-buttons" in:fade={{duration: 250}}>
    {#if viewableFileContainsViewForm(file, FileViewForm.PLAIN_TEXT, FileViewForm.RICH_DOCUMENT)}
        <div class="search-button-container"
             class:mobile={mobileLayout}>

            <MvTooltippedIconButton id="search-action-btn"
                                    tooltipArrowDirection="top"
                                    tooltipLocation="bottom"
                                    icon="search"
                                    label="{localize(/* @kp-localization mediaviewer.content.ActionsSearchInFile */ 'mediaviewer.content.ActionsSearchInFile')}"
                                    customSize="34px"
                                    highlighted="{searchOpen}"
                                    on:click={handleSearchClick}/>

            {#if searchOpen}
                <div class="search-popover-container"
                     transition:fly={searchPopoverFlyAnimParams}
                     use:clickOutside
                     on:click-outside={() => searchOpen = false}>

                    <MvFileContentsSearch on:close={() => searchOpen = false}/>
                </div>
            {/if}
        </div>
    {/if}

    {#if file.exportPermission.allowed}
        <MvTooltippedIconButton id="email-action-btn"
                                tooltipArrowDirection="top"
                                tooltipLocation="bottom"
                                icon="envelope"
                                label="{localize(/* @kp-localization mediaviewer.content.ActionsSendInEmail */ 'mediaviewer.content.ActionsSendInEmail')}"
                                customSize="34px"
                                on:click={() => context.service.mailFiles([file])}/>
    {/if}

    {#if file.printPermission.allowed}
        <MvTooltippedIconButton id="print-action-btn"
                                tooltipArrowDirection="top"
                                tooltipLocation="bottom"
                                icon="print"
                                label="{localize(/* @kp-localization mediaviewer.content.ActionsPrint */ 'mediaviewer.content.ActionsPrint')}"
                                customSize="34px"
                                on:click={() => context.service.printFile(file)}/>
    {/if}

    <MvTooltippedIconButton id="copy-action-btn"
                            tooltipArrowDirection="top"
                            tooltipLocation="bottom"
                            icon="copy-alt"
                            label="{localize(/* @kp-localization mediaviewer.content.ActionsCopyUrl */ 'mediaviewer.content.ActionsCopyUrl')}"
                            customSize="34px"
                            on:click={() => context.service.copyUrlToClipboard()}/>

    {#if file.exportPermission.allowed}
        <MvTooltippedIconButton id="download-action-btn"
                                tooltipArrowDirection="top"
                                tooltipLocation="bottom"
                                icon="download"
                                label="{localize(/* @kp-localization mediaviewer.content.ActionsDownload */ 'mediaviewer.content.ActionsDownload')}"
                                customSize="34px"
                                on:click={handleFileDownloadClick}/>
    {/if}

    {#if exists(pageData) && isCurrentLayoutOrSmaller(MediaViewerLayoutSize.TABLET, $mediaViewerLayoutWidth)}
        <MvTooltippedIconButton id="toggle-pages-action-btn"
                                tooltipArrowDirection="top"
                                tooltipLocation="bottom"
                                icon="book-alt"
                                label="{localize(/* @kp-localization mediaviewer.content.ActionsPages */ 'mediaviewer.content.ActionsPages')}"
                                customSize="34px"
                                highlighted="{pageData.pagesShown}"
                                on:click={handleTogglePagesClick}/>
    {/if}
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-file-action-buttons {
        height: @media-viewer-topbar-elements-height;
        display: flex;
        align-items: center;
        border-radius: @border-radius-default;
        background-color: var(--viewer-default-button-color);
        outline: 1px solid var(--viewer-default-border);
    }

    .search-button-container {
        &:not(.mobile) {
            position: relative;
        }

        .search-popover-container {
            position: absolute;
            width: @media-viewer-topbar-search-popover-width;
            top: calc(100% + @spacing-ml);
            right: 0;
        }

        &.mobile .search-popover-container {
            top: calc(100% + @spacing-s);
            left: 50%;
            transform: translateX(-50%);
        }
    }

    :global {
        .mv-file-action-buttons {
            & > .search-button-container > .mv-tooltipped-icon-button {
                border-top-left-radius: @border-radius-default;
                border-bottom-left-radius: @border-radius-default;
            }

            & > .mv-tooltipped-icon-button:first-child {
                border-top-left-radius: @border-radius-default;
                border-bottom-left-radius: @border-radius-default;
            }

            & > .mv-tooltipped-icon-button:last-child {
                border-right: none;
                border-top-right-radius: @border-radius-default;
                border-bottom-right-radius: @border-radius-default;
            }
        }
    }
</style>