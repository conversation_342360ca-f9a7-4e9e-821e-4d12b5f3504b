<script lang="ts">
    import 'pdfjs-dist/web/pdf_viewer.css'; // CSS for text and annotation layers

    import type * as Pdfjs from 'pdfjs-dist/types/src/pdf';
    import type {ViewableFile} from 'typings/portaro.be.types';
    import {createEventDispatcher, onDestroy, onMount, tick} from 'svelte';
    import {loadPdfJsModule} from 'src/features/media-viewer/components/mv-viewed-content/mv-pdf-viewer/pdfjs';
    import {cleanup, exists, isFunction} from 'shared/utils/custom-utils';
    import {get} from 'svelte/store';
    import {getViewedContentContext} from 'src/features/media-viewer/lib/mv-viewed-content';
    import {range} from 'shared/utils/array-utils';
    import {MvPdfRenderService} from 'src/features/media-viewer/services/mv-pdf-render.service';
    import {calculateOptimalPdfPageScaling} from 'src/features/media-viewer/components/mv-viewed-content/mv-pdf-viewer/pdf-utils';
    import {getMediaViewerContext} from 'src/features/media-viewer/lib/mv-context';
    import {isCurrentLayoutOrSmaller, MediaViewerLayoutSize, mediaViewerLayoutWidth} from 'src/features/media-viewer/lib/mv-layout';
    import {CONTENT_SCROLL_EVENT, GO_TO_PAGE_EVENT, PDF_THUMBNAILS_WIDTH_PX, SCALE_TO_FIT_EVENT} from 'src/features/media-viewer/lib/mv-constants';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import MvFileTextualForm from 'src/features/media-viewer/components/mv-viewed-content/MvFileTextualForm.svelte';
    import MvPdfPage from './MvPdfPage.svelte';
    import MvPdfPasswordPrompt from './MvPdfPasswordPrompt.svelte';
    import MvPdfPageThumbnail from 'src/features/media-viewer/components/mv-viewed-content/mv-pdf-viewer/MvPdfPageThumbnail.svelte';

    export let file: ViewableFile;

    const context = getMediaViewerContext();
    const viewedContent = getViewedContentContext();
    const dispatch = createEventDispatcher<{
        'load-start': void,
        'load-success': void,
        'load-error': void
    }>();

    // PDF props
    let pdfDocument: Pdfjs.PDFDocumentProxy;
    let defaultPdfViewport: Pdfjs.PageViewport;
    let loadingTask: Pdfjs.PDFDocumentLoadingTask;
    const CMAP_URL = 'pdfjs-dist/cmaps/';

    // PDF password props
    let requirePassword = false;
    let wrongPassword = false;
    let newPasswordCallback: ((password: string) => void) | undefined;

    // Viewer props
    let loading = true;
    let scrolledToCenter = false;
    let pagesScrollableElement: HTMLDivElement;
    let pagesContainerElement: HTMLDivElement;
    let pdfRenderer: MvPdfRenderService;
    let viewerViewport = get(viewedContent.viewport);
    let viewerZoom = get(viewedContent.zoom);
    let pageData = get(viewedContent.pageData);
    let showingTextualInformation = get(viewedContent.showingTextualForm);
    let pdfRotation = get(viewedContent.pdfRotation);

    $: isTabletOrSmaller = isCurrentLayoutOrSmaller(MediaViewerLayoutSize.TABLET, $mediaViewerLayoutWidth);
    $: if (exists(pagesScrollableElement) && !scrolledToCenter) {
        scrollToCenterHorizontally(pagesScrollableElement);
        scrolledToCenter = true;
    }

    const viewportUnsubscribe = viewedContent.viewport.subscribe((currentViewport) => viewerViewport = currentViewport);
    const showingTextualInformationUnsubscribe = viewedContent.showingTextualForm.subscribe((textualForm) => showingTextualInformation = textualForm);
    const pdfRotationUnsubscribe = viewedContent.pdfRotation.subscribe((rotation) => pdfRotation = rotation);

    const zoomUnsubscribe = viewedContent.zoom.subscribe((currentZoom) => {
        viewerZoom = currentZoom;

        if (exists(pagesScrollableElement)) {
            scrollToCenterHorizontally(pagesScrollableElement);
        }

        if (exists(pageData?.current)) {
            viewedContent.eventBus.dispatchEvent(new CustomEvent<number>(GO_TO_PAGE_EVENT, {detail: pageData.current}));
        }
    });

    const pageDataUnsubscribe = viewedContent.pageData.subscribe((currentPageData) => {
        pageData = currentPageData;

        if (exists(pageData) && exists(context.urlManagementService)) {
            context.urlManagementService.setPageNumber(pageData.current);
        }
    });

    const urlStateSubscription = exists(context.urlManagementService) ? context.urlManagementService.getUrlState$().subscribe((state) => {
        const currentPageData = get(viewedContent.pageData);

        if (exists(state.page) && state.page !== currentPageData?.current?.toString()) {
            const pageNumber = Number(state.page);

            if (!isNaN(pageNumber)) {
                waitForPdfLoad().then(() => viewedContent.eventBus.dispatchEvent(new CustomEvent<number>(GO_TO_PAGE_EVENT, {detail: pageNumber})));
            }
        }
    }) : undefined;

    onMount(async () => {
        pdfDocument = await startLoadingDocument(`/files/${file.id}`);
        pdfRenderer = new MvPdfRenderService(pdfDocument);
        const firstPdfPage = await pdfDocument.getPage(1);
        defaultPdfViewport = firstPdfPage.getViewport({scale: 1, rotation: 0});

        const optimalZoom = calculateOptimalPdfPageScaling(defaultPdfViewport, viewerViewport, pdfRotation, isTabletOrSmaller ? 0 : PDF_THUMBNAILS_WIDTH_PX + 50);
        viewedContent.setZoom(optimalZoom);

        viewedContent.setPageData({
            current: 1,
            total: pdfDocument.numPages,
            pagesShown: false
        });

        viewedContent.eventBus.addEventListener(GO_TO_PAGE_EVENT, handleGoToPage);
        viewedContent.eventBus.addEventListener(SCALE_TO_FIT_EVENT, handleScaleToFit);

        loading = false;
        dispatch('load-success');
    });

    onDestroy(async () => {
        if (exists(loadingTask) && exists(loadingTask._worker) && loadingTask._worker && !loadingTask._worker.destroyed) {
            loadingTask._worker.destroy();
        }

        if (exists(loadingTask) && !loadingTask.destroyed) {
            await loadingTask.destroy();
        }

        viewedContent.setPageData(null);
        viewedContent.eventBus.removeEventListener(GO_TO_PAGE_EVENT, handleGoToPage);
        viewedContent.eventBus.removeEventListener(SCALE_TO_FIT_EVENT, handleScaleToFit);

        cleanup(viewportUnsubscribe, zoomUnsubscribe, pageDataUnsubscribe, showingTextualInformationUnsubscribe, pdfRotationUnsubscribe, urlStateSubscription);
    });

    async function startLoadingDocument(fileUrl: string): Promise<Pdfjs.PDFDocumentProxy> {
        const pdfJsModule = await loadPdfJsModule();

        loadingTask = pdfJsModule.getDocument({
            url: fileUrl,
            rangeChunkSize: 1048576 * 4, // 4MB chunk size
            enableHWA: true,
            cMapPacked: true,
            cMapUrl: CMAP_URL
        });

        loadingTask.onPassword = (newPassword: (value: string) => void, reason: number) => {
            wrongPassword = reason === 2; // reason equals 2 means that the password is wrong
            requirePassword = true;
            newPasswordCallback = newPassword;
        };

        dispatch('load-start');

        return loadingTask.promise;
    }

    const handlePdfPasswordSubmit = (event: CustomEvent<string>) => {
        if (!isFunction(newPasswordCallback)) {
            return;
        }

        newPasswordCallback(event.detail);
        requirePassword = false;
    };

    const handleGoToPage = (event: CustomEvent<number>) => {
        const pdfPageElement = document.getElementById(`mv-pdf-page-${event.detail}`);
        if (!exists(pdfPageElement)) {
            return;
        }

        pdfPageElement.scrollIntoView({
            behavior: 'instant',
            block: 'center',
            inline: 'center'
        });
    };

    const handleScaleToFit = () => {
        viewedContent.setZoom(calculateOptimalPdfPageScaling(defaultPdfViewport, viewerViewport, pdfRotation, isTabletOrSmaller ? 0 : PDF_THUMBNAILS_WIDTH_PX + 50));
    };

    const handlePagesScroll = (event: Event) => {
        const element = event.target as HTMLElement;

        viewedContent.eventBus.dispatchEvent(new CustomEvent<number>(
            CONTENT_SCROLL_EVENT,
            {detail: element.scrollTop}
        ));

        const children = Array.from(pagesContainerElement.children) as HTMLElement[];
        const viewportMiddle = window.innerHeight / 2;

        let closestChild: HTMLElement | null = null;
        let closestDistance = Infinity;

        for (const child of children) {
            const rect = child.getBoundingClientRect();
            const childMiddle = rect.top + rect.height / 2;
            const distance = Math.abs(viewportMiddle - childMiddle);

            if (distance < closestDistance) {
                closestDistance = distance;
                closestChild = child;
            }
        }

        if (exists(closestChild)) {
            const pageNumber = parseInt(closestChild.dataset.pageNumber, 10);
            const currentPageData = get(viewedContent.pageData);

            if (currentPageData.current !== pageNumber && !isNaN(pageNumber)) {
                viewedContent.setPageData({
                    ...currentPageData,
                    current: pageNumber
                });
            }
        }
    };

    function scrollToCenterHorizontally(element: HTMLDivElement) {
        tick().then(() => {
            const rect = element.getBoundingClientRect();
            const scrollWidth = element.scrollWidth;

            const centerX = (scrollWidth - rect.width) / 2;

            element.scrollTo({
                left: centerX,
                behavior: 'instant'
            });
        });
    }

    function waitForPdfLoad(): Promise<void> {
        return new Promise((resolve) => {
            if (!loading) {
                resolve();
            }

            const interval = setInterval(() => {
                if (!loading) {
                    clearInterval(interval);
                    resolve();
                }
            }, 100);
        });
    }
</script>

{#if loading || !exists(pdfDocument)}
    <div class="loading-container">
        <KpLoadingBlock/>
    </div>
{/if}

{#if requirePassword}
    <div class="password-prompt-container">
        <MvPdfPasswordPrompt {wrongPassword} on:password-submit={handlePdfPasswordSubmit}/>
    </div>
{/if}

{#if !loading && exists(pdfDocument)}
    <div class="mv-pdf-viewer">
        <div class="pages-container" on:scroll={handlePagesScroll} bind:this={pagesScrollableElement}>
            <div class="pages-overflow-container" bind:this={pagesContainerElement} style:--gap="{24 * viewerZoom}px">
                {#if !showingTextualInformation}
                    {#each range(1, pdfDocument.numPages) as pageNumber}
                        <MvPdfPage {pdfDocument} {viewerZoom} {pdfRotation} {pdfRenderer} {pageNumber}/>
                    {/each}
                {/if}

                {#if showingTextualInformation}
                    <MvFileTextualForm {file}/>
                {/if}
            </div>
        </div>

        <div class="thumbnails-container"
             style:--width="{PDF_THUMBNAILS_WIDTH_PX}px"
             class:is-tablet-or-smaller={isTabletOrSmaller}
             class:opened={pageData?.pagesShown ?? false}>

            {#each range(1, pageData.total) as pageNumber}
                <MvPdfPageThumbnail {pdfDocument} {pdfRotation} {pdfRenderer} {pageNumber} currentPage="{pageData.current}"/>
            {/each}
        </div>
    </div>
{/if}

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .password-prompt-container,
    .loading-container {
        position: absolute;
        z-index: 2;
        top: 47.5%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .mv-pdf-viewer {
        position: absolute;
        display: flex;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: var(--viewer-content-bg);

        .pages-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow-y: scroll;
            padding: @spacing-ml;

            .pages-overflow-container {
                margin-left: auto;
                margin-right: auto;
                display: flex;
                flex-direction: column;
                gap: var(--gap);
            }
        }

        .thumbnails-container {
            flex-shrink: 0;
            width: var(--width);
            height: 100%;
            display: flex;
            overflow-y: scroll;
            padding: @spacing-ml;
            gap: @spacing-sm;
            flex-direction: column;
            align-items: center;
            background-color: var(--viewer-content-bg);
            border-left: 1px solid var(--viewer-default-border);

            &.is-tablet-or-smaller {
                position: absolute;
                top: 0;
                bottom: 0;
                right: calc(var(--width) * -1);
                transition: right 0.3s ease-in-out;

                &.opened {
                    right: 0;
                }
            }
        }
    }
</style>