<script lang="ts">
    import MvOnboardingPage from '../MvOnboardingPage.svelte';
    import imgSrc from '../images/multimedia-players.webp';
    import {getLocalization} from 'core/svelte-context/context';

    const localize = getLocalization();
</script>

<MvOnboardingPage>
    <img src="{imgSrc}"
         alt="Multimedia Players"
         aria-labelledby="onboarding-new-multimedia-players"/>

    <h2 id="onboarding-new-multimedia-players">
        {localize(/* @kp-localization mediaviewer.onboarding.MediaPlayersImprovementsTitle */ 'mediaviewer.onboarding.MediaPlayersImprovementsTitle')}
    </h2>

    <span>
        {localize(/* @kp-localization mediaviewer.onboarding.MediaPlayersImprovementsParagraph1 */ 'mediaviewer.onboarding.MediaPlayersImprovementsParagraph1')}
    </span>

    <span>
        {localize(/* @kp-localization mediaviewer.onboarding.MediaPlayersImprovementsParagraph2 */ 'mediaviewer.onboarding.MediaPlayersImprovementsParagraph2')}
    </span>
</MvOnboardingPage>