<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import {pipe} from 'core/utils';
    import {byteFormatter} from 'shared/utils/pipes';
    import {getMediaViewerContext} from '../../../../lib/mv-context';
    import {onDestroy} from 'svelte';
    import {getThemeContext} from 'shared/theme/portaro-theme';
    import {fade} from 'svelte/transition';
    import {isFileImage} from '../../../../lib/mv-utils';
    import {getLocalization} from 'core/svelte-context/context';
    import MvDirectoryOrFileIcon from '../../../mv-misc/MvDirectoryOrFileIcon.svelte';
    import MvContextMenuWrapper from '../../mv-context-menu/MvContextMenuWrapper.svelte';
    import MvFileContextMenuContent from '../../mv-context-menu/MvFileContextMenuContent.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import MvCacheableThumbnail from '../../../mv-misc/MvCacheableThumbnail.svelte';

    export let file: ViewableFile;
    const context = getMediaViewerContext();
    const localize = getLocalization();
    const currentTheme = getThemeContext().currentTheme;
    const selectFileFadeAnimParams = {duration: 250} as const;

    let currentFileChecked = false;
    let selectingFiles = false;
    let viewedFile: ViewableFile | null;
    const viewedFileUnsubscribe = context.viewedFile.subscribe((currentViewedFile) => viewedFile = currentViewedFile);
    const selectingFilesUnsubscribe = context.selectingFiles.subscribe((currentSelectingFiles) => {
        selectingFiles = currentSelectingFiles;
        if (!currentSelectingFiles) {
            currentFileChecked = false;
        }
    });

    $: isFileViewed = viewedFile?.id === file.id;
    $: context.updateSelectedFiles(file, currentFileChecked);

    onDestroy(() => {
        selectingFilesUnsubscribe();
        viewedFileUnsubscribe();
    });

    const handleSelectFileClick = () => {
        if (selectingFiles) {
            currentFileChecked = !currentFileChecked;
            return;
        }

        context.setViewedFile(file);
    }
</script>

<MvContextMenuWrapper id="grid-view-file-{file.id}"
                      on:click={handleSelectFileClick}
                      let:open>

    <MvFileContextMenuContent slot="context-content" {file}/>

    <div class="mv-grid-directory-file"
         class:viewed={isFileViewed}
         class:context-menu-opened={open}
         class:file-selected={currentFileChecked}>

        {#if isFileViewed}
            <div class="file-viewed-icon-container"
                 transition:fade={selectFileFadeAnimParams}>

                <UIcon color="var(--brand-orange-new)"
                       icon="eye"
                       label="{localize(/* @kp-localization mediaviewer.fileExplorer.CurrentlyShownFileLabel */ 'mediaviewer.fileExplorer.CurrentlyShownFileLabel')}"/>
            </div>
        {/if}

        {#if selectingFiles}
            <div class="file-select-checkbox-container"
                 transition:fade={selectFileFadeAnimParams}>

                <input id="file-{file.id}-select"
                       tabindex="-1"
                       aria-hidden="true"
                       type="checkbox"
                       name="file-selected"
                       bind:checked={currentFileChecked}
                       style="color-scheme: {$currentTheme}"/>
            </div>
        {/if}

        <div class="icon-container">
            {#if isFileImage(file)}
                <MvCacheableThumbnail {file}
                                      let:loading
                                      let:loadError
                                      let:blobUrl>

                    <img class="thumbnail"
                         class:thumbnail-loading={loading || loadError}
                         loading="lazy"
                         alt="File {file.text}"
                         src="{blobUrl}"/>
                </MvCacheableThumbnail>
            {:else}
                <MvDirectoryOrFileIcon directoryOrFile="{file}"/>
            {/if}
        </div>

        <span class="file-info">
            <small class="file-name">{file.name}</small>
            <small class="file-size text-muted">{pipe(file.size, byteFormatter())}</small>
        </span>
    </div>
</MvContextMenuWrapper>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    @file-icon-size: 30px;

    .mv-grid-directory-file {
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;
        aspect-ratio: 11/16;
        padding: @spacing-s;
        border: 1px solid var(--viewer-default-border);
        cursor: pointer;
        outline: none;
        border-radius: @border-radius-default;
        transition: border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;

        &:hover,
        &.context-menu-opened {
            border-color: var(--brand-orange-new);
        }

        &.viewed {
            background-color: var(--viewer-orange-highlight-transparent);
            border-color: var(--brand-orange-new);
        }

        &.file-selected {
            border-color: var(--brand-orange-new);
        }

        .icon-container {
            position: relative;
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: @file-icon-size;
            margin-bottom: @spacing-xs;

            .thumbnail {
                max-width: 100%;
                max-height: 100%;
                width: 100%;
                height: 100%;
                overflow: hidden;
                border-radius: @border-radius-default;
                margin: 0;
                padding: 0;
                opacity: 1;
                border: 1px solid var(--viewer-default-border);
                transition: opacity 0.3s ease-in-out;

                &.thumbnail-loading {
                    opacity: 0.25;
                }
            }
        }

        .file-select-checkbox-container {
            position: absolute;
            top: @spacing-s;
            left: @spacing-s;
            display: flex;
            align-items: center;

            input {
                accent-color: var(--brand-orange-new);
                margin: 0;
                cursor: pointer;
            }
        }

        .file-viewed-icon-container {
            position: absolute;
            top: @spacing-s;
            right: @spacing-s;
        }

        .file-info {
            word-break: break-word;
            word-wrap: break-word;
            text-align: center;

            small {
                white-space: normal;
            }
        }
    }
</style>