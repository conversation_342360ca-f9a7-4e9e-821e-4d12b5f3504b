<script lang="ts">
    import MvOnboardingPage from '../MvOnboardingPage.svelte';
    import imgSrc from '../images/file-explorer.webp';
    import {getLocalization} from 'core/svelte-context/context';

    const localize = getLocalization();
</script>

<MvOnboardingPage>
    <img class="fill-size-image"
         src="{imgSrc}"
         alt="File explorer"
         aria-labelledby="onboarding-file-explorer-title"/>

    <h2 id="onboarding-file-explorer-title">
        {localize(/* @kp-localization mediaviewer.onboarding.FileExplorerInfoTitle */ 'mediaviewer.onboarding.FileExplorerInfoTitle')}
    </h2>

    <span>
        {localize(/* @kp-localization mediaviewer.onboarding.FileExplorerInfoParagraph1 */ 'mediaviewer.onboarding.FileExplorerInfoParagraph1')}
    </span>

    <span>
        {localize(/* @kp-localization mediaviewer.onboarding.FileExplorerInfoParagraph2 */ 'mediaviewer.onboarding.FileExplorerInfoParagraph2')}
    </span>

    <span>
        {localize(/* @kp-localization mediaviewer.onboarding.FileExplorerInfoParagraph3 */ 'mediaviewer.onboarding.FileExplorerInfoParagraph3')}
    </span>

    <small>
        {localize(/* @kp-localization mediaviewer.onboarding.FileExplorerInfoParagraph3Label */ 'mediaviewer.onboarding.FileExplorerInfoParagraph3Label')}
    </small>
</MvOnboardingPage>