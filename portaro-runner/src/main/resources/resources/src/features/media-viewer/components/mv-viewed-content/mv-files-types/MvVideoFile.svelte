<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import {fly} from 'svelte/transition';
    import MvVideoPlayer from '../mv-media-players/video/MvVideoPlayer.svelte';
    import {getLocalization} from 'core/svelte-context/context';

    export let file: ViewableFile;
    const flyInAnimParams = {y: 20, duration: 250};
    const localize = getLocalization();

    let loading = true;
    let loadError = false;

    const handleLoadSuccess = () => {
        loading = false;
    };

    const handleLoadError = () => {
        loadError = true;
        loading = false;
    };
</script>

<div class="mv-video-file">
    <MvVideoPlayer src="/files/{file.id}"
                   shown="{!loading && !loadError}"
                   on:loaded={handleLoadSuccess}
                   on:error={handleLoadError}/>
</div>

{#if loading}
    <div class="loading-container">
        <KpLoadingBlock size="sm"/>
    </div>
{/if}

{#if loadError}
    <div class="loading-container load-error-container" in:fly={flyInAnimParams}>
        <IconedContent icon="exclamation"
                       orientation="vertical"
                       align="center"
                       justify="center"
                       iconColor="var(--danger-red)">
            {localize(/* @kp-localization mediaviewer.content.VideoLoadError */ 'mediaviewer.content.VideoLoadError')}
        </IconedContent>
    </div>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.media-queries.less";
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-video-file {
        position: relative;
        width: 100%;
        height: 100%;
        flex: 1;
        padding: @spacing-ml @spacing-ml 30px;
        align-items: center;
        justify-content: center;
        display: flex;
        flex-direction: column;

        @media only screen and (max-width: @screen-sm-max) {
            padding: @spacing-m;
        }
    }

    .loading-container {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .load-error-container {
        color: var(--danger-red);
    }
</style>