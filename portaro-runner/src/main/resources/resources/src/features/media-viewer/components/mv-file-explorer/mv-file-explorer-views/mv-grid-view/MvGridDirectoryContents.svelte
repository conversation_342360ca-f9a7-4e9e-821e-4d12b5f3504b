<script lang="ts">
    import type {DirectoryNode, ViewableFile} from 'typings/portaro.be.types';
    import type {DirectoryContent} from '../../../../lib/mv-types';
    import {onMount} from 'svelte';
    import {getMediaViewerContext} from '../../../../lib/mv-context';
    import {flip} from 'svelte/animate';
    import {fly} from 'svelte/transition';
    import {popInAnim, popOutAnim} from 'shared/animations/pop-animations';
    import {isCurrentLayoutOrSmaller, MediaViewerLayoutSize, mediaViewerLayoutWidth} from '../../../../lib/mv-layout';
    import {getLocalization} from 'core/svelte-context/context';
    import {mediaViewerSidebarWidth} from '../../../../lib/mv-sidebar';
    import MvGridFile from './MvGridFile.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import MvGridDirectory from './MvGridDirectory.svelte';
    import {
        getGridViewColumnsCount,
        isDirectoryNode,
        isViewableFile,
        sortDirectoryContents
    } from '../../../../lib/mv-utils';

    export let directoryNode: DirectoryNode;

    const context = getMediaViewerContext();
    const localize = getLocalization();
    const directoryContentsFlipAnimParams = {duration: 250};
    const directoryContentsFlyInAnimParams = {y: 15, duration: 250};

    let loadingFiles = true;
    let files: ViewableFile[];
    let directoryEmpty = true;
    let directoryContents: DirectoryContent[];
    $: directoryEmpty = (directoryContents ?? []).length === 0;
    $: mobileSidebar = isCurrentLayoutOrSmaller(MediaViewerLayoutSize.TABLET, $mediaViewerLayoutWidth);

    onMount(async () => {
        files = await context.service.getFilesByDirectory(directoryNode.id);
        directoryContents = sortDirectoryContents([...directoryNode.children, ...files]);
        loadingFiles = false;
    });
</script>

{#if loadingFiles}
    <KpLoadingBlock size="sm"/>
{:else}
    {#if directoryEmpty}
        <span class="text-center">
            {localize(/* @kp-localization mediaviewer.fileExplorer.DirectoryEmptyLabel */ 'mediaviewer.fileExplorer.DirectoryEmptyLabel')}
        </span>
    {/if}

    <ul class="directory-contents-grid"
        in:fly={directoryContentsFlyInAnimParams}
        style:--columns="{getGridViewColumnsCount($mediaViewerSidebarWidth, mobileSidebar)}">

        {#each directoryContents as directoryContent(directoryContent.id)}
            <li animate:flip={directoryContentsFlipAnimParams}
                in:popInAnim={{key: directoryContent.id}}
                out:popOutAnim={{key: directoryContent.id}}>

                {#if isDirectoryNode(directoryContent)}
                    <MvGridDirectory directoryNode="{directoryContent}"/>
                {/if}

                {#if isViewableFile(directoryContent)}
                    <MvGridFile file="{directoryContent}"/>
                {/if}
            </li>
        {/each}
    </ul>
{/if}

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .directory-contents-grid {
        display: grid;
        gap: @spacing-base;
        grid-template-columns: repeat(var(--columns), 1fr);
    }
</style>