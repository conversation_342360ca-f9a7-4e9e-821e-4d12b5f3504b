import type {IDownloadManager} from 'pdfjs-dist/types/web/download_manager';

export class MvDownloadManager implements IDownloadManager {

    // Download data as a file
    downloadData(data: Uint8Array, filename: string, contentType: string = 'application/pdf'): void {
        const blob = new Blob([data], {type: contentType});
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';

        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        URL.revokeObjectURL(url);
    }

    // Either open the data or download it, destination is optional
    openOrDownloadData(data: Uint8Array, filename: string, dest: string = 'download'): boolean {
        try {
            if (dest === 'download') {
                this.downloadData(data, filename);
            } else {
                const blob = new Blob([data], {type: 'application/pdf'});
                const url = URL.createObjectURL(blob);
                window.open(url, '_blank');
            }
            return true;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('Failed to open or download data:', error);
            return false;
        }
    }

    // Downloading of data from a URL
    download(data: Uint8Array, url: string, filename: string, options?: any): void {
        fetch(url, options)
            .then((response) => response.arrayBuffer())
            .then((arrayBuffer) => {
                const newData = new Uint8Array(arrayBuffer);
                this.downloadData(newData, filename);
            })
            .catch((error) => {
                // eslint-disable-next-line no-console
                console.error('Failed to download file from URL:', error);
                this.downloadData(data, filename);
            });
    }
}