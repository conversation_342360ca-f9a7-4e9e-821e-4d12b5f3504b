<script lang="ts">
    import {createEventDispatcher, onDestroy, onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {getThemeContext} from 'shared/theme/portaro-theme';
    import {fly} from 'svelte/transition';
    import {getLocalization} from 'core/svelte-context/context';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import MvMediaPlayerVolumeControl from '../MvMediaPlayerVolumeControl.svelte';
    import MvMediaPlayerProgressBar from '../MvMediaPlayerProgressBar.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import MvMediaPlayerSpeedOptions from '../MvMediaPlayerSpeedOptions.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';

    export let src: string;
    export let shown = true;

    const currentTheme = getThemeContext().currentTheme;
    const controlsFlyAnimParams = {y: 25, duration: 250};
    const pictureInPictureInfoFlyAnimParams = {y: 10, duration: 250};
    const hideControlsDuration = 2500;
    const dispatch = createEventDispatcher<{ 'loaded': void, 'error': void }>();
    const localize = getLocalization();

    let isFullscreen = false;
    let isPictureInPicture = false;
    let buffering = false;
    let videoPlayerWrapperElement: HTMLDivElement;
    let videoPlayerElement: HTMLVideoElement;
    let isPlaying = false;
    let showingControls = false;
    let hideControlsTimeout: number | undefined;
    let viewportWidth = 0;
    let timeSkipAnimationTriggerKey = 0;
    $: isCompact = viewportWidth < 500;

    onMount(() => {
        window.addEventListener('keydown', handleKeyDown);

        if (exists(videoPlayerElement)) {
            videoPlayerElement.addEventListener('leavepictureinpicture', handleLeavePictureInPicture);
            videoPlayerElement.addEventListener('enterpictureinpicture', handleEnterPictureInPicture);
        }
    });

    onDestroy(() => {
        window.removeEventListener('keydown', handleKeyDown);

        if (exists(videoPlayerElement)) {
            videoPlayerElement.pause();
            // This will close the previous HTTP connection and trigger reload
            videoPlayerElement.src = 'data:video/mp4;base64,AAAAHG...MTAw';
            videoPlayerElement.load();
            videoPlayerElement.remove();

            videoPlayerElement.removeEventListener('leavepictureinpicture', handleLeavePictureInPicture);
            videoPlayerElement.removeEventListener('enterpictureinpicture', handleEnterPictureInPicture);
        }

        if (exists(hideControlsTimeout)) {
            window.clearTimeout(hideControlsTimeout);
        }
    });

    function play() {
        videoPlayerElement.play();
        isPlaying = true;
    }

    function pause() {
        videoPlayerElement.pause();
        isPlaying = false;
    }

    function hasPictureInPictureEnabled() {
        return 'pictureInPictureEnabled' in document
            && exists(document.pictureInPictureEnabled)
            && document.pictureInPictureEnabled === true;
    }

    const handlePlayerFullscreenChange = () => {
        isFullscreen = exists(document.fullscreenElement);
    }

    const handleFullScreenClick = () => {
        if (isFullscreen) {
            document.exitFullscreen();
            return;
        }

        videoPlayerWrapperElement.requestFullscreen();
    }

    const handlePictureInPictureClick = () => {
        if (document.pictureInPictureElement) {
            document.exitPictureInPicture().then(handleLeavePictureInPicture);
        } else if (document.pictureInPictureEnabled) {
            videoPlayerElement.requestPictureInPicture().then((pipWindow) => {
                if (!exists(pipWindow)) {
                    return;
                }

                isPictureInPicture = true;
            });
        }
    }

    const handleLeavePictureInPicture = () => {
        setTimeout(() => isPictureInPicture = false, 200);
    }

    const handleEnterPictureInPicture = () => {
        isPictureInPicture = true;
    }

    const handleKeyDown = (event: KeyboardEvent) => {
        if (event.code === 'Space') {
            handleVideoClick();
            return;
        }

        if (event.code === 'KeyF') {
            handleFullScreenClick();
            return;
        }

        if (event.code === 'ArrowLeft') {
            videoPlayerElement.currentTime = Math.max(0, videoPlayerElement.currentTime - 5);
            timeSkipAnimationTriggerKey = timeSkipAnimationTriggerKey < 0 ? timeSkipAnimationTriggerKey - 1 : -1;
            return;
        }

        if (event.code === 'ArrowRight') {
            videoPlayerElement.currentTime = Math.min(videoPlayerElement.duration, videoPlayerElement.currentTime + 5);
            timeSkipAnimationTriggerKey = timeSkipAnimationTriggerKey > 0 ? timeSkipAnimationTriggerKey + 1 : 1;
            return;
        }
    }

    const handleVideoClick = () => {
        if (isPlaying) {
            pause();
            return;
        }

        play();
    }

    const handleVideoMouseEvents = (enter: boolean) => {
        if (enter) {
            showingControls = true;
        }

        window.clearTimeout(hideControlsTimeout);
        hideControlsTimeout = window.setTimeout(() => showingControls = false, hideControlsDuration);
    }

    const handleBuffering = (value: boolean) => {
        buffering = value;
    }

    const handlePlaying = (value: boolean) => {
        isPlaying = value;
    }

    const handleLoadSuccess = () => {
        dispatch('loaded');
    }

    const handleLoadError = () => {
        dispatch('error');
    }
</script>

<!-- svelte-ignore a11y-click-events-have-key-events a11y-no-noninteractive-element-interactions -->
<div class="mv-video-player"
     role="application"
     bind:clientWidth={viewportWidth}
     bind:this={videoPlayerWrapperElement}
     on:click={handleVideoClick}
     on:fullscreenchange={handlePlayerFullscreenChange}
     on:mousemove={() => handleVideoMouseEvents(true)}
     on:mouseleave={() => handleVideoMouseEvents(false)}
     class:bordered={!isFullscreen}
     class:not-shown={!shown}>

    {#if isPictureInPicture}
        <div class="centered-over-video-container" transition:fly={pictureInPictureInfoFlyAnimParams}>
            <IconedContent icon="airplay"
                           orientation="vertical"
                           align="center"
                           justify="center">

                {localize(/* @kp-localization mediaviewer.content.MultiMediaVideoInPip */ 'mediaviewer.content.MultiMediaVideoInPip')}
            </IconedContent>
        </div>
    {/if}

    {#if buffering && !isPictureInPicture}
        <div class="centered-over-video-container"
             aria-label="{localize(/* @kp-localization mediaviewer.content.MultiMediaVideoLoadingLabel */ 'mediaviewer.content.MultiMediaVideoLoadingLabel')}">

            <KpLoadingBlock size="md" color="white"/>
        </div>
    {/if}

    {#key isPlaying && !isPictureInPicture}
        <div class="centered-over-video-container" aria-hidden="true">
            <div class="status-icon-container">
                <UIcon icon="{isPlaying ? 'play' : 'pause'}"/>
            </div>
        </div>
    {/key}

    {#key timeSkipAnimationTriggerKey}
        {#if timeSkipAnimationTriggerKey !== 0}
            {@const forwardSkip = timeSkipAnimationTriggerKey > 0}
            <div class="skip-video-container"
                 aria-hidden="true"
                 class:forward-skip={forwardSkip}
                 class:backward-skip={!forwardSkip}>

                <UIcon icon="time-{forwardSkip ? 'forward' : 'past'}"/>
                <span>5sec</span>
            </div>
        {/if}
    {/key}

    <!-- svelte-ignore a11y-media-has-caption -->
    <video class="video-player {$currentTheme}"
           class:player-hidden={isPictureInPicture}
           bind:this={videoPlayerElement}
           on:loadedmetadata={handleLoadSuccess}
           on:waiting={() => handleBuffering(true)}
           on:playing={() => handleBuffering(false)}
           on:pause={() => handlePlaying(false)}
           on:play={() => handlePlaying(true)}
           preload="auto">

        <source src="{src}"
                type="video/mp4"
                on:error={handleLoadError}>
    </video>

    {#if (showingControls || !isPlaying) && exists(videoPlayerElement)}
        <div class="controls-container"
             class:compact-controls={isCompact}
             role="group"
             on:click|stopPropagation
             transition:fly={controlsFlyAnimParams}
             class:full-screen-controls={isFullscreen}>

            <button class="icon-button"
                    aria-label="{isPlaying
                        ? localize(/* @kp-localization mediaviewer.content.MultiMediaPause */ 'mediaviewer.content.MultiMediaPause')
                        : localize(/* @kp-localization mediaviewer.content.MultiMediaPlay */ 'mediaviewer.content.MultiMediaPlay')}"
                    on:click={isPlaying ? pause : play}>

                <UIcon icon="{isPlaying ? 'pause' : 'play'}"/>
            </button>

            <MvMediaPlayerVolumeControl mediaElement="{videoPlayerElement}"/>
            <MvMediaPlayerProgressBar mediaElement="{videoPlayerElement}"/>

            {#if !isCompact}
                <MvMediaPlayerSpeedOptions mediaElement="{videoPlayerElement}"/>
            {/if}

            {#if hasPictureInPictureEnabled() && !isCompact}
                <button class="icon-button"
                        aria-label="{isPictureInPicture
                            ? localize(/* @kp-localization mediaviewer.content.MultiMediaLeavePip */ 'mediaviewer.content.MultiMediaLeavePip')
                            : localize(/* @kp-localization mediaviewer.content.MultiMediaEnterPip */ 'mediaviewer.content.MultiMediaEnterPip')}"
                        on:click={handlePictureInPictureClick}>

                    <UIcon icon="{isPictureInPicture ? 'desktop-arrow-down' : 'airplay'}"/>
                </button>
            {/if}

            <button class="icon-button"
                    aria-label="{isFullscreen
                        ? localize(/* @kp-localization mediaviewer.content.MultiMediaLeaveFullscreen */ 'mediaviewer.content.MultiMediaLeaveFullscreen')
                        : localize(/* @kp-localization mediaviewer.content.MultiMediaEnterFullscreen */ 'mediaviewer.content.MultiMediaEnterFullscreen')}"
                    on:click={handleFullScreenClick}>

                <UIcon icon="{isFullscreen ? 'compress' : 'expand'}"/>
            </button>
        </div>
    {/if}
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    @controls-height: 80px;
    @controls-height-compact: 60px;
    @horizontal-spacing: 32px;
    @horizontal-spacing-compact: 22px;
    @progress-bar-height: 8px;
    @progress-bar-gap: 20px;
    @progress-bar-gap-compact: 12px;

    .mv-video-player {
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: center;
        background-color: black;
        user-select: none;
        transition: scale 0.3s ease-in-out, opacity 0.3s ease-in-out;

        &.not-shown {
            scale: 0.9;
            opacity: 0;
        }

        &.bordered {
            border-radius: @border-radius-large;
            border: 1px solid var(--viewer-default-border);
        }

        &.light {
            box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.07);
        }

        .centered-over-video-container {
            position: absolute;
            top: 50%;
            left: 50%;
            z-index: 1;
            transform: translate(-50%, -50%);
            text-shadow: 2px 2px 35px rgba(0, 0, 0, 0.7);
            color: white;

            .status-icon-container {
                font-size: 32px;
                animation: 0.4s ease-in-out 0s 1 status-icon-pop-anim;
                animation-fill-mode: forwards;
            }
        }

        .skip-video-container {
            position: absolute;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: @spacing-xs;
            top: 52.5%;
            z-index: 1;
            transform: translateY(-50%);
            text-shadow: 2px 2px 20px rgba(0, 0, 0, 0.6);
            color: white;
            font-size: 20px;
            animation: 0.4s ease-in-out 0s 1 status-icon-pop-anim;
            animation-fill-mode: forwards;

            &.backward-skip {
                left: 36px;
            }

            &.forward-skip {
                right: 36px;
            }

            span {
                font-size: 12px;
                opacity: 0.75;
            }
        }

        //noinspection ALL
        .video-player {
            pointer-events: none;
            max-width: 100%;

            &::-webkit-media-controls {
                display: none !important;
            }

            &::-moz-media-controls {
                display: none !important;
            }

            &::media-controls {
                display: none !important;
            }

            &.player-hidden {
                opacity: 0;
            }
        }

        .controls-container {
            position: absolute;
            bottom: 0;
            left: 0;
            color: white;
            width: 100%;
            z-index: 1;
            padding: 0 @horizontal-spacing;
            display: flex;
            align-items: center;
            gap: @progress-bar-gap;
            height: @controls-height;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 30%, rgba(0, 0, 0, 0.75) 100%);

            &.full-screen-controls {
                position: fixed;
                z-index: 100;
            }

            &.compact-controls {
                padding: 0 @horizontal-spacing-compact;
                height: @controls-height-compact;
                gap: @progress-bar-gap-compact;
            }

            .icon-button {
                position: relative;
                background: none;
                outline: none;
                border: none;
                cursor: pointer;

                &::before {
                    content: '';
                    top: 50%;
                    left: 50%;
                    border-radius: 50%;
                    transform: translate(-50%, -50%);
                    position: absolute;
                    width: 32px;
                    height: 32px;
                    transition: background-color 0.3s ease-in-out;
                }

                &:hover::before {
                    background-color: var(--viewer-blue-highlight-transparent);
                }
            }
        }
    }

    @keyframes status-icon-pop-anim {
        0% {
            opacity: 1;
        }
        100% {
            scale: 1.3;
            opacity: 0;
        }
    }
</style>