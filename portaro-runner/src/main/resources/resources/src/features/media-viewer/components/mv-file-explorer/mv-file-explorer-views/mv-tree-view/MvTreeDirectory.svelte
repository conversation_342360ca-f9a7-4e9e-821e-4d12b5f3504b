<script lang="ts">
    import type {DirectoryNode, ViewableFile} from 'typings/portaro.be.types';
    import type {DirectoryContent} from '../../../../lib/mv-types';
    import type {ExpandUrlDirectoryEventData} from '../../../../lib/mv-constants';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import {slide} from 'svelte/transition';
    import {onDestroy, onMount, tick} from 'svelte';
    import KpLoadingInline from 'shared/components/kp-loading/KpLoadingInline.svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {getMediaViewerContext} from '../../../../lib/mv-context';
    import {isDirectoryNode, isViewableFile, sortDirectoryContents} from '../../../../lib/mv-utils';
    import {flip} from 'svelte/animate';
    import {popInAnim, popOutAnim} from 'shared/animations/pop-animations';
    import {replaceAll, byId} from 'shared/utils/array-utils';
    import {getLocalization} from 'core/svelte-context/context';
    import MvContextMenuWrapper from '../../mv-context-menu/MvContextMenuWrapper.svelte';
    import MvDirectoryContextMenuContent from '../../mv-context-menu/MvDirectoryContextMenuContent.svelte';
    import MvTreeFile from './MvTreeFile.svelte';
    import {DIRECTORY_LEFT_ARROW_EVENT, DIRECTORY_RIGHT_ARROW_EVENT, getTreeViewEventContext} from './event-context';
    import {
        DIRECTORY_CREATE_EVENT,
        DIRECTORY_DELETE_EVENT,
        DIRECTORY_EDIT_EVENT,
        FILE_CREATE_EVENT,
        FILES_DELETE_EVENT,
        FILE_METADATA_EDIT_EVENT,
        FILES_UPLOAD_EVENT,
        EXPAND_URL_DIRECTORY_EVENT,
    } from '../../../../lib/mv-constants';

    export let directoryNode: DirectoryNode;
    export let initiallyOpen = false;
    export let level = 1;

    const context = getMediaViewerContext();
    const localize = getLocalization();
    const treeViewEventBus = getTreeViewEventContext();
    const directoryContentsFlipAnimParams = {duration: 250};

    let loadingFiles = true;
    let files: ViewableFile[];
    let directoryEmpty = true;
    let subDirectoriesOpen = false;
    let directoryContents: DirectoryContent[];
    let contentsListElement: HTMLUListElement;
    $: directoryEmpty = (directoryContents ?? []).length === 0;

    let fileExplorerSelected: DirectoryNode;
    let viewedFile: ViewableFile | null;
    const fileExplorerSelectedUnsubscribe = context.fileExplorerSelectedDirectory.subscribe((currentFileExplorerSelected) => fileExplorerSelected = currentFileExplorerSelected);
    const viewedFileUnsubscribe = context.viewedFile.subscribe((currentViewedFile) => viewedFile = currentViewedFile);
    $: directorySelected = fileExplorerSelected?.id === directoryNode.id
        || exists((files ?? []).find((file) => file.id === viewedFile?.id)) && (!exists(fileExplorerSelected) || fileExplorerSelected?.id === directoryNode.id);

    onMount(async () => {
        context.eventBus.addEventListener(EXPAND_URL_DIRECTORY_EVENT, handleExpandUrlDirectory);

        files = await context.service.getFilesByDirectory(directoryNode.id);
        directoryContents = sortDirectoryContents([...directoryNode.children, ...files]);
        subDirectoriesOpen = initiallyOpen && directoryContents.length > 0;
        loadingFiles = false;

        context.eventBus.addEventListener(DIRECTORY_CREATE_EVENT, handleDirectoryCreate);
        context.eventBus.addEventListener(DIRECTORY_EDIT_EVENT, handleDirectoryEdit);
        context.eventBus.addEventListener(DIRECTORY_DELETE_EVENT, handleDirectoryDelete);
        context.eventBus.addEventListener(FILE_CREATE_EVENT, handleFileCreate);
        context.eventBus.addEventListener(FILES_UPLOAD_EVENT, handleFilesUpload);
        context.eventBus.addEventListener(FILE_METADATA_EDIT_EVENT, handleFileMetadataEdit);
        context.eventBus.addEventListener(FILES_DELETE_EVENT, handleFilesDelete);

        treeViewEventBus.addEventListener(DIRECTORY_LEFT_ARROW_EVENT, handleLeftArrowEvent);
        treeViewEventBus.addEventListener(DIRECTORY_RIGHT_ARROW_EVENT, handleRightArrowEvent);
    });

    onDestroy(() => {
        fileExplorerSelectedUnsubscribe();
        viewedFileUnsubscribe();

        context.eventBus.removeEventListener(DIRECTORY_CREATE_EVENT, handleDirectoryCreate);
        context.eventBus.removeEventListener(DIRECTORY_EDIT_EVENT, handleDirectoryEdit);
        context.eventBus.removeEventListener(DIRECTORY_DELETE_EVENT, handleDirectoryDelete);
        context.eventBus.removeEventListener(FILE_CREATE_EVENT, handleFileCreate);
        context.eventBus.removeEventListener(FILES_UPLOAD_EVENT, handleFilesUpload);
        context.eventBus.removeEventListener(FILE_METADATA_EDIT_EVENT, handleFileMetadataEdit);
        context.eventBus.removeEventListener(FILES_DELETE_EVENT, handleFilesDelete);
        context.eventBus.removeEventListener(EXPAND_URL_DIRECTORY_EVENT, handleExpandUrlDirectory);

        treeViewEventBus.removeEventListener(DIRECTORY_LEFT_ARROW_EVENT, handleLeftArrowEvent);
        treeViewEventBus.removeEventListener(DIRECTORY_RIGHT_ARROW_EVENT, handleRightArrowEvent);
    });

    const handleDirectoryCreate = (event: CustomEvent<DirectoryNode>) => {
        if (event.detail.parentDirectoryId !== directoryNode.id) {
            return;
        }

        directoryContents = sortDirectoryContents([...directoryContents, event.detail]);
        subDirectoriesOpen = true;
    }

    const handleDirectoryEdit = (event: CustomEvent<DirectoryNode>) => {
        if (event.detail.parentDirectoryId !== directoryNode.id) {
            return;
        }

        directoryContents = replaceAll(directoryContents, byId(event.detail.id), event.detail);
    }

    const handleDirectoryDelete = (event: CustomEvent<DirectoryNode>) => {
        if (event.detail.parentDirectoryId !== directoryNode.id) {
            return;
        }

        directoryContents = sortDirectoryContents(directoryContents.filter((dir) => {
            if (!isDirectoryNode(dir)) {
                return true;
            }

            return dir.id !== event.detail.id;
        }));

        if (directoryContents.length === 0 && subDirectoriesOpen) {
            subDirectoriesOpen = false;
        }

        if (fileExplorerSelected.id === event.detail.id) {
            context.setFileExplorerSelectedDirectory(directoryNode);
        }
    }

    const handleFolderClick = () => {
        const isCurrentlySelected = fileExplorerSelected?.id === directoryNode.id;
        context.setFileExplorerSelectedDirectory(directoryNode);

        if (subDirectoriesOpen && !isCurrentlySelected) {
            return;
        }

        subDirectoriesOpen = !directoryEmpty && !subDirectoriesOpen;
    }

    const handleFileCreate = (event: CustomEvent<ViewableFile>) => {
        const isCurrentDirectoryFile = event.detail.directory?.id === directoryNode.id;
        if (!isCurrentDirectoryFile) {
            return;
        }

        directoryContents = [...directoryContents, event.detail];

        if (!subDirectoriesOpen) {
            subDirectoriesOpen = true;
        }

        context.setViewedFile(event.detail);
    }

    const handleFilesUpload = (event: CustomEvent<ViewableFile[]>) => {
        const currentDirectoryFiles = event.detail.filter((file) => file.directory?.id === directoryNode.id);
        if (currentDirectoryFiles.length === 0) {
            return;
        }

        directoryContents = [...directoryContents, ...currentDirectoryFiles];

        if (!subDirectoriesOpen) {
            subDirectoriesOpen = true;
        }

        context.setViewedFile(currentDirectoryFiles[0]);
    }

    const handleFilesDelete = (event: CustomEvent<ViewableFile[]>) => {
        const currentDirectoryFiles = event.detail.filter((file) => file.directory?.id === directoryNode.id);
        if (currentDirectoryFiles.length === 0) {
            return;
        }

        directoryContents = directoryContents.filter((file) => !exists(currentDirectoryFiles.find((currentDirFile) => file.id === currentDirFile.id)));

        if (directoryContents.length === 0 && subDirectoriesOpen) {
            subDirectoriesOpen = false;
        }
    }

    const handleFileMetadataEdit = (event: CustomEvent<ViewableFile>) => {
        const isCurrentDirectoryFile = event.detail.directory?.id === directoryNode.id;
        if (!isCurrentDirectoryFile) {
            return;
        }

        directoryContents = replaceAll(directoryContents, byId(event.detail.id), event.detail);
    }

    const handleToggleContentsOpen = (event: CustomEvent<boolean>) => {
        subDirectoriesOpen = event.detail;
    }

    const handleLeftArrowEvent = (event: CustomEvent<number>) => {
        if (event.detail !== directoryNode.id) {
            return;
        }

        if (subDirectoriesOpen) {
            subDirectoriesOpen = false;
            return;
        }

        if (exists(directoryNode.parentDirectoryId)) {
            const parentElement = document.getElementById(`tree-view-directory-${directoryNode.parentDirectoryId}`);
            parentElement?.focus();
        }
    }

    const handleRightArrowEvent = (event: CustomEvent<number>) => {
        if (event.detail !== directoryNode.id) {
            return;
        }

        if (!subDirectoriesOpen) {
            subDirectoriesOpen = true && !directoryEmpty;
            return;
        }

        if (exists(contentsListElement)) {
            const firstChildElement = contentsListElement.querySelectorAll('li:first-child > *:first-child > *')[0] as HTMLElement | undefined;
            firstChildElement?.focus();
        }
    }

    const handleExpandUrlDirectory = (event: CustomEvent<ExpandUrlDirectoryEventData>) => {
        if (event.detail.parent.id !== directoryNode.id) {
            return;
        }

        // Function to wait until loadingFiles becomes true
        const waitForLoadingFiles = (): Promise<void> => {
            return new Promise((resolve) => {
                if (!loadingFiles) {
                    resolve();
                }

                const interval = setInterval(() => {
                    if (!loadingFiles) {
                        clearInterval(interval);
                        resolve();
                    }
                }, 100);
            });
        };

        waitForLoadingFiles().then(() => {
            const pathIndex = event.detail.path.indexOf(directoryNode);
            const nextDirectory = event.detail.path[pathIndex + 1];

            if (!directoryEmpty) {
                subDirectoriesOpen = true;
            }

            // Last directory has been opened
            if (!exists(nextDirectory)) {
                return;
            }

            tick().then(() => {
                context.eventBus.dispatchEvent(new CustomEvent<ExpandUrlDirectoryEventData>(EXPAND_URL_DIRECTORY_EVENT, {
                    detail: {
                        parent: nextDirectory,
                        path: event.detail.path
                    }
                }));
            });
        });
    }
</script>

<div id="directory-{directoryNode.id}"
     class="mv-directory"
     role="treeitem"
     aria-level="{level}"
     aria-selected="{directorySelected}"
     aria-expanded="{subDirectoriesOpen}">

    <MvContextMenuWrapper id="tree-view-directory-{directoryNode.id}"
                          on:click={handleFolderClick}
                          let:open>

        <MvDirectoryContextMenuContent slot="context-content"
                                       directory="{directoryNode}"
                                       contentsOpen="{subDirectoriesOpen}"
                                       on:toggle-contents-open={handleToggleContentsOpen}/>

        <div class="directory-name-row"
             class:context-menu-opened={open}
             class:selected={directorySelected}>

            <UIcon icon="{subDirectoriesOpen ? 'folder-open' : 'folder'}"/>

            <span class="text-ellipsis">{directoryNode.text}</span>

            <span class="full-width-divider"></span>

            {#if loadingFiles}
                <span class="loading-container">
                    <KpLoadingInline size="xs"/>
                </span>
            {:else}
                {#if !directoryEmpty}
                    <span class="unset-style open-indicator-container" class:open={subDirectoriesOpen}>
                        <UIcon icon="angle-small-right"/>
                    </span>
                {:else}
                    <span class="text-muted empty-label">
                        {localize(/* @kp-localization mediaviewer.fileExplorer.DirectoryEmptyShortLabel */ 'mediaviewer.fileExplorer.DirectoryEmptyShortLabel')}
                    </span>
                {/if}
            {/if}
        </div>
    </MvContextMenuWrapper>

    {#if subDirectoriesOpen}
        <ul class="directory-list"
            role="group"
            bind:this={contentsListElement}
            class:highlighted={directorySelected}
            transition:slide={{duration: 250 + directoryContents.length * Math.max(0, 10 - directoryContents.length / 15)}}>

            {#each directoryContents as directoryContent(directoryContent.id)}
                <li animate:flip={directoryContentsFlipAnimParams}
                    in:popInAnim={{key: directoryContent.id}}
                    out:popOutAnim={{key: directoryContent.id}}>

                    {#if isDirectoryNode(directoryContent)}
                        <svelte:self directoryNode="{directoryContent}"
                                     level="{level + 1}"/>
                    {/if}

                    {#if isViewableFile(directoryContent)}
                        <MvTreeFile file="{directoryContent}"
                                    parentDirectory="{directoryNode}"
                                    level="{level + 1}"/>
                    {/if}
                </li>
            {/each}
        </ul>
    {/if}
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    @vertical-row-spacing: 4px;

    .mv-directory {
        display: flex;
        flex-direction: column;

        .directory-name-row {
            text-align: start;
            position: relative;
            display: flex;
            outline: none;
            cursor: pointer;
            align-items: center;
            gap: @spacing-sm;
            padding: @vertical-row-spacing @spacing-s @vertical-row-spacing @spacing-base;
            border-radius: @border-radius-default;
            border: 1px solid transparent;
            transition: border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;

            &:hover,
            &.context-menu-opened {
                border-color: var(--accent-blue-new);
            }

            &.selected {
                background-color: var(--viewer-blue-highlight-transparent);
                border-color: var(--accent-blue-new);
            }

            .open-indicator-container {
                opacity: 0.5;
                transition: transform 0.2s ease-in-out, opacity 0.2s ease-in-out;

                &.open {
                    transform: rotate(90deg);
                    opacity: 1;
                }
            }

            .empty-label {
                font-size: @font-size-small;
            }

            .full-width-divider {
                margin-left: auto;
            }

            .loading-container {
                position: absolute;
                top: 50%;
                right: @spacing-s;
                scale: 0.9;
                transform: translateY(-50%);
            }
        }

        .directory-list {
            margin-top: @spacing-base;
            margin-left: 14px;
            border-left: 1px solid var(--viewer-bold-border);
            display: flex;
            flex-direction: column;
            gap: @spacing-base;
            padding-left: @spacing-base;
            transition: border-left-color 0.15s ease-in-out;

            &.highlighted {
                border-left-color: var(--accent-blue-new);
            }
        }
    }
</style>