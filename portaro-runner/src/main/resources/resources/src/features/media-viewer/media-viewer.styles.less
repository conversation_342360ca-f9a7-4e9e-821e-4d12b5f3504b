@import "styles/portaro.mixins.less";
@import "styles/portaro.variables.less";
@import "styles/portaro.media-queries.less";

main,
kp-svelte-component-wrapper:has(#media-viewer) {
    display: flex;
    flex-direction: column;
    flex: 1 1 0;
    touch-action: manipulation; // Disable mobile "double-tap zoom"
}

.global-alert.bg-danger {
    z-index: 100;

    &:last-of-type {
        border-bottom: 1px solid red;
    }

    @media only screen and (max-width: @screen-sm-max) {
        display: none;
    }
}

.kp-browser-progress-bar-component-wrapper {
    z-index: @browser-progress-bar-z-index;
    position: fixed;
}

.kp-toast-messages-component-wrapper {
    position: fixed;
    z-index: @toasts-z-index;
}

.erp-connection-bar-component-wrapper {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: @under-modal-z-index;
}

#media-viewer {
    .custom-scrollbars(var(--viewer-default-border), var(--viewer-bold-border));
}