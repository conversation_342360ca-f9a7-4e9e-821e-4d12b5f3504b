import type {DirectoryNode, ViewableFile} from 'typings/portaro.be.types';
import type {Readable} from 'svelte/store';
import type {FileExplorerViewType} from './mv-types';
import type {IMvStateService} from './mv-types';
import type {MvFileExplorerService} from '../services/mv-file-explorer.service';
import type {MvUrlStateService} from '../services/mv-url-state.service';
import type {MvImageCacheService} from '../services/mv-image-cache.service';
import {MvMemoryStateService} from '../services/mv-memory-state.service';
import {get} from 'svelte/store';
import {getContext, setContext} from 'svelte';
import {writable} from 'svelte/store';
import {exists} from 'shared/utils/custom-utils';
import {byIdOf, findFirstIndex} from 'shared/utils/array-utils';

const mediaViewerContextKey = 'media-viewer-ctx';

type ViewerType = 'media-viewer' | 'singular-file-viewer' | 'media-viewer-inline';

export interface MediaViewerContext {
    service: MvFileExplorerService;
    urlManagementService: IMvStateService,
    imageCacheService: MvImageCacheService;
    rootDirectory: DirectoryNode;
    fileExplorerSelectedDirectory: Readable<DirectoryNode>;
    viewedFile: Readable<ViewableFile | null>;
    selectingFiles: Readable<boolean>;
    filesSelected: Readable<ViewableFile[]>;
    searchQuery: Readable<string | null>;
    searchingForResults: Readable<boolean>;
    ongoingUploadPercentage: Readable<number | null>;
    fileExplorerViewType: Readable<FileExplorerViewType>;
    allowTreeViewType: boolean;
    canEdit: boolean;
    backToCatalogUrl: string | null;
    eventBus: EventTarget;
    setFileExplorerSelectedDirectory: (selected: DirectoryNode) => void;
    setViewedFile: (viewed: ViewableFile | null) => void;
    setSelectingFiles: (value: boolean) => void;
    addSelectedFile: (file: ViewableFile) => void;
    updateSelectedFiles: (file: ViewableFile, checked: boolean) => void;
    removeSelectedFile: (file: ViewableFile) => void;
    setSearchQuery: (query: string) => void;
    setFileExplorerViewType: (type: FileExplorerViewType) => void;
    setSearchingForResults: (value: boolean) => void;
    setOngoingUploadPercentage: (value: number | null) => void;
    viewerType: ViewerType;
}

export function createMediaViewerContext(service: MvFileExplorerService, urlStateService: MvUrlStateService, imageCacheService: MvImageCacheService, rootDirectory: DirectoryNode, canEdit: boolean, backToCatalogUrl: string | null, initialViewType: FileExplorerViewType, allowTreeViewType: boolean, currentlyViewedFile: ViewableFile | null = null, isInInlineMode: boolean): MediaViewerContext {
    const eventBus = new EventTarget();
    const fileExplorerSelectedDirectory = writable<DirectoryNode>(rootDirectory);
    const viewedFile = writable<ViewableFile | null>(currentlyViewedFile);
    const selectingFiles = writable<boolean>(false);
    const filesSelected = writable<ViewableFile[]>([]);
    const searchQuery = writable<string | null>(null);
    const searchingForResults = writable<boolean>(false);
    const fileExplorerViewType = writable<FileExplorerViewType>(initialViewType);
    const ongoingUploadPercentage = writable<number | null>(null);

    const addSelectedFile = (addedFile: ViewableFile) => filesSelected.update((files) => {
        const fileIndex = findFirstIndex(files, byIdOf(addedFile));

        if (fileIndex !== -1) {
            return files;
        }

        return [...files, addedFile];
    });

    const removeSelectedFile = (removedFile: ViewableFile) => {
        filesSelected.update((files) => files.filter((file) => file.id !== removedFile.id));

        if (get(filesSelected).length === 0) {
            selectingFiles.set(false);
        }
    };

    return setContext<MediaViewerContext>(mediaViewerContextKey, {
        service,
        urlManagementService: isInInlineMode ? new MvMemoryStateService(rootDirectory) : urlStateService,
        imageCacheService,
        rootDirectory,
        fileExplorerSelectedDirectory,
        viewedFile,
        selectingFiles,
        filesSelected,
        canEdit,
        backToCatalogUrl,
        eventBus,
        searchQuery,
        fileExplorerViewType,
        allowTreeViewType,
        searchingForResults,
        ongoingUploadPercentage,
        addSelectedFile,
        removeSelectedFile,
        setFileExplorerSelectedDirectory: (value: DirectoryNode) => {
            fileExplorerSelectedDirectory.set(value);
            urlStateService.setSelectedDirectory(value);
        },
        setViewedFile: (value: ViewableFile | null) => {
            viewedFile.set(value);
            urlStateService.setViewedFile(value);

            if (!exists(value)) {
                urlStateService.setPageNumber(null);
            }
        },
        setSelectingFiles: (value: boolean) => {
            if (!canEdit) {
                return;
            }

            selectingFiles.set(value);

            if (!value) {
                filesSelected.set([]);
            }
        },
        updateSelectedFiles: (file: ViewableFile, checked: boolean) => {
            if (!checked) {
                removeSelectedFile(file);
                return;
            }

            addSelectedFile(file);
        },
        setSearchQuery: (query: string) => {
            const newQuery = !exists(query) || query.length === 0 ? null : query;
            searchQuery.set(newQuery);
            urlStateService.setSearchQuery(newQuery);
        },
        setFileExplorerViewType: (type: FileExplorerViewType) => fileExplorerViewType.set(type),
        setSearchingForResults: (value: boolean) => searchingForResults.set(value),
        setOngoingUploadPercentage: (value: number | null) => ongoingUploadPercentage.set(value),
        viewerType: isInInlineMode ? 'media-viewer-inline' : 'media-viewer'
    });
}

export function createSingularFileViewerContext(file: ViewableFile, imageCacheService: MvImageCacheService): MediaViewerContext {
    const eventBus = new EventTarget();
    const viewedFile = writable<ViewableFile | null>(file);

    return setContext<MediaViewerContext>(mediaViewerContextKey, {
        eventBus,
        viewedFile,
        imageCacheService,
        canEdit: false,
        service: undefined,
        urlManagementService: undefined,
        rootDirectory: undefined,
        fileExplorerSelectedDirectory: undefined,
        selectingFiles: undefined,
        filesSelected: undefined,
        searchQuery: undefined,
        searchingForResults: undefined,
        ongoingUploadPercentage: undefined,
        fileExplorerViewType: undefined,
        allowTreeViewType: undefined,
        backToCatalogUrl: undefined,
        setFileExplorerSelectedDirectory: undefined,
        setViewedFile: undefined,
        setSelectingFiles: undefined,
        addSelectedFile: undefined,
        updateSelectedFiles: undefined,
        removeSelectedFile: undefined,
        setSearchQuery: undefined,
        setFileExplorerViewType: undefined,
        setSearchingForResults: undefined,
        setOngoingUploadPercentage: undefined,
        viewerType: 'singular-file-viewer'
    });
}

export function getMediaViewerContext(): MediaViewerContext {
    return getContext<MediaViewerContext>(mediaViewerContextKey);
}