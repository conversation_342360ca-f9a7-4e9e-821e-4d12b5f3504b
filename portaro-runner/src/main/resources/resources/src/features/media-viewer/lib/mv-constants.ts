import type {DirectoryNode} from 'typings/portaro.be.types';

// Version string displayed in settings popover
export const MV_VERSION = 'v2.2.1 release';

// File explorer related events
export const DIRECTORY_CREATE_EVENT = 'mv-file-explorer-directory-create-event';
export const DIRECTORY_EDIT_EVENT = 'mv-file-explorer-directory-edit-event';
export const DIRECTORY_DELETE_EVENT = 'mv-file-explorer-directory-delete-event';
export const FILE_CREATE_EVENT = 'mv-file-explorer-file-create-event';
export const FILE_METADATA_EDIT_EVENT = 'mv-file-explorer-metadata-edit-event';
export const FILES_UPLOAD_EVENT = 'mv-file-explorer-files-upload-event';
export const FILES_DELETE_EVENT = 'mv-file-explorer-files-delete-event';
export const EXPAND_URL_DIRECTORY_EVENT = 'mv-expand-url-directory-event';

// Viewed content related events
export const SCALE_TO_FIT_EVENT = 'mv-viewed-content-scale-to-fit-event';
export const CONTENTS_SEARCH_TOGGLE_EVENT = 'mv-viewed-content-search-toggle-event';
export const SEARCH_IN_FILE_EVENT = 'mv-viewed-content-search-in-file-event';
export const SEARCH_SHOW_NEXT_RESULT_EVENT = 'mv-viewed-content-search-show-next-result-event';
export const SEARCH_SHOW_PREVIOUS_RESULT_EVENT = 'mv-viewed-content-show-previous-result-event';
export const GO_TO_PAGE_EVENT = 'mv-viewed-go-to-page-event';
export const CONTENT_SCROLL_EVENT = 'mv-viewed-content-scroll-event';
export const TOGGLE_PDF_PRESENTATION_EVENT = 'mv-toggle-pdf-presentation-event';
export const NEXT_IMAGE_EVENT = 'mv-next-image-event';
export const PREVIOUS_IMAGE_EVENT = 'mv-previous-image-event';

// Misc events
export const OPEN_ONBOARDING_MODAL = 'mv-open-onboarding-modal-event';

// File extensions to ACE mode
export const fileExtensionToAceMode = {
    'css': 'css',
    'html': 'html',
    'json': 'json',
    'vtl': 'velocity',
    'vm': 'velocity',
    'properties': 'properties'
};

// Data for event EXPAND_URL_DIRECTORY_EVENT
export interface ExpandUrlDirectoryEventData {
    parent: DirectoryNode;
    path: DirectoryNode[];
}

// PDF constants
export const PDF_THUMBNAILS_WIDTH_PX = 150;