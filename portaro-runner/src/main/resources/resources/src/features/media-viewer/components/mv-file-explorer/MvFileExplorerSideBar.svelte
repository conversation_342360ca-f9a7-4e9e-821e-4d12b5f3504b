<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import type {TouchSwipeEvent} from '../../actions/use.touch-swipe';
    import type {FileExplorerViewType} from '../../lib/mv-types';
    import {onDestroy} from 'svelte';
    import {getMediaViewerContext} from '../../lib/mv-context';
    import {clickOutside} from 'shared/svelte-actions/use.click-outside';
    import {exists} from 'shared/utils/custom-utils';
    import {touchSwipe} from '../../actions/use.touch-swipe';
    import {getLocalization} from 'core/svelte-context/context';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import MvFilesSearchBar from './mv-file-search/MvFilesSearchBar.svelte';
    import MvSideBarDraggableResizer from './MvSideBarDraggableResizer.svelte';
    import KpButtonStyleAnchor from 'shared/ui-widgets/button/KpButtonStyleAnchor.svelte';
    import MvViewSwitcher from './mv-file-explorer-views/MvViewSwitcher.svelte';
    import MvFilesSearcher from './mv-file-search/MvFilesSearcher.svelte';
    import MvDirectoriesTreeView from './mv-file-explorer-views/mv-tree-view/MvDirectoriesTreeView.svelte';
    import MvSideBarActionButtonsContainer from './mv-action-buttons/MvSideBarActionButtonsContainer.svelte';
    import MvDirectoriesGridView from './mv-file-explorer-views/mv-grid-view/MvDirectoriesGridView.svelte';
    import {
        isCurrentLayoutOrBigger,
        isCurrentLayoutOrSmaller,
        MediaViewerLayoutSize,
        mediaViewerLayoutWidth
    } from '../../lib/mv-layout';
    import {
        mediaViewerSidebarOpen,
        mediaViewerSidebarWidth,
        mobileSidebarWidth,
        toggleMediaViewerSidebar
    } from '../../lib/mv-sidebar';

    const context = getMediaViewerContext();
    const localize = getLocalization();
    let selectingFiles = false;
    let searchQuery: string | null = null;
    let viewedFile: ViewableFile | null;
    let fileExplorerViewType: FileExplorerViewType;
    $: mobileSidebar = isCurrentLayoutOrSmaller(MediaViewerLayoutSize.TABLET, $mediaViewerLayoutWidth);
    $: isInSearch = searchQuery && searchQuery.length > 0;
    const fileExplorerViewTypeUnsubscribe = context.fileExplorerViewType.subscribe((currentViewType) => fileExplorerViewType = currentViewType);
    const selectingFilesUnsubscribe = context.selectingFiles.subscribe((currentSelectingFiles) => selectingFiles = currentSelectingFiles);
    const searchQueryUnsubscribe = context.searchQuery.subscribe((currentSearchQuery) => searchQuery = currentSearchQuery);
    const viewedFileUnsubscribe = context.viewedFile.subscribe((currentViewedFile) => {
        if (currentViewedFile?.id === viewedFile?.id) {
            return;
        }

        viewedFile = currentViewedFile;
    });
    const urlStateSubscription = context.urlManagementService.getUrlState$().subscribe((state) => {
        if (exists(state.q) && state.q !== searchQuery) {
            context.setSearchQuery(state.q);
        }
    });

    onDestroy(() => {
        selectingFilesUnsubscribe();
        searchQueryUnsubscribe();
        viewedFileUnsubscribe();
        fileExplorerViewTypeUnsubscribe();
        urlStateSubscription.unsubscribe();
    });

    const handleCloseSidebarButtonClick = () => {
        toggleMediaViewerSidebar(false);
    }

    const handleToggleSelectingFilesClick = () => {
        context.setSelectingFiles(!selectingFiles);
    }

    const handleCancelSearchClick = () => {
        context.setSearchQuery(null);
    }

    const handleClickOutside = () => {
        if (!mobileSidebar || !$mediaViewerSidebarOpen) {
            return;
        }

        toggleMediaViewerSidebar(false);
    }

    const handleTouchSwipe = (event: TouchSwipeEvent) => {
        if (event.detail !== 'left') {
            return;
        }

        toggleMediaViewerSidebar(false);
    }
</script>

<div class="mv-sidebar"
     style="width: {mobileSidebar ? mobileSidebarWidth : $mediaViewerSidebarWidth}px"
     use:touchSwipe={{disabled: !mobileSidebar}}
     class:mobile-sidebar={mobileSidebar}
     use:clickOutside
     on:click-outside={handleClickOutside}
     on:touch-swipe={handleTouchSwipe}>

    {#if context.viewerType === 'media-viewer'}
        <div class="path-name-container">
            <span class="name" title="{context.rootDirectory.name}">{context.rootDirectory.name}</span>

            {#if mobileSidebar}
                <KpButton additionalClasses="mv-close-sidebar-button"
                          buttonStyle="default-viewer-themed"
                          on:click={handleCloseSidebarButtonClick}>
                    <UIcon icon="arrow-left"/>
                </KpButton>
            {/if}
        </div>
    {/if}

    <div class="sidebar-content-container">
        {#if mobileSidebar && context.backToCatalogUrl}
            <KpButtonStyleAnchor href="{context.backToCatalogUrl}" buttonStyle="brand-orange-new">
                <IconedContent icon="arrow-small-left">
                    {localize(/* @kp-localization mediaviewer.fileExplorer.BackToCatalogBtnLabel */ 'mediaviewer.fileExplorer.BackToCatalogBtnLabel')}
                </IconedContent>
            </KpButtonStyleAnchor>
        {/if}

        <MvFilesSearchBar/>

        {#if isInSearch}
            <KpButton buttonStyle="danger-new" on:click={handleCancelSearchClick}>
                <IconedContent icon="cross-circle">
                    {localize(/* @kp-localization mediaviewer.fileExplorer.CancelSearch */ 'mediaviewer.fileExplorer.CancelSearch')}
                </IconedContent>
            </KpButton>

            <MvViewSwitcher selectableViewTypes="{['grid', 'column']}"/>
        {/if}

        {#if context.canEdit}
            <KpButton buttonStyle="{selectingFiles ? 'brand-orange-new' : 'default-viewer-themed'}"
                      on:click={handleToggleSelectingFilesClick}>
                <IconedContent icon="{selectingFiles ? 'cross-circle' : 'checkbox'}">
                    {selectingFiles
                        ? localize(/* @kp-localization mediaviewer.fileExplorer.HideFileSelection */ 'mediaviewer.fileExplorer.HideFileSelection')
                        : localize(/* @kp-localization mediaviewer.fileExplorer.ShowFileSelection */ 'mediaviewer.fileExplorer.ShowFileSelection')}
                </IconedContent>
            </KpButton>
        {/if}

        {#if isInSearch}
            {#key searchQuery}
                <MvFilesSearcher {searchQuery}/>
            {/key}
        {:else}
            {#if !context.canEdit}
                <MvViewSwitcher selectableViewTypes="{['tree', 'grid']}"/>
            {/if}

            {#if fileExplorerViewType === 'grid'}
                <MvDirectoriesGridView/>
            {:else}
                <MvDirectoriesTreeView/>
            {/if}
        {/if}

        {#if mobileSidebar && context.canEdit}
            <MvSideBarActionButtonsContainer mobile="{true}"/>
        {/if}
    </div>

    {#if !mobileSidebar && context.canEdit}
        <MvSideBarActionButtonsContainer mobile="{false}"/>
    {/if}

    {#if isCurrentLayoutOrBigger(MediaViewerLayoutSize.LAPTOP, $mediaViewerLayoutWidth)}
        <MvSideBarDraggableResizer/>
    {/if}
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    :global {
        .mv-close-sidebar-button {
            height: @media-viewer-topbar-elements-height;
        }
    }

    .mv-sidebar {
        max-width: 95vw;
        height: 100%;
        display: flex;
        flex-direction: column;
        background-color: var(--viewer-sidebar-bg);
        transition: border-right-color 0.3s ease-in-out;
        animation: 0.4s ease-in-out 0s 1 sidebar-slide-from-left;

        &.mobile-sidebar {
            border-right-color: var(--viewer-bold-border);
            animation: none;
        }

        .path-name-container {
            width: 100%;
            padding: 0 @spacing-ml;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: @media-viewer-topbar-height;

            .name {
                font-size: 14.5px;
                font-weight: bold;
                overflow: hidden;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                line-height: 1.2em;
                max-height: 2.4em;
            }
        }

        .sidebar-content-container {
            flex: 1;
            padding: @spacing-ml;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            overflow-x: hidden;
            gap: @spacing-ml;
        }
    }

    @keyframes sidebar-slide-from-left {
        0% {
            transform: translateX(-25%);
            opacity: 0;
        }
        100% {
            transform: translateX(0);
            opacity: 100%;
        }
    }
</style>