<script lang="ts">
    import type * as Pdfjs from 'pdfjs-dist';
    import type {PdfRotation} from 'src/features/media-viewer/lib/mv-viewed-content';
    import type {MvPdfRenderService} from 'src/features/media-viewer/services/mv-pdf-render.service';
    import {getViewedContentContext} from 'src/features/media-viewer/lib/mv-viewed-content';
    import {onDestroy, onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {GO_TO_PAGE_EVENT} from 'src/features/media-viewer/lib/mv-constants';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';

    export let pdfDocument: Pdfjs.PDFDocumentProxy;
    export let pageNumber: number;
    export let pdfRenderer: MvPdfRenderService;
    export let pdfRotation: PdfRotation;
    export let currentPage: number;

    const viewedContent = getViewedContentContext();
    const maxThumbnailWidthPx = 86;

    let page: Pdfjs.PDFPageProxy;
    let wrapperElement: HTMLElement;
    let intersectionObserver: IntersectionObserver;
    let renderedThumbnailUrl: string | null = null;

    let isMounted = false;
    let isVisible = false;
    let oldRotation = -1;
    $: updateProps(pdfRotation, isMounted, isVisible);

    onMount(async () => {
        page = await pdfDocument.getPage(pageNumber);

        intersectionObserver = new IntersectionObserver((entries: IntersectionObserverEntry[]) => {
            isVisible = entries.some((entry) => entry.isIntersecting);
        }, {
            rootMargin: '500px',
            threshold: 0.01
        });

        intersectionObserver.observe(wrapperElement);
        isMounted = true;
    });

    onDestroy(() => {
        if (exists(intersectionObserver)) {
            intersectionObserver.disconnect();
        }
    });

    async function updateProps(newRotation: PdfRotation, mounted: boolean, visible: boolean) {
        if (!mounted) {
            return;
        }

        if (oldRotation !== newRotation) {
            oldRotation = newRotation;
            renderedThumbnailUrl = null;
        }

        if (!exists(renderedThumbnailUrl) && visible) {
            renderedThumbnailUrl = await pdfRenderer.renderThumbnail(page, newRotation, maxThumbnailWidthPx);
        }
    }

    const handleClick = () => {
        viewedContent.eventBus.dispatchEvent(new CustomEvent<number>(GO_TO_PAGE_EVENT, {detail: pageNumber}));
    };
</script>

<button class="mv-pdf-page-thumbnail"
        bind:this={wrapperElement}
        class:current={pageNumber === currentPage}
        data-page-number={pageNumber}
        on:click={handleClick}>

    {#if exists(renderedThumbnailUrl)}
        <img class="pdf-thumbnail"
             alt="Page {pageNumber}"
             src="{renderedThumbnailUrl}"
             style:--max-width="{maxThumbnailWidthPx}px"/>
    {:else}
        <KpLoadingBlock size="xs"/>
    {/if}

    <span class="page-number">{pageNumber}</span>
</button>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-pdf-page-thumbnail {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: @spacing-s;
        outline: none;
        background: none;
        border: none;
        cursor: pointer;
        transition: opacity 0.3s ease-in-out;

        &:hover {
            opacity: 0.75;
        }

        &.current {
            .pdf-thumbnail {
                outline-color: var(--brand-orange-new);
            }
        }

        .pdf-thumbnail {
            max-width: var(--max-width);
            border-radius: @border-radius-default;
            background-color: var(--viewer-bg);
            border: 1px solid var(--viewer-default-border);
            outline: 1px solid transparent;
            transition: outline-color 0.3s ease-in-out;
        }

        .page-number {
            font-size: @font-size-small;
            color: var(--viewer-label-text-color);
            transition: color 0.3s ease-in-out;
        }
    }
</style>