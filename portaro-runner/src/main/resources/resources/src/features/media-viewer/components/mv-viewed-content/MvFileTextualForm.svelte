<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import {getMediaViewerContext} from '../../lib/mv-context';
    import {onMount} from 'svelte';
    import {getThemeContext} from 'shared/theme/portaro-theme';
    import {fade, fly} from 'svelte/transition';
    import {exists} from 'shared/utils/custom-utils';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';

    export let file: ViewableFile;

    const context = getMediaViewerContext();
    const currentTheme = getThemeContext().currentTheme;
    const loadingFadeAnimParams = {duration: 250};
    const pageFlyInAnimParams = {y: 10, duration: 250};

    let loading = true;
    let content: string;

    onMount(async () => {
        content = await context.service.getFileContent(file, 'text');
        loading = false;
    });
</script>

{#if !loading && exists(content)}
    <div class="mv-pdf-textual {$currentTheme}" in:fly={pageFlyInAnimParams}>
        <p class="content">{content}</p>
    </div>
{/if}

{#if loading}
    <div class="loading-container" transition:fade={loadingFadeAnimParams}>
        <KpLoadingBlock size="sm"/>
    </div>
{/if}

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-pdf-textual {
        width: 800px;
        max-width: 95%;
        background-color: var(--viewer-bg);
        color: var(--viewer-default-text-color);
        position: relative;
        display: flex;
        border-radius: @border-radius-default;
        overflow: hidden;
        flex-shrink: 0;
        padding: @spacing-m @spacing-ml;
        outline: 1px solid var(--viewer-default-border);
        transition: scale 0.3s ease-in-out;

        &.light {
            box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.07);
        }

        .content {
            white-space: pre-wrap;
        }
    }

    .loading-container {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
    }
</style>