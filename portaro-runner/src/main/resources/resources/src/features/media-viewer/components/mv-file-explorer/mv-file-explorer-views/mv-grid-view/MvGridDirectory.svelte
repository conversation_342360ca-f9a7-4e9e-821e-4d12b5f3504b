<script lang="ts">
    import type {DirectoryNode} from 'typings/portaro.be.types';
    import {getMediaViewerContext} from '../../../../lib/mv-context';
    import MvDirectoryOrFileIcon from '../../../mv-misc/MvDirectoryOrFileIcon.svelte';
    import MvContextMenuWrapper from '../../mv-context-menu/MvContextMenuWrapper.svelte';
    import MvDirectoryContextMenuContent from '../../mv-context-menu/MvDirectoryContextMenuContent.svelte';

    export let directoryNode: DirectoryNode;
    const context = getMediaViewerContext();

    const handleOpenDirectoryClick = () => {
        context.setFileExplorerSelectedDirectory(directoryNode);
    }
</script>

<MvContextMenuWrapper id="grid-view-directory-{directoryNode.id}"
                      on:click={handleOpenDirectoryClick}
                      let:open>

    <MvDirectoryContextMenuContent slot="context-content"
                                   directory="{directoryNode}"
                                   contentsOpen="{false}"/>

    <div class="mv-grid-directory" class:context-menu-opened={open}>
        <div class="icon-container">
            <MvDirectoryOrFileIcon directoryOrFile="{directoryNode}"/>
        </div>

        <span class="directory-info">
            <small class="file-name">{directoryNode.name}</small>
        </span>
    </div>
</MvContextMenuWrapper>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    @file-icon-size: 30px;

    .mv-grid-directory {
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;
        aspect-ratio: 11/16;
        padding: @spacing-s;
        border: 1px solid var(--viewer-default-border);
        cursor: pointer;
        outline: none;
        border-radius: @border-radius-default;
        transition: border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;

        &:hover,
        &.context-menu-opened {
            border-color: var(--brand-orange-new);
        }

        .icon-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: @file-icon-size;
        }

        .directory-info {
            word-break: break-word;
            word-wrap: break-word;
            text-align: center;
        }
    }
</style>