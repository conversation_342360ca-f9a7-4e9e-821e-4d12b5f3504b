import register from '@kpsys/angularjs-register';
import MediaViewerController from './media-viewer.controller';
import {mvSearcherFactory} from './services/mv-searcher.factory';
import {MvFileExplorerService} from './services/mv-file-explorer.service';
import {MvUrlStateService} from './services/mv-url-state.service';
import {MediaViewerService} from './services/media-viewer.service';
import {MvImageCacheService} from './services/mv-image-cache.service';

export default register('portaro.features.media-viewer')
    .factory(mvSearcherFactory.factoryName, mvSearcherFactory)
    .service(MvFileExplorerService.serviceName, MvFileExplorerService)
    .service(MvUrlStateService.serviceName, MvUrlStateService)
    .service(MvImageCacheService.serviceName, MvImageCacheService)
    .service(MediaViewerService.serviceName, MediaViewerService)
    .controller(MediaViewerController.controllerName, MediaViewerController)
    .name();