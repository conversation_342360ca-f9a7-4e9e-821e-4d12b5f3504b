<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import {getMediaViewerContext} from '../../../lib/mv-context';
    import {onDestroy} from 'svelte';
    import {areAllFilesMailable, areAllFilesPrintable} from '../../../lib/mv-utils';
    import {getLocalization} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import {strParams} from 'shared/utils/pipes';
    import {
        handleFileEditMetadataClick, handleFilePrintClick,
        handleFilesDeleteClick,
        handleFilesMailClick,
        handleFilesPrintClick
    } from '../../../lib/mv-file-explorer-handlers';

    const context = getMediaViewerContext();
    const localize = getLocalization();

    let selectedFiles: ViewableFile[];
    const selectedFilesUnsubscribe = context.filesSelected.subscribe((files) => selectedFiles = files);
    $: emptySelection = selectedFiles.length === 0;

    onDestroy(() => {
        selectedFilesUnsubscribe();
    });

    const handleCancelSelectionClick = () => {
        context.setSelectingFiles(false);
    }

    const handleFileOrFilesPrintClick = () => {
        if (selectedFiles.length === 1) {
            handleFilePrintClick(context, selectedFiles[0]);
            return;
        }

        handleFilesPrintClick(context, selectedFiles);
    }
</script>

<span class="selected-files-count-label text-center text-muted">
    {pipe(localize(/* @kp-localization mediaviewer.fileExplorer.SelectedFilesLabel */ 'mediaviewer.fileExplorer.SelectedFilesLabel'), strParams(selectedFiles.length.toString()))}
</span>

<KpButton isBlock
          buttonStyle="accent-blue-new"
          isDisabled="{selectedFiles.length !== 1}"
          on:click={() => handleFileEditMetadataClick(context, selectedFiles[0])}>

    <IconedContent icon="file-edit">
        {localize(/* @kp-localization mediaviewer.fileExplorer.EditFileMetadata */ 'mediaviewer.fileExplorer.EditFileMetadata')}
    </IconedContent>
</KpButton>

<KpButton isBlock
          buttonStyle="default-viewer-themed"
          isDisabled="{emptySelection || !areAllFilesPrintable(selectedFiles)}"
          on:click={handleFileOrFilesPrintClick}>

    <IconedContent icon="print">
        {localize(/* @kp-localization mediaviewer.fileExplorer.PrintSelectedFiles */ 'mediaviewer.fileExplorer.PrintSelectedFiles')}
    </IconedContent>
</KpButton>

<KpButton isBlock
          buttonStyle="default-viewer-themed"
          isDisabled="{emptySelection || !areAllFilesMailable(selectedFiles)}"
          on:click={() => handleFilesMailClick(context, selectedFiles)}>

    <IconedContent icon="envelope">
        {localize(/* @kp-localization mediaviewer.fileExplorer.EmailSelectedFiles */ 'mediaviewer.fileExplorer.EmailSelectedFiles')}
    </IconedContent>
</KpButton>

<KpButton isBlock
          buttonStyle="default-viewer-themed"
          on:click={handleCancelSelectionClick}>

    <IconedContent icon="cross-circle">
        {localize(/* @kp-localization mediaviewer.fileExplorer.CancelFilesSelection */ 'mediaviewer.fileExplorer.CancelFilesSelection')}
    </IconedContent>
</KpButton>

<KpButton isBlock
          buttonStyle="danger-new"
          isDisabled="{emptySelection}"
          on:click={() => handleFilesDeleteClick(context, selectedFiles)}>

    <IconedContent icon="trash">
        {pipe(localize(/* @kp-localization mediaviewer.fileExplorer.DeleteSelectedFiles */ 'mediaviewer.fileExplorer.DeleteSelectedFiles'), strParams(selectedFiles.length.toString()))}
    </IconedContent>
</KpButton>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .selected-files-count-label {
        margin-bottom: @spacing-sm;
    }
</style>