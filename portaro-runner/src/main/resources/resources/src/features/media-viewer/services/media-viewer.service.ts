import type {DirectoryNode, Rec, ViewableFile} from 'typings/portaro.be.types';
import type {MediaViewerFullPageData, MediaViewerInlineData} from '../lib/mv-types';
import type {DirectoryDataService} from 'shared/data-services/directory.data-service';
import type {MvUrlStateService} from './mv-url-state.service';
import type {FileService} from '../../file/file.service';
import type CurrentAuthService from 'shared/services/current-auth.service';
import {getUrlParams} from 'shared/utils/url-utils';
import {strToBool} from 'shared/utils/string-utils';
import {exists} from 'shared/utils/custom-utils';

export class MediaViewerService {
    public static serviceName = 'mediaViewerService';

    /*@ngInject*/
    constructor(private fileService: FileService,
                private currentAuthService: CurrentAuthService,
                private directoryDataService: DirectoryDataService,
                private mediaViewerUrlManagementService: MvUrlStateService) {
    }

    public async getInlineMediaViewerData(record: Rec): Promise<MediaViewerInlineData> {
        const parentDirectory = await this.directoryDataService.getDirectoryTree(record.directoryId);
        let editMode = false;

        if (this.currentAuthService.hasAnyRole('ROLE_ADMIN', 'ROLE_LIBRARIAN')) {
            editMode = true;
        }

        return {
            parentDirectory,
            editMode
        };
    }

    public async getFullPageMediaViewerData(rootDir: DirectoryNode): Promise<MediaViewerFullPageData> {
        const urlParams = getUrlParams(true);
        const backUrl = urlParams.origin || '/';

        let editMode: boolean;
        let openedFile: ViewableFile | null;

        try {
            editMode = strToBool(urlParams.edit);
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (e) {
            editMode = false;
        }

        let parentDir = rootDir;
        while (parentDir.children.length === 1) {
            parentDir = parentDir.children[0];

            if (parentDir.children.length !== 1) {
                this.mediaViewerUrlManagementService.setSelectedDirectory(parentDir);
                break;
            }
        }

        if (exists(urlParams.file)) {
            const fileId = Number(urlParams.file);

            if (!isNaN(fileId)) {
                openedFile = await this.fileService.getFileById(fileId);
            }
        }

        if (!editMode && !exists(openedFile)) {
            const files = await this.fileService.getFilesByDirectory(rootDir.id);
            if (files.length > 0) {
                openedFile = files[0];
            }
        }

        if (this.currentAuthService.hasAnyRole('ROLE_ADMIN', 'ROLE_LIBRARIAN')) {
            editMode = true;
        }

        return {
            editMode,
            backUrl,
            openedFile
        };
    }
}