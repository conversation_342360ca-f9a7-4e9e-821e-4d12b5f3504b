import * as Pdfjs from 'pdfjs-dist';
import type {PdfRotation, ViewedContentContext} from 'src/features/media-viewer/lib/mv-viewed-content';
import {MvPdfLinkManager} from 'src/features/media-viewer/components/mv-viewed-content/mv-pdf-viewer/mv-pdf-link.manager';
import {MvDownloadManager} from 'src/features/media-viewer/components/mv-viewed-content/mv-pdf-viewer/mv-download.manager';

export class MvPdfRenderService {

    constructor(private readonly pdfDocument: Pdfjs.PDFDocumentProxy) {
    }

    public async renderThumbnail(page: Pdfjs.PDFPageProxy, rotation: PdfRotation, maxWidthPx: number): Promise<string> {
        const effectiveRotation = (page.rotate + rotation) % 360;
        const defaultViewport = page.getViewport({scale: 1, rotation: effectiveRotation});

        const scale = maxWidthPx / defaultViewport.width;
        const thumbnailViewport = page.getViewport({scale, rotation: effectiveRotation});
        const canvasElement = document.createElement('canvas');
        const outputScale = 1;

        canvasElement.width = Math.floor(thumbnailViewport.width * outputScale);
        canvasElement.height = Math.floor(thumbnailViewport.height * outputScale);

        const transform = outputScale !== 1 ? [outputScale, 0, 0, outputScale, 0, 0] : null;
        const canvasContext = canvasElement.getContext('2d');

        const renderContext = {
            enableWebGL: false,
            intent: 'any',
            canvasContext,
            viewport: thumbnailViewport,
            transform
        };

        await page.render(renderContext).promise;

        return new Promise((resolve, reject) => {
            canvasElement.toBlob((blob) => {
                if (blob) {
                    const url = URL.createObjectURL(blob);
                    resolve(url);
                } else {
                    reject(new Error('Failed to create blob from canvas'));
                }
            }, 'image/png', 0.8); // Use PNG format with 80% quality
        });
    }

    public async renderPage(page: Pdfjs.PDFPageProxy, viewport: Pdfjs.PageViewport): Promise<HTMLCanvasElement> {
        const canvasElement = document.createElement('canvas');
        const outputScale = window.devicePixelRatio || 1;

        canvasElement.width = Math.floor(viewport.width * outputScale);
        canvasElement.height = Math.floor(viewport.height * outputScale);
        canvasElement.style.width = `${Math.floor(viewport.width)}px`;
        canvasElement.style.height = `${Math.floor(viewport.height)}px`;

        const transform = outputScale !== 1 ? [outputScale, 0, 0, outputScale, 0, 0] : null;
        const canvasContext = canvasElement.getContext('2d');

        const renderContext = {
            enableWebGL: true,
            intent: 'any',
            canvasContext,
            viewport,
            transform
        };

        await page.render(renderContext).promise;
        return canvasElement;
    }

    public async createAdditionalLayersElement(page: Pdfjs.PDFPageProxy, viewport: Pdfjs.PageViewport, viewedContentContext: ViewedContentContext): Promise<HTMLDivElement> {
        const annotationsLayerElement = await this.createAnnotationsLayerElement(page, viewport, viewedContentContext);
        const textLayerElement = await this.createTextLayerElement(page, viewport);

        const additionalLayersElement = document.createElement('div');
        additionalLayersElement.className = 'additional-layers-wrapper';
        additionalLayersElement.id = `additional-layers-wrapper-${page.pageNumber}`;
        additionalLayersElement.style.position = 'absolute';
        additionalLayersElement.style.top = '0';
        additionalLayersElement.style.left = '0';
        additionalLayersElement.style.width = '100%';
        additionalLayersElement.style.height = '100%';
        additionalLayersElement.style.zIndex = '1';

        additionalLayersElement.appendChild(annotationsLayerElement);
        additionalLayersElement.appendChild(textLayerElement);

        return additionalLayersElement;
    }

    private async createAnnotationsLayerElement(page: Pdfjs.PDFPageProxy, viewport: Pdfjs.PageViewport, viewedContentContext: ViewedContentContext): Promise<HTMLDivElement> {
        const annotationData = await page.getAnnotations();

        const annotationsLayerElement = document.createElement('div');
        annotationsLayerElement.id = `annotation-layer-${page.pageNumber}`;
        annotationsLayerElement.className = 'annotationLayer';

        const annotationLayer = new Pdfjs.AnnotationLayer({
            page,
            viewport,
            div: annotationsLayerElement,
            structTreeLayer: null,
            accessibilityManager: null,
            annotationCanvasMap: null,
            annotationEditorUIManager: null
        });

        await annotationLayer.render({
            page,
            viewport,
            div: annotationsLayerElement,
            annotations: annotationData,
            linkService: new MvPdfLinkManager(viewedContentContext, this.pdfDocument),
            downloadManager: new MvDownloadManager(),
            renderForms: false
        });

        annotationsLayerElement.style.width = '100%';
        annotationsLayerElement.style.height = '100%';

        return annotationsLayerElement;
    }

    private async createTextLayerElement(page: Pdfjs.PDFPageProxy, viewport: Pdfjs.PageViewport): Promise<HTMLDivElement> {
        const textData = await page.getTextContent();

        const textLayerElement = document.createElement('div');
        textLayerElement.id = `text-layer-${page.pageNumber}`;
        textLayerElement.className = 'textLayer';

        const textLayer = new Pdfjs.TextLayer({
            viewport,
            textContentSource: textData,
            container: textLayerElement
        });

        await textLayer.render();

        textLayerElement.style.width = '100%';
        textLayerElement.style.height = '100%';

        return textLayerElement;
    }
}