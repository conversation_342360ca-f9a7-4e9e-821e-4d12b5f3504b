<script lang="ts">
    import {oneWayDragging} from '../../actions/use.one-way-dragging';
    import {
        clampSideBarWidth,
        defaultSidebarWidth,
        mediaViewerSidebarWidth,
        setMediaViewerSidebarWidth
    } from '../../lib/mv-sidebar';

    $: width = $mediaViewerSidebarWidth ?? defaultSidebarWidth;
    $: resizeWidth = width;
    $: setMediaViewerSidebarWidth(resizeWidth);
    let dragging = false;

    const handleDragStart = () => {
        dragging = true;
    }

    const handleDrag = (event: CustomEvent<number>) => {
        dragging = true;
        resizeWidth = clampSideBarWidth(width + event.detail);
    }

    const handleOnDragEnd = () => {
        dragging = false;
        width = clampSideBarWidth(resizeWidth);
    }
</script>

<div class="mv-sidebar-draggable-resizer"
     role="separator"
     class:dragging={dragging}
     use:oneWayDragging="{{orientation:'vertical'}}"
     on:dragging-start={handleDragStart}
     on:dragging-progress={handleDrag}
     on:dragging-end={handleOnDragEnd}>
</div>

<style lang="less">
    .mv-sidebar-draggable-resizer {
        cursor: col-resize;
        position: absolute;
        z-index: 20;
        transform: translateX(1px);
        right: 0;
        top: 0;
        height: 100%;
        width: 4px;
        background-color: transparent;
        border-right: 3px dashed transparent;
        transition: 0.2s ease-in-out border-color;

        &:hover, &.dragging {
            border-color: var(--accent-blue-new);
        }
    }
</style>