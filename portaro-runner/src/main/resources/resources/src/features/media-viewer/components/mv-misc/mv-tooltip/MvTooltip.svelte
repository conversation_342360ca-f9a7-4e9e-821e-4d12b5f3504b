<script lang="ts">
    import type {TooltipArrowDirection} from '../../../lib/mv-types';

    export let id: string;
    export let text: string;
    export let arrowDirection: TooltipArrowDirection = 'bottom';
</script>

<div class="mv-tooltip" id="{id}" role="tooltip">
    {text}

    <svg class="arrow-triangle-svg arrow-{arrowDirection}" height="6" width="13" xmlns="http://www.w3.org/2000/svg">
        <polygon points="0,-1 13,-1 6,5"/>
    </svg>
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-tooltip {
        position: relative;
        isolation: isolate;
        width: min-content;
        padding: 0 @spacing-sm;
        height: @media-viewer-tooltip-height;
        font-size: @font-size-small;
        border-radius: @border-radius-default;
        display: flex;
        align-items: center;
        white-space: nowrap;
        background-color: var(--viewer-bg);
        border: 1px solid var(--viewer-default-border);
        box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08);

        .arrow-triangle-svg {
            position: absolute;
            z-index: -1;

            &.arrow-bottom {
                left: 50%;
                top: 100%;
                transform: translateX(-50%);
            }

            &.arrow-top {
                right: 50%;
                bottom: 100%;
                rotate: 180deg;
                transform: translateX(-50%);
            }

            &.arrow-left {
                right: 100%;
                rotate: 90deg;
                transform: translateY(-50%);
            }

            &.arrow-right {
                left: 100%;
                rotate: -90deg;
                transform: translateY(-50%);
            }

            polygon {
                fill: var(--viewer-bg);
                stroke: var(--viewer-default-border);
                stroke-width: 1px;
            }
        }
    }
</style>