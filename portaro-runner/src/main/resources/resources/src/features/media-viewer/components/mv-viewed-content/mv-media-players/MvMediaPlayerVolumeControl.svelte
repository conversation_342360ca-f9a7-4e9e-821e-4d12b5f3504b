<script lang="ts">
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import {exists, isTouchDevice} from 'shared/utils/custom-utils';
    import {getThemeContext} from 'shared/theme/portaro-theme';
    import {getLocalization} from 'core/svelte-context/context';

    export let mediaElement: HTMLMediaElement;

    const currentTheme = getThemeContext().currentTheme;
    const localize = getLocalization();

    let volume = mediaElement.volume;
    $: isMuted = volume === 0;

    function calculateProgressPercentage(event: MouseEvent): number {
        const progressBar = event.currentTarget as HTMLDivElement | undefined;
        if (!exists(progressBar)) {
            return 0;
        }

        const clickPosition = event.clientY - progressBar.getBoundingClientRect().top;
        return clickPosition / progressBar.clientHeight;
    }

    const handleSetProgressClick = (event: MouseEvent) => {
        const newVolume = 1 - calculateProgressPercentage(event);
        mediaElement.volume = newVolume;
        volume = newVolume;
    }

    const handleMuteButtonClick = () => {
        mediaElement.volume = mediaElement.volume > 0 ? 0 : 1;
        volume = mediaElement.volume;
    }
</script>

<div class="mv-media-player-volume-control">
    <button class="volume-button"
            class:muted={isMuted}
            on:click={handleMuteButtonClick}>

        <UIcon icon="{isMuted ? 'volume-mute' : 'volume'}"/>
    </button>

    {#if !isTouchDevice()}
        <div class="volume-slider-container">
            <div class="volume-slider {$currentTheme}"
                 aria-label="{localize(/* @kp-localization mediaviewer.content.MultiMediaVolumeLabel */ 'mediaviewer.content.MultiMediaVolumeLabel')}"
                 aria-valuemin="{0}"
                 aria-valuenow="{Math.floor(volume * 100)}"
                 aria-valuetext="{isMuted ? localize(/* @kp-localization mediaviewer.content.MultiMediaVolumeMuted */ 'mediaviewer.content.MultiMediaVolumeMuted') : `${Math.floor(volume * 100)}%`}"
                 aria-valuemax="{100}">

                <small class="volume-value" aria-hidden="true">{Math.floor(volume * 100)}%</small>

                <button class="progress-bar" aria-hidden="true" on:click={handleSetProgressClick}>
                    <div class="progress" style="height: {volume * 100}%"></div>
                </button>
            </div>
        </div>
    {/if}
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    @progress-bar-width: 8px;

    .mv-media-player-volume-control {
        position: relative;
        display: flex;

        &:hover {
            .volume-slider-container {
                transform: translateX(-50%);
                opacity: 1;
                visibility: visible;
            }
        }

        .volume-button {
            position: relative;
            background: none;
            outline: none;
            border: none;
            cursor: pointer;

            &::before {
                content: '';
                top: 50%;
                left: 50%;
                border-radius: 50%;
                transform: translate(-50%, -50%);
                position: absolute;
                width: 32px;
                height: 32px;
                transition: background-color 0.3s ease-in-out;
            }

            &:hover::before {
                background-color: var(--viewer-blue-highlight-transparent);
            }

            &.muted {
                color: var(--danger-red);
            }
        }

        .volume-slider-container {
            left: 50%;
            bottom: 100%;
            z-index: 1;
            position: absolute;
            padding-bottom: @spacing-m;
            visibility: hidden;
            opacity: 0;
            color: var(--viewer-default-text-color);
            transform: translate(-50%, 3px);
            transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;

            .volume-slider {
                width: 38px;
                border-radius: @border-radius-default;
                border: 1px solid var(--viewer-default-border);
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: @spacing-xs;
                padding: @spacing-sm 0 @spacing-m;
                background-color: var(--viewer-default-button-color);

                &.light {
                    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.07);
                }

                .volume-value {
                    opacity: 0.75;
                    font-size: 11px;
                }

                .progress-bar {
                    display: flex;
                    flex-direction: column;
                    justify-content: end;
                    height: 100px;
                    cursor: pointer;
                    outline: none;
                    border: none;
                    width: @progress-bar-width;
                    border-radius: calc(@progress-bar-width / 2);
                    background-color: var(--viewer-default-border);
                    padding: 0;

                    .progress {
                        margin: 0;
                        overflow: unset;
                        width: @progress-bar-width;
                        border-radius: calc(@progress-bar-width / 2);
                        background-color: var(--accent-blue-new);
                        transition: height 0.2s ease-in-out;

                        &::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            width: calc(@progress-bar-width * 1.75);
                            height: calc(@progress-bar-width * 1.75);
                            border-radius: 50%;
                        }
                    }
                }
            }
        }
    }
</style>