<script lang="ts">
    import type {DirectoryNode} from 'typings/portaro.be.types';
    import {getMediaViewerContext} from '../../../lib/mv-context';
    import {createEventDispatcher} from 'svelte';
    import {get} from 'svelte/store';
    import {getLocalization} from 'core/svelte-context/context';
    import MvContextMenuDivider from './MvContextMenuDivider.svelte';
    import MvContextMenuTitle from './MvContextMenuTitle.svelte';
    import KpContextMenuButton from 'shared/components/kp-context-menu/KpContextMenuButton.svelte';
    import {
        handleDirectoryCreateClick,
        handleDirectoryCreateTextFileClick,
        handleDirectoryDeleteClick,
        handleDirectoryEditMetadataClick,
        handleDirectoryUploadLocalFilesClick
    } from '../../../lib/mv-file-explorer-handlers';

    export let directory: DirectoryNode;
    export let contentsOpen: boolean;

    const context = getMediaViewerContext();
    const localize = getLocalization();
    const dispatch = createEventDispatcher<{'toggle-contents-open': boolean}>();

    const handleToggleContentsOpenClick = () => {
        const fileExplorerViewType = get(context.fileExplorerViewType);

        if (fileExplorerViewType === 'grid') {
            context.setFileExplorerSelectedDirectory(directory);
            return;
        }

        dispatch('toggle-contents-open', !contentsOpen);
    };
</script>

<MvContextMenuTitle>
    {localize(/* @kp-localization mediaviewer.fileExplorer.DirectoryContextLabel */ 'mediaviewer.fileExplorer.DirectoryContextLabel')}
</MvContextMenuTitle>

<MvContextMenuDivider/>

{#if context.canEdit}
    <KpContextMenuButton icon="edit"
                         on:click={() => handleDirectoryEditMetadataClick(context, directory)}>

        {localize(/* @kp-localization mediaviewer.fileExplorer.EditDirectory */ 'mediaviewer.fileExplorer.EditDirectory')}
    </KpContextMenuButton>
{/if}

<KpContextMenuButton icon="{contentsOpen ? 'folder' : 'folder-open'}"
                     on:click={handleToggleContentsOpenClick}>

    {contentsOpen
        ? localize(/* @kp-localization mediaviewer.fileExplorer.HideDirectoryContents */ 'mediaviewer.fileExplorer.HideDirectoryContents')
        : localize(/* @kp-localization mediaviewer.fileExplorer.ShowDirectoryContents */ 'mediaviewer.fileExplorer.ShowDirectoryContents')}
</KpContextMenuButton>

{#if context.canEdit}
    <MvContextMenuDivider/>

    <KpContextMenuButton icon="cloud-upload"
                         on:click={() => handleDirectoryUploadLocalFilesClick(context, directory)}>

        {localize(/* @kp-localization mediaviewer.fileExplorer.UploadDirectoryFiles */ 'mediaviewer.fileExplorer.UploadDirectoryFiles')}
    </KpContextMenuButton>

    <KpContextMenuButton icon="add-folder"
                         on:click={() => handleDirectoryCreateClick(context, directory)}>

        {localize(/* @kp-localization mediaviewer.fileExplorer.CreateNewSubdirectory */ 'mediaviewer.fileExplorer.CreateNewSubdirectory')}
    </KpContextMenuButton>

    <KpContextMenuButton icon="add-document"
                         on:click={() => handleDirectoryCreateTextFileClick(context, directory)}>

        {localize(/* @kp-localization mediaviewer.fileExplorer.CreateNewTextFile */ 'mediaviewer.fileExplorer.CreateNewTextFile')}
    </KpContextMenuButton>

    <MvContextMenuDivider/>

    <KpContextMenuButton icon="remove-folder"
                         buttonStyle="danger"
                         on:click={() => handleDirectoryDeleteClick(context, directory)}>

        {localize(/* @kp-localization mediaviewer.fileExplorer.DeleteDirectory */ 'mediaviewer.fileExplorer.DeleteDirectory')}
    </KpContextMenuButton>
{/if}