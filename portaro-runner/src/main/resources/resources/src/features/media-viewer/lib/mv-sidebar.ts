import type {Writable} from 'svelte/store';
import {get, writable} from 'svelte/store';
import {exists} from 'shared/utils/custom-utils';

const sideBarLocalStorageKey = 'mvSidebarWidth';
export const defaultSidebarWidth = 333;
export const mobileSidebarWidth = 380;

const sideNavsMaxWidth = 540;
const sideNavsMinWidth = 260;

export const mediaViewerSidebarOpen: Writable<boolean> = writable(false);

const initialWidthValue: number = exists(window.localStorage.getItem(sideBarLocalStorageKey)) ? Number(window.localStorage.getItem(sideBarLocalStorageKey)) : defaultSidebarWidth;
export const mediaViewerSidebarWidth: Writable<number> = writable(initialWidthValue);

export function toggleMediaViewerSidebar(value: boolean | null = null) {
    value = value ?? !get(mediaViewerSidebarOpen);
    mediaViewerSidebarOpen.set(value);
}

export function setMediaViewerSidebarWidth(value: number) {
    mediaViewerSidebarWidth.set(value);
    window.localStorage.setItem(sideBarLocalStorageKey, value.toString());
}

export const clampSideBarWidth = (value: number) => Math.max(sideNavsMinWidth, Math.min(value, sideNavsMaxWidth));