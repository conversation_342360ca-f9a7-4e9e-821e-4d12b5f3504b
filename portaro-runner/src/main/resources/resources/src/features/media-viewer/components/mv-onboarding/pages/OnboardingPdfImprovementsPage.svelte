<script lang="ts">
    import MvOnboardingPage from '../MvOnboardingPage.svelte';
    import imgSrc from '../images/pdf.webp';
    import {getLocalization} from 'core/svelte-context/context';

    const localize = getLocalization();
</script>

<MvOnboardingPage>
    <img src="{imgSrc}"
         alt="New PDF viewer"
         aria-labelledby="onboarding-pdf"/>

    <h2 id="onboarding-pdf">
        {localize(/* @kp-localization mediaviewer.onboarding.PdfImprovementsTitle */ 'mediaviewer.onboarding.PdfImprovementsTitle')}
    </h2>

    <span>
        {localize(/* @kp-localization mediaviewer.onboarding.PdfImprovementsParagraph1 */ 'mediaviewer.onboarding.PdfImprovementsParagraph1')}
    </span>

    <span>
        {localize(/* @kp-localization mediaviewer.onboarding.PdfImprovementsParagraph2 */ 'mediaviewer.onboarding.PdfImprovementsParagraph2')}
    </span>
</MvOnboardingPage>