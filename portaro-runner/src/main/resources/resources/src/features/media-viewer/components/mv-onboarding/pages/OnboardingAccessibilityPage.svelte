<script lang="ts">
    import MvOnboardingPage from '../MvOnboardingPage.svelte';
    import imgSrc from '../images/accessibility.webp';
    import {getLocalization} from 'core/svelte-context/context';

    const localize = getLocalization();
</script>

<MvOnboardingPage>
    <img src="{imgSrc}"
         alt="Accessibility"
         aria-labelledby="onboarding-accessibility"/>

    <h2 id="onboarding-accessibility">
        {localize(/* @kp-localization mediaviewer.onboarding.BetterAccessibilityTitle */ 'mediaviewer.onboarding.BetterAccessibilityTitle')}
    </h2>

    <span>
        {localize(/* @kp-localization mediaviewer.onboarding.BetterAccessibilityParagraph1 */ 'mediaviewer.onboarding.BetterAccessibilityParagraph1')}
    </span>
</MvOnboardingPage>