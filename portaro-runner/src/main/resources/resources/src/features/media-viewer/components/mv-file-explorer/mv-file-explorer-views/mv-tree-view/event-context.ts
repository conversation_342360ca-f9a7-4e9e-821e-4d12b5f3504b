import {getContext, setContext} from 'svelte';

const treeViewEventContextKey = 'tree-view-event-ctx';

export const DIRECTORY_LEFT_ARROW_EVENT = 'mv-directory-left-arrow-event';
export const DIRECTORY_RIGHT_ARROW_EVENT = 'mv-directory-right-arrow-event';
export const FILE_LEFT_ARROW_EVENT = 'mv-file-left-arrow-event';

export function createTreeViewEventContext(): EventTarget {
    return setContext<EventTarget>(treeViewEventContextKey, new EventTarget());
}

export function getTreeViewEventContext(): EventTarget {
    return getContext<EventTarget>(treeViewEventContextKey);
}