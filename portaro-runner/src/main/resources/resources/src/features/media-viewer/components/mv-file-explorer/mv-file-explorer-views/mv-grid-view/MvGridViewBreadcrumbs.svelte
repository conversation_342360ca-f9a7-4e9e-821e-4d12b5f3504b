<script lang="ts">
    import type {DirectoryNode} from 'typings/portaro.be.types';
    import {getMediaViewerContext} from '../../../../lib/mv-context';
    import {onDestroy, onMount} from 'svelte';
    import {findDirectoryInTreeById} from '../../../../lib/mv-utils';
    import {exists} from 'shared/utils/custom-utils';
    import {getLocalization} from 'core/svelte-context/context';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    const context = getMediaViewerContext();
    const localize = getLocalization();

    let breadcrumbsPath: DirectoryNode[] = [];

    const urlStateSubscription = context.urlManagementService.getUrlState$().subscribe((state) => {
        if (exists(state.directory)) {
            recalculatePath(state.directory);
        }
    });

    onMount(async () => {
        const state = await context.urlManagementService.getCurrentUrlState();
        recalculatePath(state.directory ?? context.rootDirectory.id.toString());
    });

    onDestroy(() => {
        urlStateSubscription.unsubscribe();
    });

    const handleDirectoryClick = (directoryNode: DirectoryNode) => {
        context.setFileExplorerSelectedDirectory(directoryNode);
    }

    const handleGoToPreviousDirectoryClick = () => {
        const previousDirectory = breadcrumbsPath[breadcrumbsPath.length - 2];
        if (exists(previousDirectory)) {
            context.setFileExplorerSelectedDirectory(previousDirectory);
        }
    }

    function recalculatePath(directoryIdString: string) {
        const directoryId = Number(directoryIdString);
        if (isNaN(directoryId)) {
            return;
        }

        const path: DirectoryNode[] = [];
        const selectedDirectory = findDirectoryInTreeById(context.rootDirectory, directoryId, path);

        if (!exists(selectedDirectory)) {
            return;
        }

        breadcrumbsPath = path;
    }
</script>

{#if breadcrumbsPath.length > 1}
    <div class="mv-grid-view-breadcrumbs">
        {#each breadcrumbsPath as pathDirectory, index}
            <button class="breadcrumb-directory"
                    class:current-directory={index === breadcrumbsPath.length - 1}
                    on:click={() => handleDirectoryClick(pathDirectory)}>

                {pathDirectory.name}
            </button>

            {#if index < breadcrumbsPath.length - 1}
                <UIcon color="var(--viewer-label-text-color)" icon="angle-small-right"/>
            {/if}
        {/each}
    </div>

    <KpButton isBlock
              buttonStyle="default-viewer-themed"
              on:click={handleGoToPreviousDirectoryClick}>

        <IconedContent icon="undo-alt">
            {localize(/* @kp-localization mediaviewer.fileExplorer.BackToPreviousDirectoryBtnLabel */ 'mediaviewer.fileExplorer.BackToPreviousDirectoryBtnLabel')}
        </IconedContent>
    </KpButton>
{/if}

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-grid-view-breadcrumbs {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: @spacing-s;
        padding: @spacing-sm @spacing-m;
        border-radius: @border-radius-default;
        background-color: var(--viewer-default-button-color);
        border: 1px solid var(--viewer-default-border);

        .breadcrumb-directory {
            outline: none;
            border: none;
            background-color: transparent;
            cursor: pointer;
            max-width: 100px;
            overflow: hidden;
            text-wrap: nowrap;
            text-overflow: ellipsis;
            border-radius: @border-radius-default;
            transition: background-color 0.3s ease-in-out;

            &:not(.current-directory):hover {
                background-color: var(--viewer-blue-highlight-transparent);
            }

            &.current-directory {
                background-color: var(--viewer-orange-highlight-transparent);
            }
        }
    }
</style>