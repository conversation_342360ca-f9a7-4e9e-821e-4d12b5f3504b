<script lang="ts">
    import KpContextMenuWrapper from 'shared/components/kp-context-menu/KpContextMenuWrapper.svelte';

    export let id: string | null;
</script>

<KpContextMenuWrapper additionalContextMenuClasses="mv-context-menu"
                      wrapperAsButton
                      {id}
                      on:click
                      let:open>

    <slot {open}/>
    <slot slot="context-content" name="context-content"/>
</KpContextMenuWrapper>

<style lang="less">
    :global {
        .mv-context-menu {
            background-color: var(--viewer-content-bg) !important;
            border: 1px solid var(--viewer-default-border) !important;
            width: 16.5em !important;

            .kp-context-menu-button {
                &.default-style {
                    color: var(--viewer-default-text-color) !important;
                }

                .icon-container {
                    color: var(--viewer-label-text-color) !important;
                }

                &:hover {
                    background-color: var(--viewer-bg) !important;
                }
            }
        }
    }
</style>