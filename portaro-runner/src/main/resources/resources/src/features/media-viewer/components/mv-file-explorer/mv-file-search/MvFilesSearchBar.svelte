<script lang="ts">
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import {uuid} from 'shared/utils/custom-utils';
    import {getMediaViewerContext} from '../../../lib/mv-context';
    import {onDestroy} from 'svelte';
    import {getThemeContext} from 'shared/theme/portaro-theme';
    import {getLocalization} from 'core/svelte-context/context';

    const searchBarId = uuid();
    const context = getMediaViewerContext();
    const localize = getLocalization();
    const currentTheme = getThemeContext().currentTheme;
    let searching = false;
    const searchingUnsubscribe = context.searchingForResults.subscribe((currentSearching) => searching = currentSearching);

    let fileName = '';
    const searchQueryUnsubscribe = context.searchQuery.subscribe((currentSearchQuery) => {
        if (!currentSearchQuery) {
            fileName = '';
        }

        if (currentSearchQuery !== fileName) {
            fileName = currentSearchQuery;
        }
    });

    onDestroy(() => {
        searchQueryUnsubscribe();
        searchingUnsubscribe();
    });

    const handleSubmit = () => {
        context.setSearchQuery(fileName);
    }
</script>

<form on:submit|preventDefault={handleSubmit}
      class="mv-files-search-bar">

    <label for="files-search-bar-{searchBarId}" class="search-label">
        {localize(/* @kp-localization mediaviewer.fileExplorer.SearchInputLabel */ 'mediaviewer.fileExplorer.SearchInputLabel')}
    </label>

    <input id="files-search-bar-{searchBarId}"
           type="search"
           placeholder="{localize(/* @kp-localization mediaviewer.fileExplorer.SearchInputPlaceholder */ 'mediaviewer.fileExplorer.SearchInputPlaceholder')}"
           style="color-scheme: {$currentTheme}"
           bind:value={fileName}/>

    <button type="submit" disabled="{searching}">
        {#if searching}
            <KpLoadingBlock size="xs" color="white"/>
        {:else}
            <UIcon icon="search"
                   label="{localize(/* @kp-localization mediaviewer.fileExplorer.SearchBtnLabel */ 'mediaviewer.fileExplorer.SearchBtnLabel')}"/>
        {/if}
    </button>
</form>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-files-search-bar {
        width: 100%;
        height: @sidebar-search-height;
        display: flex;
        flex-shrink: 0;

        .search-label {
            border: 0;
            clip: rect(0 0 0 0);
            height: 1px;
            margin: -1px;
            overflow: hidden;
            padding: 0;
            position: absolute;
            width: 1px;
        }

        input {
            border-radius: @border-radius-default 0 0 @border-radius-default;
            height: 100%;
            flex: 1;
            background-color: var(--viewer-default-button-color);
            border: 1px solid var(--viewer-default-border);
            border-right: none;
            outline: none;
            padding: 0 @spacing-m;
            transition: border-color 0.3s ease-in-out;

            &:active,
            &:focus {
                border-color: var(--accent-blue-new);
            }

            &:focus-visible {
                outline: none !important;
                box-shadow: none !important;
            }
        }

        button {
            border-radius: 0 @border-radius-default @border-radius-default 0;
            display: flex;
            align-items: center;
            justify-content: center;
            outline: none;
            background-color: var(--accent-blue-new);
            border: 1px solid rgba(0, 0, 0, 0.175);
            border-left: none;
            cursor: pointer;
            height: 100%;
            color: white;
            width: calc(@sidebar-search-height * 1.33);
            transition: opacity 0.3s ease-in-out;

            &:disabled {
                opacity: 0.5;
            }

            &:hover {
                opacity: 0.7;
            }
        }
    }
</style>