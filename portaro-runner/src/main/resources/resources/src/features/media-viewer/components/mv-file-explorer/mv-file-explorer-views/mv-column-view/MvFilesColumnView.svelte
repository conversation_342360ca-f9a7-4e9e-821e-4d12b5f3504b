<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import {flip} from 'svelte/animate';
    import {popInAnim, popOutAnim} from 'shared/animations/pop-animations';
    import MvTreeColumnFile from '../MvTreeColumnFile.svelte';

    const filesFlipAnimParams = {duration: 250};

    export let files: ViewableFile[];
</script>

<ul class="mv-files-column-view">
    {#each files as file(file.id)}
        <li animate:flip={filesFlipAnimParams}
            in:popInAnim={{key: file.id}}
            out:popOutAnim={{key: file.id}}>

            <MvTreeColumnFile {file}/>
        </li>
    {/each}
</ul>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-files-column-view {
        display: flex;
        flex-direction: column;
        gap: @spacing-base;
    }
</style>