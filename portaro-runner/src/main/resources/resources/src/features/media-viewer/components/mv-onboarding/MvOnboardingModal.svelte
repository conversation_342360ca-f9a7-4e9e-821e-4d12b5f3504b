<script lang="ts">
    import type {PortaroTheme} from 'shared/theme/portaro-theme';
    import type {TouchSwipeEvent} from '../../actions/use.touch-swipe';
    import {isAlreadyOnBoard, saveOnboardingSuccess} from '../../lib/mv-onboarding';
    import {getThemeContext} from 'shared/theme/portaro-theme';
    import {getMediaViewerContext} from '../../lib/mv-context';
    import {onDestroy, onMount} from 'svelte';
    import {fly} from 'svelte/transition';
    import {touchSwipe} from '../../actions/use.touch-swipe';
    import {exists} from 'shared/utils/custom-utils';
    import {getLocalization} from 'core/svelte-context/context';
    import {OPEN_ONBOARDING_MODAL} from '../../lib/mv-constants';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import OnboardingNewDesignPage from './pages/OnboardingNewDesignPage.svelte';
    import OnboardingResponsivityPage from './pages/OnboardingResponsivityPage.svelte';
    import OnboardingPdfImprovementsPage from './pages/OnboardingPdfImprovementsPage.svelte';
    import OnboardingFileExplorerInfoPage from './pages/OnboardingFileExplorerInfoPage.svelte';
    import OnboardingMediaPlayersImprovementsPage from './pages/OnboardingMediaPlayersImprovementsPage.svelte';
    import OnboardingAccessibilityPage from './pages/OnboardingAccessibilityPage.svelte';

    const context = getMediaViewerContext();
    const currentTheme = getThemeContext().currentTheme;
    const localize = getLocalization();

    let shown = !isAlreadyOnBoard();
    let currentPage = 0;
    let animationDirection = 0;

    let closingDialog = false;
    let initialAnimPlayed = false;
    let dialogElement: HTMLDialogElement;

    $: if (!shown) animationDirection = 0;
    $: toggleDialogElement(shown);

    const onboardingPages = [
        OnboardingNewDesignPage,
        OnboardingAccessibilityPage,
        OnboardingMediaPlayersImprovementsPage,
        OnboardingResponsivityPage,
        OnboardingPdfImprovementsPage,
        OnboardingFileExplorerInfoPage,
    ];

    onMount(() => {
        window.addEventListener('keydown', handleKeyDown);
        context.eventBus.addEventListener(OPEN_ONBOARDING_MODAL, handleOpenOnboarding);

        if (shown) {
            toggleDialogElement(true);
        }
    });

    onDestroy(() => {
        window.removeEventListener('keydown', handleKeyDown);
        context.eventBus.removeEventListener(OPEN_ONBOARDING_MODAL, handleOpenOnboarding);
    });

    const handleCloseClick = () => {
        shown = false;
        saveOnboardingSuccess();
    }

    const handleOpenOnboarding = () => {
        currentPage = 0;
        animationDirection = 0;
        shown = true;
    }

    const handleNextPageClick = () => {
        if (currentPage === onboardingPages.length) {
            shown = false;
            saveOnboardingSuccess();
            return;
        }

        animationDirection = 1;
        currentPage++;
    }

    const handlePreviousPageClick = () => {
        if (currentPage === 0) {
            return;
        }

        animationDirection = -1;
        currentPage--;
    }

    const handleTouchSwipe = (event: TouchSwipeEvent) => {
        if (event.detail === 'left') {
            handleNextPageClick();
        }

        if (event.detail === 'right') {
            handlePreviousPageClick();
        }
    }

    const handleKeyDown = (event: KeyboardEvent) => {
        if (!shown) {
            return;
        }

        if (event.key === 'ArrowLeft') {
            handlePreviousPageClick();
        }

        if (event.key === 'ArrowRight') {
            handleNextPageClick();
        }
    }

    const handleModalClose = () => {
        toggleDialogElement(false);
    }

    function getThemeLogoPath(themeValue: PortaroTheme) {
        return `/resources/img/brand/verbis-viewer-logo-${themeValue === 'light' ? 'black' : 'white'}.svg`;
    }

    function toggleDialogElement(value: boolean) {
        if (!exists(dialogElement)) {
            return;
        }

        if (value) {
            dialogElement.showModal();
            setTimeout(() => {
                initialAnimPlayed = true;
            }, 1000);

            return;
        }

        closingDialog = true;
        setTimeout(() => {
            dialogElement.close();
            closingDialog = false;
            shown = false;
            initialAnimPlayed = false;
        }, 300);
    }
</script>

<dialog class="mv-onboarding-modal"
        class:closing-dialog={closingDialog}
        bind:this={dialogElement}
        aria-modal="{shown}"
        use:touchSwipe={{disabled: !shown}}
        on:cancel|preventDefault={handleModalClose}
        on:touch-swipe={handleTouchSwipe}>

    {#if currentPage > 0}
        <div class="close-button-container" in:fly={{x: 15, duration: 250}}>
            <KpButton buttonSize="md"
                      buttonStyle="danger-new"
                      aria-label="{localize(/* @kp-localization mediaviewer.onboarding.CloseModalBtnLabel */ 'mediaviewer.onboarding.CloseModalBtnLabel')}"
                      on:click={handleCloseClick}>

                <UIcon icon="cross-circle"/>
            </KpButton>
        </div>
    {/if}

    {#key currentPage}
        <div class="onboarding-page-container"
             class:initial-anim-played={initialAnimPlayed}
             in:fly={{x: 15 * animationDirection, duration: 250}}>

            {#if currentPage === 0}
                <img class="viewer-logo"
                     src="{getThemeLogoPath($currentTheme)}"
                     alt="Verbis Viewer Logo"
                     loading="lazy"
                     style:--slide-anim-delay="0s"/>

                <h1 class="title-text" style:--slide-anim-delay="0.2s">
                    {localize(/* @kp-localization mediaviewer.onboarding.WelcomeTitle */ 'mediaviewer.onboarding.WelcomeTitle')}
                </h1>

                <span class="label-text" style:--slide-anim-delay="0.4s">
                    {localize(/* @kp-localization mediaviewer.onboarding.WelcomeParagraph1 */ 'mediaviewer.onboarding.WelcomeParagraph1')}
                    <br/>
                    {localize(/* @kp-localization mediaviewer.onboarding.WelcomeParagraph2 */ 'mediaviewer.onboarding.WelcomeParagraph2')}
                    <br/>
                    <small>
                        {localize(/* @kp-localization mediaviewer.onboarding.WelcomeParagraph2Label */ 'mediaviewer.onboarding.WelcomeParagraph2Label')}
                    </small>
                </span>

                <div class="landing-action-buttons-row" style:--slide-anim-delay="0.6s">
                    <KpButton buttonSize="md"
                              buttonStyle="danger-new"
                              on:click={handleCloseClick}>

                        <IconedContent icon="cross-circle">
                            {localize(/* @kp-localization mediaviewer.onboarding.CloseBtnLabel */ 'mediaviewer.onboarding.CloseBtnLabel')}
                        </IconedContent>
                    </KpButton>

                    <KpButton buttonSize="md"
                              buttonStyle="default-viewer-themed"
                              on:click={handleNextPageClick}>

                        <IconedContent icon="arrow-small-right">
                            {localize(/* @kp-localization mediaviewer.onboarding.MoreInfoBtnLabel */ 'mediaviewer.onboarding.MoreInfoBtnLabel')}
                        </IconedContent>
                    </KpButton>
                </div>
            {/if}

            {#if currentPage > 0}
                <svelte:component this="{onboardingPages[currentPage - 1]}"/>
            {/if}
        </div>
    {/key}

    {#if currentPage > 0}
        <div class="pages-indicator" in:fly={{x: 15, duration: 250}}>
            {#each onboardingPages as onboardingPage}
                {@const pageNumber = onboardingPages.indexOf(onboardingPage) + 1}

                <div class="indicator-circle" class:indicator-active={currentPage === pageNumber}></div>
            {/each}
        </div>

        <div class="pages-buttons-container" in:fly={{x: 15, duration: 250}}>
            <KpButton buttonSize="md"
                      aria-label="{localize(/* @kp-localization mediaviewer.onboarding.PreviousPageBtnLabel */ 'mediaviewer.onboarding.PreviousPageBtnLabel')}"
                      buttonStyle="default-viewer-themed"
                      on:click={handlePreviousPageClick}>

                <UIcon icon="arrow-small-left"/>
            </KpButton>

            <KpButton buttonSize="md"
                      aria-label="{localize(/* @kp-localization mediaviewer.onboarding.NextPageBtnLabel */ 'mediaviewer.onboarding.NextPageBtnLabel')}"
                      buttonStyle="default-viewer-themed"
                      on:click={handleNextPageClick}>

                <UIcon icon="{currentPage === onboardingPages.length ? 'cross-circle' : 'arrow-small-right'}"/>
            </KpButton>
        </div>
    {/if}
</dialog>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-onboarding-modal {
        max-width: 95dvw;
        max-height: 90dvh;
        width: 800px;
        height: 940px;
        border-radius: @border-radius-xl;
        background-color: var(--viewer-bg);
        border: 1px solid var(--viewer-default-border);
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-20px);
        padding: @spacing-l @spacing-xl;

        &[open] {
            animation: dialog-open 0.3s forwards ease-out;
            animation-fill-mode: forwards;

            .onboarding-page-container:not(.initial-anim-played) > * {
                animation: 0.4s ease-in-out 0s 1 content-slide-from-top forwards;
                animation-delay: var(--slide-anim-delay);
            }

            .onboarding-page-container.initial-anim-played > * {
                animation: none;
            }
        }

        &::backdrop {
            background-color: rgba(0, 0, 0, 0.225);
            backdrop-filter: blur(2px);
            -webkit-backdrop-filter: blur(2px);
        }

        &.closing-dialog {
            animation: dialog-close 0.25s forwards ease-in;
            animation-fill-mode: forwards;
        }

        .close-button-container {
            display: flex;
            justify-content: end;
            margin-bottom: @spacing-ml;
        }

        .onboarding-page-container {
            display: flex;
            width: 100%;
            height: 100%;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            margin-bottom: @spacing-ml;
            gap: @spacing-sm;

            &:not(.initial-anim-played) > * {
                transform: translateY(-20px);
                opacity: 0;
            }

            .viewer-logo {
                height: 26px;
            }

            .title-text {
                margin: @spacing-l 0;
                color: var(--viewer-default-text-color);
            }

            .label-text {
                display: flex;
                flex-direction: column;
                padding: 0 @spacing-ml;
                color: var(--viewer-label-text-color);

                small {
                    opacity: 0.75;
                    font-style: italic;
                }
            }

            .landing-action-buttons-row {
                display: flex;
                align-items: center;
                margin-top: 40px;
                justify-content: center;
                gap: @spacing-sm;
            }
        }

        .pages-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: @spacing-m;

            .indicator-circle {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background-color: var(--viewer-default-border);
                transition: background-color 0.2s ease-in-out, scale 0.2s ease-in-out, transform 0.2s ease-in-out;

                &.indicator-active {
                    scale: 1.2;
                    background-color: var(--brand-orange-new);
                }
            }
        }

        .pages-buttons-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: @spacing-sm;
        }
    }

    @keyframes content-slide-from-top {
        to {
            transform: translateY(0);
            opacity: 100%;
        }
    }

    @keyframes dialog-open {
        to {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
    }

    @keyframes dialog-close {
        from {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }
        to {
            opacity: 0;
            transform: translateY(-20px);
            visibility: hidden;
        }
    }
</style>