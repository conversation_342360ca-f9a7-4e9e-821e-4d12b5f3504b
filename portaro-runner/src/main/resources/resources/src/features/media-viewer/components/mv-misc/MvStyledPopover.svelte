<script lang="ts">
    import KpPopover from 'shared/ui-widgets/popover/KpPopover.svelte';

    export let additionalPopoverButtonClasses = '';
    export let additionalPopoverPanelClasses = '';
</script>

<KpPopover buttonSize="md"
           placement="bottom-end"
           buttonStyle="default-viewer-themed"
           additionalPopoverPanelClasses="mv-styled-popover-panel {additionalPopoverPanelClasses}"
           additionalPopoverButtonClasses="mv-styled-popover-button {additionalPopoverButtonClasses}">

    <slot name="button" slot="button"/>

    <span slot="popover-title">
        <slot name="heading"/>
    </span>

    <div slot="popover-content" class="mv-styled-popover-content">
        <slot name="content"/>
    </div>
</KpPopover>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-styled-popover-content {
        padding: @spacing-s 0;
        display: flex;
        flex-direction: column;
    }

    :global {
        .mv-styled-popover-button {
            height: @media-viewer-topbar-elements-height;
        }

        .mv-styled-popover-panel {
            --popover-bg-color: var(--viewer-bg);
            --popover-border-radius: @border-radius-large;
            --popover-border-color: var(--viewer-default-border);
            --popover-title-bg-color: var(--viewer-content-bg);
            --popover-title-border-color: var(--viewer-default-border);
            --popover-content-padding: @spacing-m @spacing-ml;

            margin-top: @spacing-s;
            margin-left: calc(@spacing-m * -1);

            .title {
                display: flex;
                align-items: center;
                height: 42px;
                font-size: 14.5px;
                font-weight: 600;
            }

            .content {
                overflow-y: auto;
                max-height: 400px;
            }
        }
    }
</style>