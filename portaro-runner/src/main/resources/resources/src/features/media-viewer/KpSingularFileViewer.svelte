<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import {createSingularFileViewerContext} from './lib/mv-context';
    import {getInjector} from 'core/svelte-context/context';
    import {MvImageCacheService} from './services/mv-image-cache.service';
    import MvViewedContent from './components/mv-viewed-content/MvViewedContent.svelte';
    import PortaroThemeContainer from 'shared/theme/PortaroThemeContainer.svelte';

    export let file: ViewableFile;

    const imageCacheService = getInjector().getByToken<MvImageCacheService>(MvImageCacheService.serviceName);
    createSingularFileViewerContext(file, imageCacheService);
</script>

<div class="kp-singular-file-viewer">
    <PortaroThemeContainer overrideThemeValue="light">
        <MvViewedContent/>
    </PortaroThemeContainer>
</div>

<style lang="less">
    @import (reference) "styles/portaro.themes.less";

    .kp-singular-file-viewer {
        width: 100%;
        height: 100%;
        flex: 1 1 0;

        --viewer-bg: @themed-body-bg;
        --viewer-content-bg: @themed-body-bg;
        --viewer-sidebar-bg: @themed-body-bg;
        --viewer-light-border: @themed-border-muted;
        --viewer-default-border: @themed-border-default;
        --viewer-bold-border: @themed-border-bold;
        --viewer-icon-color: @themed-text-default;
        --viewer-default-text-color: @themed-text-default;
        --viewer-label-text-color: @themed-text-muted;
        --viewer-default-button-color: @themed-body-bg;
        --viewer-orange-highlight: @themed-body-bg-orange-highlighted;
        --viewer-blue-highlight: @themed-body-bg-blue-highlighted;
        --viewer-orange-highlight-transparent: rgba(255, 87, 18, 0.13);
        --viewer-blue-highlight-transparent: rgba(18, 57, 255, 0.08);
    }

    :global {
        .kp-singular-file-viewer .mv-viewed-content {
            background-color: @themed-body-bg;
            border: none !important;
            border-radius: 0 !important;
        }
    }
</style>