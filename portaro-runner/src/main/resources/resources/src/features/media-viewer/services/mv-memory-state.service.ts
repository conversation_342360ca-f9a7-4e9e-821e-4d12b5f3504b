import type {IMvStateService, MediaViewerState} from '../lib/mv-types';
import type {DirectoryNode, ViewableFile} from 'typings/portaro.be.types';
import {MemoryStateManager} from 'shared/state-manager/memory-state-manager';
import {firstValueFrom, type Observable} from 'rxjs';

export class MvMemoryStateService implements IMvStateService {
    private readonly memoryStateManagerService: MemoryStateManager<MediaViewerState>;

    constructor(rootDirectory: DirectoryNode) {
        this.memoryStateManagerService = new MemoryStateManager<MediaViewerState>({
            rootDirectory: rootDirectory.id.toString(10)
        });
    }

    public async getCurrentUrlState(): Promise<MediaViewerState> {
        const urlState$ = this.memoryStateManagerService.getState$();
        return firstValueFrom(urlState$);
    }

    public getUrlState$(): Observable<MediaViewerState> {
        return this.memoryStateManagerService.getState$();
    }

    public setSelectedDirectory(value: DirectoryNode) {
        this.memoryStateManagerService.requestChangeStateParameter('directory', value.id.toString());
    }

    public setViewedFile(value: ViewableFile | null) {
        this.memoryStateManagerService.requestChangeStateParameter('file', value?.id?.toString() ?? null);
    }

    public setSearchQuery(value: string | null) {
        this.memoryStateManagerService.requestChangeStateParameter('q', value);
    }

    public setPageNumber(value: number | null) {
        this.memoryStateManagerService.requestChangeStateParameter('page', value?.toString());
    }

    public resetToDefault(): void {
        this.setPageNumber(null);
    }
}