<script lang="ts">
    import type {MediaViewerInlineData} from './lib/mv-types';
    import type {Rec} from 'typings/portaro.be.types';
    import {onMount} from 'svelte';
    import {getInjector} from 'core/svelte-context/context';
    import {exists} from 'shared/utils/custom-utils';
    import {MediaViewerService} from './services/media-viewer.service';
    import KpMediaViewer from './KpMediaViewer.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';

    export let record: Rec;
    export let editMode = null;

    const service = getInjector().getByToken<MediaViewerService>(MediaViewerService.serviceName);
    let data: MediaViewerInlineData;

    onMount(async () => {
        data = await service.getInlineMediaViewerData(record);
        editMode ??= data.editMode;
    });
</script>

{#if exists(data)}
    <div class="inline-media-viewer-wrapper">
        <KpMediaViewer rootDirectoryNode="{data.parentDirectory}"
                       editMode={editMode}
                       viewedFile="{null}"
                       backToCatalogUrl="{null}"
                       isInInlineMode/>
    </div>
{:else}
    <div class="inline-media-viewer-wrapper">
        <KpLoadingBlock fillAvailableSpace/>
    </div>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro-erp.less";

    .inline-media-viewer-wrapper {
        .flex-grow();
        position: relative;
    }

    :global {
        .inline-media-viewer-wrapper > #media-viewer {
            --viewer-bg: @themed-body-bg;
            --viewer-content-bg: @themed-body-bg;
            --viewer-sidebar-bg: @themed-body-bg;
            --viewer-light-border: @themed-border-muted;
            --viewer-default-border: @themed-border-default;
            --viewer-bold-border: @themed-border-bold;
            --viewer-icon-color: @themed-text-default;
            --viewer-default-text-color: @themed-text-default;
            --viewer-label-text-color: @themed-text-muted;
            --viewer-default-button-color: @themed-body-bg;
            --viewer-orange-highlight: @themed-body-bg-orange-highlighted;
            --viewer-blue-highlight: @themed-body-bg-blue-highlighted;
            --viewer-orange-highlight-transparent: rgba(255, 87, 18, 0.13);
            --viewer-blue-highlight-transparent: rgba(18, 57, 255, 0.08);

            .mv-viewed-content {
                border-top: none !important;
                border-top-left-radius: 0 !important;
            }
        }
    }
</style>