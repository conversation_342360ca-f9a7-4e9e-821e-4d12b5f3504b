import type {State} from 'shared/constants/portaro.constants';
import type {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
import {states} from 'shared/constants/portaro.constants';
import {ngAsync} from 'shared/utils/ng-@decorators';
import {ignoreUnusedProperties} from 'shared/utils/custom-utils';
import {resolveErrorMessage} from 'shared/utils/error-utils';

export interface Searcher<T> {
    query: string;

    search(query: string): Promise<T[]>;

    cancel(): void;

    getAll(): T[];

    isEmpty(): boolean;

    getCurrent(): T;

    getCurrentIndex(): number;

    getPrevious(): T | null;

    getNext(): T | null;

    getState(): any;

    hasPrevious(): boolean;

    hasNext(): boolean;
}

/*@ngInject*/
export function mvSearcherFactory<T = any>(toastMessageService: ToastMessageService): SearcherFactory<T> {

    class SearcherImpl implements Searcher<T> {

        private state: State = states.INITIAL;
        private searchedList: T[] = [];
        private currentIdx = 0;
        private searchQuery: string = null;

        constructor(private searchFunction: (query: any) => Promise<T[]>) {
        }

        @ngAsync()
        public async search(query: string): Promise<T[]> {
            this.state = states.PROCESSING;

            try {
                this.searchedList = await this.searchFunction(query);
                this.currentIdx = 0;
                this.state = states.NORMAL;
                this.searchQuery = query;

                return this.searchedList;
            } catch (e) {
                this.state = states.NORMAL;
                toastMessageService.showError(resolveErrorMessage(e));
                return [];
            }
        }

        public cancel() {
            this.searchQuery = null;
            this.searchedList = [];
            this.currentIdx = 0;
            this.state = states.INITIAL;
        }

        public getAll(): T[] {
            return this.searchedList;
        }

        public isEmpty(): boolean {
            return this.searchedList.length === 0;
        }

        public getCurrent(): T {
            return this.searchedList[this.currentIdx];
        }

        public getCurrentIndex(): number {
            return this.currentIdx;
        }

        public getPrevious(): T | null {
            if (!this.hasPrevious()) {
                return null;
            }

            this.currentIdx--;
            return this.getCurrent();
        }

        public getNext(): T | null {
            if (!this.hasNext()) {
                return null;
            }

            this.currentIdx++;
            return this.getCurrent();
        }

        public getState() {
            return this.state;
        }

        public hasPrevious(): boolean {
            return this.currentIdx > 0;
        }

        public hasNext(): boolean {
            return this.currentIdx < this.searchedList.length - 1;
        }

        public get query(): string {
            return this.searchQuery;
        }

        public set query(q: string) {
            ignoreUnusedProperties(q);
            throw new TypeError('Searcher: Field `query` is readonly.');
        }
    }

    return {
        create(searchFunction: (query: any) => Promise<T[]>): Searcher<T> {
            return new SearcherImpl(searchFunction);
        }
    };
}

mvSearcherFactory.factoryName = 'searcherFactory';

export interface SearcherFactory<T = any> {
    create: (searchFunction: (query: string) => Promise<T[]>) => Searcher<T>;
}
