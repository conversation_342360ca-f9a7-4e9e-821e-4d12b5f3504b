<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import type {RawImageData} from '../../../services/mv-image-cache.service';
    import {fade, scale} from 'svelte/transition';
    import {getViewedContentContext} from '../../../lib/mv-viewed-content';
    import {getLocalization} from 'core/svelte-context/context';
    import {getMediaViewerContext} from '../../../lib/mv-context';
    import {exists} from 'shared/utils/custom-utils';
    import {touchSwipe, type TouchSwipeEvent} from '../../../actions/use.touch-swipe';
    import {createEventDispatcher, onDestroy, onMount} from 'svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import MvCacheableThumbnail from '../../mv-misc/MvCacheableThumbnail.svelte';

    export let viewedFile: ViewableFile;
    export let currentDirectoryImageFiles: ViewableFile[] = [];

    let currentImagePromiseId = 0;
    let mounted = false;
    $: if (mounted) fetchMainImage(viewedFile);

    const localize = getLocalization();
    const dispatch = createEventDispatcher<{ 'close': void }>();
    const context = getMediaViewerContext();
    const viewedContent = getViewedContentContext();
    const galleryFadeAnimParams = {duration: 250};
    const imageScaleInAnimParams = {duration: 250, start: 0.9};
    const loaderFadeAnimParams = {duration: 250};

    let fullScreenGalleryElement: HTMLDivElement;
    let maxMainImageWidth = 0;
    let maxMainImageHeight = 0;
    let mainImageWidth = 0;
    let mainImageHeight = 0;

    let fetchingImage = true;
    let fetchedFileId: number | null = null;
    let thumbnailImageData: RawImageData | null = null;
    let imageData: RawImageData;
    $: mainImageSource = exists(imageData) ? imageData : thumbnailImageData;

    onMount(() => {
        mounted = true;
        window.addEventListener('keydown', handleKeyDown);

        if (exists(fullScreenGalleryElement)) {
            fullScreenGalleryElement.requestFullscreen();
        }
    });

    onDestroy(() => {
        window.removeEventListener('keydown', handleKeyDown);
    });

    async function fetchMainImage(file: ViewableFile) {
        if (fetchedFileId === file.id) {
            return;
        }

        currentImagePromiseId++;
        const currentPromiseIdCopy = currentImagePromiseId;

        thumbnailImageData = null;
        imageData = null;
        fetchingImage = true;

        thumbnailImageData = context.imageCacheService.getCachedThumbnailImage(file);
        calculateMainImageDimensions(thumbnailImageData, maxMainImageWidth, maxMainImageHeight);
        fetchedFileId = file.id;

        const loadedImageData = await context.imageCacheService.fetchOriginalImageWithCaching(file);
        if (currentPromiseIdCopy !== currentImagePromiseId) {
            return;
        }

        imageData = loadedImageData;
        calculateMainImageDimensions(loadedImageData, maxMainImageWidth, maxMainImageHeight);
        fetchingImage = false;
    }

    function calculateMainImageDimensions(rawImageData: RawImageData | null, maxWidth: number, maxHeight: number) {
        if (!exists(rawImageData)) {
            mainImageWidth = 0;
            mainImageHeight = 0;
            return;
        }

        const originalWidth = rawImageData.width;
        const originalHeight = rawImageData.height;
        const aspectRatio = originalWidth / originalHeight;

        let calculatedMainImageWidth = maxWidth;
        let calculatedMainImageHeight = maxHeight;

        if (calculatedMainImageWidth / aspectRatio > maxHeight) {
            calculatedMainImageHeight = maxHeight;
            calculatedMainImageWidth = maxHeight * aspectRatio;
        } else {
            calculatedMainImageWidth = maxWidth;
            calculatedMainImageHeight = maxWidth / aspectRatio;
        }

        mainImageWidth = calculatedMainImageWidth;
        mainImageHeight = calculatedMainImageHeight;
    }

    const handleImageClick = (file: ViewableFile) => {
        context.setViewedFile(file);
    }

    const handleCloseClick = () => {
        viewedContent.setFullScreenImageGallery(false);
    }

    const handlePlayerFullscreenChange = () => {
        if (!exists(document.fullscreenElement)) {
            dispatch('close');
        }
    }

    const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key !== 'ArrowLeft' && event.key !== 'ArrowRight') {
            return;
        }

        const nextImageIndex = currentDirectoryImageFiles.indexOf(viewedFile) + (event.key === 'ArrowRight' ? 1 : -1);
        const nextImage = currentDirectoryImageFiles[nextImageIndex];

        if (exists(nextImage)) {
            context.setViewedFile(nextImage);
        }
    }

    const handleTouchSwipe = (event: TouchSwipeEvent) => {
        if (event.detail !== 'left' && event.detail !== 'right') {
            return;
        }

        const nextImageIndex = currentDirectoryImageFiles.indexOf(viewedFile) + (event.detail === 'left' ? 1 : -1);
        const nextImage = currentDirectoryImageFiles[nextImageIndex];

        if (exists(nextImage)) {
            context.setViewedFile(nextImage);
        }
    }
</script>

<div class="mv-fullscreen-image-gallery"
     use:touchSwipe
     on:touch-swipe={handleTouchSwipe}
     bind:this={fullScreenGalleryElement}
     transition:fade={galleryFadeAnimParams}
     on:fullscreenchange={handlePlayerFullscreenChange}>

    <div class="close-button-container">
        <KpButton on:click="{handleCloseClick}"
                  additionalClasses="gallery-close-btn"
                  buttonStyle="danger-new"
                  title="{localize(/* @kp-localization commons.zavrit */ 'commons.zavrit')}">

            <UIcon icon="cross" label="{localize(/* @kp-localization commons.zavrit */ 'commons.zavrit')}"/>
        </KpButton>
    </div>

    <div class="gallery-content-container">
        {#key fetchedFileId}
            <div class="main-image-container-wrapper"
                 bind:clientWidth={maxMainImageWidth}
                 bind:clientHeight={maxMainImageHeight}
                 in:scale={imageScaleInAnimParams}>

                <div class="main-image-container">
                    <img class="main-image"
                         style:width="{mainImageWidth}px"
                         style:height="{mainImageHeight}px"
                         class:loading={fetchingImage}
                         alt="File {viewedFile.id}"
                         src="{mainImageSource?.blobUrl}"/>
                </div>

                {#if fetchingImage}
                    <div class="loading-container" transition:fade={loaderFadeAnimParams}>
                        <KpLoadingBlock size="sm"/>
                    </div>
                {/if}
            </div>
        {/key}

        <div class="image-thumbnails-container-wrapper">
            <div class="image-thumbnails-container">
                {#each currentDirectoryImageFiles as imageThumbnail(imageThumbnail.id)}
                    <button class="image-thumbnail-btn"
                            on:click={() => handleImageClick(imageThumbnail)}
                            class:selected={viewedFile?.id === imageThumbnail.id}>

                        <MvCacheableThumbnail file="{imageThumbnail}"
                                              let:loading
                                              let:loadError
                                              let:blobUrl>

                            <img class="image-thumbnail"
                                 class:thumbnail-loading={loading || loadError}
                                 alt="File {imageThumbnail.id}"
                                 src="{blobUrl}"/>
                        </MvCacheableThumbnail>
                    </button>
                {/each}
            </div>
        </div>
    </div>
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-fullscreen-image-gallery {
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        z-index: 1000;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        background-color: rgba(0, 0, 0, 0.33);
        backdrop-filter: blur(2px);
        -webkit-backdrop-filter: blur(2px);

        .close-button-container {
            position: absolute;
            top: @spacing-ml;
            right: @spacing-xl;
        }

        .gallery-content-container {
            width: 100%;
            flex: 1 1 0;
            padding: calc(@spacing-ml + @media-viewer-topbar-elements-height + @spacing-sm) @spacing-xl 0;
            display: flex;
            flex-direction: column;
            gap: @spacing-l;

            .main-image-container-wrapper {
                display: flex;
                flex-direction: column;
                position: relative;
                flex: 1 1 0;
                justify-content: center;
                align-items: center;

                .main-image-container {
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    left: 0;
                    top: 0;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;

                    .main-image {
                        border-radius: @border-radius-large;
                        box-shadow: rgba(99, 99, 99, 0.2) 0 2px 8px 0;
                        transition: filter 0.2s ease-in-out;

                        &.loading {
                            filter: blur(2px);
                            -webkit-filter: blur(2px);
                        }
                    }
                }

                .loading-container {
                    position: absolute;
                    top: 0;
                    left: 0;
                    bottom: 0;
                    right: 0;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                }
            }

            .image-thumbnails-container-wrapper {
                overflow-x: auto;
                width: calc(100% + @spacing-xl * 2);
                margin: 0 calc(@spacing-xl * -1);
                padding: @spacing-m 0;

                .image-thumbnails-container {
                    display: flex;
                    align-items: center;
                    width: min-content;
                    gap: @spacing-ml;
                    padding: 0 @spacing-xl;
                    margin: 0 auto;

                    .image-thumbnail-btn {
                        border: none;
                        background: none;
                        cursor: pointer;
                        padding: 0;
                        border-radius: @border-radius-default;

                        &.selected {
                            outline: 2px solid var(--accent-blue-new);
                            outline-offset: 1px;
                        }

                        &:not(.selected) .image-thumbnail {
                            opacity: 0.8;
                        }

                        &:hover .image-thumbnail {
                            opacity: 1;
                        }

                        .image-thumbnail {
                            height: 80px;
                            width: auto;
                            border-radius: @border-radius-default;
                            box-shadow: rgba(99, 99, 99, 0.2) 0 2px 8px 0;
                            transition: opacity 0.3s ease-in-out;

                            &.thumbnail-loading {
                                opacity: 0.25;
                            }
                        }
                    }
                }
            }
        }
    }

    :global {
        .mv-fullscreen-image-gallery {
            .gallery-close-btn {
                width: @media-viewer-topbar-elements-height;
                height: @media-viewer-topbar-elements-height;
                padding: 0;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }
</style>