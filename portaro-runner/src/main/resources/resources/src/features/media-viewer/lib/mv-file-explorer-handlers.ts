import type {DirectoryNode, ViewableFile} from 'typings/portaro.be.types';
import type {MediaViewerContext} from './mv-context';
import {exists} from 'shared/utils/custom-utils';
import {clickFileSelect} from 'shared/utils/click-file-select';
import {get} from 'svelte/store';
import {
    DIRECTORY_CREATE_EVENT,
    DIRECTORY_DELETE_EVENT,
    DIRECTORY_EDIT_EVENT,
    FILE_CREATE_EVENT,
    FILES_DELETE_EVENT,
    FILE_METADATA_EDIT_EVENT,
    FILES_UPLOAD_EVENT
} from './mv-constants';

// Directory actions
export const handleDirectoryCreateClick = async (context: MediaViewerContext, parentDirectory: DirectoryNode) => {
    const createdDirectory = await context.service.createSubdirectory(parentDirectory);
    if (!exists(createdDirectory)) {
        return;
    }

    context.eventBus.dispatchEvent(new CustomEvent<DirectoryNode>(DIRECTORY_CREATE_EVENT, {detail: createdDirectory}));
    context.setFileExplorerSelectedDirectory(createdDirectory);
};

export const handleDirectoryEditMetadataClick = async (context: MediaViewerContext, directory: DirectoryNode) => {
    const editedDirectory = await context.service.editSelectedDirectory(directory);
    if (!exists(editedDirectory)) {
        return;
    }

    context.eventBus.dispatchEvent(new CustomEvent<DirectoryNode>(DIRECTORY_EDIT_EVENT, {detail: editedDirectory}));
};

export const handleDirectoryUploadLocalFilesClick = async (context: MediaViewerContext, parentDirectory: DirectoryNode) => {
    const selectedFiles = await clickFileSelect();
    if (selectedFiles.length === 0) {
        return;
    }

    await uploadDirectoryLocalFiles(context, parentDirectory, selectedFiles);
};

export const uploadDirectoryLocalFiles = async (context: MediaViewerContext, parentDirectory: DirectoryNode, files: File[]) => {
    context.setOngoingUploadPercentage(0);

    try {
        const uploadedFilesIds = (await context.service.uploadFiles(parentDirectory, files, (progressPercentage) => {
            context.setOngoingUploadPercentage(progressPercentage);
        })).map((file) => file.id);

        if (uploadedFilesIds.length === 0) {
            context.setOngoingUploadPercentage(null);
            return;
        }

        const uploadedFiles = await Promise.all(uploadedFilesIds.map((id) => context.service.getFileById(id)));
        context.setOngoingUploadPercentage(null);

        context.eventBus.dispatchEvent(new CustomEvent<ViewableFile[]>(FILES_UPLOAD_EVENT, {detail: uploadedFiles}));
    } catch {
        // When any error occurs while uploading files, set the progress to null to hide file upload overlay
        context.setOngoingUploadPercentage(null);
    }
};

export const handleDirectoryCreateTextFileClick = async (context: MediaViewerContext, parentDirectory: DirectoryNode) => {
    const createdFile = await context.service.createFile(parentDirectory);
    if (!exists(createdFile)) {
        return;
    }

    context.eventBus.dispatchEvent(new CustomEvent<ViewableFile>(FILE_CREATE_EVENT, {detail: createdFile}));
};

export const handleDirectoryDeleteClick = async (context: MediaViewerContext, directory: DirectoryNode) => {
    const successful = await context.service.deleteSelectedDirectory(directory);
    if (!successful) {
        return;
    }

    context.eventBus.dispatchEvent(new CustomEvent<DirectoryNode>(DIRECTORY_DELETE_EVENT, {detail: directory}));
};

// File actions
export const handleFilesDeleteClick = async (context: MediaViewerContext, files: ViewableFile[]) => {
    const successful = await context.service.deleteFiles(files);
    if (!successful) {
        return;
    }

    const currentFile = get(context.viewedFile);

    if (exists(currentFile)) {
        const isDeletedFileViewed = exists(files.find((file) => file.id === currentFile.id));

        if (isDeletedFileViewed) {
            context.setViewedFile(null);
        }
    }

    context.eventBus.dispatchEvent(new CustomEvent<ViewableFile[]>(FILES_DELETE_EVENT, {detail: files}));
    context.setSelectingFiles(false);
};

export const handleFileEditMetadataClick = async (context: MediaViewerContext, file: ViewableFile) => {
    const editedFile = await context.service.editFileMetadata(file);
    if (!exists(editedFile)) {
        return;
    }

    context.eventBus.dispatchEvent(new CustomEvent<ViewableFile>(FILE_METADATA_EDIT_EVENT, {detail: editedFile}));
    context.setSelectingFiles(false);
};

export const handleFilePrintClick = async (context: MediaViewerContext, file: ViewableFile) => {
    await context.service.printFile(file);
};

export const handleFilesPrintClick = async (context: MediaViewerContext, files: ViewableFile[]) => {
    await context.service.printFiles(files);
};

export const handleFilesMailClick = async (context: MediaViewerContext, files: ViewableFile[]) => {
    await context.service.mailFiles(files);
};