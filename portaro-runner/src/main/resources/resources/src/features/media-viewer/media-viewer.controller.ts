import type {DirectoryNode, ViewableFile} from 'typings/portaro.be.types';
import type {MediaViewerService} from './services/media-viewer.service';
import type {SvelteComponent} from 'svelte';
import {ngAsync} from 'shared/utils/ng-@decorators';
import KpBrowserProgressBar from 'shared/components/kp-browser-progress-bar/KpBrowserProgressBar.svelte';
import KpToastMessages from 'shared/components/kp-toast-messages/KpToastMessages.svelte';
import ErpConnectionBar from 'src/features/erp/components/erp-connection-bar/ErpConnectionBar.svelte';
import KpModalDialogsPortal from 'shared/modal-dialogs/KpModalDialogsPortal.svelte';

declare global {
    interface Window {
        // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
        model: any & {
            rootDirectory: DirectoryNode;
        }
    }
}

export default class MediaViewerController {
    public static controllerName = 'MediaViewerCtrl';

    public kpMediaViewerComponent: SvelteComponent;
    public kpToastMessagesComponent = KpToastMessages;
    public kpBrowserProgressBarComponent = KpBrowserProgressBar;
    public erpConnectionBar = ErpConnectionBar;
    public kpModalDialogsPortal = KpModalDialogsPortal;

    private mediaViewerSvelteComponentModule: { default: any; };

    public modelRootDirectory = window.model.rootDirectory;
    public openedFile?: ViewableFile;
    public editMode: boolean;
    public backUrl: string;
    public loadingData = true;

    /*@ngInject*/
    constructor(private mediaViewerService: MediaViewerService) {
    }

    @ngAsync()
    async $onInit(): Promise<void> {
        await this.initialize();
        this.loadingData = false;
    }

    private async initialize(): Promise<void> {
        import('./media-viewer.styles.less');

        this.mediaViewerSvelteComponentModule = await import(/* webpackChunkName: "mediaViewer" */ './KpMediaViewer.svelte');
        this.kpMediaViewerComponent = this.mediaViewerSvelteComponentModule.default;

        ({openedFile: this.openedFile, editMode: this.editMode, backUrl: this.backUrl} = await this.mediaViewerService.getFullPageMediaViewerData(this.modelRootDirectory));
    }
}