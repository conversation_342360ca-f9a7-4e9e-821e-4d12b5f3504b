<script lang="ts">
    import './media-viewer.themes.less';

    import type {DirectoryNode, ViewableFile} from 'typings/portaro.be.types';
    import {createMediaViewerContext} from './lib/mv-context';
    import {fade} from 'svelte/transition';
    import {getInjector} from 'core/svelte-context/context';
    import {MvFileExplorerService} from './services/mv-file-explorer.service';
    import {MvUrlStateService} from './services/mv-url-state.service';
    import {MvImageCacheService} from './services/mv-image-cache.service';
    import {onDestroy, onMount} from 'svelte';
    import {mediaViewerSidebarOpen, mediaViewerSidebarWidth} from './lib/mv-sidebar';
    import PortaroThemeContainer from 'shared/theme/PortaroThemeContainer.svelte';
    import MvViewedContent from './components/mv-viewed-content/MvViewedContent.svelte';
    import MvFilesDropZone from './components/mv-misc/MvFilesDropZone.svelte';
    import MvFileExplorerSideBar from './components/mv-file-explorer/MvFileExplorerSideBar.svelte';
    import {
        MediaViewerLayoutSize,
        setMediaViewerLayoutSize,
        isCurrentLayoutOrSmaller,
        mediaViewerLayoutWidth
    } from './lib/mv-layout';

    export let rootDirectoryNode: DirectoryNode;
    export let editMode: boolean;
    export let backToCatalogUrl: string | null;
    export let viewedFile: ViewableFile | null;
    export let isInInlineMode = false;

    const service = getInjector().getByToken<MvFileExplorerService>(MvFileExplorerService.serviceName);
    const urlStateService = getInjector().getByToken<MvUrlStateService>(MvUrlStateService.serviceName);
    const imageCacheService = getInjector().getByToken<MvImageCacheService>(MvImageCacheService.serviceName);
    const context = createMediaViewerContext(service, urlStateService, imageCacheService, rootDirectoryNode, editMode, backToCatalogUrl, editMode ? 'tree' : 'grid', true, viewedFile, isInInlineMode);

    let innerWidth = 0;
    $: setMediaViewerLayoutSize(innerWidth);
    $: mobileSidebarMode = isCurrentLayoutOrSmaller(MediaViewerLayoutSize.TABLET, $mediaViewerLayoutWidth);
    $: mobileSidebarOpen = mobileSidebarMode && $mediaViewerSidebarOpen;

    onMount(() => {
        imageCacheService.startCleanUpTask();

        // Special hack to prevent zoom-to-tabs and pinch-to-zoom gesture in Safari on iOS
        // Normal browsers use meta-tag viewport with "user-scalable=no"
        document.addEventListener('gesturestart', preventZoomHandler);
        document.addEventListener('gesturechange', preventZoomHandler);
        document.addEventListener('gestureend', preventZoomHandler);
    });

    onDestroy(() => {
        imageCacheService.stopCleanUpTask();

        document.removeEventListener('gesturestart', preventZoomHandler);
        document.removeEventListener('gesturechange', preventZoomHandler);
        document.removeEventListener('gestureend', preventZoomHandler);
    });

    const preventZoomHandler = (event: Event) => {
        event.preventDefault();

        if ('zoom' in document.body.style) {
            document.body.style.zoom = '0.99';
        }
    };
</script>

<svelte:window bind:innerWidth/>

<PortaroThemeContainer>
    <div id="media-viewer" class="kp-media-viewer">
        <!--<MvOnboardingModal/>-->

        <MvFilesDropZone disabled="{!context.canEdit}">
            <div class="side-bar-container"
                 class:mobile-sidebar={mobileSidebarMode}
                 class:mobile-sidebar-open={mobileSidebarOpen}>

                <MvFileExplorerSideBar/>
            </div>

            <div class="viewed-content-container"
                 style:margin-left="{mobileSidebarMode ? 0 : $mediaViewerSidebarWidth}px"
                 style:width="calc(100% - {mobileSidebarMode ? 0 : $mediaViewerSidebarWidth}px)"
                 class:mobile-sidebar-open={mobileSidebarOpen}>

                <MvViewedContent/>
            </div>

            {#if mobileSidebarOpen}
                <div class="dark-overlay" transition:fade={{duration: 250}}></div>
            {/if}
        </MvFilesDropZone>
    </div>
</PortaroThemeContainer>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    @page-bottom-offset: 85px;

    .kp-media-viewer {
        position: relative;
        display: flex;
        flex: 1;
        width: 100%;
        touch-action: manipulation; // Disable mobile "double-tap zoom"
        color: var(--viewer-default-text-color);

        .side-bar-container {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            z-index: 20;

            &.mobile-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
            }

            &.mobile-sidebar-open {
                transform: translateX(0);
            }
        }

        .viewed-content-container {
            flex: 1;
            display: flex;
            position: relative;
            flex-direction: column;
            background-color: var(--viewer-content-bg);
            transition: transform 0.3s ease-in-out;

            &.mobile-sidebar-open {
                transform: translateX(@media-viewer-mobile-sidebar-open-content-offset);
            }
        }

        .dark-overlay {
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            width: 100%;
            height: 100%;
            position: absolute;
            z-index: 1;
            background-color: rgba(0, 0, 0, 0.33);
        }
    }

    :global {
        .kp-media-viewer {
            .kp-vertical-separator,
            .kp-horizontal-separator {
                background-color: var(--viewer-default-border);
            }
        }
    }
</style>