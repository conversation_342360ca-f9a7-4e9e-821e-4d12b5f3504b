<script lang="ts">
    import type {DirectoryNode, ViewableFile} from 'typings/portaro.be.types';
    import {onDestroy, onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import MvTreeColumnFile from '../MvTreeColumnFile.svelte';
    import {getMediaViewerContext} from '../../../../lib/mv-context';
    import {FILE_LEFT_ARROW_EVENT, getTreeViewEventContext} from './event-context';

    export let file: ViewableFile;
    export let parentDirectory: DirectoryNode | null = null;
    export let level = 1;

    const context = getMediaViewerContext();
    const treeViewEventBus = getTreeViewEventContext();

    let currentFileChecked = false;
    const currentSelectedFilesUnsubscribe = context.filesSelected.subscribe((currentFilesSelected) => {
        currentFileChecked = exists(currentFilesSelected.find((e) => e.id === file.id));
    });

    onMount(() => {
        treeViewEventBus.addEventListener(FILE_LEFT_ARROW_EVENT, handleLeftArrowEvent);
    });

    onDestroy(() => {
        currentSelectedFilesUnsubscribe();

        treeViewEventBus.removeEventListener(FILE_LEFT_ARROW_EVENT, handleLeftArrowEvent);
    });

    const handleLeftArrowEvent = (event: CustomEvent<number>) => {
        if (event.detail !== file.id) {
            return;
        }

        if (exists(parentDirectory)) {
            const parentElement = document.getElementById(`tree-view-directory-${parentDirectory.id}`);
            parentElement?.focus();
        }
    }
</script>

<div id="file-{file.id}"
     class="mv-tree-column-file"
     aria-level="{level}"
     aria-selected="{currentFileChecked}"
     role="treeitem">

    <MvTreeColumnFile id="tree-view-file-{file.id}"
                      {file}
                      {parentDirectory}/>
</div>