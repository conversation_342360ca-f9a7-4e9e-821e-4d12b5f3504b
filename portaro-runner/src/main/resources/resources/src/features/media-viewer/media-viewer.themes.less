.portaro-theme-light {
    .light-theme();
}

.portaro-theme-dark {
    .dark-theme();
}

.light-theme() {
    // Light MV themes
    --viewer-bg: #F8F8F8;
    --viewer-content-bg: #FFFFFF;
    --viewer-sidebar-bg: #FFFFFF;
    --viewer-light-border: #D2D2D2;
    --viewer-default-border: #B9B9B9;
    --viewer-bold-border: #949494;
    --viewer-icon-color: #374957;
    --viewer-default-text-color: #232328;
    --viewer-label-text-color: #696979;
    --viewer-default-button-color: #FFFFFF;
    --viewer-orange-highlight: #F4E1DC;
    --viewer-blue-highlight: #EAEEFF;
    --viewer-orange-highlight-transparent: rgba(255, 87, 18, 0.13);
    --viewer-blue-highlight-transparent: rgba(18, 57, 255, 0.08);
}

.dark-theme() {
    // Redefine focus colors
    --focus-outline-color: white;
    --focus-box-shadow-color: transparent;

    // Dark MV themes
    --viewer-bg: #19191C;
    --viewer-content-bg: #121214;
    --viewer-sidebar-bg: #121214;
    --viewer-icon-color: #D6D5DB;
    --viewer-default-text-color: #e9e9ec;
    --viewer-label-text-color: #b3b3b4;
    --viewer-default-button-color: #1E1D21;
    --viewer-light-border: #3f3f4f;
    --viewer-default-border: #454556;
    --viewer-bold-border: #525265;
    --viewer-orange-highlight: #3F302A;
    --viewer-blue-highlight: #262940;
    --viewer-orange-highlight-transparent: rgba(225, 76, 17, 0.1);
    --viewer-blue-highlight-transparent: rgba(17, 59, 225, 0.14);
}