<script lang="ts">
    import type {ViewedContentPageData} from '../../../lib/mv-viewed-content';
    import {createEventDispatcher, tick} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';

    export let pageData: ViewedContentPageData;

    const dispatch = createEventDispatcher<{'go-to-page': number}>();

    let editing = false;
    let newPage = pageData.current;
    let inputElement: HTMLInputElement;

    const handleClickToEdit = async () => {
        if (editing) {
            return;
        }

        editing = true;

        await tick(); // Wait for input element render
        if (exists(inputElement)) {
            inputElement.focus();
        }
    };

    const handleSubmit = () => {
        dispatch('go-to-page', newPage);
        editing = false;
    };
</script>

<!-- svelte-ignore a11y-click-events-have-key-events a11y-no-noninteractive-element-interactions -->
<form class="mv-controls-page-indicator" on:click={handleClickToEdit} on:submit|preventDefault={handleSubmit}>
    {#if editing}
        <input type="number" bind:this={inputElement} bind:value={newPage} min="1" max={pageData.total} on:blur={handleSubmit}/>
    {:else}
        <span class="page-indicator">{pageData.current}/{pageData.total}</span>
    {/if}
</form>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-controls-page-indicator {
        display: flex;
        height: 100%;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        background-color: var(--viewer-orange-highlight);
        border-right: 1px solid var(--viewer-default-border);

        input {
            width: 100%;
            height: 100%;
            background: none;
            border: none;
            outline: none;
            padding: 0 @spacing-m;
            min-width: 75px;

            &:focus,
            &:focus-visible {
                outline: none !important;
                box-shadow: none !important;
            }

            &::-webkit-inner-spin-button,
            ::-webkit-outer-spin-button {
                -webkit-appearance: none;
                margin: 0;
            }
        }

        .page-indicator {
            padding: 0 @spacing-xl;
        }
    }
</style>