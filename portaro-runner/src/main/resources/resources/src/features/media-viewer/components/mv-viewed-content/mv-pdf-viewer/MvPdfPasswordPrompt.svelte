<script lang="ts">
    import {getLocalization} from 'core/svelte-context/context';
    import {createEventDispatcher} from 'svelte';
    import {fly} from 'svelte/transition';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let wrongPassword = false;

    const localize = getLocalization();
    const dispatch = createEventDispatcher<{'password-submit': string}>();

    let pdfPassword = '';
</script>

<form on:submit|preventDefault={() => dispatch('password-submit', pdfPassword)}
      transition:fly={{y: 10, duration: 250}}
      class="password-input-container">

    <span class="title">{localize(/* @kp-localization mediaviewer.content.PdfInputPasswordLabel */ 'mediaviewer.content.PdfInputPasswordLabel')}</span>

    <div class="input-row">
        <!-- svelte-ignore a11y-autofocus -->
        <input bind:value={pdfPassword}
               type="password"
               placeholder="{localize(/* @kp-localization mediaviewer.content.PdfPasswordInputPlaceholder */ 'mediaviewer.content.PdfPasswordInputPlaceholder')}"
               autofocus/>

        <KpButton buttonType="submit" buttonStyle="accent-blue-new">
            <IconedContent icon="check">
                {localize(/* @kp-localization mediaviewer.content.PdfPasswordSubmitBtnLabel */ 'mediaviewer.content.PdfPasswordSubmitBtnLabel')}
            </IconedContent>
        </KpButton>
    </div>

    {#if wrongPassword}
        <small class="wrong-password">
            {localize(/* @kp-localization mediaviewer.content.PdfWrongPasswordLabel */ 'mediaviewer.content.PdfWrongPasswordLabel')}
        </small>
    {/if}
</form>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .password-input-container {
        padding: @spacing-ml @spacing-l;
        border-radius: @border-radius-large;
        background-color: var(--viewer-bg);
        border: 1px solid var(--viewer-default-border);
        display: flex;
        gap: @spacing-ml;
        flex-direction: column;

        .input-row {
            display: flex;
            gap: @spacing-sm;

            input {
                padding: 0 @spacing-m;
                flex: 1;
                outline: none;
                border-radius: @border-radius-default;
                background-color: var(--viewer-default-button-color);
                border: 1px solid var(--viewer-default-border);

                &:focus {
                    border-color: var(--focusBorderColor);
                }

                &:focus-visible {
                    outline: none !important;
                    box-shadow: none !important;
                }
            }
        }

        .wrong-password {
            text-align: center;
            color: var(--danger-red);
        }
    }
</style>