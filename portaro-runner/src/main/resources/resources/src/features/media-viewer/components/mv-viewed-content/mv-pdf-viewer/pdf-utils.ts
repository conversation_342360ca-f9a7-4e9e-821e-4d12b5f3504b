import type * as Pdfjs from 'pdfjs-dist/types/src/pdf';
import type {PdfRotation, ViewedContentViewport} from '../../../lib/mv-viewed-content';

export function calculateOptimalPdfPageScaling(defaultPdfViewport: Pdfjs.PageViewport, viewerViewport: ViewedContentViewport, pdfRotation: PdfRotation, thumbnailsWidthPx: number): number {
    const pdfWidth = isPdfRotatedSideways(pdfRotation) ? defaultPdfViewport.height : defaultPdfViewport.width;
    const pdfHeight = isPdfRotatedSideways(pdfRotation) ? defaultPdfViewport.width : defaultPdfViewport.height;

    const widthRatio = (viewerViewport.width - thumbnailsWidthPx) / pdfWidth;
    const heightRatio = viewerViewport.height / pdfHeight;

    return Math.min(widthRatio, heightRatio);
}

export function isPdfRotatedSideways(pdfRotation: PdfRotation): boolean {
    return pdfRotation === 90 || pdfRotation === 270;
}