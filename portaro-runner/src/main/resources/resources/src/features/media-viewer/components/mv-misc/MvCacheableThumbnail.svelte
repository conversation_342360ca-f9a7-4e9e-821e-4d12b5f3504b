<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import type {RawImageData} from '../../services/mv-image-cache.service';
    import {exists} from 'shared/utils/custom-utils';
    import {fade} from 'svelte/transition';
    import {getMediaViewerContext} from '../../lib/mv-context';
    import {onMount} from 'svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';

    export let file: ViewableFile;
    export let loadersScale = 1;

    const imageCacheService = getMediaViewerContext().imageCacheService;

    let loading = true;
    let loadError = false;
    let rawImageData: RawImageData | null = null;

    let containerElement: HTMLDivElement;
    let intersectionObserver: IntersectionObserver;

    onMount(() => {
        intersectionObserver = new IntersectionObserver((entries: IntersectionObserverEntry[], observer: IntersectionObserver) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    loadRawThumbnailData();

                    if (exists(observer)) {
                        observer.unobserve(entry.target);
                    }
                }
            });
        }, {
            root: null,
            threshold: 0.1
        });

        if (exists(containerElement)) {
            intersectionObserver.observe(containerElement);
        }
    });

    async function loadRawThumbnailData() {
        const thumbnailImageData = await imageCacheService.fetchThumbnailWithCaching(file);
        if (!exists(thumbnailImageData)) {
            loading = false;
            loadError = true;
            return;
        }

        rawImageData = thumbnailImageData;
        loading = false;
    }
</script>

<div class="mv-cached-thumbnail"
     style:--loaders-scale="{loadersScale}"
     bind:this={containerElement}>

    <slot {loading} {loadError} blobUrl="{rawImageData?.blobUrl}"/>

    {#if loading}
        <div class="thumbnail-loader-container" out:fade={{duration: 250}}>
            <KpLoadingBlock size="xs"/>
        </div>
    {/if}

    {#if loadError}
        <div class="thumbnail-loader-container" in:fade={{duration: 250}}>
            <UIcon color="var(--danger-red)" icon="exclamation"/>
        </div>
    {/if}
</div>

<style lang="less">
    .mv-cached-thumbnail {
        position: relative;

        .thumbnail-loader-container {
            scale: var(--loaders-scale);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
</style>