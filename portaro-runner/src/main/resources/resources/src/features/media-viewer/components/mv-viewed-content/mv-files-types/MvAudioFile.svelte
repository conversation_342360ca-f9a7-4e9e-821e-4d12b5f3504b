<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import {fly} from 'svelte/transition';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import MvAudioPlayer from '../mv-media-players/audio/MvAudioPlayer.svelte';
    import {getLocalization} from 'core/svelte-context/context';

    export let file: ViewableFile;
    const flyInAnimParams = {y: 20, duration: 250};
    const localize = getLocalization();

    let loading = true;
    let loadError = false;

    const handleLoadSuccess = () => {
        loading = false;
    };

    const handleLoadError = () => {
        loadError = true;
        loading = false;
    };
</script>

<div class="mv-audio-file" class:audio-loading={loading || loadError}>
    <MvAudioPlayer src="/files/{file.id}"
                   on:loaded={handleLoadSuccess}
                   on:error={handleLoadError}/>
</div>

{#if loading}
    <div class="loading-container">
        <KpLoadingBlock size="sm"/>
    </div>
{/if}

{#if loadError}
    <div class="loading-container load-error-container" in:fly={flyInAnimParams}>
        <IconedContent icon="exclamation"
                       orientation="vertical"
                       align="center"
                       justify="center"
                       iconColor="var(--danger-red)">
            {localize(/* @kp-localization mediaviewer.content.AudioLoadError */ 'mediaviewer.content.AudioLoadError')}
        </IconedContent>
    </div>
{/if}

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-audio-file {
        position: relative;
        width: 100%;
        height: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: @spacing-m @spacing-m 30px;
        transition: scale 0.3s ease-in-out, opacity 0.3s ease-in-out;

        &.audio-loading {
            scale: 0.9;
            opacity: 0;
        }
    }

    .loading-container {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .load-error-container {
        color: var(--danger-red);
    }
</style>