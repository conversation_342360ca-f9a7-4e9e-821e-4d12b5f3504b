<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import type {TextValueEditorOptions} from 'shared/value-editors/internal/editors/text/types';
    import type {PortaroTheme} from 'shared/theme/portaro-theme';
    import type {Ace} from 'ace-builds';
    import {getThemeContext} from 'shared/theme/portaro-theme';
    import {onDestroy, onMount} from 'svelte';
    import {getMediaViewerContext} from '../../../lib/mv-context';
    import {fly, slide} from 'svelte/transition';
    import {exists} from 'shared/utils/custom-utils';
    import {getViewedContentContext} from '../../../lib/mv-viewed-content';
    import {getLocalization, getSanitize, getServerUrl} from 'core/svelte-context/context';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import {
        fileExtensionToAceMode,
        FILE_METADATA_EDIT_EVENT,
        CONTENTS_SEARCH_TOGGLE_EVENT,
        SEARCH_IN_FILE_EVENT,
        SEARCH_SHOW_NEXT_RESULT_EVENT,
        SEARCH_SHOW_PREVIOUS_RESULT_EVENT
    } from '../../../lib/mv-constants';

    export let file: ViewableFile;
    const context = getMediaViewerContext();
    const viewedContent = getViewedContentContext();
    const currentTheme = getThemeContext().currentTheme;
    const editorFlyInAnimParams = {y: 10, duration: 250};
    const saveLabelSlideAnimParams = {duration: 250};
    const localize = getLocalization();
    const sanitizeHtml = getSanitize();

    let loading = true;
    let aceEditor: Ace.Editor;
    let initialContents = '';
    let fileModel = '';
    let isRenderableHtml = false;
    let htmlPreview = false;
    $: saved = (fileModel ?? '') === (initialContents ?? '');

    onMount(async () => {
        viewedContent.eventBus.addEventListener(SEARCH_IN_FILE_EVENT, handleSearchInFile);
        viewedContent.eventBus.addEventListener(SEARCH_SHOW_NEXT_RESULT_EVENT, handleShowNextSearchResult);
        viewedContent.eventBus.addEventListener(SEARCH_SHOW_PREVIOUS_RESULT_EVENT, handleShowPreviousSearchResult);

        const fileExtension = getFileExtension();

        try {
            initialContents = await context.service.getFileContent(file);
            if (typeof initialContents !== 'string') {
                initialContents = JSON.stringify(initialContents, null, 4);
            }
            fileModel = initialContents;
        } finally {
            if (fileExtension === 'html') {
                isRenderableHtml = isValidHtml(initialContents);
            }

            loading = false;
        }
    });

    onDestroy(() => {
        viewedContent.eventBus.removeEventListener(SEARCH_IN_FILE_EVENT, handleSearchInFile);
        viewedContent.eventBus.removeEventListener(SEARCH_SHOW_NEXT_RESULT_EVENT, handleShowNextSearchResult);
        viewedContent.eventBus.removeEventListener(SEARCH_SHOW_PREVIOUS_RESULT_EVENT, handleShowPreviousSearchResult);
    });

    function getAceEditorOptions(theme: PortaroTheme): TextValueEditorOptions {
        const fileExtension = getFileExtension();
        return {
            type: 'rich-textarea',
            aceOptions: {
                theme: theme === 'light' ? 'tomorrow' : 'one_dark',
                showPrintMargin: false,
                mode: fileExtensionToAceMode[fileExtension],
                onLoad: handleAceOnLoad
            }
        };
    }

    function isValidHtml(htmlString: string): boolean {
        const parser = new DOMParser();

        const doc = parser.parseFromString(htmlString, 'text/html');

        const errors = doc.querySelectorAll('parsererror');
        return errors.length === 0;
    }

    function getFileExtension() {
        const fileNameSplit = file.filename.split('.');
        return fileNameSplit[fileNameSplit.length - 1]?.toLowerCase() ?? '';
    }

    const handleAceOnLoad = (aceInstance: Ace.Editor) => {
        aceEditor = aceInstance;
        aceEditor.setReadOnly(!context.canEdit);

        aceEditor.commands.addCommand({
            name: 'saving',
            bindKey: {win: 'Ctrl-S', mac: 'Command-S'},
            exec: () => {
                if (!saved && context.canEdit) {
                    handleSaveFileContents();
                }
            },
            readOnly: false
        });

        aceEditor.commands.addCommand({
            name: 'searching',
            bindKey: {win: 'Ctrl-F', mac: 'Command-F'},
            exec: () => context.eventBus.dispatchEvent(new CustomEvent<void>(CONTENTS_SEARCH_TOGGLE_EVENT)),
            readOnly: false
        });
    };

    const handleSaveFileContents = async () => {
        const newFileSize = await context.service.saveFileContents(file, fileModel);
        if (!exists(newFileSize)) {
            return;
        }

        initialContents = fileModel;
        file.size = newFileSize;
        file = file; // Trigger reactivity
        context.eventBus.dispatchEvent(new CustomEvent<ViewableFile>(FILE_METADATA_EDIT_EVENT, {detail: file}));
    };

    const handleSearchInFile = (event: CustomEvent<string>) => {
        const totalResults = aceEditor.findAll(event.detail);
        aceEditor.find(event.detail);

        viewedContent.setSearchResult({
            searchQuery: event.detail,
            currentResult: totalResults > 0 ? 1 : 0,
            totalResults
        });
    };

    const handleShowNextSearchResult = () => {
        aceEditor.findNext();
        viewedContent.searchResult.update((currentSearchResult) => {
            currentSearchResult.currentResult += 1;
            return currentSearchResult;
        });
    };

    const handleShowPreviousSearchResult = () => {
        aceEditor.findPrevious();
        viewedContent.searchResult.update((currentSearchResult) => {
            currentSearchResult.currentResult -= 1;
            return currentSearchResult;
        });
    };

    const handleToggleHtmlPreviewClick = () => {
        htmlPreview = !htmlPreview;
    };
</script>

<div class="mv-text-file">
    {#if !loading}
        {#if isRenderableHtml && (!context.canEdit || htmlPreview)}
            <div class="html-preview-container" in:fly={editorFlyInAnimParams}>
                {@html sanitizeHtml(fileModel)}
            </div>
        {:else}
            <div class="editor-container" in:fly={editorFlyInAnimParams}>
                <KpValueEditor type="text"
                               options="{getAceEditorOptions($currentTheme)}"
                               bind:model={fileModel}/>
            </div>
        {/if}
    {:else}
        <div class="loading-container">
            <KpLoadingBlock size="sm"/>
        </div>
    {/if}

    {#if context.canEdit}
        <div class="editor-footer-container">
            <div class="save-button-container">
                <KpButton buttonStyle="{saved ? 'success-new' : 'accent-blue-new'}"
                          isDisabled="{saved}"
                          on:click={handleSaveFileContents}>

                    <IconedContent icon="{saved ? 'check' : 'disk'}">
                        {saved
                            ? localize(/* @kp-localization mediaviewer.content.TextAllChangesSavedBtnLabel */ 'mediaviewer.content.TextAllChangesSavedBtnLabel')
                            : localize(/* @kp-localization mediaviewer.content.TextSaveChangesBtnLabel */ 'mediaviewer.content.TextSaveChangesBtnLabel')}
                    </IconedContent>
                </KpButton>

                {#if !saved}
                        <span class="save-label" transition:slide={saveLabelSlideAnimParams}>
                            {localize(/* @kp-localization mediaviewer.content.TextSaveShortcutLabel */ 'mediaviewer.content.TextSaveShortcutLabel')}
                        </span>
                {/if}

                {#if isRenderableHtml}
                    <div class="html-render-toggle-button-container">
                        <KpButton buttonStyle="brand-orange-new"
                                  on:click={handleToggleHtmlPreviewClick}>

                            <IconedContent icon="{htmlPreview ? 'eye-crossed' : 'eye'}">
                                {htmlPreview
                                    ? localize(/* @kp-localization mediaviewer.content.TextHtmlShowInTextEditorBtnLabel */ 'mediaviewer.content.TextHtmlShowInTextEditorBtnLabel')
                                    : localize(/* @kp-localization mediaviewer.content.TextHtmlRenderBtnLabel */ 'mediaviewer.content.TextHtmlRenderBtnLabel')}
                            </IconedContent>
                        </KpButton>
                    </div>
                {/if}
            </div>

            <div class="info-container">
                <span class="text-muted">
                    {localize(/* @kp-localization mediaviewer.content.TextUrlLabel */ 'mediaviewer.content.TextUrlLabel')}
                    <code>{getServerUrl()}/files/{file.id}</code>
                </span>

                <span class="text-muted">
                    {localize(/* @kp-localization mediaviewer.content.TextUrlPathLabel */ 'mediaviewer.content.TextUrlPathLabel')}
                    <code>/files/{file.id}</code>
                </span>

                <span class="text-muted">
                    {localize(/* @kp-localization mediaviewer.content.TextCustomFileTemplateLabel */ 'mediaviewer.content.TextCustomFileTemplateLabel')}
                    <code>${`{serverUrl}/custom/${file.directory.name}/${file.filename}`}</code>
                </span>
            </div>
        </div>
    {/if}
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";
    @import (reference) "styles/portaro.media-queries.less";

    .mv-text-file {
        position: relative;
        width: 100%;
        height: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;

        .loading-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .editor-container {
            width: 100%;
            height: 100%;
        }

        .html-preview-container {
            width: 100%;
            flex: 1 1 0;
            overflow-y: auto;
            padding: @spacing-l @spacing-xl;

            @media (max-width: @screen-xs-max) {
                padding: @spacing-l @spacing-ml;
            }
        }

        .editor-footer-container {
            width: 100%;
            display: flex;
            align-items: center;
            padding: @spacing-base @spacing-l;
            justify-content: space-between;
            background-color: var(--viewer-content-bg);
            border-top: 1px solid var(--viewer-default-border);
            gap: @spacing-m;
            flex-wrap: wrap;

            .save-button-container {
                display: flex;
                flex-direction: column;

                .save-label {
                    margin-top: 4px;
                    margin-left: 1px;
                    font-size: @font-size-sm;
                }

                .html-render-toggle-button-container {
                    margin-top: @spacing-s;
                }
            }

            .info-container {
                display: flex;
                flex-direction: column;
                gap: @spacing-xs;
                font-size: @font-size-sm;

                code {
                    background-color: var(--viewer-orange-highlight-transparent);
                    color: var(--brand-orange-new);
                }
            }
        }
    }

    :global {
        .mv-text-file {
            kp-value-editor {
                height: 100%;
            }

            .form-control {
                background-color: var(--viewer-content-bg);
                color: var(--viewer-default-text-color);
                border-radius: 0;
                border: none;
            }

            .ace-editor-wrapper {
                height: 100%;

                .ace-editor {
                    height: 100%;

                    .ace_gutter {
                        border-right: 1px solid var(--viewer-default-border);
                        background-color: var(--viewer-content-bg);
                    }

                    .ace_mobile-menu {
                        display: none;
                    }
                }

                .fullscreen-button-container {
                    right: @spacing-xxl;

                    .kp-icon-button {
                        background-color: var(--viewer-default-button-color);
                        border: 1px solid var(--viewer-default-border);
                    }
                }
            }
        }
    }
</style>