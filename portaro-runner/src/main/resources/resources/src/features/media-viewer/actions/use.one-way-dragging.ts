import type {ActionReturn} from 'svelte/action';

interface Attributes {
    'on:dragging-start'?: (e: CustomEvent<void>) => void;
    'on:dragging-progress': (e: CustomEvent<number>) => void;
    'on:dragging-end'?: (e: CustomEvent<void>) => void;
}

export const oneWayDragging = (
    node: HTMLElement,
    params: { orientation: string }
): ActionReturn<boolean, Attributes> => {

    let dragStart: number | null = null;

    const attr = params.orientation === 'vertical' ? 'screenX' : 'screenY';

    const mouseDownAction = (e: MouseEvent) => {
        e.preventDefault();
        node.dispatchEvent(new CustomEvent<void>('dragging-start'));
        dragStart = e[attr];
    }

    const mouseMoveAction = (e: MouseEvent) => {
        if (dragStart !== null) {
            const delta = e[attr] - dragStart;
            node.dispatchEvent(new CustomEvent<number>('dragging-progress', {detail: delta / window.devicePixelRatio}));
        }
    }

    const mouseUpAction = () => {
        dragStart = null;
        node.dispatchEvent(new CustomEvent<void>('dragging-end'));
    }

    node.addEventListener('mousedown', mouseDownAction);
    document.addEventListener('mousemove', mouseMoveAction);
    document.addEventListener('mouseup', mouseUpAction);

    return {
        destroy() {
            node.removeEventListener('mousedown', mouseDownAction);
            document.removeEventListener('mousemove', mouseMoveAction);
            document.removeEventListener('mouseup', mouseUpAction);
        }
    }
}