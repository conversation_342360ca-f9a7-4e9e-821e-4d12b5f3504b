<script lang="ts">
    import MvSideBarDirectoryActionButtons from './MvSideBarDirectoryActionButtons.svelte';
    import MvSideBarFilesActionButtons from './MvSideBarFilesActionButtons.svelte';
    import {onDestroy} from 'svelte';
    import {getMediaViewerContext} from '../../../lib/mv-context';
    import {fade, slide} from 'svelte/transition';

    export let mobile: boolean;

    const context = getMediaViewerContext();
    const actionButtonsFadeInAnimParams = {duration: 250};

    let selectingFiles = false;
    let searchQuery: string | null = null;
    const selectingFilesUnsubscribe = context.selectingFiles.subscribe((currentSelectingFiles) => selectingFiles = currentSelectingFiles);
    const searchQueryUnsubscribe = context.searchQuery.subscribe((currentSearchQuery) => searchQuery = currentSearchQuery);

    $: isInSearch = searchQuery && searchQuery.length > 0;

    onDestroy(() => {
        selectingFilesUnsubscribe();
        searchQueryUnsubscribe();
    });
</script>

{#key selectingFiles}
    <div class="mv-action-buttons-container"
         class:mobile={mobile}
         in:fade={actionButtonsFadeInAnimParams}
         out:slide={{duration: isInSearch && !mobile ? 250 : 0}}>

        {#if selectingFiles}
            <div class="action-buttons" class:mobile={mobile}>
                <MvSideBarFilesActionButtons/>
            </div>
        {:else if !isInSearch}
            <div class="action-buttons" class:mobile={mobile}>
                <MvSideBarDirectoryActionButtons/>
            </div>
        {/if}
    </div>
{/key}

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: @spacing-s;
        border-top: 1px solid var(--viewer-default-border);

        &.mobile {
            margin-top: auto;
            width: calc(100% + @spacing-ml * 2);
            margin-left: calc(@spacing-ml * -1);
            margin-right: calc(@spacing-ml * -1);
            padding: @spacing-ml;
        }

        &:not(.mobile) {
            padding: @spacing-ml;
        }
    }
</style>