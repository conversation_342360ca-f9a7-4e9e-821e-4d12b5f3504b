import type {ViewableFile} from 'typings/portaro.be.types';
import {FileViewForm} from 'shared/constants/portaro.constants';
import {exists} from 'shared/utils/custom-utils';
import {MvCachedService} from './mv-cached-service';

type ImageSize = 'original' | 'thumbnail';
type ImageCacheKey = `${ImageSize}-${number}`;

export interface RawImageData {
    blobUrl: string;
    width: number;
    height: number;
}

export class MvImageCacheService extends MvCachedService<ImageCacheKey, RawImageData> {
    public static serviceName = 'mvImageCacheService';
    private static thumbnailWidth = 128;

    constructor() {
        super((item) => {
            URL.revokeObjectURL(item.blobUrl);
        });
    }

    public async fetchThumbnailWithCaching(file: ViewableFile): Promise<RawImageData | null> {
        return this.fetchRawImageDataWithCaching(file, 'thumbnail');
    }

    public async fetchOriginalImageWithCaching(file: ViewableFile): Promise<RawImageData | null> {
        return this.fetchRawImageDataWithCaching(file, 'original');
    }

    public getCachedThumbnailImage(file: ViewableFile): RawImageData | null {
        const cachedRawImageData = this.getCachedItem(`thumbnail-${file.id}`);
        if (!exists(cachedRawImageData)) {
            return null;
        }

        return cachedRawImageData;
    }

    private async fetchRawImageDataWithCaching(file: ViewableFile, imageSizeType: ImageSize): Promise<RawImageData | null> {
        if (!file.viewForms.includes(FileViewForm.THUMBNAIL)) {
            return null;
        }

        const cachedRawImageData = this.getCachedItem(`${imageSizeType}-${file.id}`);
        if (exists(cachedRawImageData)) {
            return cachedRawImageData;
        }

        try {
            const response = await fetch(imageSizeType === 'original' ? `/files/${file.id}` : `/files/${file.id}?width=${MvImageCacheService.thumbnailWidth}`);
            if (!response.ok) {
                return null;
            }

            const imageBlob = await response.blob();
            const rawImageData = await this.createRawImageData(imageBlob);

            this.setCachedItem(`${imageSizeType}-${file.id}`, rawImageData);
            return rawImageData;
        } catch {
            return null;
        }
    }

    private async createRawImageData(imageBlob: Blob): Promise<RawImageData> {
        const blobUrl = URL.createObjectURL(imageBlob);
        const imageBitmap = await createImageBitmap(imageBlob);

        return {
            blobUrl,
            width: imageBitmap.width,
            height: imageBitmap.height
        };
    }
}