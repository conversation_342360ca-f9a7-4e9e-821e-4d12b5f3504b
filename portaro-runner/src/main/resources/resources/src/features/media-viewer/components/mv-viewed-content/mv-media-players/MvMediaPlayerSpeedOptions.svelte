<script lang="ts">
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {getThemeContext} from 'shared/theme/portaro-theme';
    import {slide} from 'svelte/transition';
    import {getLocalization} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import {strParams} from 'shared/utils/pipes';

    export let mediaElement: HTMLMediaElement;
    const iconSlideAnimParams = {axis: 'x', duration: 250} as const;

    let playbackSpeedValue = mediaElement.playbackRate;

    const currentTheme = getThemeContext().currentTheme;
    const speedOptions = generatePlaybackSpeeds();
    const localize = getLocalization();

    function hasPlaybackSpeedOptions() {
        return 'playbackRate' in mediaElement && exists(mediaElement.playbackRate);
    }

    function generatePlaybackSpeeds(): number[] {
        const speeds: number[] = [];
        for (let i = 0.25; i <= 2; i += 0.25) {
            speeds.push(parseFloat(i.toFixed(2)));
        }
        return speeds;
    }

    const handleSpeedOptionClick = (value: number) => {
        mediaElement.playbackRate = value;
        playbackSpeedValue = value;
    }

    const handleSpeedButtonClick = () => {
        if (playbackSpeedValue === 1) {
            return;
        }

        handleSpeedOptionClick(1);
    }
</script>

{#if hasPlaybackSpeedOptions()}
    <div class="mv-media-player-speed-options">
        <button class="speed-button"
                aria-label="{localize(/* @kp-localization mediaviewer.content.MultiMediaPlaybackSpeedLabel */ 'mediaviewer.content.MultiMediaPlaybackSpeedLabel')}"
                on:click={handleSpeedButtonClick}>

            <UIcon icon="time-fast"/>
        </button>

        {#if playbackSpeedValue !== 1}
            <small class="speed-value"
                   aria-label="{pipe(localize(/* @kp-localization mediaviewer.content.MultiMediaCurrentPlaybackSpeedLabel */ 'mediaviewer.content.MultiMediaCurrentPlaybackSpeedLabel'), strParams(playbackSpeedValue.toFixed(2)))}"
                   transition:slide={iconSlideAnimParams}>
                ({playbackSpeedValue.toFixed(2)}×)
            </small>
        {/if}

        <div class="speed-options-container">
            <div class="speed-options {$currentTheme}">
                <span class="title">
                    {localize(/* @kp-localization mediaviewer.content.MultiMediaPlaybackSpeedTitle */ 'mediaviewer.content.MultiMediaPlaybackSpeedTitle')}
                </span>

                {#each speedOptions as speedValue(speedValue)}
                    <button class="speed-option"
                            aria-label="{pipe(localize(/* @kp-localization mediaviewer.content.MultiMediaPlaybackSpeedValueLabel */ 'mediaviewer.content.MultiMediaPlaybackSpeedValueLabel'), strParams(speedValue.toString()))}"
                            on:click={() => handleSpeedOptionClick(speedValue)}>

                        {#if playbackSpeedValue === speedValue}
                            <div transition:slide={iconSlideAnimParams} class="selected-icon-container">
                                <UIcon icon="check"/>
                            </div>
                        {/if}

                        {speedValue === 1 ? localize(/* @kp-localization mediaviewer.content.MultiMediaPlaybackSpeedNormal */ 'mediaviewer.content.MultiMediaPlaybackSpeedNormal') : `${speedValue}×`}
                    </button>
                {/each}
            </div>
        </div>
    </div>
{/if}

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-media-player-speed-options {
        position: relative;
        display: flex;
        align-items: center;

        &:hover {
            .speed-options-container {
                transform: none;
                opacity: 1;
                visibility: visible;
            }
        }

        .speed-button {
            position: relative;
            background: none;
            outline: none;
            border: none;
            cursor: pointer;

            &::before {
                content: '';
                top: 50%;
                left: 50%;
                border-radius: 50%;
                transform: translate(-50%, -50%);
                position: absolute;
                width: 32px;
                height: 32px;
                transition: background-color 0.3s ease-in-out;
            }

            &:hover::before {
                background-color: var(--viewer-blue-highlight-transparent);
            }
        }

        .speed-value {
            font-size: 10px;
            opacity: 0.75;
            cursor: pointer;
        }

        .speed-options-container {
            bottom: 100%;
            right: 0;
            z-index: 1;
            position: absolute;
            padding-bottom: @spacing-m;
            visibility: hidden;
            opacity: 0;
            color: var(--viewer-default-text-color);
            transform: translateY(3px);
            transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;

            .speed-options {
                width: 200px;
                border-radius: @border-radius-default;
                border: 1px solid var(--viewer-default-border);
                display: flex;
                padding: @spacing-xs 0;
                flex-direction: column;
                background-color: var(--viewer-default-button-color);

                &.light {
                    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.07);
                }

                .title {
                    padding: @spacing-s @spacing-m @spacing-sm;
                    font-weight: 500;
                    border-bottom: 1px solid var(--viewer-light-border);
                }

                .speed-option {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: start;
                    padding: @spacing-s @spacing-m;
                    background: none;
                    outline: none;
                    border: none;
                    border-bottom: 1px solid var(--viewer-light-border);
                    transition: background-color 0.3s ease-in-out;

                    &:last-child {
                        border-bottom: none;
                    }

                    &:hover {
                        background-color: var(--viewer-blue-highlight-transparent);
                    }

                    .selected-icon-container {
                        margin-right: @spacing-s;
                    }
                }
            }
        }
    }
</style>