<script lang="ts">
    import type {DirectoryNode} from 'typings/portaro.be.types';
    import type {ExpandUrlDirectoryEventData} from '../../../../lib/mv-constants';
    import {getMediaViewerContext} from '../../../../lib/mv-context';
    import {exists} from 'shared/utils/custom-utils';
    import {get} from 'svelte/store';
    import {onDestroy, onMount} from 'svelte';
    import {findDirectoryInTreeById} from '../../../../lib/mv-utils';
    import {getLocalization} from 'core/svelte-context/context';
    import {EXPAND_URL_DIRECTORY_EVENT} from '../../../../lib/mv-constants';
    import MvTreeDirectory from './MvTreeDirectory.svelte';
    import {
        createTreeViewEventContext,
        DIRECTORY_LEFT_ARROW_EVENT,
        DIRECTORY_RIGHT_ARROW_EVENT,
        FILE_LEFT_ARROW_EVENT
    } from './event-context';

    const context = getMediaViewerContext();
    const localize = getLocalization();
    const eventBus = createTreeViewEventContext();
    const rootDirectory = context.rootDirectory;
    const urlStateSubscription = context.urlManagementService.getUrlState$().subscribe((state) => {
        const currentSelectedDirectory = get(context.fileExplorerSelectedDirectory);
        const currentViewedFile = get(context.viewedFile);

        if (exists(state.directory) && state.directory !== currentSelectedDirectory?.id?.toString()) {
            expandUrlDirectory(state.directory);
            selectUrlDirectory(state.directory);
        }

        if (exists(state.file) && state.file !== currentViewedFile?.id?.toString()) {
            selectUrlFile(state.file);
        }
    });

    onMount(async () => {
        const state = await context.urlManagementService.getCurrentUrlState();

        if (exists(state.directory)) {
            expandUrlDirectory(state.directory);
            selectUrlDirectory(state.directory);
        }
    });

    onDestroy(() => {
        urlStateSubscription.unsubscribe();
    });

    let currentlyInFocusType: 'directory' | 'file' | undefined;
    let currentlyInFocusId: number | undefined;

    // Tree view keyboard navigation keys that are used
    const handledKeys = ['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'];

    // Handle tree keyboard navigation
    const handleKeyDown = (event: KeyboardEvent) => {
        if (!exists(currentlyInFocusType) || !exists(currentlyInFocusId) || !handledKeys.includes(event.key)) {
            return;
        }

        event.preventDefault();

        // When focus is on an open node, closes the node.
        // When focus is on a child node that is also either an end node or a closed node, moves focus to its parent node.
        // When focus is on a root node that is also either an end node or a closed node, does nothing.
        if (event.key === 'ArrowLeft') {
            eventBus.dispatchEvent(new CustomEvent<number>(
                currentlyInFocusType === 'directory' ? DIRECTORY_LEFT_ARROW_EVENT : FILE_LEFT_ARROW_EVENT,
                {detail: currentlyInFocusId})
            );
            return;
        }

        // When focus is on a closed node, opens the node; focus does not move.
        // When focus is on an open node, moves focus to the first child node.
        // When focus is on an end node, does nothing.
        if (event.key === 'ArrowRight' && currentlyInFocusType === 'directory') {
            eventBus.dispatchEvent(new CustomEvent<number>(DIRECTORY_RIGHT_ARROW_EVENT, {detail: currentlyInFocusId}));
            return;
        }

        // Moves focus to the previous node that is focusable without opening or closing a node.
        if (event.key === 'ArrowUp') {
            const focusedElement = document.getElementById(`tree-view-${currentlyInFocusType}-${currentlyInFocusId}`);
            if (!exists(focusedElement)) {
                return;
            }

            // We need to recursively go one level up and see if there is previous element
            let parentElement = focusedElement.parentElement;

            while (exists(parentElement) && !exists(parentElement.previousElementSibling)) {
                parentElement = parentElement.parentElement;
            }

            if (exists(parentElement) && exists(parentElement.previousElementSibling)) {
                const treeViewElement = extractTreeViewElement(parentElement.previousElementSibling);
                if (!exists(treeViewElement)) {
                    return;
                }

                const innerTreeViewElement = getInnerTreeViewChild(treeViewElement, true);

                if (Number(focusedElement.parentElement.ariaLevel) > Number(treeViewElement.parentElement.ariaLevel)) {
                    treeViewElement.focus();
                    return;
                }

                innerTreeViewElement.focus();
                return;
            }

            return;
        }

        // Moves focus to the next node that is focusable without opening or closing a node.
        if (event.key === 'ArrowDown') {
            const focusedElement = document.getElementById(`tree-view-${currentlyInFocusType}-${currentlyInFocusId}`);
            if (!exists(focusedElement)) {
                return;
            }

            // If currently focused element is directory that contains directory list, select first child
            const lastInnerChild = getInnerTreeViewChild(focusedElement, false);
            if (lastInnerChild !== focusedElement) {
                lastInnerChild.focus();
                return;
            }

            // We need to recursively go one level up and see if there is next element
            let parentElement = focusedElement.parentElement;

            while (exists(parentElement) && !exists(parentElement.nextElementSibling)) {
                parentElement = parentElement.parentElement;
            }

            if (exists(parentElement) && exists(parentElement.nextElementSibling)) {
                const treeViewElement = extractTreeViewElement(parentElement.nextElementSibling);
                if (!exists(treeViewElement)) {
                    return;
                }

                treeViewElement.focus();
                return;
            }

            return;
        }

        // Moves focus to the first node in the tree without opening or closing a node.
        if (event.key === 'Home') {
            const rootDirectoryElement = document.querySelector(`tree-view-directory-${rootDirectory.id}`);
            if (!exists(rootDirectoryElement)) {
                return;
            }

            (rootDirectoryElement as HTMLElement).focus();
            return;
        }

        // Moves focus to the last node in the tree that is focusable without opening a node.
        if (event.key === 'End') {
            const rootDirectoryElement = document.querySelector(`tree-view-directory-${rootDirectory.id}`);
            if (!exists(rootDirectoryElement)) {
                return;
            }

            getInnerTreeViewChild(rootDirectoryElement, true).focus();
            return;
        }
    }

    // Extract element with tree-view id (because there might be some wrapper around it)
    function extractTreeViewElement(element: Element): HTMLElement | null {
        let childElement = element;
        while (exists(childElement) && !childElement.id.includes('tree-view-')) {
            childElement = childElement.children[0];
        }

        if (exists(childElement) && childElement.id.includes('tree-view-')) {
            return childElement as HTMLElement;
        }

        return null;
    }

    // Get selectable child element
    function getInnerTreeViewChild(treeViewElement: Element, lastRecursive: boolean): HTMLElement {
        let innerElement = treeViewElement;

        while (exists(innerElement) && innerElement.id.includes('directory') && exists(innerElement.nextElementSibling) && innerElement.nextElementSibling.classList.contains('directory-list')) {
            const directoryListElement = innerElement.nextElementSibling;
            const innerTreeViewElement = extractTreeViewElement(directoryListElement.children[lastRecursive ? (directoryListElement.children.length - 1) : 0]);

            if (exists(innerTreeViewElement)) {
                innerElement = innerTreeViewElement;

                if (lastRecursive) {
                    continue;
                }
            }

            return innerTreeViewElement;
        }

        return innerElement as HTMLElement;
    }

    // Save currently focused data on focus change
    const handleFocusIn = (event: FocusEvent) => {
        const focusData = parseFocusedData(event.target);
        if (!exists(focusData)) {
            currentlyInFocusType = undefined;
            currentlyInFocusId = undefined;
            return;
        }

        currentlyInFocusType = focusData.type;
        currentlyInFocusId = focusData.id;
    }

    const handleFocusOut = (event: FocusEvent) => {
        const focusData = parseFocusedData(event.target);

        if (!exists(focusData) || (currentlyInFocusType === focusData.type && currentlyInFocusId === focusData.id)) {
            currentlyInFocusType = undefined;
            currentlyInFocusId = undefined;
        }
    }

    // Get type (directory or file) and id from tree-view element
    function parseFocusedData(eventTarget: EventTarget): { type: 'directory' | 'file', id: number } | null {
        const target = eventTarget as HTMLElement;
        if (!exists(target) || !target.id.includes('tree-view-')) {
            return null;
        }

        const type = target.id.includes('file') ? 'file' : 'directory';
        const id = Number(target.id.replace(`tree-view-${type}-`, ''));
        return {type, id};
    }

    function expandUrlDirectory(directoryIdString: string) {
        const directoryId = Number(directoryIdString);
        if (isNaN(directoryId)) {
            return;
        }

        const path: DirectoryNode[] = [];
        const selectedDirectory = findDirectoryInTreeById(context.rootDirectory, directoryId, path);

        if (!exists(selectedDirectory) || path.length === 0) {
            return;
        }

        context.eventBus.dispatchEvent(new CustomEvent<ExpandUrlDirectoryEventData>(EXPAND_URL_DIRECTORY_EVENT, {
            detail: {
                parent: path[0],
                path
            }
        }));
    }

    function selectUrlDirectory(directoryIdString: string) {
        const directoryId = Number(directoryIdString);
        if (isNaN(directoryId)) {
            return;
        }

        const selectedDirectory = findDirectoryInTreeById(context.rootDirectory, directoryId);
        context.setFileExplorerSelectedDirectory(selectedDirectory);
    }

    async function selectUrlFile(fileIdString: string) {
        const fileId = Number(fileIdString);
        if (isNaN(fileId)) {
            return;
        }

        const file = await context.service.getFileById(fileId);
        if (!exists(file)) {
            return;
        }

        expandUrlDirectory(file.directory?.id?.toString());
        context.setViewedFile(file);
    }
</script>

<div class="mv-directories-tree-view"
     on:focusin={handleFocusIn}
     on:focusout={handleFocusOut}
     on:keydown={handleKeyDown}
     role="tree"
     aria-label="{localize(/* @kp-localization mediaviewer.fileExplorer.TreeViewLabel */ 'mediaviewer.fileExplorer.TreeViewLabel')}"
     tabindex="-1">

    <MvTreeDirectory initiallyOpen="{true}"
                     directoryNode="{rootDirectory}"
                     level="{1}"/>
</div>

<style lang="less">
    .mv-directories-tree-view {
        margin: 0 -6px;
        flex: 1;
    }
</style>