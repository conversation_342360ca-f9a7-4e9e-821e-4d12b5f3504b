import type {Writable} from 'svelte/store';
import {writable} from 'svelte/store';
import {mediaViewerSidebarOpen} from './mv-sidebar';

export const mediaViewerLayoutWidth: Writable<number> = writable(0);

export enum MediaViewerLayoutSize {
    MOBILE = 1,
    TABLET = 2,
    LAPTOP = 3,
    DESKTOP = 4
}

interface LayoutConstraints {
    minWidth: number;
    maxWidth: number;
}

const layoutSizes: Record<MediaViewerLayoutSize, LayoutConstraints> = {
    [MediaViewerLayoutSize.MOBILE]: {minWidth: 0, maxWidth: 767},
    [MediaViewerLayoutSize.TABLET]: {minWidth: 768, maxWidth: 1023},
    [MediaViewerLayoutSize.LAPTOP]: {minWidth: 1024, maxWidth: 1439},
    [MediaViewerLayoutSize.DESKTOP]: {minWidth: 1440, maxWidth: Infinity}
};

function getLayoutSize(width: number): MediaViewerLayoutSize {
    for (const [size, constraints] of Object.entries(layoutSizes)) {
        if (
            width >= constraints.minWidth &&
            width <= constraints.maxWidth
        ) {
            return Number(size) as MediaViewerLayoutSize;
        }
    }

    return MediaViewerLayoutSize.MOBILE;
}

export function isCurrentLayout(size: MediaViewerLayoutSize, layoutWidth: number): boolean {
    const currentLayout = getLayoutSize(layoutWidth);
    return size === currentLayout;
}

export function isCurrentLayoutOrBigger(size: MediaViewerLayoutSize, layoutWidth: number): boolean {
    const currentLayout = getLayoutSize(layoutWidth);
    return currentLayout >= size;
}

export function isCurrentLayoutOrSmaller(size: MediaViewerLayoutSize, layoutWidth: number): boolean {
    const currentLayout = getLayoutSize(layoutWidth);
    return currentLayout <= size;
}

export function setMediaViewerLayoutSize(width: number) {
    mediaViewerLayoutWidth.set(width);
    mediaViewerSidebarOpen.set(false);
}