<script lang="ts">
    import MvOnboardingPage from '../MvOnboardingPage.svelte';
    import imgSrc from '../images/responsivity.webp';
    import {getLocalization} from 'core/svelte-context/context';

    const localize = getLocalization();
</script>

<MvOnboardingPage>
    <img src="{imgSrc}"
         alt="Responsivity"
         aria-labelledby="onboarding-responsivity"/>

    <h2 id="onboarding-responsivity">
        {localize(/* @kp-localization mediaviewer.onboarding.BetterResponsivityTitle */ 'mediaviewer.onboarding.BetterResponsivityTitle')}
    </h2>

    <span>
        {localize(/* @kp-localization mediaviewer.onboarding.BetterResponsivityParagraph1 */ 'mediaviewer.onboarding.BetterResponsivityParagraph1')}
    </span>

    <span>
        {localize(/* @kp-localization mediaviewer.onboarding.BetterResponsivityParagraph2 */ 'mediaviewer.onboarding.BetterResponsivityParagraph2')}
    </span>

    <span>
        {localize(/* @kp-localization mediaviewer.onboarding.BetterResponsivityParagraph3 */ 'mediaviewer.onboarding.BetterResponsivityParagraph3')}
    </span>
</MvOnboardingPage>