import type {DirectoryNode, ViewableFile} from 'typings/portaro.be.types';
import type {Observable} from 'rxjs';

export type DirectoryContent = DirectoryNode | ViewableFile;

export type FileExplorerViewType = 'tree' | 'grid' | 'column';

export type TooltipArrowDirection = 'left' | 'right' | 'bottom' | 'top';

export interface MediaViewerInlineData {
    parentDirectory: DirectoryNode;
    editMode: boolean;
}

export interface MediaViewerFullPageData {
    editMode: boolean;
    backUrl: string;
    openedFile: ViewableFile | null;
}

export interface IMvStateService {
    getCurrentUrlState(): Promise<MediaViewerState>;

    getUrlState$(): Observable<MediaViewerState>;

    setSelectedDirectory(value: DirectoryNode): void;

    setViewedFile(value: ViewableFile | null): void;

    setSearchQuery(value: string | null): void;

    setPageNumber(value: number | null): void;

    resetToDefault(): void;
}

export interface MediaViewerState {
    rootDirectory: string;
    origin?: string;
    edit?: string;
    file?: string;
    directory?: string;
    q?: string;
    page?: string;
}