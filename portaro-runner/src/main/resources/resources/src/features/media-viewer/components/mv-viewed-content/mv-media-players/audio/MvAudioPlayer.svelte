<script lang="ts">
    import {createEventDispatcher, onDestroy, onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {getThemeContext} from 'shared/theme/portaro-theme';
    import {getLocalization} from 'core/svelte-context/context';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import MvMediaPlayerVolumeControl from '../MvMediaPlayerVolumeControl.svelte';
    import MvMediaPlayerProgressBar from '../MvMediaPlayerProgressBar.svelte';
    import MvMediaPlayerSpeedOptions from '../MvMediaPlayerSpeedOptions.svelte';

    export let src: string;
    const currentTheme = getThemeContext().currentTheme;
    const dispatch = createEventDispatcher<{ 'loaded': void, 'error': void }>();
    const localize = getLocalization();

    let isPlaying = false;
    let audioPlayerElement: HTMLAudioElement;

    onMount(() => {
        window.addEventListener('keydown', handleKeyDown);
    });

    onDestroy(() => {
        window.removeEventListener('keydown', handleKeyDown);

        if (exists(audioPlayerElement)) {
            audioPlayerElement.pause();
            // This will close the previous HTTP connection and trigger reload
            audioPlayerElement.src = 'data:audio/mp3;base64,AAAAHG...MTAw';
            audioPlayerElement.load();
            audioPlayerElement.remove();
        }
    });

    function play() {
        if (!exists(audioPlayerElement)) {
            return;
        }

        audioPlayerElement.play();
        isPlaying = true;
    }

    function pause() {
        audioPlayerElement.pause();
        isPlaying = false;
    }

    const handleKeyDown = (event: KeyboardEvent) => {
        if (event.code === 'Space') {
            if (isPlaying) {
                pause();
                return;
            }

            play();
        }
    }

    const handleAudioLoadSuccess = () => {
        dispatch('loaded');
    }

    const handleAudioLoadError = () => {
        dispatch('error');
    }
</script>

<audio src="{src}"
       bind:this={audioPlayerElement}
       on:loadedmetadata={handleAudioLoadSuccess}
       on:error={handleAudioLoadError}
       on:pause={() => isPlaying = false}
       on:play={() => isPlaying = true}/>

<div class="mv-audio-player {$currentTheme}">
    <button class="play-pause-button"
            type="button"
            aria-label="{isPlaying
                ? localize(/* @kp-localization mediaviewer.content.MultiMediaPause */ 'mediaviewer.content.MultiMediaPause')
                : localize(/* @kp-localization mediaviewer.content.MultiMediaPlay */ 'mediaviewer.content.MultiMediaPlay')}"
            class:paused={!isPlaying}
            on:click={isPlaying ? pause : play}>

        <UIcon icon="{isPlaying ? 'pause' : 'play'}"/>
    </button>

    {#if exists(audioPlayerElement)}
        <div class="volume-control-wrapper">
            <MvMediaPlayerVolumeControl mediaElement="{audioPlayerElement}"/>
        </div>

        <MvMediaPlayerProgressBar mediaElement="{audioPlayerElement}"/>

        <div class="speed-options-wrapper">
            <MvMediaPlayerSpeedOptions mediaElement="{audioPlayerElement}"/>
        </div>
    {/if}
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    @audio-player-height: 54px;
    @progress-bar-height: 8px;

    .mv-audio-player {
        width: 100%;
        max-width: 560px;
        height: @audio-player-height;
        border-radius: calc(@audio-player-height / 2);
        padding-right: @spacing-xl;
        display: flex;
        align-items: center;
        background-color: var(--viewer-default-button-color);
        border: 1px solid var(--viewer-default-border);
        transition: opacity 0.3s ease-in-out;

        &.light {
            box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.07);
        }

        .play-pause-button {
            width: @audio-player-height;
            height: 100%;
            outline: none;
            background-color: var(--accent-blue-new);
            border: rgba(0, 0, 0, 0.15);
            display: flex;
            cursor: pointer;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: calc(@audio-player-height / 2);
            color: white;
            scale: 0.9;
            transition: background-color 0.3s ease-in-out, opacity 0.3s ease-in-out, scale 0.3s ease-in-out;

            &.paused {
                background-color: var(--brand-orange-new);
            }

            &:hover {
                scale: 0.87;
                opacity: 0.75;
            }
        }

        .volume-control-wrapper {
            margin-left: @spacing-s;
            margin-right: @spacing-sm;
        }

        .speed-options-wrapper {
            margin-left: @spacing-sm;
        }
    }
</style>