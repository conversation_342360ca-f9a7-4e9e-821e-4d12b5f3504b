import type {Readable, Writable} from 'svelte/store';
import {get, writable} from 'svelte/store';
import type {ViewableFile} from 'typings/portaro.be.types';
import {getContext, setContext} from 'svelte';
import {viewableFileContainsViewForm} from './mv-utils';
import {FileViewForm} from 'shared/constants/portaro.constants';

const viewedContentContextKey = 'viewed-content-ctx';
const initialZoom = 1;
const viewportPadding = 16;

export type PdfRotation = 0 | 90 | 180 | 270;

export interface ViewedContentViewport {
    width: number;
    height: number;
}

export interface ViewedContentPageData {
    current: number;
    total: number;
    pagesShown: boolean;
}

export interface ViewedContentSearchResult {
    searchQuery: string;
    totalResults: number;
    currentResult: number;
}

export interface ViewedContentContext {
    eventBus: EventTarget;
    viewport: Readable<ViewedContentViewport>;
    viewportPadding: number;
    zoom: Readable<number>;
    currentPage: Readable<number>;
    pdfRotation: Readable<PdfRotation>;
    pageData: Readable<ViewedContentPageData | null>;
    showingTextualForm: Readable<boolean>;
    presentationMode: Readable<boolean>;
    canSwipeNavigate: Readable<boolean>;
    fullScreenImageGallery: Readable<boolean>;
    searchResult: Writable<ViewedContentSearchResult>;
    setViewportSize: (width: number, height: number) => void;
    zoomIn: () => void;
    zoomOut: () => void;
    setZoom: (value: number) => void;
    resetZoom: () => void;
    resetToDefaults: (file: ViewableFile) => void;
    setSearchResult: (result: ViewedContentSearchResult | null) => void;
    setPageData: (data: ViewedContentPageData | null) => void;
    setShowingTextualForm: (value: boolean) => void;
    setPresentationMode: (value: boolean) => void;
    setCanSwipeNavigate: (value: boolean) => void;
    setFullScreenImageGallery: (value: boolean) => void;
    rotatePdf: () => void;
}

export function createViewedContentContext(): ViewedContentContext {
    const eventBus = new EventTarget();
    const viewport = writable<ViewedContentViewport>({width: 0, height: 0});
    const zoom = writable<number>(initialZoom);
    const currentPage = writable<number>(1);
    const searchResult = writable<ViewedContentSearchResult | null>(null);
    const pageData = writable<ViewedContentPageData | null>(null);
    const showingTextualForm = writable<boolean>(false);
    const presentationMode = writable<boolean>(false);
    const canSwipeNavigate = writable<boolean>(false);
    const fullScreenImageGallery = writable<boolean>(false);
    const pdfRotation = writable<PdfRotation>(0);

    return setContext<ViewedContentContext>(viewedContentContextKey, {
        eventBus,
        viewport,
        viewportPadding,
        zoom,
        currentPage,
        searchResult,
        pageData,
        showingTextualForm,
        presentationMode,
        canSwipeNavigate,
        fullScreenImageGallery,
        pdfRotation,
        setViewportSize: (width: number, height: number) => viewport.set({
            width: width - viewportPadding * 2,
            height: height - viewportPadding * 2
        }),
        zoomIn: () => {
            const currentZoomValue = get(zoom);
            if (currentZoomValue < 10) {
                zoom.set(currentZoomValue + 0.2);
            }
        },
        zoomOut: () => {
            const currentZoomValue = get(zoom);
            if (currentZoomValue > 0.5) {
                zoom.set(currentZoomValue - 0.2);
            }
        },
        setZoom: (value: number) => {
            const clampedZoomValue = Math.min(10, Math.max(0.4, value));
            zoom.set(clampedZoomValue);
        },
        resetZoom: () => zoom.set(1),
        resetToDefaults: (file: ViewableFile) => {
            zoom.set(initialZoom);
            currentPage.set(1);
            searchResult.set(null);
            pageData.set(null);
            showingTextualForm.set(false);
            presentationMode.set(false);
            canSwipeNavigate.set(false);
            pdfRotation.set(0);

            if (!viewableFileContainsViewForm(file, FileViewForm.IMAGE)) {
                fullScreenImageGallery.set(false);
            }
        },
        setSearchResult: (result: ViewedContentSearchResult | null) => searchResult.set(result),
        setPageData: (data: ViewedContentPageData | null) => pageData.set(data),
        setShowingTextualForm: (value: boolean) => showingTextualForm.set(value),
        setPresentationMode: (value: boolean) => presentationMode.set(value),
        setCanSwipeNavigate: (value: boolean) => canSwipeNavigate.set(value),
        setFullScreenImageGallery: (value: boolean) => fullScreenImageGallery.set(value),
        rotatePdf: () => {
            const currentRotation = get(pdfRotation);
            const newRotation = (currentRotation + 90) % 360;
            pdfRotation.set(newRotation as PdfRotation);
        }
    });
}

export function getViewedContentContext(): ViewedContentContext {
    return getContext<ViewedContentContext>(viewedContentContextKey);
}