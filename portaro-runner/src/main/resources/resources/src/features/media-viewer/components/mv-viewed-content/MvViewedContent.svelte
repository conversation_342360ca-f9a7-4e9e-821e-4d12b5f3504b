<script lang="ts">
    import type {UUID, ViewableFile} from 'typings/portaro.be.types';
    import type {TouchSwipeEvent} from '../../actions/use.touch-swipe';
    import {touchSwipe} from '../../actions/use.touch-swipe';
    import {getMediaViewerContext} from '../../lib/mv-context';
    import {onDestroy, onMount, tick} from 'svelte';
    import {createViewedContentContext} from '../../lib/mv-viewed-content';
    import {fly} from 'svelte/transition';
    import {exists, uuid} from 'shared/utils/custom-utils';
    import {FileViewForm} from 'shared/constants/portaro.constants';
    import {pipe} from 'core/utils';
    import {byteFormatter} from 'shared/utils/pipes';
    import {viewableFileContainsViewForm} from '../../lib/mv-utils';
    import {pinchZoom} from '../../actions/use.pinch-zoom';
    import {isCurrentLayoutOrSmaller, MediaViewerLayoutSize, mediaViewerLayoutWidth} from '../../lib/mv-layout';
    import {get} from 'svelte/store';
    import {getLocalization} from 'core/svelte-context/context';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import MvTopBar from './mv-topbar/MvTopBar.svelte';
    import MvFloatingControls from './mv-floating-controls/MvFloatingControls.svelte';
    import MvImageFile from './mv-files-types/MvImageFile.svelte';
    import MvDirectoryOrFileIcon from '../mv-misc/MvDirectoryOrFileIcon.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import MvTextFile from './mv-files-types/MvTextFile.svelte';
    import MvVideoFile from './mv-files-types/MvVideoFile.svelte';
    import MvAudioFile from './mv-files-types/MvAudioFile.svelte';
    import MvPdfFile from './mv-files-types/MvPdfFile.svelte';
    import MvFullscreenImageGallery from './mv-fullscreen-image-gallery/MvFullscreenImageGallery.svelte';
    import {mediaViewerSidebarOpen, toggleMediaViewerSidebar} from '../../lib/mv-sidebar';
    import {FILES_UPLOAD_EVENT, NEXT_IMAGE_EVENT, PREVIOUS_IMAGE_EVENT} from '../../lib/mv-constants';

    const context = getMediaViewerContext();
    const viewedContent = createViewedContentContext();
    const flyInAnimParams = {y: 15, duration: 250};
    const supportedViewForms = [FileViewForm.IMAGE, FileViewForm.RICH_DOCUMENT, FileViewForm.PLAIN_TEXT, FileViewForm.VIDEO, FileViewForm.AUDIO, FileViewForm.PDF];
    const localize = getLocalization();

    let viewedFile: ViewableFile | null;
    let viewedFileChangeReactivityTriggerKey: UUID;
    let presentationMode = false;
    let canSwipeNavigate = false;
    let pinchZooming = false;
    let fullscreenImageGallery = false;
    let imageSwitchAnimationDirection = 0;
    let currentDirectoryId: number | null = null;
    let currentDirectoryImageFiles: ViewableFile[] = [];
    const presentationModeUnsubscribe = viewedContent.presentationMode.subscribe((currentPresentationMode) => presentationMode = currentPresentationMode);
    const canSwipeNavigateUnsubscribe = viewedContent.canSwipeNavigate.subscribe((currentCanSwipeNavigate) => canSwipeNavigate = currentCanSwipeNavigate);
    const fullscreenImageGalleryUnsubscribe = viewedContent.fullScreenImageGallery.subscribe((currentFullscreenImageGallery) => fullscreenImageGallery = currentFullscreenImageGallery);
    const viewedFileUnsubscribe = context.viewedFile.subscribe((currentViewedFile) => {
        viewedFileChangeReactivityTriggerKey = uuid();
        viewedFile = currentViewedFile;
        viewedContent.resetToDefaults(currentViewedFile);

        if (context.viewerType === 'media-viewer') {
            context.urlManagementService.resetToDefault();
            updateCurrentDirectoryImageNavigation(currentViewedFile);
        }

        setTimeout(() => {
            imageSwitchAnimationDirection = 0;
        }, 100);
    });

    let viewedContentElement: HTMLDivElement;
    let viewportSizeMounted = false;
    let viewportWidth = 0;
    let viewportHeight = 0;
    $: if (viewportSizeMounted) viewedContent.setViewportSize(viewportWidth, viewportHeight);

    $: if (!viewportSizeMounted && exists(viewedContentElement)) {
        const rect = viewedContentElement.getBoundingClientRect();
        viewportWidth = rect.width;
        viewportHeight = rect.height;
        viewportSizeMounted = true;
    }

    $: sidebarHidable = isCurrentLayoutOrSmaller(MediaViewerLayoutSize.TABLET, $mediaViewerLayoutWidth);
    $: mobileSidebarOpen = isCurrentLayoutOrSmaller(MediaViewerLayoutSize.TABLET, $mediaViewerLayoutWidth) && $mediaViewerSidebarOpen;

    onMount(() => {
        window.addEventListener('keydown', handleKeyDown);
        context.eventBus.addEventListener(FILES_UPLOAD_EVENT, handleFilesUploaded);
    });

    onDestroy(() => {
        window.removeEventListener('keydown', handleKeyDown);
        context.eventBus.removeEventListener(FILES_UPLOAD_EVENT, handleFilesUploaded);

        viewedFileUnsubscribe();
        presentationModeUnsubscribe();
        canSwipeNavigateUnsubscribe();
        fullscreenImageGalleryUnsubscribe();
    });

    function isSupportedViewForm(file: ViewableFile): boolean {
        const supported = file.viewForms.filter((form) => supportedViewForms.includes(form));
        return supported.length > 0;
    }

    const handleTouchSwipe = (event: TouchSwipeEvent) => {
        if (!canSwipeNavigate || pinchZooming) {
            return;
        }

        if (event.detail === 'right' && !$mediaViewerSidebarOpen) {
            toggleMediaViewerSidebar(true);
            return;
        }

        if (event.detail === 'up') {
            imageSwitchAnimationDirection = -1;
            viewedContent.eventBus.dispatchEvent(new CustomEvent(PREVIOUS_IMAGE_EVENT));
        }

        if (event.detail === 'down') {
            imageSwitchAnimationDirection = 1;
            viewedContent.eventBus.dispatchEvent(new CustomEvent(NEXT_IMAGE_EVENT));
        }
    };

    let startingZoomValue = 0;

    const handlePinchZoom = (event: CustomEvent<number>) => {
        viewedContent.setZoom(startingZoomValue * event.detail);
    };

    const handlePinchZoomStart = () => {
        startingZoomValue = get(viewedContent.zoom);
        pinchZooming = true;
    };

    const handlePinchZoomEnd = () => {
        tick().then(() => pinchZooming = false);
    };

    const handleKeyDown = (event: KeyboardEvent) => {
        if (fullscreenImageGallery || (event.key !== 'ArrowLeft' && event.key !== 'ArrowRight') || !viewableFileContainsViewForm(viewedFile, FileViewForm.IMAGE, FileViewForm.THUMBNAIL)) {
            return;
        }

        if (event.key === 'ArrowLeft') {
            viewedContent.eventBus.dispatchEvent(new CustomEvent(PREVIOUS_IMAGE_EVENT));
        }

        if (event.key === 'ArrowRight') {
            viewedContent.eventBus.dispatchEvent(new CustomEvent(NEXT_IMAGE_EVENT));
        }
    };

    const handleFilesUploaded = (event: CustomEvent<ViewableFile[]>) => {
        if (!exists(currentDirectoryId)) {
            return;
        }

        const uploadedCurrentDirImages = event.detail.filter((file) => file.directory?.id === currentDirectoryId && viewableFileContainsViewForm(file, FileViewForm.IMAGE));

        if (uploadedCurrentDirImages.length === 0) {
            return;
        }

        currentDirectoryId = null;
        updateCurrentDirectoryImageNavigation(viewedFile);
    };

    const handleFullscreenGalleryClose = () => {
        viewedContent.setFullScreenImageGallery(false);
    };

    function updateCurrentDirectoryImageNavigation(currentFile: ViewableFile) {
        if (!exists(currentFile)) {
            return;
        }

        if (!viewableFileContainsViewForm(currentFile, FileViewForm.IMAGE) || !exists(currentFile.directory)) {
            currentDirectoryId = null;
            currentDirectoryImageFiles = [];
            return;
        }

        if (viewableFileContainsViewForm(currentFile, FileViewForm.IMAGE) && exists(currentFile.directory)) {
            if (currentDirectoryId === currentFile.directory.id) {
                return;
            }

            currentDirectoryId = currentFile.directory.id;

            context.service.getFilesByDirectory(currentFile.directory.id).then((files) => {
                currentDirectoryImageFiles = files.filter((file) => viewableFileContainsViewForm(file, FileViewForm.IMAGE));
            });
        }
    }

    const handleFileDownloadClick = async (file: ViewableFile) => {
        await context.service.downloadFile(file);
    };
</script>

{#if context.viewerType === 'media-viewer'}
    <MvTopBar fileAccessAllowed="{exists(viewedFile) && viewedFile.showPermission.allowed}"/>
{/if}

{#key viewedFileChangeReactivityTriggerKey}
    <!-- svelte-ignore a11y-no-static-element-interactions -->
    <div class="mv-viewed-content"
         class:fullscreen-gallery-open={fullscreenImageGallery}
         class:sidebar-hidable={sidebarHidable}
         use:touchSwipe={{disabled: mobileSidebarOpen}}
         use:pinchZoom={{disabled: mobileSidebarOpen}}
         on:touch-swipe={handleTouchSwipe}
         on:pinch-zoom={handlePinchZoom}
         on:pinch-zoom-start={handlePinchZoomStart}
         on:pinch-zoom-end={handlePinchZoomEnd}
         in:fly={flyInAnimParams}
         bind:this={viewedContentElement}
         bind:clientWidth={viewportWidth}
         bind:clientHeight={viewportHeight}>

        {#if viewportSizeMounted}
            {#if !exists(viewedFile)}
                <div class="info-container">
                    <IconedContent orientation="vertical"
                                   align="center"
                                   icon="info"
                                   textAlign="center"
                                   iconColor="var(--brand-orange-new)">

                        {localize(/* @kp-localization mediaviewer.content.NoFileSelectedLabel */ 'mediaviewer.content.NoFileSelectedLabel')}
                    </IconedContent>
                </div>
            {:else if (!fullscreenImageGallery)}
                {#if isSupportedViewForm(viewedFile)}
                    {#if !viewedFile.showPermission.allowed}
                        <div class="unsupported-file-container no-view-permission">
                            <IconedContent icon="exclamation"
                                           orientation="vertical"
                                           align="center"
                                           justify="center"
                                           iconColor="var(--danger-red)">
                                {viewedFile.showPermission.reason.text}
                            </IconedContent>
                        </div>
                    {:else}
                        <div class="file-content-container">
                            {#if viewableFileContainsViewForm(viewedFile, FileViewForm.PDF)}
                                <MvPdfFile file="{viewedFile}"/>
                            {/if}

                            {#if viewableFileContainsViewForm(viewedFile, FileViewForm.IMAGE)}
                                <MvImageFile file="{viewedFile}"
                                             {imageSwitchAnimationDirection}
                                             {pinchZooming}
                                             {fullscreenImageGallery}/>
                            {/if}

                            {#if viewableFileContainsViewForm(viewedFile, FileViewForm.PLAIN_TEXT, FileViewForm.RICH_DOCUMENT)}
                                <MvTextFile file="{viewedFile}"/>
                            {/if}

                            {#if viewableFileContainsViewForm(viewedFile, FileViewForm.VIDEO)}
                                <MvVideoFile file="{viewedFile}"/>
                            {/if}

                            {#if viewableFileContainsViewForm(viewedFile, FileViewForm.AUDIO)}
                                <MvAudioFile file="{viewedFile}"/>
                            {/if}
                        </div>
                    {/if}
                {:else}
                    <div class="unsupported-file-container">
                        <div class="icon-container">
                            <MvDirectoryOrFileIcon directoryOrFile="{viewedFile}"/>
                        </div>

                        <div class="texts-container">
                            <p class="file-name">
                                {viewedFile.text}
                                <span class="file-size">{pipe(viewedFile.size, byteFormatter())}</span>
                            </p>

                            {localize(/* @kp-localization mediaviewer.content.UnsupportedFileType */ 'mediaviewer.content.UnsupportedFileType')}
                        </div>

                        {#if viewedFile.exportPermission.allowed}
                            <KpButton buttonStyle="accent-blue-new"
                                      on:click={() => handleFileDownloadClick(viewedFile)}>

                                <IconedContent icon="download">
                                    {localize(/* @kp-localization mediaviewer.content.DownloadFileBtnLabel */ 'mediaviewer.content.DownloadFileBtnLabel')}
                                </IconedContent>
                            </KpButton>
                        {/if}
                    </div>
                {/if}
            {/if}
        {/if}
    </div>
{/key}

{#if fullscreenImageGallery}
    <MvFullscreenImageGallery {currentDirectoryImageFiles}
                              {viewedFile}
                              on:close={handleFullscreenGalleryClose}/>
{/if}

{#if exists(viewedFile) && !presentationMode && !fullscreenImageGallery && viewedFile.showPermission.allowed}
    <div class="floating-controls-container" out:fly={{y: 30}}>
        <MvFloatingControls {currentDirectoryImageFiles}/>
    </div>
{/if}

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";
    @import (reference) "styles/portaro.variables.less";

    .mv-viewed-content {
        flex: 1 1 0;
        width: 100%;
        height: 100%;
        display: flex;
        overflow: hidden;
        flex-direction: column;
        border-top: 1px solid var(--viewer-default-border);
        border-left: 1px solid var(--viewer-default-border);
        border-top-left-radius: @border-radius-xxl;
        transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;

        &.sidebar-hidable {
            border-left: none;
            border-top-left-radius: 0;
        }

        &.fullscreen-gallery-open {
            opacity: 0;
            visibility: hidden;
        }

        .info-container,
        .unsupported-file-container {
            padding-bottom: 40px;
            max-width: 300px;
            display: flex;
            flex-direction: column;
            align-self: center;
            justify-self: center;
            align-items: center;
            justify-content: center;
            margin-top: auto;
            margin-bottom: auto;
        }

        .file-content-container {
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .unsupported-file-container {
            gap: @spacing-m;

            .icon-container {
                font-size: 18px;
                color: var(--brand-orange-new);
            }

            .texts-container {
                display: flex;
                align-items: center;
                text-align: center;
                flex-direction: column;
                gap: @spacing-xs;

                .file-name {
                    font-weight: 500;
                    margin: 0;

                    .file-size {
                        font-weight: 400;
                        opacity: 0.75;
                    }
                }
            }

            &.no-view-permission {
                color: var(--danger-red);

                .icon-container {
                    color: var(--danger-red);
                }

                .file-name {
                    color: var(--viewer-label-text-color);
                }
            }
        }
    }

    .floating-controls-container {
        position: absolute;
        bottom: @spacing-ml;
        left: 50%;
        z-index: 10;
        transform: translateX(-50%);
    }
</style>