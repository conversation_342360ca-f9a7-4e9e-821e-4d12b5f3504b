<script lang="ts">
    import MvOnboardingPage from '../MvOnboardingPage.svelte';
    import imgSrc from '../images/design.webp';
    import {getLocalization} from 'core/svelte-context/context';

    const localize = getLocalization();
</script>

<MvOnboardingPage>
    <img src="{imgSrc}"
         alt="New design"
         aria-labelledby="onboarding-new-design"/>

    <h2 id="onboarding-new-design">
        {localize(/* @kp-localization mediaviewer.onboarding.NewDesignTitle */ 'mediaviewer.onboarding.NewDesignTitle')}
    </h2>

    <span>
        {localize(/* @kp-localization mediaviewer.onboarding.NewDesignParagraph1 */ 'mediaviewer.onboarding.NewDesignParagraph1')}
    </span>

    <span>
        {localize(/* @kp-localization mediaviewer.onboarding.NewDesignParagraph2 */ 'mediaviewer.onboarding.NewDesignParagraph2')}
    </span>
</MvOnboardingPage>