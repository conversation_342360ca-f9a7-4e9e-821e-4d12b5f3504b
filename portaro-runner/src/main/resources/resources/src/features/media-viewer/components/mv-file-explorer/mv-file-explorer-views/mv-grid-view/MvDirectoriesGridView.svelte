<script lang="ts">
    import type {DirectoryNode} from 'typings/portaro.be.types';
    import {get} from 'svelte/store';
    import {exists, ignoreUnusedProperties} from 'shared/utils/custom-utils';
    import {onDestroy, onMount} from 'svelte';
    import {getMediaViewerContext} from '../../../../lib/mv-context';
    import {findDirectoryInTreeById} from '../../../../lib/mv-utils';
    import MvGridViewBreadcrumbs from './MvGridViewBreadcrumbs.svelte';
    import MvGridDirectoryContents from './MvGridDirectoryContents.svelte';

    const context = getMediaViewerContext();
    let selectedDirectory: DirectoryNode;
    const currentDirectoryUnsubscribe = context.fileExplorerSelectedDirectory.subscribe((currentDirectory) => selectedDirectory = currentDirectory);
    const urlStateSubscription = context.urlManagementService.getUrlState$().subscribe((state) => {
        const currentSelectedDirectory = get(context.fileExplorerSelectedDirectory);
        const currentViewedFile = get(context.viewedFile);

        if (exists(state.directory) && state.directory !== currentSelectedDirectory?.id?.toString()) {
            selectUrlDirectory(state.directory);
        }

        if (exists(state.file) && state.file !== currentViewedFile?.id?.toString()) {
            selectUrlFile(state.file);
        }
    });

    onMount(async () => {
        const state = await context.urlManagementService.getCurrentUrlState();

        if (exists(state.directory)) {
            selectUrlDirectory(state.directory);
        }
    });

    onDestroy(() => {
        urlStateSubscription.unsubscribe();
        currentDirectoryUnsubscribe();
    });

    function selectUrlDirectory(directoryIdString: string) {
        const directoryId = Number(directoryIdString);
        if (isNaN(directoryId)) {
            return;
        }

        const foundDirectory = findDirectoryInTreeById(context.rootDirectory, directoryId, []);
        if (!exists(foundDirectory)) {
            return;
        }

        context.setFileExplorerSelectedDirectory(foundDirectory);
    }

    function selectUrlFile(fileIdString: string) {
        ignoreUnusedProperties(fileIdString);
    }
</script>

<div class="mv-files-grid-view">
    <MvGridViewBreadcrumbs/>

    {#key selectedDirectory}
        {#if exists(selectedDirectory)}
            <MvGridDirectoryContents directoryNode="{selectedDirectory}"/>
        {/if}
    {/key}
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-files-grid-view {
        display: flex;
        flex-direction: column;
        gap: @spacing-m;
    }
</style>