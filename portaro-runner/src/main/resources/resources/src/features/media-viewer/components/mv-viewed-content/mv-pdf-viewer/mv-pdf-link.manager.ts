import type * as Pdfjs from 'pdfjs-dist/types/src/pdf';
import type {IPDFLinkService} from 'pdfjs-dist/types/web/pdf_link_service';
import type {ViewedContentContext} from 'src/features/media-viewer/lib/mv-viewed-content';
import {exists, ignoreUnusedProperties} from 'shared/utils/custom-utils';
import {get} from 'svelte/store';
import {isString} from 'shared/utils/string-utils';
import {isObject} from 'lodash-es';
import {GO_TO_PAGE_EVENT} from 'src/features/media-viewer/lib/mv-constants';

export class MvPdfLinkManager implements IPDFLinkService {

    private pageRefs: {[key: number]: any} = {};

    constructor(private viewedContentContext: ViewedContentContext,
                private pdfDocument: Pdfjs.PDFDocumentProxy) {
    }

    addLinkAttributes(link: HTMLAnchorElement, url: string, newWindow?: boolean): void {
        link.href = url;

        if (newWindow) {
            link.target = '_blank';
        }
    }

    cachePageRef(pageNum: number, pageRef: any): void {
        this.pageRefs[pageNum] = pageRef;
    }

    executeNamedAction(action: string) {
        switch (action) {
            case 'NextPage':
                this.setCurrentPageValue(this.getCurrentPage() + 1);
                break;
            case 'PrevPage':
                this.setCurrentPageValue(this.getCurrentPage() - 1);
                break;
            case 'FirstPage':
                this.setCurrentPageValue(1);
                break;
            case 'LastPage':
                this.setCurrentPageValue(this.pagesCount);
                break;
            default:
                // eslint-disable-next-line no-console
                console.warn(`Unsupported PDF named action: ${action}`);
                break;
        }
    }

    executeSetOCGState(action: any): void {
        ignoreUnusedProperties(action);
    }

    set externalLinkEnabled(value: boolean) {
        ignoreUnusedProperties(value);
    }

    getAnchorUrl(hash: any): string {
        return `#${hash}`;
    }

    getDestinationHash(dest: any): string {
        return isString(dest) ? dest : JSON.stringify(dest);
    }

    async goToDestination(dest: string | any[]): Promise<void> {
        if (isString(dest)) {
            const destination = await this.pdfDocument.getDestination(dest);
            await this.goToDestinationPage(destination);
            return;
        }

        await this.goToDestinationPage(dest);
    }

    goToPage(val: number | string): void {
        const pageNumber = isString(val) ? parseInt(val, 10) : val;
        this.setCurrentPageValue(pageNumber);
    }

    get isInPresentationMode(): boolean {
        return false;
    }

    set page(value: number) {
        this.setCurrentPageValue(value);
    }

    get pagesCount(): number {
        return this.pdfDocument.numPages;
    }

    set rotation(value: number) {
        ignoreUnusedProperties(value);
    }

    setHash(hash: string): void {
        ignoreUnusedProperties(hash);
    }

    private setCurrentPageValue(pageNumber: number) {
        const currentPageData = get(this.viewedContentContext.pageData);
        if (!exists(currentPageData)) {
            return;
        }

        this.viewedContentContext.setPageData({...currentPageData, current: pageNumber});
        this.viewedContentContext.eventBus.dispatchEvent(new CustomEvent<number>(GO_TO_PAGE_EVENT, {detail: pageNumber}));
    }

    private async goToDestinationPage(destination: any[]) {
        const refProxy = destination?.find((e) => isObject(e) && 'gen' in e && 'num' in e && exists(e.gen) && exists(e.num));

        if (!exists(refProxy)) {
            return;
        }

        const pageIndex = await this.pdfDocument.getPageIndex(refProxy);
        this.setCurrentPageValue(pageIndex + 1);
    }

    private getCurrentPage(): number {
        const currentPageData = get(this.viewedContentContext.pageData);
        return exists(currentPageData) ? currentPageData.current : 1;
    }
}