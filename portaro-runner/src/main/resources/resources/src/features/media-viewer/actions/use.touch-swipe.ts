import type {ActionReturn} from 'svelte/action';
import {exists, isTouchDevice} from 'shared/utils/custom-utils';

type TouchSwipeType = 'right' | 'left' | 'up' | 'down';
export type TouchSwipeEvent = CustomEvent<TouchSwipeType>;

const activationThresholdPx = 30;

interface Attributes {
    'on:touch-swipe': (e: CustomEvent<TouchSwipeType>) => void;
}

interface Parameters {
    disabled: boolean;
}

export const touchSwipe = (
    node: HTMLElement,
    params: Parameters = {disabled: false}
): ActionReturn<Parameters, Attributes> => {
    let xDown = null;
    let yDown = null;

    let isDisabled = params.disabled;

    const handleTouchStart = (event: TouchEvent) => {
        if (isDisabled || !isTouchDevice()) {
            return;
        }

        if (event.touches.length > 1) {
            return;
        }

        const firstTouch = event.touches[0];
        xDown = firstTouch.clientX;
        yDown = firstTouch.clientY;
    }

    const handleTouchMove = (event: TouchEvent) => {
        if (isDisabled || !isTouchDevice()) {
            return;
        }

        if (!exists(xDown) || !exists(yDown)) {
            return;
        }

        if (event.touches.length > 1) {
            return;
        }

        const xUp = event.touches[0].clientX;
        const yUp = event.touches[0].clientY;

        const xDiff = xDown - xUp;
        const yDiff = yDown - yUp;

        if (Math.abs(xDiff) < activationThresholdPx && Math.abs(yDiff) < activationThresholdPx) {
            return;
        }

        const swipeType = Math.abs(xDiff) > Math.abs(yDiff) ?
            (xDiff > 0 ? 'left' : 'right') :
            (yDiff > 0 ? 'down' : 'up');

        node.dispatchEvent(new CustomEvent<TouchSwipeType>('touch-swipe', {detail: swipeType}));

        xDown = null;
        yDown = null;
    }

    node.addEventListener('touchstart', handleTouchStart, false);
    document.addEventListener('touchmove', handleTouchMove, false);

    return {
        update(parameters) {
            isDisabled = parameters.disabled;
        },
        destroy() {
            node.removeEventListener('touchstart', handleTouchStart, false);
            document.removeEventListener('touchmove', handleTouchMove, false);
        }
    }
}