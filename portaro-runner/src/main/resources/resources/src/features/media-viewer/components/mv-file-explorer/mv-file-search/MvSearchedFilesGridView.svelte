<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import {mediaViewerSidebarWidth} from '../../../lib/mv-sidebar';
    import MvGridFile from '../mv-file-explorer-views/mv-grid-view/MvGridFile.svelte';
    import {isCurrentLayoutOrSmaller, MediaViewerLayoutSize, mediaViewerLayoutWidth} from '../../../lib/mv-layout';
    import {getGridViewColumnsCount} from '../../../lib/mv-utils';

    export let files: ViewableFile[];

    $: mobileSidebar = isCurrentLayoutOrSmaller(MediaViewerLayoutSize.TABLET, $mediaViewerLayoutWidth);
</script>

<div class="mv-searched-files-grid-view"
     style:--columns="{getGridViewColumnsCount($mediaViewerSidebarWidth, mobileSidebar)}">

    {#each files as file(file.id)}
        <MvGridFile {file}/>
    {/each}
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-searched-files-grid-view {
        display: grid;
        gap: @spacing-base;
        grid-template-columns: repeat(var(--columns), 1fr);
    }
</style>