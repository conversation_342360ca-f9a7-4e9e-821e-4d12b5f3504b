import type {<PERSON><PERSON><PERSON>, RawBodyMailSendRequest, ViewableFile} from 'typings/portaro.be.types';
import type {LocalizationService} from 'shared/providers/localization.provider';
import type {Searcher, SearcherFactory} from './mv-searcher.factory';
import type {FileService} from '../../file/file.service';
import type {DirectoryDataService} from 'shared/data-services/directory.data-service';
import type {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
import type {MailerService} from 'shared/mailer/mailer.service';
import type {SearchService} from '../../search/search.service';
import type {FileDownloadService} from 'core/data-services/file-download.service';
import type {FileContentDataService} from '../../file/file-content.data-service';
import type {ChunkedFileUploadService} from 'core/data-services/chunked-file-upload.service';
import type WalkerService from 'shared/services/walker.service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import {FileViewForm, Kind, SearchType} from 'shared/constants/portaro.constants';
import {exists, isFunction} from 'shared/utils/custom-utils';

export class MvFileExplorerService {
    public static serviceName = 'mediaViewerFileExplorerService';

    /*@ngInject*/
    constructor(private fileService: FileService,
                private fileDownloadService: FileDownloadService,
                private searchService: SearchService,
                private searcherFactory: SearcherFactory<ViewableFile>,
                private directoryDataService: DirectoryDataService,
                private walker: WalkerService,
                private toastMessageService: ToastMessageService,
                private mailerService: MailerService,
                private localizationService: LocalizationService,
                private fileContentDataService: FileContentDataService,
                private chunkedFileUploadService: ChunkedFileUploadService,
                private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    public createFileSearcher(rootDirectory: DirectoryNode): Searcher<ViewableFile> {
        return this.searcherFactory.create(
            async (query) => {
                const params = {
                    kind: [Kind.KIND_FILE],
                    type: SearchType.TYPE_VIEWABLE_FILE_SEARCH,
                    q: query,
                    pageSize: 50,
                    rootDirectory: rootDirectory.id
                };
                const searchResult = await this.searchService.search<ViewableFile>(params);
                return searchResult.result.content;
            });
    }

    public getFilesByDirectory(directoryId: number): Promise<ViewableFile[]> {
        return this.fileService.getFilesByDirectory(directoryId);
    }

    public getFileById(id: number): Promise<ViewableFile> {
        return this.fileService.getFileById(id);
    }

    public async createSubdirectory(currentDirectory: DirectoryNode): Promise<DirectoryNode | null> {
        try {
            const actionResponse = await this.directoryDataService.createSubdirectory(currentDirectory.id);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
            return {
                ...actionResponse.savedObject,
                children: []
            };
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            return null;
        }
    }

    public async editSelectedDirectory(currentDirectory: DirectoryNode): Promise<DirectoryNode | null> {
        try {
            const actionResponse = await this.directoryDataService.editDirectory(currentDirectory);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
            return {
                ...actionResponse.savedObject,
                children: currentDirectory.children
            };
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            return null;
        }
    }

    public async deleteSelectedDirectory(currentDirectory: DirectoryNode): Promise<boolean> {
        try {
            const actionResponse = await this.directoryDataService.deleteDirectory(currentDirectory);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
            return true;
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            return false;
        }
    }

    public async editFileMetadata(file: ViewableFile): Promise<ViewableFile | null> {
        try {
            const actionResponse = await this.fileService.editFileMetadata(file);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
            return actionResponse.savedObject;
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            return null;
        }
    }

    public async printFiles(files: ViewableFile[]): Promise<void> {
        const fileIdsUrlParam = files.map((file) => file.id).join(',');
        await this.walker.newPage('/export', {target: 'files-print:/html/files-print.vtl', object: fileIdsUrlParam});
    }

    public async printFile(file: ViewableFile): Promise<void> {
        if (file.viewForms.includes(FileViewForm.PDF)) {
            this.toastMessageService.showWarning('PDF soubory nezle tisknout přímo ze stránky. Stáhněte prosím tento soubor a vytiskněte pomocí programu.');
            return;
        }

        await this.walker.newPage('/export', {
            target: 'files-print:/html/files-print.vtl',
            object: file.id
        });
    }

    public async mailFiles(files: ViewableFile[]): Promise<void> {
        const request: RawBodyMailSendRequest = {
            recipientEmails: null,
            subject: /* @kp-localization @kp-localization mediaviewer.mail.Subject */ this.localizationService.get('mediaviewer.mail.Subject'),
            attachments: files,
            body: /* @kp-localization mediaviewer.mail.Body */ this.localizationService.get('mediaviewer.mail.Body')
        };
        return this.mailerService.sendMail(request);
    }

    public async deleteFiles(files: ViewableFile[]): Promise<boolean> {
        try {
            const promises = files.map((file) => this.fileService.deleteFile(file));
            await Promise.all(promises);
            this.toastMessageService.showSuccess('Soubory smazány');
            return true;
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            return false;
        }
    }

    public async createFile(parentDirectory: DirectoryNode): Promise<ViewableFile | null> {
        try {
            return await this.fileService.createNewFile(parentDirectory.id);
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            return null;
        }
    }

    public async uploadFiles(parentDirectory: DirectoryNode, files: File[], progressCallback?: (percentageValue: number) => void): Promise<ViewableFile[]> {
        const totalSize = files.reduce((acc, file) => acc + file.size, 0);
        const fileUploadProgresses = Array(files.length).fill(0);

        const calculateTotalProgressPercentage = () => {
            let uploadedSize = 0;

            fileUploadProgresses.forEach((progress, index) => {
                uploadedSize += files[index].size * (progress / 100);
            });

            const totalProgressPercentage = (uploadedSize / totalSize) * 100;

            if (isFunction(progressCallback)) {
                progressCallback(totalProgressPercentage);
            }
        };

        const uploadedFiles: ViewableFile[] = [];

        const uploadPromises = files.map((file) => {
            const chunkedFileUploader = this.chunkedFileUploadService.createUploader(
                file,
                parentDirectory,
                (progress) => {
                    const fileIndex = files.indexOf(file);
                    fileUploadProgresses[fileIndex] = progress;

                    calculateTotalProgressPercentage();
                },
                (viewableFile) => {
                    if (exists(viewableFile)) {
                        uploadedFiles.push(viewableFile);
                    }
                }
            );

            return chunkedFileUploader.startUploading();
        });

        await Promise.all(uploadPromises);

        return uploadedFiles;
    }

    public async copyUrlToClipboard(): Promise<void> {
        try {
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.delete('origin');

            await navigator.clipboard.writeText(currentUrl.toString());
            this.toastMessageService.showInfo('URL odkaz na soubor byl zkopírován do schránky!');
        } catch {
            this.toastMessageService.showError('Nepodařilo se zkopírovat URL do schránky.');
        }
    }

    public async getFileContent(file: ViewableFile, type: 'original' | 'thumbnail' | 'text' = 'original'): Promise<string> {
        try {
            return this.fileContentDataService.getFileContent(file, type);
        } catch {
            return '';
        }
    }

    public async downloadFile(file: ViewableFile): Promise<void> {
        try {
            await this.fileDownloadService
                .withoutBaseUrl()
                .createRequest(`files/${file.id}?inline=false`)
                .download(file.filename);
        } catch (e) {
            this.toastMessageService.showError(`Nepodařilo se stáhnout soubor: ${e.message}`);
        }
    }

    public async saveFileContents(file: ViewableFile, newContent: string): Promise<number | null> {
        try {
            const responseFile = await this.fileService.saveFileContent(file, newContent);
            this.toastMessageService.showSuccess('Změny souboru uloženy!');
            return responseFile.size;
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            return null;
        }
    }
}