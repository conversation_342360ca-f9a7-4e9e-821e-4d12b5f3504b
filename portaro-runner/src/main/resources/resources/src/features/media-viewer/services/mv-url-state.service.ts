import type {DirectoryNode, ViewableFile} from 'typings/portaro.be.types';
import type {IMvStateService, MediaViewerState} from '../lib/mv-types';
import type {UrlStateManagerService} from 'shared/state-manager/url-state-manager.service';
import type {Observable} from 'rxjs';
import {firstValueFrom} from 'rxjs';

export class MvUrlStateService implements IMvStateService {
    public static serviceName = 'mediaViewerUrlManagementService';

    /*@ngInject*/
    constructor(private urlStateManagerService: UrlStateManagerService<MediaViewerState>) {
    }

    public async getCurrentUrlState(): Promise<MediaViewerState> {
        const urlState$ = this.urlStateManagerService.getState$();
        return firstValueFrom(urlState$);
    }

    public getUrlState$(): Observable<MediaViewerState> {
        return this.urlStateManagerService.getState$();
    }

    public setSelectedDirectory(value: DirectoryNode) {
        this.urlStateManagerService.requestChangeStateParameter('directory', value.id.toString());
    }

    public setViewedFile(value: ViewableFile | null) {
        this.urlStateManagerService.requestChangeStateParameter('file', value?.id?.toString() ?? null);
    }

    public setSearchQuery(value: string | null) {
        this.urlStateManagerService.requestChangeStateParameter('q', value);
    }

    public setPageNumber(value: number | null) {
        this.urlStateManagerService.requestChangeStateParameter('page', value?.toString());
    }

    public resetToDefault(): void {
        this.setPageNumber(null);
    }
}