import type {ActionReturn} from 'svelte/action';
import {isTouchDevice} from 'shared/utils/custom-utils';

export interface MoveDraggingEvent extends CustomEvent {
    detail: MoveDraggingEventDetail;
}

interface Attributes {
    'on:dragging-start'?: (e: CustomEvent<void>) => void;
    'on:dragging-progress': (e: MoveDraggingEvent) => void;
    'on:dragging-end'?: (e: CustomEvent<void>) => void;
}

interface Parameters {
    disabled: boolean;
}

interface MoveDraggingEventDetail {
    x: number;
    y: number;
}

const isTouchSupportedDevice = () => isTouchDevice() || navigator.maxTouchPoints > 0;

export const moveDragging = (
    node: HTMLElement,
    params: { disabled: boolean } = {disabled: false}
): ActionReturn<Parameters, Attributes> => {
    let dragStartX: number | null = null;
    let dragStartY: number | null = null;

    let isDisabled = params.disabled;

    const startAction = (x: number, y: number) => {
        if (isDisabled) return;

        node.dispatchEvent(new CustomEvent<void>('dragging-start'));
        dragStartX = x;
        dragStartY = y;
    }

    const moveAction = (x: number, y: number) => {
        if (isDisabled) return;

        if (dragStartX !== null && dragStartY !== null) {
            const deltaX = x - dragStartX;
            const deltaY = y - dragStartY;
            dragStartX = x;
            dragStartY = y;

            node.dispatchEvent(new CustomEvent<MoveDraggingEventDetail>('dragging-progress', {
                detail: {
                    x: deltaX,
                    y: deltaY
                }
            }));
        }
    }

    const endAction = () => {
        dragStartX = null;
        dragStartY = null;
        node.dispatchEvent(new CustomEvent<void>('dragging-end'));
    }

    const mouseDownAction = (e: MouseEvent) => startAction(e.screenX, e.screenY);
    const mouseMoveAction = (e: MouseEvent) => moveAction(e.screenX, e.screenY);
    const mouseUpAction = () => endAction();

    const touchStartAction = (e: TouchEvent) => {
        const touch = e.touches[0];
        if (touch) startAction(touch.screenX, touch.screenY);
    };
    const touchMoveAction = (e: TouchEvent) => {
        const touch = e.touches[0];
        if (touch) moveAction(touch.screenX, touch.screenY);
    };
    const touchEndAction = () => endAction();

    if (isTouchSupportedDevice()) {
        node.addEventListener('touchstart', touchStartAction);
        document.addEventListener('touchmove', touchMoveAction);
        document.addEventListener('touchend', touchEndAction);
    } else {
        node.addEventListener('mousedown', mouseDownAction);
        document.addEventListener('mousemove', mouseMoveAction);
        document.addEventListener('mouseup', mouseUpAction);
    }

    return {
        update(parameters) {
            isDisabled = parameters.disabled;
        },
        destroy() {
            if (isTouchSupportedDevice()) {
                node.removeEventListener('touchstart', touchStartAction);
                document.removeEventListener('touchmove', touchMoveAction);
                document.removeEventListener('touchend', touchEndAction);
            } else {
                node.removeEventListener('mousedown', mouseDownAction);
                document.removeEventListener('mousemove', mouseMoveAction);
                document.removeEventListener('mouseup', mouseUpAction);
            }
        }
    }
}