import type {ActionReturn} from 'svelte/action';
import {isTouchDevice} from 'shared/utils/custom-utils';

interface Attributes {
    'on:pinch-zoom-start': (e: CustomEvent<void>) => void;
    'on:pinch-zoom': (e: CustomEvent<number>) => void;
    'on:pinch-zoom-end': (e: CustomEvent<void>) => void;
}

interface Parameters {
    disabled: boolean;
}

export const pinchZoom = (
    node: HTMLElement,
    params: Parameters = {disabled: false}
): ActionReturn<Parameters, Attributes> => {
    let initialDistance: number | null = null;
    let isDisabled = params.disabled;

    const getDistance = (touch1: Touch, touch2: Touch) => {
        return Math.sqrt(
            Math.pow(touch2.clientX - touch1.clientX, 2) +
            Math.pow(touch2.clientY - touch1.clientY, 2)
        );
    }

    const handleTouchStart = (event: TouchEvent) => {
        if (isDisabled || !isTouchDevice()) {
            return;
        }

        if (event.touches.length === 2) {
            initialDistance = getDistance(event.touches[0], event.touches[1]);
            node.dispatchEvent(new CustomEvent<void>('pinch-zoom-start'));
        }
    }

    const handleTouchMove = (event: TouchEvent) => {
        if (isDisabled || !isTouchDevice() || initialDistance === null) {
            return;
        }

        if (event.touches.length === 2) {
            const newDistance = getDistance(event.touches[0], event.touches[1]);
            const scale = newDistance / initialDistance;
            node.dispatchEvent(new CustomEvent<number>('pinch-zoom', {detail: scale}));
        }
    }

    const handleTouchEnd = (event: TouchEvent) => {
        if (isDisabled || !isTouchDevice()) {
            return;
        }

        if (initialDistance !== null && event.touches.length < 2) {
            initialDistance = null;
            node.dispatchEvent(new CustomEvent<void>('pinch-zoom-end'));
        }
    }

    node.addEventListener('touchstart', handleTouchStart, false);
    node.addEventListener('touchmove', handleTouchMove, false);
    node.addEventListener('touchend', handleTouchEnd, false);
    node.addEventListener('touchcancel', handleTouchEnd, false);

    return {
        update(parameters) {
            isDisabled = parameters.disabled;
        },
        destroy() {
            node.removeEventListener('touchstart', handleTouchStart, false);
            node.removeEventListener('touchmove', handleTouchMove, false);
            node.removeEventListener('touchend', handleTouchEnd, false);
            node.removeEventListener('touchcancel', handleTouchEnd, false);
        }
    }
}