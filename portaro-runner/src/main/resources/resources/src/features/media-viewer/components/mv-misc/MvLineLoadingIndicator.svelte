<script lang="ts">
    import KpLineLoadingIndicator from 'shared/components/kp-loading/KpLineLoadingIndicator.svelte';
</script>

<div class="mv-line-loading-indicator">
    <KpLineLoadingIndicator/>
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    :global {
        .mv-line-loading-indicator .kp-line-loading-indicator {
            background-color: var(--viewer-default-border);
        }
    }
</style>