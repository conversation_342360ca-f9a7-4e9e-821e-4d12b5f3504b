<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import type {PdfRotation, ViewedContentPageData} from '../../../lib/mv-viewed-content';
    import {getThemeContext} from 'shared/theme/portaro-theme';
    import {getMediaViewerContext} from '../../../lib/mv-context';
    import {onDestroy, onMount} from 'svelte';
    import {viewableFileContainsViewForm} from '../../../lib/mv-utils';
    import {getViewedContentContext} from '../../../lib/mv-viewed-content';
    import {FileViewForm} from 'shared/constants/portaro.constants';
    import {cleanup, exists} from 'shared/utils/custom-utils';
    import {slide} from 'svelte/transition';
    import {getLocalization} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import {strParams} from 'shared/utils/pipes';
    import MvTooltippedIconButton from '../../mv-misc/MvTooltippedIconButton.svelte';
    import MvControlsPageIndicator from './MvControlsPageIndicator.svelte';
    import {
        CONTENT_SCROLL_EVENT,
        GO_TO_PAGE_EVENT,
        NEXT_IMAGE_EVENT,
        PREVIOUS_IMAGE_EVENT,
        SCALE_TO_FIT_EVENT,
        TOGGLE_PDF_PRESENTATION_EVENT
    } from '../../../lib/mv-constants';
    import {
        isCurrentLayout,
        isCurrentLayoutOrBigger,
        MediaViewerLayoutSize,
        mediaViewerLayoutWidth
    } from '../../../lib/mv-layout';

    export let currentDirectoryImageFiles: ViewableFile[] = [];

    const currentTheme = getThemeContext().currentTheme;
    const context = getMediaViewerContext();
    const viewedContent = getViewedContentContext();
    const paginationButtonsSlideAnimParams = {axis: 'x', duration: 250} as const;
    const localize = getLocalization();

    let viewedFile: ViewableFile | null;
    let zoom = 1;
    let pdfRotation: PdfRotation = 0;
    let viewedContentScroll = 0;
    let lastScrollValue = 0;
    let pageData: ViewedContentPageData;
    let showingTextualForm = false;
    let mobileScrollHidden = false;
    let presentationMode = false;
    const zoomUnsubscribe = viewedContent.zoom.subscribe((currentZoom) => zoom = currentZoom);
    const presentationModeUnsubscribe = viewedContent.presentationMode.subscribe((currentPresentationMode) => presentationMode = currentPresentationMode);
    const pageDataUnsubscribe = viewedContent.pageData.subscribe((currentPageData) => pageData = currentPageData);
    const showingTextualFormUnsubscribe = viewedContent.showingTextualForm.subscribe((currentTextualForm) => showingTextualForm = currentTextualForm);
    const viewedFileUnsubscribe = context.viewedFile.subscribe((currentViewedFile) => viewedFile = currentViewedFile);
    const pdfRotationUnsubscribe = viewedContent.pdfRotation.subscribe((currentPdfRotation) => pdfRotation = currentPdfRotation);

    $: isPdf = viewableFileContainsViewForm(viewedFile, FileViewForm.PDF);
    $: isImage = viewableFileContainsViewForm(viewedFile, FileViewForm.IMAGE);

    onMount(() => {
        viewedContent.eventBus.addEventListener(CONTENT_SCROLL_EVENT, handleViewedContentScroll);
        viewedContent.eventBus.addEventListener(NEXT_IMAGE_EVENT, handleNextDirectoryImageClick);
        viewedContent.eventBus.addEventListener(PREVIOUS_IMAGE_EVENT, handlePreviousDirectoryImageClick);
    });

    onDestroy(() => {
        cleanup(viewedFileUnsubscribe, zoomUnsubscribe, pageDataUnsubscribe, showingTextualFormUnsubscribe, presentationModeUnsubscribe, pdfRotationUnsubscribe);

        viewedContent.eventBus.removeEventListener(CONTENT_SCROLL_EVENT, handleViewedContentScroll);
        viewedContent.eventBus.removeEventListener(NEXT_IMAGE_EVENT, handleNextDirectoryImageClick);
        viewedContent.eventBus.removeEventListener(PREVIOUS_IMAGE_EVENT, handlePreviousDirectoryImageClick);
    });

    const handleScaleToFitClick = () => {
        viewedContent.eventBus.dispatchEvent(new CustomEvent<void>(SCALE_TO_FIT_EVENT));
    };

    const handleNextPageClick = () => {
        const newPage = pageData.current + 1;
        if (newPage <= pageData.total) {
            updateCurrentPage(newPage);
        }
    };

    const handlePreviousPageClick = () => {
        const newPage = pageData.current - 1;
        if (newPage > 0) {
            updateCurrentPage(newPage);
        }
    };

    const handleFirstPageClick = () => {
        updateCurrentPage(1);
    };

    const handleLastPageClick = () => {
        updateCurrentPage(pageData.total);
    };

    const handleGoToPage = (event: CustomEvent<number>) => {
        const newPage = Math.max(1, Math.min(pageData.total, event.detail));
        updateCurrentPage(newPage);
    };

    const handleToggleTextualFormClick = () => {
        if (!viewedFile.hasTextualForm) {
            return;
        }

        viewedContent.setShowingTextualForm(!showingTextualForm);
    };

    const handleViewedContentScroll = (event: CustomEvent<number>) => {
        viewedContentScroll = event.detail;
        calculateFloatingControlsVisibility(viewedContentScroll);
    };

    const handleTogglePdfPresentationModeClick = () => {
        viewedContent.setPresentationMode(!presentationMode);
        viewedContent.eventBus.dispatchEvent(new CustomEvent<void>(TOGGLE_PDF_PRESENTATION_EVENT));
    };

    const handlePreviousDirectoryImageClick = () => {
        const currentFile = currentDirectoryImageFiles.find((file) => file.id === viewedFile.id);
        const previousImageIndex = currentDirectoryImageFiles.indexOf(currentFile) - 1;
        const previousImageFile = currentDirectoryImageFiles[previousImageIndex];

        if (exists(previousImageFile)) {
            context.setViewedFile(previousImageFile);
        }
    };

    const handleNextDirectoryImageClick = () => {
        const currentFile = currentDirectoryImageFiles.find((file) => file.id === viewedFile.id);
        const nextImageIndex = currentDirectoryImageFiles.indexOf(currentFile) + 1;
        const nextImageFile = currentDirectoryImageFiles[nextImageIndex];

        if (exists(nextImageFile)) {
            context.setViewedFile(nextImageFile);
        }
    };

    const handleFullscreenGalleryClick = () => {
        viewedContent.setFullScreenImageGallery(true);
    };

    const handleRotatePdfClick = () => {
        viewedContent.rotatePdf();
    };

    function hasPreviousDirectoryImage(directoryFiles: ViewableFile[], currentImage: ViewableFile): boolean {
        if (context.viewerType !== 'media-viewer' || directoryFiles.length < 2) {
            return false;
        }

        const currentFile = currentDirectoryImageFiles.find((file) => file.id === currentImage.id);
        const currentImageIndex = directoryFiles.indexOf(currentFile);
        return currentImageIndex > 0;
    }

    function hasNextDirectoryImage(directoryFiles: ViewableFile[], currentImage: ViewableFile): boolean {
        if (context.viewerType !== 'media-viewer' || directoryFiles.length < 2) {
            return false;
        }

        const currentFile = currentDirectoryImageFiles.find((file) => file.id === currentImage.id);
        const currentImageIndex = directoryFiles.indexOf(currentFile);
        return currentImageIndex !== directoryFiles.length - 1;
    }

    function updateCurrentPage(newPage: number) {
        viewedContent.setPageData({current: newPage, total: pageData.total, pagesShown: pageData.pagesShown});
        viewedContent.eventBus.dispatchEvent(new CustomEvent<number>(GO_TO_PAGE_EVENT, {detail: newPage}));
    }

    function calculateFloatingControlsVisibility(scroll: number) {
        mobileScrollHidden = scroll > lastScrollValue;
        lastScrollValue = scroll;
    }
</script>

{#if isImage || isPdf}
    {#if isCurrentLayout(MediaViewerLayoutSize.MOBILE, $mediaViewerLayoutWidth) && zoom !== 1 && context.viewerType === 'media-viewer'}
        {#key zoom}
            <span class="zoom-value">({zoom.toFixed(1)}×)</span>
        {/key}
    {/if}

    <div class="mv-floating-controls {$currentTheme}"
         class:scroll-hidden={viewedContentScroll > 0 && mobileScrollHidden && isCurrentLayout(MediaViewerLayoutSize.MOBILE, $mediaViewerLayoutWidth) && context.viewerType !== 'singular-file-viewer'}>

        {#if isImage && hasPreviousDirectoryImage(currentDirectoryImageFiles, viewedFile)}
            <MvTooltippedIconButton id="previous-page-control-btn"
                                    icon="angle-small-left"
                                    label="{localize(/* @kp-localization mediaviewer.content.ControlsPreviousImage */ 'mediaviewer.content.ControlsPreviousImage')}"
                                    on:click={handlePreviousDirectoryImageClick}/>
        {/if}

        <MvTooltippedIconButton id="zoom-in-control-btn"
                                icon="zoom-in"
                                label="{pipe(localize(/* @kp-localization mediaviewer.content.ControlsZoomIn */ 'mediaviewer.content.ControlsZoomIn'), strParams(zoom.toFixed(1)))}"
                                highlighted="{zoom > 1}"
                                on:click={() => viewedContent.zoomIn()}/>

        <MvTooltippedIconButton id="zoom-out-control-btn"
                                icon="zoom-out"
                                label="{pipe(localize(/* @kp-localization mediaviewer.content.ControlsZoomOut */ 'mediaviewer.content.ControlsZoomOut'), strParams(zoom.toFixed(1)))}"
                                highlighted="{zoom < 1}"
                                on:click={() => viewedContent.zoomOut()}/>

        <MvTooltippedIconButton id="expand-control-btn"
                                icon="expand"
                                label="{localize(/* @kp-localization mediaviewer.content.ControlsScaleToFit */ 'mediaviewer.content.ControlsScaleToFit')}"
                                on:click={handleScaleToFitClick}/>

        <MvTooltippedIconButton id="shrink-control-btn"
                                icon="compress-alt"
                                label="{localize(/* @kp-localization mediaviewer.content.ControlsOriginalSize */ 'mediaviewer.content.ControlsOriginalSize')}"
                                on:click={() => viewedContent.resetZoom()}/>

        {#if isImage && isCurrentLayoutOrBigger(MediaViewerLayoutSize.LAPTOP, $mediaViewerLayoutWidth)}
            <MvTooltippedIconButton id="fullscreen-gallery-btn"
                                    icon="screen"
                                    label="{localize(/* @kp-localization mediaviewer.content.ControlsFullscreenGallery */ 'mediaviewer.content.ControlsFullscreenGallery')}"
                                    on:click={handleFullscreenGalleryClick}/>
        {/if}

        {#if viewedFile.hasTextualForm && context.viewerType === 'media-viewer'}
            <MvTooltippedIconButton id="{showingTextualForm ? 'original' : 'text'}-version-control-btn"
                                    icon="{showingTextualForm ? 'file-image' : 'text'}"
                                    label="{showingTextualForm
                                        ? localize(/* @kp-localization mediaviewer.content.ControlsOriginalVersion */ 'mediaviewer.content.ControlsOriginalVersion')
                                        : localize(/* @kp-localization mediaviewer.content.ControlsTextVersion */ 'mediaviewer.content.ControlsTextVersion')}"
                                    on:click={handleToggleTextualFormClick}/>
        {/if}

        {#if isImage && hasNextDirectoryImage(currentDirectoryImageFiles, viewedFile)}
            <MvTooltippedIconButton id="next-page-control-btn"
                                    icon="angle-small-right"
                                    label="{localize(/* @kp-localization mediaviewer.content.ControlsNextImage */ 'mediaviewer.content.ControlsNextImage')}"
                                    on:click={handleNextDirectoryImageClick}/>
        {/if}

        {#if isPdf}
            {#if !isCurrentLayout(MediaViewerLayoutSize.MOBILE, $mediaViewerLayoutWidth) && context.viewerType === 'media-viewer'}
                <MvTooltippedIconButton id="display-in-browser-control-btn"
                                        icon="presentation"
                                        label="{presentationMode
                                            ? localize(/* @kp-localization mediaviewer.content.ControlsStopPresentationMode */ 'mediaviewer.content.ControlsStopPresentationMode')
                                            : localize(/* @kp-localization mediaviewer.content.ControlsPresentationMode */ 'mediaviewer.content.ControlsPresentationMode')}"
                                        highlighted="{presentationMode}"
                                        on:click={handleTogglePdfPresentationModeClick}/>
            {/if}

            <MvTooltippedIconButton id="rotate-pdf-control-btn"
                                    icon="rotate-right"
                                    highlighted="{pdfRotation !== 0}"
                                    label="{localize(/* @kp-localization mediaviewer.content.ControlsRotatePdf */ 'mediaviewer.content.ControlsRotatePdf')} ({pdfRotation}°)"
                                    on:click={handleRotatePdfClick}/>

            {#if exists(pageData) && !isCurrentLayout(MediaViewerLayoutSize.MOBILE, $mediaViewerLayoutWidth)}
                <div class="slideable-buttons-container" transition:slide={paginationButtonsSlideAnimParams}>
                    <MvTooltippedIconButton id="first-page-control-btn"
                                            icon="angle-double-small-left"
                                            label="{localize(/* @kp-localization mediaviewer.content.ControlsFirstPage */ 'mediaviewer.content.ControlsFirstPage')}"
                                            on:click={handleFirstPageClick}/>

                    <MvTooltippedIconButton id="previous-page-control-btn"
                                            icon="angle-small-left"
                                            label="{localize(/* @kp-localization mediaviewer.content.ControlsPreviousPage */ 'mediaviewer.content.ControlsPreviousPage')}"
                                            on:click={handlePreviousPageClick}/>

                    <MvControlsPageIndicator {pageData} on:go-to-page={handleGoToPage}/>

                    <MvTooltippedIconButton id="next-page-control-btn"
                                            icon="angle-small-right"
                                            label="{localize(/* @kp-localization mediaviewer.content.ControlsNextPage */ 'mediaviewer.content.ControlsNextPage')}"
                                            on:click={handleNextPageClick}/>

                    <MvTooltippedIconButton id="last-page-control-btn"
                                            icon="angle-double-small-right"
                                            label="{localize(/* @kp-localization mediaviewer.content.ControlsLastPage */ 'mediaviewer.content.ControlsLastPage')}"
                                            on:click={handleLastPageClick}/>
                </div>
            {/if}
        {/if}
    </div>
{/if}

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-floating-controls {
        height: @media-viewer-floating-controls-height;
        display: flex;
        align-items: center;
        border-radius: @border-radius-default;
        background-color: var(--viewer-default-button-color);
        outline: 1px solid var(--viewer-default-border);
        animation: 0.4s ease-in-out 0s 1 floating-controls-slide-from-bottom;
        transition: transform 0.4s ease-in-out, opacity 0.4s ease-in-out;

        &.scroll-hidden {
            transform: translateY(100px);
            opacity: 0.5;
        }

        &.light {
            box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08);
        }

        .slideable-buttons-container {
            display: flex;
            align-items: center;
            height: 100%;
        }
    }

    .zoom-value {
        position: absolute;
        bottom: calc(100% + @spacing-s);
        left: 50%;
        transform: translateX(-50%);
        color: var(--viewer-label-text-color);
        animation: 0.6s ease-in-out 0s 1 zoom-value-fade-out forwards;
    }

    :global {
        .mv-floating-controls {
            .mv-tooltipped-icon-button:first-child {
                border-top-left-radius: @border-radius-default;
                border-bottom-left-radius: @border-radius-default;
            }

            & > .mv-tooltipped-icon-button:last-child,
            & > .slideable-buttons-container:last-child > .mv-tooltipped-icon-button:last-child {
                border-right: none;
                border-top-right-radius: @border-radius-default;
                border-bottom-right-radius: @border-radius-default;
            }
        }
    }

    @keyframes floating-controls-slide-from-bottom {
        0% {
            transform: translateY(100%);
            opacity: 0;
        }
        100% {
            transform: translateX(0);
            opacity: 100%;
        }
    }

    @keyframes zoom-value-fade-out {
        0%, 50% {
            opacity: 100%;
        }
        100% {
            opacity: 0;
        }
    }
</style>