<script lang="ts" context="module">
    import MvTooltip from './MvTooltip.svelte';

    export const meta = {
        title: 'MediaViewer/MvTooltip',
        component: MvTooltip
    };

    const defaultArgs = {
        text: 'Testovací label',
        arrowDirection: 'right'
    }
</script>

<script>
    import {Story, Template} from '@storybook/addon-svelte-csf';
    import MvStoryContainer from '../MvStoryContainer.svelte';
</script>

<Template let:args>
    <MvStoryContainer>
        <MvTooltip id="test-tooltip" text="{args.text}" arrowDirection="{args.arrowDirection}"/>
    </MvStoryContainer>
</Template>

<Story name="Default" args="{defaultArgs}"/>