<script lang="ts">
    import type {Px} from 'shared/ui-widgets/types';
    import type {UIcons} from 'shared/ui-widgets/uicons/types';
    import type {TooltipArrowDirection} from '../../lib/mv-types';
    import MvTooltip from './mv-tooltip/MvTooltip.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let id: string;
    export let icon: UIcons;
    export let label: string;
    export let tooltipArrowDirection: TooltipArrowDirection = 'bottom';
    export let tooltipLocation: TooltipArrowDirection = 'top';
    export let customSize: Px | null = null;
    export let highlighted = false;
    export let disabled = false;
</script>

<button id="{id}"
        style="{customSize ? `--size: ${customSize}` : ''}"
        class="mv-tooltipped-icon-button"
        aria-describedby="{id}-tooltip"
        disabled="{disabled}"
        class:highlighted={highlighted}
        on:click>

    <span class="icon-container">
        <IconedContent icon="{icon}"/>
    </span>

    <span class="tooltip-container {tooltipLocation}">
        <MvTooltip id="{id}-tooltip" text="{label}" arrowDirection="{tooltipArrowDirection}"/>
    </span>
</button>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-tooltipped-icon-button {
        position: relative;
        width: var(--size, @media-viewer-floating-controls-height);
        height: var(--size, @media-viewer-floating-controls-height);
        background-color: var(--viewer-default-button-color);
        display: flex;
        align-items: center;
        flex-shrink: 0;
        justify-content: center;
        border: none;
        border-right: 1px solid var(--viewer-default-border);
        padding-bottom: 1px;
        transition: background-color 0.3s ease-in-out, opacity 0.3s ease-in-out;

        &:not(:disabled):hover {
            @media (hover: hover) {
                .icon-container {
                    transform: translateY(-2px);
                }
            }
        }

        &:not(:disabled):focus-visible {
            border-right: none;
            // Otherwise, if not using z-index, the focus outline would be obscured by the next element
            z-index: 1;
        }

        &:not(:disabled):hover,
        &:not(:disabled):focus-visible {
            @media (hover: hover) {
                background-color: var(--viewer-blue-highlight);

                .tooltip-container {
                    opacity: 1;
                    visibility: visible;

                    &.top {
                        transform: translate(-50%, calc(@spacing-sm * -1));
                    }

                    &.bottom {
                        transform: translate(-50%, @spacing-sm);
                    }

                    &.left {
                        transform: translate(calc(@spacing-sm * -1), -50%);
                    }

                    &.right {
                        transform: translate(@spacing-sm, -50%);
                    }
                }
            }
        }

        &:disabled {
            opacity: 0.6;
        }

        &.highlighted {
            background-color: var(--viewer-orange-highlight-transparent);

            .icon-container {
                color: var(--brand-orange-new);
            }
        }

        .icon-container {
            transition: transform 0.3s ease-in-out, color 0.3s ease-in-out;
        }

        .tooltip-container {
            position: absolute;
            opacity: 0;
            visibility: hidden;
            transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;

            &.top {
                transform: translate(-50%, calc(@spacing-s * -1));
                bottom: 100%;
                left: 50%;
            }

            &.bottom {
                transform: translate(-50%, @spacing-s);
                top: 100%;
                left: 50%;
            }

            &.left {
                transform: translate(calc(@spacing-s * -1), -50%);
                top: 50%;
                right: 100%;
            }

            &.right {
                transform: translate(@spacing-s, -50%);
                top: 50%;
                left: 100%;
            }
        }
    }
</style>