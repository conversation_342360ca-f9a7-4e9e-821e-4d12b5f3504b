<script lang="ts">
    import type {DirectoryNode} from 'typings/portaro.be.types';
    import Kp<PERSON>utton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import {getMediaViewerContext} from '../../../lib/mv-context';
    import {onDestroy} from 'svelte';
    import {getLocalization} from 'core/svelte-context/context';
    import {
        handleDirectoryCreateClick,
        handleDirectoryCreateTextFileClick,
        handleDirectoryDeleteClick,
        handleDirectoryEditMetadataClick,
        handleDirectoryUploadLocalFilesClick
    } from '../../../lib/mv-file-explorer-handlers';

    const context = getMediaViewerContext();
    const localize = getLocalization();

    let fileExplorerSelected: DirectoryNode;
    const fileExplorerSelectedUnsubscribe = context.fileExplorerSelectedDirectory.subscribe((currentFileExplorerSelected) => fileExplorerSelected = currentFileExplorerSelected);

    onDestroy(() => {
        fileExplorerSelectedUnsubscribe();
    });
</script>

{#if fileExplorerSelected && context.canEdit}
    <KpButton isBlock
              buttonStyle="accent-blue-new"
              on:click={() => handleDirectoryUploadLocalFilesClick(context, fileExplorerSelected)}>

        <IconedContent icon="cloud-upload">
            {localize(/* @kp-localization mediaviewer.fileExplorer.UploadDirectoryFiles */ 'mediaviewer.fileExplorer.UploadDirectoryFiles')}
        </IconedContent>
    </KpButton>

    <KpButton isBlock
              buttonStyle="default-viewer-themed"
              on:click={() => handleDirectoryCreateTextFileClick(context, fileExplorerSelected)}>

        <IconedContent icon="add-document">
            {localize(/* @kp-localization mediaviewer.fileExplorer.CreateNewTextFile */ 'mediaviewer.fileExplorer.CreateNewTextFile')}
        </IconedContent>
    </KpButton>

    <KpButton isBlock
              buttonStyle="default-viewer-themed"
              on:click={() => handleDirectoryCreateClick(context, fileExplorerSelected)}>

        <IconedContent icon="add-folder">
            {localize(/* @kp-localization mediaviewer.fileExplorer.CreateNewSubdirectory */ 'mediaviewer.fileExplorer.CreateNewSubdirectory')}
        </IconedContent>
    </KpButton>

    <KpButton isBlock
              buttonStyle="default-viewer-themed"
              isDisabled="{fileExplorerSelected?.id === context.rootDirectory.id}"
              on:click={() => handleDirectoryEditMetadataClick(context, fileExplorerSelected)}>

        <IconedContent icon="edit">
            {localize(/* @kp-localization mediaviewer.fileExplorer.EditSelectedDirectory */ 'mediaviewer.fileExplorer.EditSelectedDirectory')}
        </IconedContent>
    </KpButton>

    <KpButton isBlock
              buttonStyle="danger-new"
              isDisabled="{fileExplorerSelected?.id === context.rootDirectory.id}"
              on:click={() => handleDirectoryDeleteClick(context, fileExplorerSelected)}>

        <IconedContent icon="remove-folder">
            {localize(/* @kp-localization mediaviewer.fileExplorer.DeleteSelectedDirectory */ 'mediaviewer.fileExplorer.DeleteSelectedDirectory')}
        </IconedContent>
    </KpButton>
{/if}