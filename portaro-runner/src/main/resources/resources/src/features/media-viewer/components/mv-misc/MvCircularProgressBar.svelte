<script lang="ts">
    export let progressPercentage: number;
</script>

<div class="progress-circle">
    <svg viewBox="0 0 36 36">
        <circle class="background" cx="18" cy="18" r="16"/>
        <circle class="foreground"
                cx="18"
                cy="18"
                r="16"
                stroke-dasharray="{progressPercentage}, 100"/>
    </svg>

    <div class="progress-text">{progressPercentage}%</div>
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .progress-circle {
        position: relative;
        width: 48px;
        height: 48px;

        svg {
            transform: rotate(-90deg);
            width: 100%;
            height: 100%;
        }

        circle {
            fill: none;
            stroke-width: 2.5;
        }

        .background {
            opacity: 0.75;
            stroke: var(--viewer-default-border);
        }

        .foreground {
            stroke-width: 2.6;
            stroke: var(--accent-blue-new);
        }
    }

    .progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 11px;
        opacity: 0.6;
        color: var(--viewer-default-text-color);
    }
</style>