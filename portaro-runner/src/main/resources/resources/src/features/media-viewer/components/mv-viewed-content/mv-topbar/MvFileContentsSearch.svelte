<script lang="ts">
    import type {ViewedContentSearchResult} from '../../../lib/mv-viewed-content';
    import {getThemeContext} from 'shared/theme/portaro-theme';
    import {createEventDispatcher, onDestroy, onMount} from 'svelte';
    import {getViewedContentContext} from '../../../lib/mv-viewed-content';
    import {exists} from 'shared/utils/custom-utils';
    import {slide} from 'svelte/transition';
    import {getLocalization} from 'core/svelte-context/context';
    import MvTooltippedIconButton from '../../mv-misc/MvTooltippedIconButton.svelte';
    import {
        SEARCH_IN_FILE_EVENT,
        SEARCH_SHOW_NEXT_RESULT_EVENT,
        SEARCH_SHOW_PREVIOUS_RESULT_EVENT
    } from '../../../lib/mv-constants';
    import {pipe} from 'core/utils';
    import {strParams} from 'shared/utils/pipes';

    const currentTheme = getThemeContext().currentTheme;
    const viewedContent = getViewedContentContext();
    const dispatch = createEventDispatcher<{ 'close': void }>();
    const localize = getLocalization();
    const resultsSlideAnimParams = {duration: 250};
    let searchInput = '';

    let searchResult: ViewedContentSearchResult | null;
    const searchResultUnsubscribe = viewedContent.searchResult.subscribe((currentSearchResult) => {
        if (!exists(currentSearchResult)) {
            dispatch('close');
            return;
        }

        searchResult = currentSearchResult;
    });

    onMount(() => {
        viewedContent.setSearchResult(null);
        window.addEventListener('keydown', handleEscClose);
    });

    onDestroy(() => {
        window.removeEventListener('keydown', handleEscClose);
        searchResultUnsubscribe();
    });

    const handleEscClose = (event: KeyboardEvent) => {
        if (event.key !== 'Escape') {
            return;
        }

        dispatch('close');
    }

    const handleSearchClick = () => {
        if (!searchInput) {
            return;
        }

        viewedContent.eventBus.dispatchEvent(new CustomEvent<string>(SEARCH_IN_FILE_EVENT, {detail: searchInput}));
    }

    const handleNextResultClick = () => {
        viewedContent.eventBus.dispatchEvent(new CustomEvent<void>(SEARCH_SHOW_NEXT_RESULT_EVENT));
    }

    const handlePreviousResultClick = () => {
        viewedContent.eventBus.dispatchEvent(new CustomEvent<void>(SEARCH_SHOW_PREVIOUS_RESULT_EVENT));
    }
</script>

<div class="mv-file-contents-search {$currentTheme}">
    <div class="search-input-bar">
        <form on:submit|preventDefault={handleSearchClick}>
            <!-- svelte-ignore a11y-autofocus -->
            <input autofocus
                   type="text"
                   placeholder="{localize(/* @kp-localization mediaviewer.content.ContentSearchInputPlaceholder */ 'mediaviewer.content.ContentSearchInputPlaceholder')}"
                   bind:value={searchInput}/>
        </form>

        <div class="action-buttons-container">
            <MvTooltippedIconButton id="email-action-btn"
                                    tooltipArrowDirection="top"
                                    tooltipLocation="bottom"
                                    icon="search"
                                    label="{localize(/* @kp-localization mediaviewer.content.ContentSearchSearch */ 'mediaviewer.content.ContentSearchSearch')}"
                                    customSize="34px"
                                    disabled="{!searchInput}"
                                    on:click={handleSearchClick}/>

            <MvTooltippedIconButton id="next-search-result-btn"
                                    tooltipArrowDirection="top"
                                    tooltipLocation="bottom"
                                    icon="arrow-small-down"
                                    label="{localize(/* @kp-localization mediaviewer.content.ContentSearchNextResult */ 'mediaviewer.content.ContentSearchNextResult')}"
                                    customSize="34px"
                                    disabled="{!exists(searchResult) || searchResult.totalResults - 1 < searchResult.currentResult}"
                                    on:click={handleNextResultClick}/>

            <MvTooltippedIconButton id="previous-search-result-btn"
                                    tooltipArrowDirection="top"
                                    tooltipLocation="bottom"
                                    icon="arrow-small-up"
                                    label="{localize(/* @kp-localization mediaviewer.content.ContentSearchPreviousResult */ 'mediaviewer.content.ContentSearchPreviousResult')}"
                                    customSize="34px"
                                    disabled="{!exists(searchResult) || searchResult.currentResult <= 1}"
                                    on:click={handlePreviousResultClick}/>
        </div>
    </div>

    {#if searchResult}
        <span class="results" transition:slide={resultsSlideAnimParams}>
            {pipe(localize(/* @kp-localization mediaviewer.content.ContentSearchResultsLabel */ 'mediaviewer.content.ContentSearchResultsLabel'), strParams(searchResult.totalResults.toString()))}

            {#if searchResult.totalResults > 0}
                {pipe(localize(/* @kp-localization mediaviewer.content.ContentSearchHighlightedLabel */ 'mediaviewer.content.ContentSearchHighlightedLabel'), strParams(searchResult.currentResult.toString()))}
            {/if}
        </span>
    {/if}
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-file-contents-search {
        width: 100%;
        border: 1px solid var(--viewer-default-border);
        display: flex;
        flex-direction: column;
        padding: @spacing-m @spacing-l;
        border-radius: @border-radius-default;
        background-color: var(--viewer-bg);

        &.light {
            box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.07);
        }

        .search-input-bar {
            display: flex;
            align-items: center;

            form {
                flex: 1;

                input {
                    width: 100%;
                    height: 34px;
                    background-color: var(--viewer-default-button-color);
                    border: none;
                    padding: 0 @spacing-m;
                    border-bottom-left-radius: @border-radius-default;
                    border-top-left-radius: @border-radius-default;
                    border-right: 1px solid transparent;
                    outline: 1px solid var(--viewer-default-border);
                    transition: outline-color 0.3s ease-in-out, border-right-color 0.3s ease-in-out;

                    &:active,
                    &:focus {
                        outline-color: var(--accent-blue-new);
                        border-right-color: var(--accent-blue-new);
                    }
                }
            }

            .action-buttons-container {
                display: flex;
                align-items: center;
                transform: translateX(1px);
                outline: 1px solid var(--viewer-default-border);
                border-bottom-right-radius: @border-radius-default;
                border-top-right-radius: @border-radius-default;
            }
        }

        .results {
            margin-top: @spacing-s;
        }
    }

    :global {
        .mv-file-contents-search .search-input-bar .action-buttons-container > .mv-tooltipped-icon-button:last-child {
            border-right: none;
            border-top-right-radius: @border-radius-default;
            border-bottom-right-radius: @border-radius-default;
        }
    }
</style>