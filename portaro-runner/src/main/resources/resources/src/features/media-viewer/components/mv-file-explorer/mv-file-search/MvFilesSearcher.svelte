<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import type {FileExplorerViewType} from '../../../lib/mv-types';
    import {onDestroy, onMount} from 'svelte';
    import {fade} from 'svelte/transition';
    import {getMediaViewerContext} from '../../../lib/mv-context';
    import {replaceAll, byId} from 'shared/utils/array-utils';
    import {exists} from 'shared/utils/custom-utils';
    import {get} from 'svelte/store';
    import {getLocalization} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import {strParams} from 'shared/utils/pipes';
    import {FILES_DELETE_EVENT, FILE_METADATA_EDIT_EVENT} from '../../../lib/mv-constants';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import MvFilesColumnView from '../mv-file-explorer-views/mv-column-view/MvFilesColumnView.svelte';
    import MvSearchedFilesGridView from './MvSearchedFilesGridView.svelte';

    export let searchQuery: string;

    const context = getMediaViewerContext();
    const localize = getLocalization();
    const searcher = context.service.createFileSearcher(context.rootDirectory);
    const loadingInFadeAnimParams = {duration: 250};
    const viewTypeInAnimParams = {duration: 250};

    let fileExplorerViewType: FileExplorerViewType;
    const fileExplorerViewTypeUnsubscribe = context.fileExplorerViewType.subscribe((currentViewType) => fileExplorerViewType = currentViewType);

    let loading = true;
    let foundFiles: ViewableFile[];

    let beforeSearchViewType: FileExplorerViewType;

    onMount(async () => {
        context.eventBus.addEventListener(FILE_METADATA_EDIT_EVENT, handleFileMetadataEdit);
        context.eventBus.addEventListener(FILES_DELETE_EVENT, handleFilesDelete);

        context.setViewedFile(null);
        context.setSelectingFiles(false);
        beforeSearchViewType = get(context.fileExplorerViewType);
        context.setFileExplorerViewType('grid');

        context.setSearchingForResults(true);
        foundFiles = await searcher.search(searchQuery);
        loading = false;
        context.setSearchingForResults(false);

        if (foundFiles.length > 0) {
            context.setViewedFile(foundFiles[0]);
        }
    });

    onDestroy(() => {
        context.eventBus.removeEventListener(FILE_METADATA_EDIT_EVENT, handleFileMetadataEdit);
        context.eventBus.removeEventListener(FILES_DELETE_EVENT, handleFilesDelete);

        fileExplorerViewTypeUnsubscribe();

        if (exists(beforeSearchViewType)) {
            context.setFileExplorerViewType(beforeSearchViewType);
        }
    });

    const handleFilesDelete = (event: CustomEvent<ViewableFile[]>) => {
        foundFiles = foundFiles.filter((file) => !exists(event.detail.find((currentDirFile) => file.id === currentDirFile.id)));
    }

    const handleFileMetadataEdit = (event: CustomEvent<ViewableFile>) => {
        foundFiles = replaceAll(foundFiles, byId(event.detail.id), event.detail);
    }
</script>

<div class="mv-search-result-files">
    {#if loading}
        <div in:fade={loadingInFadeAnimParams} class="centered-full-size-container">
            <KpLoadingBlock size="sm"/>
        </div>
    {/if}

    {#if !loading}
        <div class="loaded-container" in:fade>
            <div class="divider"></div>

            <span class="search-label">
                {pipe(localize(/* @kp-localization mediaviewer.fileExplorer.SearchResultsInfo */ 'mediaviewer.fileExplorer.SearchResultsInfo'), strParams(searchQuery, context.rootDirectory.text, foundFiles.length.toString()))}
            </span>

            {#if foundFiles.length === 0}
                <div class="centered-full-size-container">
                    <IconedContent orientation="vertical"
                                   icon="search"
                                   iconColor="var(--brand-orange-new)">

                        {localize(/* @kp-localization mediaviewer.fileExplorer.NoSearchResults */ 'mediaviewer.fileExplorer.NoSearchResults')}
                    </IconedContent>
                </div>
            {/if}

            {#if foundFiles.length > 0}
                {#if fileExplorerViewType === 'grid'}
                    <div in:fade={viewTypeInAnimParams}>
                        <MvSearchedFilesGridView files="{foundFiles}"/>
                    </div>
                {/if}

                {#if fileExplorerViewType === 'column'}
                    <div in:fade={viewTypeInAnimParams}>
                        <MvFilesColumnView files="{foundFiles}"/>
                    </div>
                {/if}
            {/if}
        </div>
    {/if}
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-search-result-files {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: @spacing-ml;

        .search-label {
            text-align: center;
        }

        .centered-full-size-container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .loaded-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            gap: @spacing-ml;

            .divider {
                width: 100%;
                border-bottom: 1px solid var(--viewer-default-border);
                margin: @spacing-s 0;
            }
        }
    }
</style>