<script lang="ts">
    import MvTooltip from '../../mv-misc/mv-tooltip/MvTooltip.svelte';
    import {fly} from 'svelte/transition';
    import {exists, isTouchDevice} from 'shared/utils/custom-utils';
    import {onDestroy, onMount} from 'svelte';
    import {getLocalization} from 'core/svelte-context/context';

    interface BufferedTimeRange {
        start: number;
        duration: number;
    }

    export let mediaElement: HTMLMediaElement;

    const hoverTimeTooltipFlyAnimParams = {y: 5, duration: 250};
    const valuesUpdateInterval = 100;
    const localize = getLocalization();

    let progressPercentage = 0;
    let formattedDuration = '0:00';
    let formattedCurrentTime = '0:00';
    let hoverTime: string | null = null;
    let hoverTimePosX = 0;
    let hoverTimePosY = 0;
    let progressUpdatingInterval: number | undefined;
    let bufferedTimeRanges: BufferedTimeRange[];

    updatePlayerValues();

    onMount(() => {
        progressUpdatingInterval = window.setInterval(updatePlayerValues, valuesUpdateInterval);
    });

    onDestroy(() => {
        if (exists(progressUpdatingInterval)) {
            window.clearInterval(progressUpdatingInterval);
        }
    });

    function calculateProgressPercentage(event: MouseEvent): number {
        const progressBar = event.currentTarget as HTMLDivElement | undefined;
        if (!exists(progressBar)) {
            return 0;
        }

        const clickPosition = event.clientX - progressBar.getBoundingClientRect().left;
        return (clickPosition / progressBar.clientWidth) * 100;
    }

    function formatSeconds(totalSeconds: number): string {
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = Math.floor(totalSeconds % 60);
        return `${minutes}:${seconds < 10 ? `0${seconds}` : seconds}`;
    }

    function getFormattedCurrentTime(audio: HTMLMediaElement | undefined): string {
        if (!exists(audio)) {
            return '0:00';
        }

        return formatSeconds(audio.currentTime);
    }

    function getFormattedDuration(audio: HTMLMediaElement | undefined): string {
        if (!exists(audio)) {
            return '0:00';
        }

        return formatSeconds(audio.duration);
    }

    function updatePlayerValues() {
        progressPercentage = (mediaElement.currentTime / mediaElement.duration) * 100;
        formattedDuration = getFormattedDuration(mediaElement);

        const buffered = mediaElement.buffered;
        const timeRanges: BufferedTimeRange[] = [];

        for (let i = 0; i < buffered.length; i++) {
            const start = buffered.start(i) / mediaElement.duration;
            const end = buffered.end(i) / mediaElement.duration;

            timeRanges.push({
                start,
                duration: end - start
            });
        }

        bufferedTimeRanges = timeRanges;
        formattedCurrentTime = getFormattedCurrentTime(mediaElement);
    }

    const handleSetProgressClick = (event: MouseEvent) => {
        const percentage = calculateProgressPercentage(event);
        mediaElement.currentTime = (percentage / 100) * mediaElement.duration;
        progressPercentage = percentage;
        formattedCurrentTime = getFormattedCurrentTime(mediaElement);
    }

    const handleProgressBarRecalculateHoverTime = (event: MouseEvent) => {
        if (isTouchDevice()) {
            return;
        }

        const percentage = calculateProgressPercentage(event);
        const hoverSeconds = mediaElement.duration * (percentage / 100);

        hoverTimePosX = event.clientX;
        hoverTimePosY = event.clientY;
        hoverTime = formatSeconds(hoverSeconds);
    }

    const handleProgressBarMouseLeave = () => {
        hoverTime = null;
    }
</script>

<div class="mv-media-player-progress-bar"
     aria-label="{localize(/* @kp-localization mediaviewer.content.MultiMediaProgressLabel */ 'mediaviewer.content.MultiMediaProgressLabel')}"
     aria-valuemin="{0}"
     aria-valuemax="{100}"
     aria-valuenow="{progressPercentage}"
     aria-valuetext="{formattedCurrentTime}">

    <span aria-label="{localize(/* @kp-localization mediaviewer.content.MultiMediaCurrentTimeLabel */ 'mediaviewer.content.MultiMediaCurrentTimeLabel')}">
        {formattedCurrentTime}
    </span>

    <button class="progress-bar"
            aria-hidden="true"
            on:click={handleSetProgressClick}
            on:mousemove={handleProgressBarRecalculateHoverTime}
            on:mouseenter={handleProgressBarRecalculateHoverTime}
            on:mouseleave={handleProgressBarMouseLeave}>

        <div class="progress" aria-hidden="true" style="width: {progressPercentage}%;"></div>

        {#each bufferedTimeRanges as bufferedTimeRange}
            <div class="progress buffered-progress"
                 aria-hidden="true"
                 style="width: {bufferedTimeRange.duration * 100}%; left: {bufferedTimeRange.start * 100}%;"></div>
        {/each}
    </button>

    <span aria-label="{localize(/* @kp-localization mediaviewer.content.MultiMediaTotalLengthLabel */ 'mediaviewer.content.MultiMediaTotalLengthLabel')}">
        {formattedDuration}
    </span>

    {#if hoverTime}
        <div class="hover-time-tooltip-container"
             style="--pos-x: {hoverTimePosX}px; --pos-y: {hoverTimePosY}px;"
             transition:fly={hoverTimeTooltipFlyAnimParams}>

            <MvTooltip id="audio-current-time" text="{hoverTime}"/>
        </div>
    {/if}
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    @progress-bar-height: 8px;

    .mv-media-player-progress-bar {
        display: flex;
        flex: 1;
        align-items: center;
        gap: @spacing-m;
        position: relative;

        .progress-bar {
            flex: 1;
            isolation: isolate;
            position: relative;
            cursor: pointer;
            outline: none;
            border: none;
            height: @progress-bar-height;
            border-radius: calc(@progress-bar-height / 2);
            background-color: var(--viewer-default-border);
            overflow: hidden;
            padding: 0;

            .progress {
                border-radius: calc(@progress-bar-height / 2);
                height: @progress-bar-height;
                background-color: var(--accent-blue-new);
                transition: width 0.1s ease-in-out;
            }

            .buffered-progress {
                position: absolute;
                top: 0;
                left: 0;
                z-index: -1;
                opacity: 0.3;
            }
        }

        .hover-time-tooltip-container {
            color: var(--viewer-default-text-color);
            position: fixed;
            pointer-events: none;
            transform: translate(-50%, -50%);
            top: calc(var(--pos-y) - @spacing-xl);
            left: var(--pos-x);
        }
    }
</style>