<div class="mv-onboarding-page">
    <slot/>
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    :global {
        .mv-onboarding-page {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;

            img {
                aspect-ratio: 16/11;
                object-fit: contain;
                width: 100%;
                margin-bottom: 40px;

                &.fill-size-image {
                    aspect-ratio: unset;
                    object-fit: unset;
                    border-radius: @border-radius-large;
                    border: 1px solid var(--viewer-default-border);
                }
            }

            p,
            span,
            small {
                color: var(--viewer-label-text-color);
                margin: 0 0 @spacing-m;
            }

            small {
                font-style: italic;
                opacity: 0.75;
            }

            h2 {
                color: var(--viewer-default-text-color);
                margin: 0 0 @spacing-l;
            }
        }
    }
</style>