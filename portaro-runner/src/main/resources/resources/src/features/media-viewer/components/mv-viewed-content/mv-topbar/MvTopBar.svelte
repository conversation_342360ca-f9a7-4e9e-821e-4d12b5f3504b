<script lang="ts">
    import type {PortaroTheme} from 'shared/theme/portaro-theme';
    import type {DirectoryNode, ViewableFile} from 'typings/portaro.be.types';
    import {getThemeContext} from 'shared/theme/portaro-theme';
    import {toggleMediaViewerSidebar} from '../../../lib/mv-sidebar';
    import {getMediaViewerContext} from '../../../lib/mv-context';
    import {onDestroy} from 'svelte';
    import {getLocalization} from 'core/svelte-context/context';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import MvSettingsPopover from './MvSettingsPopover.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import KpButtonStyleAnchor from 'shared/ui-widgets/button/KpButtonStyleAnchor.svelte';
    import MvTopBarFileActionButtons from './MvTopBarFileActionButtons.svelte';
    import KpGenericTopbar from 'shared/components/kp-generic-topbar/KpGenericTopbar.svelte';
    import KpVerticalSeparator from 'shared/ui-widgets/separator/KpVerticalSeparator.svelte';
    import {
        isCurrentLayout,
        isCurrentLayoutOrBigger,
        isCurrentLayoutOrSmaller,
        MediaViewerLayoutSize,
        mediaViewerLayoutWidth
    } from '../../../lib/mv-layout';

    export let fileAccessAllowed: boolean;

    const currentTheme = getThemeContext().currentTheme;
    const localize = getLocalization();
    const context = getMediaViewerContext();
    let fileExplorerSelected: DirectoryNode;
    let viewedFile: ViewableFile | null;
    const fileExplorerSelectedUnsubscribe = context.fileExplorerSelectedDirectory.subscribe((currentFileExplorerSelected) => fileExplorerSelected = currentFileExplorerSelected);
    const viewedFileUnsubscribe = context.viewedFile.subscribe((currentViewedFile) => viewedFile = currentViewedFile);

    onDestroy(() => {
        fileExplorerSelectedUnsubscribe();
        viewedFileUnsubscribe();
    });

    function getThemeLogoPath(themeValue: PortaroTheme) {
        return `/resources/img/brand/verbis-viewer-logo-${themeValue === 'light' ? 'black' : 'white'}.svg`;
    }

    function getSelectedName(selected: DirectoryNode, viewed: ViewableFile | null) {
        return viewed?.text ?? selected?.text ?? '';
    }

    const handleOpenSidebarButtonClick = () => {
        toggleMediaViewerSidebar(true);
    }
</script>

<KpGenericTopbar additionalClasses="mv-topbar" zIndex="{10}" gap="16px" horizontalPadding="0px" animate>
    {#if isCurrentLayoutOrSmaller(MediaViewerLayoutSize.TABLET, $mediaViewerLayoutWidth)}
        <KpButton additionalClasses="mv-open-sidebar-button"
                  buttonStyle="default-viewer-themed"
                  on:click={handleOpenSidebarButtonClick}>
            <UIcon icon="menu-burger"/>
        </KpButton>
    {/if}

    {#if context.viewerType === 'media-viewer'}
        {#if isCurrentLayoutOrBigger(MediaViewerLayoutSize.LAPTOP, $mediaViewerLayoutWidth)}
            <KpButtonStyleAnchor buttonStyle="brand-orange-new" href="{context.backToCatalogUrl}">
                <IconedContent icon="arrow-small-left">
                    {localize(/* @kp-localization mediaviewer.fileExplorer.BackToCatalogBtnLabel */ 'mediaviewer.fileExplorer.BackToCatalogBtnLabel')}
                </IconedContent>
            </KpButtonStyleAnchor>

            <KpVerticalSeparator height="20px"/>
        {/if}

        {#if isCurrentLayoutOrBigger(MediaViewerLayoutSize.LAPTOP, $mediaViewerLayoutWidth)}
            <img class="viewer-logo"
                 src="{getThemeLogoPath($currentTheme)}"
                 alt="VerbisViewer Logo"
                 loading="lazy"/>
        {/if}
    {/if}

    <div class="full-width-divider"></div>

    {#if fileExplorerSelected || viewedFile}
        {#if isCurrentLayoutOrBigger(MediaViewerLayoutSize.LAPTOP, $mediaViewerLayoutWidth)}
            <span class="selected-name">{getSelectedName(fileExplorerSelected, viewedFile)}</span>
        {/if}

        {#if viewedFile && fileAccessAllowed}
            {#if isCurrentLayoutOrBigger(MediaViewerLayoutSize.LAPTOP, $mediaViewerLayoutWidth)}
                <KpVerticalSeparator height="20px"/>
            {/if}

            {#key viewedFile}
                <MvTopBarFileActionButtons file="{viewedFile}"/>
            {/key}
        {/if}

        {#if isCurrentLayout(MediaViewerLayoutSize.MOBILE, $mediaViewerLayoutWidth)}
            <div class="full-width-divider"></div>
        {:else if !isCurrentLayout(MediaViewerLayoutSize.TABLET, $mediaViewerLayoutWidth)}
            <KpVerticalSeparator height="20px"/>
        {/if}
    {/if}

    {#if context.viewerType === 'media-viewer'}
        <div class="settings-buttons-container">
            <MvSettingsPopover/>
        </div>
    {/if}
</KpGenericTopbar>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    :global {
        .mv-settings-button,
        .mv-open-sidebar-button {
            height: @media-viewer-topbar-elements-height;
        }

        .kp-generic-topbar.mv-topbar .mv-open-sidebar-button {
            margin-left: @spacing-xl;
        }

        .kp-generic-topbar.mv-topbar {
            background-color: var(--viewer-sidebar-bg);
            border-bottom: none;
            height: @media-viewer-topbar-height;
            padding-right: @spacing-xl;
        }
    }

    .full-width-divider {
        margin: auto;
    }

    .viewer-logo {
        flex-shrink: 0;
        height: @topbar-logo-height;
    }

    .selected-name {
        min-width: 0;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }

    .settings-buttons-container {
        display: flex;
        align-items: center;
        gap: @spacing-s;
    }
</style>