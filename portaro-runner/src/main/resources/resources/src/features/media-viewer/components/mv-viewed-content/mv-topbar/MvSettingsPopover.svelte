<script lang="ts">
    import {getThemeContext} from 'shared/theme/portaro-theme';
    import {getLocalization} from 'core/svelte-context/context';
    import {MV_VERSION} from '../../../lib/mv-constants';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import MvStyledPopover from '../../mv-misc/MvStyledPopover.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    const themeContext = getThemeContext();
    const localize = getLocalization();
</script>

<MvStyledPopover additionalPopoverButtonClasses="mv-settings-popover" additionalPopoverPanelClasses="mv-settings-popover">
    <svelte:fragment slot="button">
        <UIcon icon="settings"/>
    </svelte:fragment>

    <svelte:fragment slot="heading">
        {localize(/* @kp-localization mediaviewer.content.SettingsTitle */ 'mediaviewer.content.SettingsTitle')}
    </svelte:fragment>

    <svelte:fragment slot="content">
        <!-- Themes -->
        <span id="mv-settings-theme-title" class="theme-title">
            {localize(/* @kp-localization mediaviewer.content.SettingsThemeTitle */ 'mediaviewer.content.SettingsThemeTitle')}
        </span>

        <div class="theme-buttons-container"
             aria-describedby="mv-settings-theme-title"
             aria-labelledby="mv-settings-theme-label">

            <KpButton isBlock
                      buttonStyle="default-viewer-themed"
                      on:click={() => themeContext.changeTheme('light')}>
                <IconedContent icon="sun">
                    {localize(/* @kp-localization mediaviewer.content.SettingsLightTheme */ 'mediaviewer.content.SettingsLightTheme')}
                </IconedContent>
            </KpButton>

            <KpButton isBlock
                      buttonStyle="accent-blue-new"
                      on:click={() => themeContext.changeTheme('dark')}>
                <IconedContent icon="moon">
                    {localize(/* @kp-localization mediaviewer.content.SettingsDarkTheme */ 'mediaviewer.content.SettingsDarkTheme')}
                </IconedContent>
            </KpButton>
        </div>

        <small class="text-center" id="mv-settings-theme-label">
            {localize(/* @kp-localization mediaviewer.content.SettingsThemeLabel */ 'mediaviewer.content.SettingsThemeLabel')}
        </small>

        <!-- Open onboarding modal button
        <button class="onboarding-button" on:click={handleShowOnboardingModalClick}>
            {localize(/* @kp-localization mediaviewer.content.SettingsNewFeatures */ 'mediaviewer.content.SettingsNewFeatures')}
        </button>
        -->

        <small class="text-center version-string">{MV_VERSION}</small>
    </svelte:fragment>
</MvStyledPopover>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    :global {
        .mv-settings-popover.mv-styled-popover-panel {
            width: @media-viewer-topbar-settings-popover-width;
        }
    }

    .theme-title {
        font-weight: 500;
        margin-bottom: @spacing-ml;
    }

    .theme-buttons-container {
        display: flex;
        flex-direction: column;
        gap: @spacing-s;
    }

    small {
        opacity: 0.8;
        margin-top: @spacing-m;
    }

    .version-string {
        margin-top: @spacing-ml;
    }
</style>