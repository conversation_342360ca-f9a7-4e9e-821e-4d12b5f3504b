<script lang="ts">
    import type {DirectoryNode, ViewableFile} from 'typings/portaro.be.types';
    import {byteFormatter} from 'shared/utils/pipes';
    import {pipe} from 'core/utils';
    import {getMediaViewerContext} from '../../../lib/mv-context';
    import {onDestroy} from 'svelte';
    import {slide} from 'svelte/transition';
    import {getThemeContext} from 'shared/theme/portaro-theme';
    import {exists} from 'shared/utils/custom-utils';
    import {isFileImage} from '../../../lib/mv-utils';
    import {getLocalization} from 'core/svelte-context/context';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import MvDirectoryOrFileIcon from '../../mv-misc/MvDirectoryOrFileIcon.svelte';
    import MvContextMenuWrapper from '../mv-context-menu/MvContextMenuWrapper.svelte';
    import MvFileContextMenuContent from '../mv-context-menu/MvFileContextMenuContent.svelte';
    import MvCacheableThumbnail from '../../mv-misc/MvCacheableThumbnail.svelte';

    export let file: ViewableFile;
    export let parentDirectory: DirectoryNode | null = null;
    export let id: string | null = null;

    const context = getMediaViewerContext();
    const localize = getLocalization();
    const currentTheme = getThemeContext().currentTheme;
    const selectFileSlideAnimParams = {axis: 'x', duration: 250} as const;

    let currentFileChecked = false;
    let viewedFile: ViewableFile | null;
    let selectingFiles = false;
    const viewedFileUnsubscribe = context.viewedFile.subscribe((currentViewedFile) => viewedFile = currentViewedFile);
    const currentSelectedFilesUnsubscribe = context.filesSelected.subscribe((currentFilesSelected) => {
        currentFileChecked = exists(currentFilesSelected.find((e) => e.id === file.id));
    });
    const selectingFilesUnsubscribe = context.selectingFiles.subscribe((currentSelectingFiles) => {
        selectingFiles = currentSelectingFiles;
        if (!currentSelectingFiles) {
            currentFileChecked = false;
        }
    });

    $: isFileViewed = viewedFile?.id === file.id;
    $: context.updateSelectedFiles(file, currentFileChecked);

    onDestroy(() => {
        viewedFileUnsubscribe();
        currentSelectedFilesUnsubscribe();
        selectingFilesUnsubscribe();
    });

    const handleSelectFileClick = () => {
        if (selectingFiles) {
            currentFileChecked = !currentFileChecked;
            return;
        }

        context.setViewedFile(file);

        if (exists(parentDirectory)) {
            context.setFileExplorerSelectedDirectory(parentDirectory);
        }
    }
</script>

<MvContextMenuWrapper {id} on:click={handleSelectFileClick} let:open>
    <MvFileContextMenuContent slot="context-content" {file}/>

    <div class="mv-grid-column-directory"
         class:context-menu-opened={open}
         class:viewed={isFileViewed}
         class:file-selected={currentFileChecked}>

        {#if selectingFiles}
            <div class="file-select-checkbox-container" transition:slide={selectFileSlideAnimParams}>
                <input id="file-{file.id}-select"
                       tabindex="-1"
                       aria-hidden="true"
                       type="checkbox"
                       name="file-selected"
                       bind:checked={currentFileChecked}
                       style="color-scheme: {$currentTheme}"/>
            </div>
        {/if}

        {#if isFileViewed}
            <div class="file-viewed-icon-container" transition:slide={selectFileSlideAnimParams}>
                <UIcon color="var(--brand-orange-new)"
                       icon="eye"
                       label="{localize(/* @kp-localization mediaviewer.fileExplorer.CurrentlyShownFileLabel */ 'mediaviewer.fileExplorer.CurrentlyShownFileLabel')}"/>
            </div>
        {/if}

        <div class="icon-container">
            {#if isFileImage(file)}
                <MvCacheableThumbnail {file}
                                      let:loading
                                      let:loadError
                                      let:blobUrl>

                    <img class="thumbnail"
                         class:thumbnail-loading={loading || loadError}
                         loading="lazy"
                         alt="File {file.text}"
                         src="{blobUrl}"/>
                </MvCacheableThumbnail>
            {:else}
                <MvDirectoryOrFileIcon directoryOrFile="{file}"/>
            {/if}
        </div>

        <span class="file-name-label text-ellipsis">{file.text}</span>

        <span class="size-label text-muted">{pipe(file.size, byteFormatter())}</span>
    </div>
</MvContextMenuWrapper>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    @vertical-row-spacing: 4px;

    .mv-grid-column-directory {
        display: flex;
        cursor: pointer;
        align-items: center;
        text-align: start;
        outline: none;
        width: 100%;
        padding: @vertical-row-spacing @spacing-s @vertical-row-spacing @spacing-base;
        border-radius: @border-radius-default;
        border: 1px solid transparent;
        transition: border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;

        &:hover,
        &.context-menu-opened {
            border-color: var(--brand-orange-new);
        }

        &.viewed {
            background-color: var(--viewer-orange-highlight-transparent);
            border-color: var(--brand-orange-new);
        }

        &.file-selected {
            border-color: var(--brand-orange-new);
        }

        .size-label {
            margin-left: auto;
            padding-left: @spacing-sm;
            font-size: @font-size-small;
        }

        .file-viewed-icon-container,
        .file-select-checkbox-container {
            margin-right: @spacing-sm;
            flex-shrink: 0;
            z-index: 1;
            text-shadow: var(--brand-orange-new) 0 0 12px;
        }

        .icon-container {
            margin-right: @spacing-sm;

            .thumbnail {
                margin: 0;
                padding: 0;
                aspect-ratio: 1/1;
                object-fit: cover;
                max-width: 16px;
                max-height: 16px;
                width: 100%;
                height: 100%;
                border: 1px solid var(--viewer-default-border);
                background-color: var(--viewer-content-bg);
                border-radius: @border-radius-small;

                &.thumbnail-loading {
                    opacity: 0.25;
                }
            }
        }

        .file-select-checkbox-container {
            display: flex;
            align-items: center;

            input {
                accent-color: var(--brand-orange-new);
                margin: 0;
                cursor: pointer;
            }
        }
    }
</style>