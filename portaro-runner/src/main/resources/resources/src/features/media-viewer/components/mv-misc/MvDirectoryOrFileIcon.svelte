<script lang="ts">
    import type {DirectoryNode, ViewableFile} from 'typings/portaro.be.types';
    import type {UIcons} from 'shared/ui-widgets/uicons/types';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import {isDirectoryNode, isViewableFile} from '../../lib/mv-utils';
    import {FileViewForm} from 'shared/constants/portaro.constants';

    export let directoryOrFile: DirectoryNode | ViewableFile;

    function getIcon(obj: DirectoryNode | ViewableFile): UIcons {
        if (isDirectoryNode(obj)) {
            return 'folder';
        }

        if (!isViewableFile(obj)) {
            return 'file';
        }

        if (obj.viewForms.includes(FileViewForm.THUMBNAIL)
            || obj.viewForms.includes(FileViewForm.IMAGE)) {
            return 'file-image';
        }

        if (obj.viewForms.includes(FileViewForm.PDF)) {
            return 'file-pdf';
        }

        if (obj.viewForms.includes(FileViewForm.AUDIO)) {
            return 'file-audio';
        }

        if (obj.viewForms.includes(FileViewForm.VIDEO)) {
            return 'file-video';
        }

        if (obj.viewForms.includes(FileViewForm.DOCUMENT) ||
            obj.viewForms.includes(FileViewForm.PLAIN_TEXT) ||
            obj.viewForms.includes(FileViewForm.RICH_DOCUMENT)) {
            return 'document';
        }

        return 'file';
    }
</script>

<UIcon icon="{getIcon(directoryOrFile)}"/>