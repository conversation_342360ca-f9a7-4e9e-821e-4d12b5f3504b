<script lang="ts">
    import type {DirectoryNode} from 'typings/portaro.be.types';
    import {onDestroy, onMount} from 'svelte';
    import {fade} from 'svelte/transition';
    import {getMediaViewerContext} from '../../lib/mv-context';
    import {uploadDirectoryLocalFiles} from '../../lib/mv-file-explorer-handlers';
    import {exists} from 'shared/utils/custom-utils';
    import {getLocalization} from 'core/svelte-context/context';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import MvCircularProgressBar from './MvCircularProgressBar.svelte';
    import MvLineLoadingIndicator from './MvLineLoadingIndicator.svelte';

    export let disabled = false;

    const context = getMediaViewerContext();
    const localize = getLocalization();

    let isDragging = false;
    let fileExplorerSelected: DirectoryNode;
    let uploadingProgress: number | null;
    const ongoingUploadProgressUnsubscribe = context.ongoingUploadPercentage.subscribe((currentOngoingUploadProgress) => uploadingProgress = currentOngoingUploadProgress);
    const fileExplorerSelectedUnsubscribe = context.fileExplorerSelectedDirectory.subscribe((currentFileExplorerSelected) => fileExplorerSelected = currentFileExplorerSelected);

    $: currentlyUploading = exists(uploadingProgress);

    onDestroy(() => {
        ongoingUploadProgressUnsubscribe();
        fileExplorerSelectedUnsubscribe();
    });

    const handleDragOver = (event: DragEvent) => {
        event.preventDefault();
        if (disabled) {
            return;
        }

        if (isDraggingFile(event)) {
            isDragging = true;
        }
    };

    const handleDragLeave = (event: DragEvent) => {
        event.preventDefault();
        if (disabled) {
            return;
        }

        isDragging = false;
    };

    const handleDrop = (event: DragEvent) => {
        event.preventDefault();
        isDragging = false;
        if (disabled) return;

        const fileList = event.dataTransfer?.files;

        if (!fileList || !fileExplorerSelected) {
            return;
        }

        const files = Array.from(fileList);
        uploadDirectoryLocalFiles(context, fileExplorerSelected, files);
    };

    const isDraggingFile = (event: DragEvent): boolean => {
        const types = Array.from(event.dataTransfer?.types || []);
        return types.includes('Files');
    };

    onMount(() => {
        window.addEventListener('dragover', handleDragOver);
        window.addEventListener('dragleave', handleDragLeave);
        window.addEventListener('drop', handleDrop);
    });

    onDestroy(() => {
        window.removeEventListener('dragover', handleDragOver);
        window.removeEventListener('dragleave', handleDragLeave);
        window.removeEventListener('drop', handleDrop);
    });
</script>

{#if !disabled && (isDragging || currentlyUploading)}
    <div class="file-dropzone-overlay" transition:fade|local={{duration: 250}}>
        {#if isDragging && !currentlyUploading}
            <div class="dashed-container">
                <div class="bouncy-icon-container">
                    <UIcon icon="upload" color="var(--brand-orange-new)"/>
                </div>

                <span>{localize(/* @kp-localization mediaviewer.fileExplorer.DropFilesToUploadLabel */ 'mediaviewer.fileExplorer.DropFilesToUploadLabel')}</span>
            </div>
        {/if}

        {#if currentlyUploading}
            <div class="uploading-container">
                {#if uploadingProgress !== 100}
                    <MvCircularProgressBar progressPercentage="{Math.floor(uploadingProgress)}"/>
                    <span>{localize(/* @kp-localization mediaviewer.fileExplorer.OngoingFileUploading */ 'mediaviewer.fileExplorer.OngoingFileUploading')}</span>
                {:else}
                    <MvLineLoadingIndicator/>
                    <span>{localize(/* @kp-localization mediaviewer.fileExplorer.FinishingFileUploading */ 'mediaviewer.fileExplorer.FinishingFileUploading')}</span>
                {/if}
            </div>
        {/if}
    </div>
{/if}

<slot/>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";
    @import (reference) "styles/portaro.variables.less";

    .file-dropzone-overlay {
        position: fixed;
        z-index: @modal-z-index;
        pointer-events: none;
        align-items: center;
        justify-content: center;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        padding: 80px;
        background-color: rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(2px);
        -webkit-backdrop-filter: blur(2px);

        .dashed-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: @spacing-s;
            width: 100%;
            height: 100%;
            border: 3px dashed var(--brand-orange-new);
            border-radius: @border-radius-xl;
        }

        .uploading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: @spacing-ml;
        }

        .bouncy-icon-container {
            font-size: 20px;
            animation: 2s ease-in-out 0s infinite bounce;
        }

        span {
            color: white;
            text-shadow: rgba(0, 0, 0, 0.5) 0 0 20px;
        }
    }

    @keyframes bounce {
        0%, 10%, 90%, 100% {
            transform: translateY(-0.25rem);
        }
        50% {
            transform: translateY(0);
        }
    }

    :global {
        // Styles for circular progress loader
        .file-dropzone-overlay .uploading-container {
            .progress-circle .background {
                color: var(--viewer-bold-border);
            }

            .progress-text {
                color: white;
                opacity: 0.85;
            }
        }
    }
</style>