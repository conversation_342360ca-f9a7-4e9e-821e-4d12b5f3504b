import {exists} from 'shared/utils/custom-utils';
import {noop} from 'rxjs';

interface CachedItem<ITEM> {
    cachedAt: number;
    item: ITEM;
}

export class MvCachedService<KEY extends string | number | symbol, ITEM> {

    private static maxCacheItemAge = 30_000 * 60; // 30 minutes

    private cleanUpIntervalId: NodeJS.Timeout | null = null;
    private cache = {} as Record<KEY, CachedItem<ITEM>>;

    constructor(private itemRemovedCallback: (item: ITEM) => void = () => noop, private cleanUpInterval: number = 30_000, private maxCachedItems: number = 200) {
    }

    /**
     * Starts task that cleans up cached images that are older than maximum age
     */
    public startCleanUpTask() {
        if (exists(this.cleanUpIntervalId)) {
            return;
        }

        this.cleanUpIntervalId = setInterval(() => {
            for (const key in this.cache) {
                if (Date.now() - this.cache[key].cachedAt > MvCachedService.maxCacheItemAge) {
                    const cachedItem = this.cache[key];
                    this.itemRemovedCallback(cachedItem.item);
                    delete this.cache[key];
                }
            }
        }, this.cleanUpInterval);
    }

    /**
     * Stops cleanup task, if it was ever started
     */
    public stopCleanUpTask() {
        if (exists(this.cleanUpIntervalId)) {
            clearInterval(this.cleanUpIntervalId);
            this.cleanUpIntervalId = null;
        }
    }

    /**
     * Gets cached item by its key
     *
     * @param key Key of searched item
     */
    public getCachedItem(key: KEY): ITEM {
        return this.cache[key]?.item;
    }

    /**
     * Adds an item into a cache
     *
     * @param key Key of added item
     * @param item Added item
     */
    public setCachedItem(key: KEY, item: ITEM) {
        this.cache[key] = {
            cachedAt: Date.now(),
            item
        };

        this.enforceCacheLimit();
    }

    // Helper function to enforce cache items limiting
    private enforceCacheLimit() {
        const cacheKeys = Object.keys(this.cache) as KEY[];
        if (cacheKeys.length > this.maxCachedItems) {
            const sortedKeys = cacheKeys.sort((a, b) => this.cache[a].cachedAt - this.cache[b].cachedAt);

            // Remove the oldest items until the cache size is within the limit
            while (sortedKeys.length > this.maxCachedItems) {
                const oldestKey = sortedKeys.shift();
                if (exists(oldestKey)) {
                    const cachedItem = this.cache[oldestKey];
                    this.itemRemovedCallback(cachedItem.item);
                    delete this.cache[oldestKey];
                }
            }
        }
    }
}