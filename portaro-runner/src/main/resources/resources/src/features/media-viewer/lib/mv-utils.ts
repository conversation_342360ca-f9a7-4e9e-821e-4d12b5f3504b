import type {DirectoryNode, ViewableFile} from 'typings/portaro.be.types';
import type {DirectoryContent} from './mv-types';
import {exists} from 'shared/utils/custom-utils';
import {FileViewForm} from 'shared/constants/portaro.constants';

export function isDirectoryNode(obj: ViewableFile | DirectoryNode): obj is DirectoryNode {
    return exists(obj) && 'children' in obj && exists(obj.children);
}

export function isViewableFile(obj: ViewableFile | DirectoryNode): obj is ViewableFile {
    return exists(obj) && 'filename' in obj && exists(obj.filename);
}

export function isFileImage(file: ViewableFile | null): boolean {
    if (!exists(file)) {
        return false;
    }

    return file.viewForms.includes(FileViewForm.IMAGE);
}

export function areAllFilesMailable(files: ViewableFile[]): boolean {
    return files.every((file) => file.exportPermission.allowed && file.showPermission.allowed);
}

export function areAllFilesPrintable(files: ViewableFile[]): boolean {
    return files.every((file) => file.printPermission.allowed && file.showPermission.allowed);
}

// Sorts directory contents - directories first, then files
export function sortDirectoryContents(content: DirectoryContent[]): DirectoryContent[] {
    return content.toSorted((a, b) => {
        if (isDirectoryNode(a) && !isDirectoryNode(b)) {
            return -1;
        } else if (!isDirectoryNode(a) && isDirectoryNode(b)) {
            return 1;
        } else {
            return 0;
        }
    });
}

// Checks if a file contains certain view forms
export function viewableFileContainsViewForm(file: ViewableFile, ...forms: FileViewForm[]): boolean {
    if (!exists(file) || !exists(file.viewForms)) {
        return false;
    }

    const contained = file.viewForms.filter((form) => forms.includes(form));
    return contained.length > 0;
}

// Function to automatically return number of columns based on set breakpoints
export const getGridViewColumnsCount = (width: number, mobile: boolean): number => {
    if (mobile) {
        return 3;
    }

    if (width > 440) {
        return 4;
    }

    if (width > 330) {
        return 3;
    }

    return 2;
};

// Recursive function to find the directory by its ID in the nested structure
export function findDirectoryInTreeById(parent: DirectoryNode, id: number, path: DirectoryNode[] = []): DirectoryNode | null {
    if (parent.id === id) {
        path.push(parent);
        return parent;
    }

    for (const childDirectory of parent.children) {
        const newPath = [...path, parent]; // Append current directory to the path
        const result = findDirectoryInTreeById(childDirectory, id, newPath);

        if (result) {
            path.push(...newPath.slice(path.length)); // Update path with newPath elements
            return result;
        }
    }

    return null;
}