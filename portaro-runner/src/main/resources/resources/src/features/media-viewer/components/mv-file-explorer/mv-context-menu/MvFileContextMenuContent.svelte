<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import {getMediaViewerContext} from '../../../lib/mv-context';
    import {areAllFilesMailable, areAllFilesPrintable} from '../../../lib/mv-utils';
    import {onDestroy} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {getLocalization} from 'core/svelte-context/context';
    import KpContextMenuButton from 'shared/components/kp-context-menu/KpContextMenuButton.svelte';
    import MvContextMenuDivider from './MvContextMenuDivider.svelte';
    import MvContextMenuTitle from './MvContextMenuTitle.svelte';
    import {
        handleFileEditMetadataClick,
        handleFilePrintClick,
        handleFilesDeleteClick,
        handleFilesMailClick
    } from '../../../lib/mv-file-explorer-handlers';

    export let file: ViewableFile;

    const context = getMediaViewerContext();
    const localize = getLocalization();

    let filesSelected: ViewableFile[];
    let viewedFile: ViewableFile | null;
    const filesSelectedUnsubscribe = context.filesSelected.subscribe((currentFilesSelected) => filesSelected = currentFilesSelected);
    const viewedFileUnsubscribe = context.viewedFile.subscribe((currentViewedFile) => viewedFile = currentViewedFile);

    $: isFileSelected = exists(filesSelected.find((selectedFile) => file.id === selectedFile.id));
    $: isFileViewed = viewedFile?.id === file.id;

    onDestroy(() => {
        filesSelectedUnsubscribe();
        viewedFileUnsubscribe();
    });

    const handleSelectFileClick = () => {
        if (isFileSelected) {
            context.removeSelectedFile(file);
            return;
        }

        context.setSelectingFiles(true);

        setTimeout(() => {
            context.addSelectedFile(file);
        }, 25);
    };

    const handleViewFileClick = () => {
        context.setViewedFile(file);
    };
</script>

<MvContextMenuTitle>
    {localize(/* @kp-localization mediaviewer.fileExplorer.FileContextLabel */ 'mediaviewer.fileExplorer.FileContextLabel')}
</MvContextMenuTitle>

<MvContextMenuDivider/>

{#if isFileViewed}
    <KpContextMenuButton icon="eye"
                         on:click={handleViewFileClick}>

        {localize(/* @kp-localization mediaviewer.fileExplorer.ShowFile */ 'mediaviewer.fileExplorer.ShowFile')}
    </KpContextMenuButton>
{/if}

{#if context.canEdit}
    {#if isFileViewed}
        <MvContextMenuDivider/>
    {/if}

    <KpContextMenuButton icon="edit"
                         on:click={() => handleFileEditMetadataClick(context, file)}>

        {localize(/* @kp-localization mediaviewer.fileExplorer.EditFileMetadata */ 'mediaviewer.fileExplorer.EditFileMetadata')}
    </KpContextMenuButton>

    <MvContextMenuDivider/>

    <KpContextMenuButton icon="{isFileSelected ? 'cross-circle' : 'checkbox'}"
                         buttonStyle="{isFileSelected ? 'brand-orange' : 'default'}"
                         on:click={handleSelectFileClick}>

        {isFileSelected
            ? localize(/* @kp-localization mediaviewer.fileExplorer.CancelFileSelection */ 'mediaviewer.fileExplorer.CancelFileSelection')
            : localize(/* @kp-localization mediaviewer.fileExplorer.SelectFile */ 'mediaviewer.fileExplorer.SelectFile')}
    </KpContextMenuButton>
{/if}

{#if areAllFilesPrintable([file])}
    <KpContextMenuButton icon="print"
                         on:click={() => handleFilePrintClick(context, file)}>

        {localize(/* @kp-localization mediaviewer.fileExplorer.PrintFile */ 'mediaviewer.fileExplorer.PrintFile')}
    </KpContextMenuButton>
{/if}

{#if areAllFilesMailable([file])}
    <KpContextMenuButton icon="envelope"
                         on:click={() => handleFilesMailClick(context, [file])}>

        {localize(/* @kp-localization mediaviewer.fileExplorer.EmailFile */ 'mediaviewer.fileExplorer.EmailFile')}
    </KpContextMenuButton>
{/if}

{#if context.canEdit}
    <MvContextMenuDivider/>

    <KpContextMenuButton icon="trash"
                         buttonStyle="danger"
                         on:click={() => handleFilesDeleteClick(context, [file])}>

        {localize(/* @kp-localization mediaviewer.fileExplorer.DeleteFile */ 'mediaviewer.fileExplorer.DeleteFile')}
    </KpContextMenuButton>
{/if}