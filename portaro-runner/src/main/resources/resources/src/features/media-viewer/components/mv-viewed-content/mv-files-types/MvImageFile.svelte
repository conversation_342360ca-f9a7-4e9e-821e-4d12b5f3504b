<script lang="ts">
    import type {ImageDimensions, ViewableFile} from 'typings/portaro.be.types';
    import type {ViewedContentViewport} from '../../../lib/mv-viewed-content';
    import type {MoveDraggingEvent} from '../../../actions/use.move-dragging';
    import type {RawImageData} from '../../../services/mv-image-cache.service';
    import type {CssSize} from 'shared/ui-widgets/types';
    import type {Subscription} from 'rxjs';
    import {getViewedContentContext} from '../../../lib/mv-viewed-content';
    import {onDestroy, onMount} from 'svelte';
    import {moveDragging} from '../../../actions/use.move-dragging';
    import {fly} from 'svelte/transition';
    import {getLocalization} from 'core/svelte-context/context';
    import {getMediaViewerContext} from '../../../lib/mv-context';
    import {exists} from 'shared/utils/custom-utils';
    import {zoomable} from '../../../actions/use.zoomable';
    import {fromEvent} from 'rxjs';
    import {debounceTime} from 'rxjs/operators';
    import {getThemeContext} from 'shared/theme/portaro-theme';
    import {SCALE_TO_FIT_EVENT} from '../../../lib/mv-constants';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import MvFileTextualForm from '../MvFileTextualForm.svelte';

    export let file: ViewableFile;
    export let pinchZooming = false;
    // Passed from MvViewedContent container - direction of an animation when navigation through mobile swipe
    export let imageSwitchAnimationDirection = 0;
    export let fullscreenImageGallery = false;

    // Copy it so it doesn't change from MvViewedContent - the anim can be played after a longer time - when image loading is finished
    const imageSwitchAnimationDirectionCopy = imageSwitchAnimationDirection;

    const supportsThumbnailing = exists(file.dimensions);
    const imageCacheService = getMediaViewerContext().imageCacheService;
    const viewedContent = getViewedContentContext();
    const currentTheme = getThemeContext().currentTheme;
    const flyInAnimParams = {y: 20, duration: 250};
    const localize = getLocalization();
    let viewport: ViewedContentViewport;
    let zoom = 1;
    let showingTextualForm = false;
    const viewportUnsubscribe = viewedContent.viewport.subscribe((currentViewport) => viewport = currentViewport);
    const showingTextualFormUnsubscribe = viewedContent.showingTextualForm.subscribe((currentTextualForm) => showingTextualForm = currentTextualForm);
    const zoomUnsubscribe = viewedContent.zoom.subscribe((currentZoom) => zoom = currentZoom);

    let loading = true;
    let loadError = false;
    let imageWidth = 0;
    let imageHeight = 0;
    let canBeDragged = false;
    let isDragging = false;
    let draggedOffsetX = 0;
    let draggedOffsetY = 0;
    let mouseOrTouchPadZooming = false;
    let mouseOrTouchPadZoomingSubscription: Subscription;
    $: calculateActualImageSize(imageWidth, imageHeight, zoom, viewport);

    let rawThumbnailImageData: RawImageData | null = null;
    let rawImageData: RawImageData | null = null;
    $: imageSource = exists(rawImageData) ? rawImageData : rawThumbnailImageData;
    $: ({initialWidth, initialHeight} = calculateInitialImageDimensions(file.dimensions, viewport));

    onMount(async () => {
        const successfulLoad = await loadImage();

        if (successfulLoad) {
            viewedContent.eventBus.addEventListener(SCALE_TO_FIT_EVENT, handleScaleToFitEvent);
        }
    });

    onDestroy(() => {
        viewportUnsubscribe();
        zoomUnsubscribe();
        showingTextualFormUnsubscribe();

        if (exists(mouseOrTouchPadZoomingSubscription)) {
            mouseOrTouchPadZoomingSubscription.unsubscribe();
        }

        viewedContent.eventBus.removeEventListener(SCALE_TO_FIT_EVENT, handleScaleToFitEvent);
    });

    async function loadImage(): Promise<boolean> {
        if (supportsThumbnailing) {
            rawThumbnailImageData = imageCacheService.getCachedThumbnailImage(file);
        }

        rawImageData = await imageCacheService.fetchOriginalImageWithCaching(file);

        if (!exists(rawImageData)) {
            loading = false;
            loadError = true;
            return false;
        }

        loading = false;
        return true;
    }

    function calculateActualImageSize(width: number, height: number, zoomValue: number, viewportValue: ViewedContentViewport) {
        if (width === 0 || height === 0) {
            return;
        }

        const actualWidth = width * zoomValue;
        const actualHeight = height * zoomValue;
        canBeDragged = actualWidth > viewportValue.width || actualHeight > viewportValue.height;

        viewedContent.setCanSwipeNavigate(!canBeDragged);

        if (!canBeDragged && (draggedOffsetX !== 0 || draggedOffsetY !== 0)) {
            draggedOffsetX = 0;
            draggedOffsetY = 0;
        }
    }

    const handleScaleToFitEvent = () => {
        const widthRatio = viewport.width / imageWidth;
        const heightRatio = viewport.height / imageHeight;
        viewedContent.setZoom(Math.max(1, Math.min(widthRatio, heightRatio)));
    };

    const handleDragStart = () => {
        isDragging = true;
    };

    const handleDrag = (event: MoveDraggingEvent) => {
        draggedOffsetX += event.detail.x / zoom;
        draggedOffsetY += event.detail.y / zoom;
        isDragging = true;
    };

    const handleOnDragEnd = () => {
        isDragging = false;
    };

    const handleZoomChange = (event: CustomEvent<number>) => {
        mouseOrTouchPadZooming = true;
        viewedContent.setZoom(zoom * event.detail);

        // Set mouseOrTouchPadZooming to false after 250ms of zooming events inactivity
        if (!exists(mouseOrTouchPadZoomingSubscription)) {
            mouseOrTouchPadZoomingSubscription = fromEvent<CustomEvent<number>>(event.target, 'zoom')
                .pipe(debounceTime(250))
                .subscribe(() => mouseOrTouchPadZooming = false);
        }
    };

    function calculateInitialImageDimensions(dimensions: ImageDimensions, currentViewport: ViewedContentViewport): {
        initialWidth: CssSize,
        initialHeight: CssSize
    } {
        if (!exists(dimensions)) {
            return {
                initialWidth: 'auto',
                initialHeight: 'auto'
            };
        }

        const imageAspectRatio = dimensions.width / dimensions.height;
        const viewportAspectRatio = currentViewport.width / currentViewport.height;

        let calculatedImageWidth = dimensions.width;
        let calculatedImageHeight = dimensions.height;

        if (calculatedImageWidth > currentViewport.width || calculatedImageHeight > currentViewport.height) {
            if (viewportAspectRatio > imageAspectRatio) {
                calculatedImageHeight = currentViewport.height;
                calculatedImageWidth = Math.floor(calculatedImageHeight * imageAspectRatio);
            } else {
                calculatedImageWidth = currentViewport.width;
                calculatedImageHeight = Math.floor(calculatedImageWidth / imageAspectRatio);
            }
        }

        return {
            initialWidth: `${calculatedImageWidth}px`,
            initialHeight: `${calculatedImageHeight}px`
        };
    }
</script>

{#if !showingTextualForm}
    <div class="mv-image-file"
         use:zoomable={{disabled: fullscreenImageGallery}}
         use:moveDragging={{disabled: !canBeDragged}}
         class:image-loading={(loading || loadError) && (!exists(rawThumbnailImageData) || !supportsThumbnailing)}
         bind:clientWidth={imageWidth}
         bind:clientHeight={imageHeight}
         in:fly={{y: 50 * imageSwitchAnimationDirection, duration: 250}}
         on:dragging-start={handleDragStart}
         on:dragging-progress={handleDrag}
         on:dragging-end={handleOnDragEnd}
         on:zoom={handleZoomChange}
         style:--maxWidth="{viewport.width}px"
         style:--maxHeight="{viewport.height}px"
         style:--zoom="{zoom}"
         style:--dragOffsetX="{draggedOffsetX}px"
         style:--dragOffsetY="{draggedOffsetY}px">

        <img src="{imageSource?.blobUrl}"
             class="{$currentTheme}"
             draggable="false"
             alt="{file.text}"
             style:width="{initialWidth}"
             style:height="{initialHeight}"
             class:original-loading={!exists(rawImageData)}
             class:anim-from-top={loading && imageSwitchAnimationDirectionCopy === -1}
             class:anim-from-bottom={loading && imageSwitchAnimationDirectionCopy === 1}
             class:draggable={canBeDragged}
             class:dragging={isDragging}
             class:zoom-changing={pinchZooming || mouseOrTouchPadZooming}/>
    </div>
{/if}

{#if showingTextualForm}
    <MvFileTextualForm {file}/>
{/if}

{#if loading}
    <KpLoadingBlock size="sm"/>
{/if}

{#if loadError}
    <div class="load-error-container" in:fly={flyInAnimParams}>
        <IconedContent icon="exclamation"
                       orientation="vertical"
                       align="center"
                       justify="center"
                       iconColor="var(--danger-red)">
            {localize(/* @kp-localization mediaviewer.content.ImageLoadError */ 'mediaviewer.content.ImageLoadError')}
        </IconedContent>
    </div>
{/if}

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-image-file {
        position: absolute;
        scale: 1;
        transition: scale 0.3s ease-in-out;

        &.image-loading {
            scale: 0.9;

            img {
                opacity: 0;
                visibility: hidden;
            }
        }

        img {
            max-width: var(--maxWidth);
            max-height: var(--maxHeight);
            width: 100%;
            height: 100%;
            opacity: 1;
            visibility: visible;
            border: 1px solid var(--viewer-default-border);
            border-radius: @border-radius-large;
            background-color: var(--viewer-bg);
            scale: var(--zoom);
            transform: translate(var(--dragOffsetX), var(--dragOffsetY));
            overflow: hidden;
            image-rendering: pixelated; // Fastest algorithm for scaled image rendering
            transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out, scale 0.3s ease-in-out, transform 0.3s ease-in-out, filter 0.2s ease-in-out;

            &.original-loading {
                filter: blur(2px);
                -webkit-filter: blur(2px);
            }

            &.light {
                box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.07);
            }

            &.draggable {
                cursor: grab;
            }

            &.dragging {
                cursor: grabbing;
                transition: scale 0.3s ease-in-out;
            }

            &.zoom-changing {
                transition: transform 0.3s ease-in-out;
            }

            &.anim-from-top {
                transform: translate(var(--dragOffsetX), calc(var(--dragOffsetY) - 60px));
            }

            &.anim-from-bottom {
                transform: translate(var(--dragOffsetX), calc(var(--dragOffsetY) + 60px));
            }
        }
    }

    .load-error-container {
        color: var(--danger-red);
    }
</style>