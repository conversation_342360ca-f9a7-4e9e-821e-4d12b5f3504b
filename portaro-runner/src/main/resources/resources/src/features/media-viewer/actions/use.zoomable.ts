import type {ActionReturn} from 'svelte/action';

interface Attributes {
    'on:zoom': (e: CustomEvent<number>) => void;
}

interface Parameters {
    disabled: boolean;
}

export const zoomable = (
    node: HTMLElement,
    params: { disabled: boolean } = {disabled: false}
): ActionReturn<Parameters, Attributes> => {
    let isDisabled = params.disabled;

    const wheelEventHandler = (event: WheelEvent) => {
        if (isDisabled) {
            return;
        }

        // Only allow zoom when pointer is above .mv-viewed-content
        const targetElement = (event.target as HTMLElement)?.closest('.mv-viewed-content');
        if (!targetElement) {
            return;
        }

        event.preventDefault();

        // When ctrl key is down, that usually means it is trackpad zooming gesture
        const divider = event.ctrlKey ? 125 : 325;
        const delta = Math.exp(-event.deltaY / divider);
        node.dispatchEvent(new CustomEvent<number>('zoom', {detail: delta}));
    }

    document.addEventListener('wheel', wheelEventHandler, {passive: false});

    return {
        update(parameters) {
            isDisabled = parameters.disabled;
        },
        destroy() {
            document.removeEventListener('wheel', wheelEventHandler);
        }
    }
}