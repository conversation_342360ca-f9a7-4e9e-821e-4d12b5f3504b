<script lang="ts">
    import type {Rec} from 'typings/portaro.be.types';
    import {KpCollectionSingleInsertButtonPresenter} from './kp-collection-inser-insert-button.presenter';
    import {getInjector, getLocalization} from 'core/svelte-context/context';

    export let record: Rec;

    const localize = getLocalization();
    const presenter = getInjector().getByToken<KpCollectionSingleInsertButtonPresenter>(KpCollectionSingleInsertButtonPresenter.presenterName);

    let isDisabled = false;

    async function insert() {
        isDisabled = true;
        try {
            await presenter.insertToCollection(record);
        } finally {
            isDisabled = false;
        }
    }

</script>

<button type="button"
        class="btn btn-block btn-default"
        disabled="{isDisabled}"
        on:click={insert}>
    {localize(/* @kp-localization recordCollections.AddToCollection */ 'recordCollections.AddToCollection')}
</button>