import type {
    BasicUser,
    Department,
    Document,
    Event,
    HierarchyTreeNode,
    Identified,
    LabeledIdentified,
    Link,
    Ordered,
    Rec,
    UUID
} from 'typings/portaro.be.types';
import type {RECORD_COLLECTION_CATEGORY_ID} from './constants';


export interface RecordCollectionViewConfiguration {
    loansEnabled: boolean;
    exportsEnabled: boolean;
    exportLinks: Link[];
    printExportLinks: Link[];
}

export type RecordCollectionCategoryId = typeof RECORD_COLLECTION_CATEGORY_ID[keyof typeof RECORD_COLLECTION_CATEGORY_ID];

export interface RecordCollectionCategory extends Identified<RecordCollectionCategoryId> {
    type: string;
    description: string;
}

export interface RecordCollection extends LabeledIdentified<UUID>, Ordered {
    parentId?: UUID;
    department: Department;
    name: string;
    creationEventId: UUID;
    note?: string;
    recordCollectionCategory: RecordCollectionCategory;
    owner: BasicUser;
}

export interface RecordCollectionItem extends Identified<UUID>, Ordered {
    recordCollectionId: UUID
    record: Document;
}

export interface RecordCollectionItemEntity extends Identified<UUID>, Ordered {
    recordCollectionId: UUID
    recordId: UUID;
}

export interface RecordCollectionInsertRequest {
    record: Rec;
    recordCollection: RecordCollection;
}

export interface RecordCollectionBulkInsertRequest {
    records: Rec[];
    recordCollection: RecordCollection;
}

export interface ViewableRecordCollection extends RecordCollection {
    deletable: boolean;
    editable: boolean;
    canCreateSubcollection: boolean;
}

export interface RecordCollectionDetail {
    creationEvent: Event;
    lastUpdateEvent: Event;
}

export type RecordCollectionHierarchyTreeNode = HierarchyTreeNode<UUID>