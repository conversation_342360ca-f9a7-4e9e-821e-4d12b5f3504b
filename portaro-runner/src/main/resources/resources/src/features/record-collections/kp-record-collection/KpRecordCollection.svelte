<script lang="ts">
    import type {RecordCollectionItemEvent} from '../kp-record-collection-pageable-list/events';
    import type {RecordCollectionEventMap} from './events';
    import type {RecordCollectionItem, RecordCollectionViewConfiguration} from '../types';
    import type {RecordCollectionManager} from '../record-collection-manager';
    import {onDestroy, createEventDispatcher} from 'svelte';
    import {KpRecordCollectionPresenter} from './kp-record-collection.presenter';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {UPDATE} from './events';
    import KpRecordCollectionPageableList from '../kp-record-collection-pageable-list/KpRecordCollectionPageableList.svelte';
    import KpLinksMenu from 'shared/components/kp-links-menu/KpLinksMenu.svelte';
    import KpDropdownMenuButton from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuButton.svelte';
    import KpDropdownMenuItem from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuItem.svelte';
    import KpDropdownMenuButtonGroup from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuButtonGroup.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let recordCollectionManager: RecordCollectionManager;
    export let viewConfiguration: RecordCollectionViewConfiguration;

    const dispatch = createEventDispatcher<RecordCollectionEventMap>();
    const localize = getLocalization();
    const presenter = getInjector().getByToken<KpRecordCollectionPresenter>(KpRecordCollectionPresenter.presenterName);

    const recordCollection = recordCollectionManager.getRecordCollection();
    const isEditable = recordCollection.editable;

    const pageableList = recordCollectionManager.getPageableList();
    let collectionSize = recordCollectionManager.getSize();
    let subcollectionsSize = recordCollectionManager.getSubCollectionsSize();
    let isCollectionEmpty = recordCollectionManager.isEmpty();
    let isChangeSortOrderDisabled = false;

    onDestroy(() => {
        // reset list in order to be reloaded in ascending order during next usage
        if (!pageableList.isSortedInAscendingOrder()) {
            pageableList.setAscendingSortingOrder(true);
            pageableList.reset();
        }
    });

    let isRequestableAll: boolean;
    $: isRequestableAll = !isCollectionEmpty && (collectionSize + subcollectionsSize) <= 10 && viewConfiguration.loansEnabled;

    function updateCollectionsInformation() {
        collectionSize = recordCollectionManager.getSize();
        subcollectionsSize = recordCollectionManager.getSubCollectionsSize();
        isCollectionEmpty = recordCollectionManager.isEmpty();
        dispatch(UPDATE);
    }

    async function removeItem(event: RecordCollectionItemEvent) {
        await presenter.removeItem(event.detail.item, recordCollectionManager);
        updateCollectionsInformation();
    }

    async function removeAll() {
        await presenter.removeAll(recordCollectionManager);
        updateCollectionsInformation();
    }

    async function removeAllIncludingSubCollections() {
        await presenter.removeAllIncludingSubCollections(recordCollectionManager);
        updateCollectionsInformation();
    }

    function swapItems(item1: RecordCollectionItem, item2: RecordCollectionItem) {
        pageableList.replaceItem({...item1, order: item2.order});
        pageableList.replaceItem({...item2, order: item1.order});
        sortList();
    }

    function swapBackItems(item1: RecordCollectionItem, item2: RecordCollectionItem) {
        pageableList.replaceItem(item1);
        pageableList.replaceItem(item2);
        sortList();
    }

    async function moveItemUp(event: RecordCollectionItemEvent) {
        const selected = event.detail.item;
        if (!presenter.canMoveUpItem(selected, pageableList)) {
            throw new Error('Unable to move item up');
        }
        const previous = presenter.findPreviousItem(selected, pageableList);
        swapItems(previous, selected); // optimistic update
        try {
            await presenter.swapItemsPersistently(previous, selected, recordCollectionManager); // persist change
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (e) {
            swapBackItems(previous, selected); // rollback
        }
    }

    async function moveItemDown(event: RecordCollectionItemEvent) {
        const selected = event.detail.item;
        if (!presenter.canMoveDownItem(selected, pageableList)) {
            throw new Error('Unable to move item down');
        }
        const next = presenter.findNextItem(selected, pageableList);
        swapItems(selected, next); // optimistic update
        try {
            await presenter.swapItemsPersistently(selected, next, recordCollectionManager); // persist change
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (e) {
            swapBackItems(selected, next); // rollback
        }
    }

    function sortList() {
        presenter.sortPageableList(pageableList);
    }

    function exportCollectionRecordsByMail() {
        presenter.exportCollectionRecordsByMail(recordCollection);
    }

    function requestAll() {
        if (!isRequestableAll) {
            throw new Error('Can not request all documents');
        }
        presenter.requestCollectionItems(pageableList.loadedItems);
    }

    function changeSortOrderToAscending() {
        return setSortOrder(true);
    }

    function changeSortOrderToDescending() {
        return setSortOrder(false);
    }

    async function setSortOrder(isAscending: boolean) {
        if (isAscending === pageableList.isSortedInAscendingOrder() || isChangeSortOrderDisabled) {
            return;
        }
        isChangeSortOrderDisabled = true;
        pageableList.setAscendingSortingOrder(isAscending);
        await pageableList.resetAndReloadFirstPage();
        isChangeSortOrderDisabled = false;
    }
</script>

{#if !isCollectionEmpty}
    <div class="buttons-panel">
        {#if isEditable}
            {#if subcollectionsSize === 0}
                <button type="button"
                        class="btn btn-default"
                        on:click={removeAll}>

                    {localize(/* @kp-localization recordCollections.removeAll */ 'recordCollections.removeAll')}
                </button>
            {:else }
                <KpDropdownMenuButtonGroup on:click={removeAll}
                                           optionsLabel="{localize(/* @kp-localization recordCollections.removeAll */ 'recordCollections.removeAll')} ({localize(/* @kp-localization commons.Options */ 'commons.Options')})">

                    <svelte:fragment slot="button">
                        {localize(/* @kp-localization recordCollections.removeAll */ 'recordCollections.removeAll')}
                    </svelte:fragment>

                    <svelte:fragment slot="menu">
                        <KpDropdownMenuItem on:click={removeAllIncludingSubCollections}>
                            {localize(/* @kp-localization recordCollections.includingSubcollections */ 'recordCollections.includingSubcollections')}
                        </KpDropdownMenuItem>
                    </svelte:fragment>
                </KpDropdownMenuButtonGroup>
            {/if}
        {/if}

        <KpLinksMenu
            links={viewConfiguration.printExportLinks}
            dropdownButtonText={localize(/* @kp-localization commons.tisk */ 'commons.tisk')}
            singleButtonOverwriteLinkText={localize(/* @kp-localization commons.tisk */ 'commons.tisk')}/>

        <KpLinksMenu
            links={viewConfiguration.exportLinks}
            dropdownButtonText={'Export'}
            singleButtonOverwriteLinkText={'Export'}/>

        <button type="button"
                class="btn btn-default"
                on:click={exportCollectionRecordsByMail}>
            {localize(/* @kp-localization detail.odeslatEmailem */ 'detail.odeslatEmailem')}
        </button>

        {#if isRequestableAll}
            <button type="button"
                    class="btn btn-default requestAllButton"
                    on:click={requestAll}>
                {localize(/* @kp-localization loan.RequestAll */ 'loan.RequestAll')}
            </button>
        {/if}

        <KpDropdownMenuButton>
            <svelte:fragment slot="button">
                <IconedContent trailingIcon="angle-small-down">
                    {localize(/* @kp-localization hledani.razeni.Sort */ 'hledani.razeni.Sort')}
                </IconedContent>
            </svelte:fragment>

            <svelte:fragment slot="menu">
                <KpDropdownMenuItem on:click={() => changeSortOrderToAscending()} isDisabled={isChangeSortOrderDisabled}>
                    {localize(/* @kp-localization hledani.vzestupne */ 'hledani.vzestupne')}
                </KpDropdownMenuItem>

                <KpDropdownMenuItem on:click={() => changeSortOrderToDescending()} isDisabled={isChangeSortOrderDisabled}>
                    {localize(/* @kp-localization hledani.sestupne */ 'hledani.sestupne')}
                </KpDropdownMenuItem>
            </svelte:fragment>
        </KpDropdownMenuButton>
    </div>
{/if}

<KpRecordCollectionPageableList
    {pageableList}
    {recordCollection}
    on:removeItem={removeItem}
    on:moveUpItem={moveItemUp}
    on:moveDownItem={moveItemDown}/>

<style lang="less">
    .buttons-panel {
        margin-bottom: 20px;
        display: flex;
        justify-content: flex-end;
        flex-wrap: wrap;
        gap: 5px;
    }
</style>
