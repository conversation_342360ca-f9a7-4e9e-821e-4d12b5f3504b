import type CurrentAuthService from 'shared/services/current-auth.service';
import type {Auth} from 'typings/portaro.be.types';
import type {RecordCollection} from './types';

export class RecordCollectionsService {
    public static serviceName = 'recordCollectionsService';

    /*@ngInject*/
    constructor(private currentAuthService: CurrentAuthService, private currentAuth: Auth) {
    }

    public isOwner(recordCollection: RecordCollection): boolean {
        return recordCollection.owner.id === this.currentAuth.activeUser.id;
    }

    public canCreateRootCollection(): boolean {
        return this.isAdminOrLibrarian();
    }

    public canShowCreatorAndDepartment(): boolean {
        return this.isAdminOrLibrarian();
    }

    public canShowContextMenuButton(): boolean {
        return this.isAdminOrLibrarian();
    }

    public canShowVisibility(recordCollection: RecordCollection) {
        return this.isAdminOrLibrarian() || this.isOwner(recordCollection);
    }

    private isAdminOrLibrarian(): boolean {
        return this.currentAuthService.hasAnyRole('ROLE_ADMIN', 'ROLE_LIBRARIAN');
    }
}