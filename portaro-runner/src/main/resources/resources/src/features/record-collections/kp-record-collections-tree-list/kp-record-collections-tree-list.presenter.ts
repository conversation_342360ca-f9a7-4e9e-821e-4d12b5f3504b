import type {RecordCollectionsDataService} from '../record-collections.data-service';
import type {Auth, Department, UUID} from 'typings/portaro.be.types';
import {ascendingOrderComparator, byIdOf, findFirstIndex} from 'shared/utils/array-utils';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
import type {LocalizationService} from 'shared/providers/localization.provider';
import type {RecordCollectionsService} from '../record-collections.service';
import {isRecordCollectionCategoryEqual} from '../util';
import {RECORD_COLLECTION_CATEGORY_ID} from '../constants';
import type {RecordCollection, ViewableRecordCollection} from '../types';

export class KpRecordCollectionsTreeListPresenter {
    public static presenterName = 'kpRecordCollectionsTreeListPresenter';

    private HIGH_DEPTH_WARNING_THRESHOLD = 7;

    /*@ngInject*/
    constructor(private recordCollectionsDataService: RecordCollectionsDataService,
                private currentAuth: Auth,
                private currentDepartment: Department,
                private finishedResponseInteractionService: FinishedResponseInteractionService,
                private toastMessageService: ToastMessageService,
                private localizationService: LocalizationService,
                private recordCollectionsService: RecordCollectionsService) {
    }

    public getTreeList(collections: ViewableRecordCollection[]): CollectionsTreeList {
        return this.transformIntoTreeList(collections);
    }

    public loadRecordCollections(): Promise<ViewableRecordCollection[]> {
        try {
            return this.recordCollectionsDataService.queryCollections(this.currentAuth.activeUser, [RECORD_COLLECTION_CATEGORY_ID.PRIVATE, RECORD_COLLECTION_CATEGORY_ID.OTHER], true);
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e);
            throw e;
        }
    }

    public async removeCollection(recordCollection: RecordCollection): Promise<void> {
        try {
            await this.recordCollectionsDataService.removeCollection(recordCollection);
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e);
            throw e;
        }
    }

    public async editCollection(recordCollection: RecordCollection): Promise<RecordCollection> {
        try {
            const request = {
                ...recordCollection,
                collection: recordCollection,
                parentCollection: recordCollection.parentId,
                confirmed: false
            };
            const response = await this.recordCollectionsDataService.editCollection(request);
            return response.savedObject;
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e);
            throw e;
        }
    }

    public showWarningIfTreeDepthOverThreshold(recordCollection: RecordCollection, collections: RecordCollection[]): void {
        const depth = this.getDepth(recordCollection, collections);

        if (depth >= this.HIGH_DEPTH_WARNING_THRESHOLD) {
            this.toastMessageService.showWarning(this.localizationService.get(/* @kp-localization recordCollections.HierarchyIsTooDeepWarning */ 'recordCollections.HierarchyIsTooDeepWarning'));
        }
    }

    public async createCollection(parentCollection: RecordCollection | null): Promise<RecordCollection> {
        try {
            const response = await this.recordCollectionsDataService.createCollection({
                creator: this.currentAuth.activeUser,
                department: this.currentDepartment,
                parentCollection
            });
            return response.savedObject;
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e);
            throw e;
        }
    }

    public moveUpCollection(recordCollection: RecordCollection, collections: RecordCollection[]): [RecordCollection, RecordCollection] {
        const previous = this.findPreviousNeighbour(recordCollection, collections);
        if (!previous) {
            throw new Error('Unable to move item up');
        }
        this.swapCollectionsOrder(recordCollection, previous);
        return [previous, recordCollection];
    }

    public moveDownCollection(recordCollection: RecordCollection, collections: RecordCollection[]): [RecordCollection, RecordCollection] {
        const next = this.findNextNeighbour(recordCollection, collections);
        if (!next) {
            throw new Error('Unable to move item down');
        }
        this.swapCollectionsOrder(recordCollection, next);
        return [recordCollection, next];
    }

    public swapCollectionsOrder(first: RecordCollection, second: RecordCollection): void {
        [first.order, second.order] = [second.order, first.order]
    }

    public async saveSwappedCollections(first: RecordCollection, second: RecordCollection): Promise<void> {
        try {
            await Promise.all([
                this.recordCollectionsDataService.editCollection({
                    ...first,
                    collection: first,
                    parentCollection: first.parentId,
                    confirmed: true
                }),
                this.recordCollectionsDataService.editCollection({
                    ...second,
                    collection: second,
                    parentCollection: second.parentId,
                    confirmed: true
                })
            ]);
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e);
            throw e;
        }
    }

    public canCreateNewCollection(): boolean {
        return this.recordCollectionsService.canCreateRootCollection();
    }

    public refreshSingleCollection(recordCollection: RecordCollection): Promise<ViewableRecordCollection> {
        return this.recordCollectionsDataService.getCollection(recordCollection.id, true);
    }

    public showRecursiveUpdateInfoIfCategoryChanged(oldRecordCollection: RecordCollection, newRecordCollection: RecordCollection, allRecordCollections: RecordCollection[]): void {
        if (!isRecordCollectionCategoryEqual(oldRecordCollection, newRecordCollection) && this.hasSubcollections(newRecordCollection, allRecordCollections)) {
            this.toastMessageService.showInfo(
                this.localizationService.get(/* @kp-localization recordCollections.RecursiveCategoryUpdateInfo */ 'recordCollections.RecursiveCategoryUpdateInfo'),
                this.localizationService.get(/* @kp-localization recordCollections.CategoryChange */ 'recordCollections.CategoryChange'));
        }
    }

    private hasSubcollections(recordCollection: RecordCollection, allRecordCollections: RecordCollection[]): boolean {
        return allRecordCollections.some((collection) => collection.parentId === recordCollection.id);
    }

    private findNextNeighbour<T extends RecordCollection | ViewableRecordCollection>(recordCollection: T, collections: T[]): T | null {
        const sortedSameLevelCollections = collections.filter((collection) => collection.parentId === recordCollection.parentId).sort(ascendingOrderComparator);
        const selfPosition = findFirstIndex(sortedSameLevelCollections, byIdOf(recordCollection));
        return sortedSameLevelCollections[selfPosition + 1];
    }

    private findPreviousNeighbour<T extends RecordCollection | ViewableRecordCollection>(recordCollection: T, collections: T[]): T | null {
        const sortedSameLevelCollections = collections.filter((collection) => collection.parentId === recordCollection.parentId).sort(ascendingOrderComparator);
        const selfPosition = findFirstIndex(sortedSameLevelCollections, byIdOf(recordCollection));
        return sortedSameLevelCollections[selfPosition - 1];
    }

    private findNeighbours<T extends RecordCollection | ViewableRecordCollection>(recordCollection: T, sortedSameLevelCollections: T[]): [previous: T | null, next: T | null] {
        const selfPosition = findFirstIndex(sortedSameLevelCollections, byIdOf(recordCollection));
        const previous = sortedSameLevelCollections[selfPosition - 1];
        const next = sortedSameLevelCollections[selfPosition + 1];
        return [previous, next];
    }

    private transformIntoTreeList(collections: ViewableRecordCollection[]): CollectionsTreeList {
        let rootCollections: ViewableRecordCollection[] = [];
        const subCollections: ViewableRecordCollection[] = [];
        // eslint-disable-next-line @typescript-eslint/prefer-for-of
        for (let i = 0; i < collections.length; i++) { // optimization: for-loop is faster than functional array functions
            const collection = collections[i];
            if (!!collection.parentId) {
                subCollections.push(collection);
            } else {
                rootCollections.push(collection);
            }
        }
        rootCollections = rootCollections.sort(ascendingOrderComparator);
        return {
            nodes: rootCollections
                .flatMap((rootCollection, index) => this.transformIntoTreeListNode(rootCollection, subCollections, 0, index, rootCollections.length, (() => this.findNeighbours(rootCollection, rootCollections))))
        };
    }

    private transformIntoTreeListNode(recordCollection: ViewableRecordCollection, collectionHeap: ViewableRecordCollection[], depth: number, collectionIndex: number, neighboursCount: number, findNeighbours: () => [previous: ViewableRecordCollection, next: ViewableRecordCollection]): CollectionsTreeListNode[] {
        let subCollections: ViewableRecordCollection[] = [];
        const otherCollections: ViewableRecordCollection[] = [];
        // eslint-disable-next-line @typescript-eslint/prefer-for-of
        for (let i = 0; i < collectionHeap.length; i++) { // optimization: for-loop is faster than functional array functions
            const collection = collectionHeap[i];
            if (collection.parentId === recordCollection.id) {
                subCollections.push(collection);
            } else {
                otherCollections.push(collection);
            }
        }
        subCollections = subCollections.sort(ascendingOrderComparator);
        const subcollectionsNodes = subCollections
            .flatMap((subcollection, index) => this.transformIntoTreeListNode(subcollection, otherCollections, depth + 1, index, subCollections.length, (() => this.findNeighbours(subcollection, subCollections))));
        const neighbours = findNeighbours();
        return [{
            id: recordCollection.id,
            depth,
            recordCollection,
            deletable: recordCollection.deletable,
            editable: recordCollection.editable,
            canCreateSubcollection: recordCollection.canCreateSubcollection,
            canMoveUp: recordCollection.editable && (neighbours[0]?.editable ?? false) && collectionIndex > 0,
            canMoveDown: recordCollection.editable && (neighbours[1]?.editable ?? false) && collectionIndex < neighboursCount - 1,
            showCreatorAndDepartment: this.recordCollectionsService.canShowCreatorAndDepartment(),
            showContextMenuButton: this.recordCollectionsService.canShowContextMenuButton(),
            showVisibility: this.recordCollectionsService.canShowVisibility(recordCollection),
        }, ...subcollectionsNodes]
    }

    private getDepth(recordCollection: RecordCollection, collections: RecordCollection[]): number {
        let depth = 0;
        let parentId = recordCollection.parentId;
        while (!!parentId) {
            depth++;
            parentId = collections.find((collection) => collection.id === parentId)?.parentId;
        }
        return depth;
    }


    public transformIntoHierarchyNodes(collections: ViewableRecordCollection[]): CollectionsTreeHierarchyNode[] {
        let rootCollections: ViewableRecordCollection[] = [];
        const subCollections: ViewableRecordCollection[] = [];
        // eslint-disable-next-line @typescript-eslint/prefer-for-of
        for (let i = 0; i < collections.length; i++) { // optimization: for-loop is faster than functional array functions
            const collection = collections[i];
            if (!!collection.parentId) {
                subCollections.push(collection);
            } else {
                rootCollections.push(collection);
            }
        }
        rootCollections = rootCollections.sort(ascendingOrderComparator);
        return rootCollections.flatMap((rootCollection, index) => this.transformIntoHierarchyNode(rootCollection, subCollections, 0, index, rootCollections.length, (() => this.findNeighbours(rootCollection, rootCollections))))

    }

    private transformIntoHierarchyNode(recordCollection: ViewableRecordCollection, collectionHeap: ViewableRecordCollection[], depth: number, collectionIndex: number, neighboursCount: number, findNeighbours: () => [previous: ViewableRecordCollection, next: ViewableRecordCollection]): CollectionsTreeHierarchyNode {
        let subCollections: ViewableRecordCollection[] = [];
        const otherCollections: ViewableRecordCollection[] = [];
        // eslint-disable-next-line @typescript-eslint/prefer-for-of
        for (let i = 0; i < collectionHeap.length; i++) { // optimization: for-loop is faster than functional array functions
            const collection = collectionHeap[i];
            if (collection.parentId === recordCollection.id) {
                subCollections.push(collection);
            } else {
                otherCollections.push(collection);
            }
        }
        subCollections = subCollections.sort(ascendingOrderComparator);
        const subcollectionsNodes = subCollections
            .flatMap((subcollection, index) => this.transformIntoHierarchyNode(subcollection, otherCollections, depth + 1, index, subCollections.length, (() => this.findNeighbours(subcollection, subCollections))));
        const neighbours = findNeighbours();
        return {
            id: recordCollection.id,
            depth,
            recordCollection,
            deletable: recordCollection.deletable,
            editable: recordCollection.editable,
            canCreateSubcollection: recordCollection.canCreateSubcollection,
            canMoveUp: recordCollection.editable && (neighbours[0]?.editable ?? false) && collectionIndex > 0,
            canMoveDown: recordCollection.editable && (neighbours[1]?.editable ?? false) && collectionIndex < neighboursCount - 1,
            showCreatorAndDepartment: this.recordCollectionsService.canShowCreatorAndDepartment(),
            showContextMenuButton: this.recordCollectionsService.canShowContextMenuButton(),
            showVisibility: this.recordCollectionsService.canShowVisibility(recordCollection),
            nodes: subcollectionsNodes
        }
    }


}

export interface CollectionsTreeList {
    nodes: CollectionsTreeListNode[];
}

export interface CollectionsTreeListNode {
    id: UUID;
    depth: number;
    recordCollection: RecordCollection;
    editable: boolean;
    deletable: boolean;
    canCreateSubcollection: boolean;
    canMoveUp: boolean;
    canMoveDown: boolean;
    showCreatorAndDepartment: boolean;
    showContextMenuButton: boolean;
    showVisibility: boolean;
}

export interface CollectionsTreeHierarchyNode extends CollectionsTreeListNode {
    nodes: CollectionsTreeHierarchyNode[];
}