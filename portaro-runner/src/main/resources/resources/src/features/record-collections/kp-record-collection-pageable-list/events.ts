import type {RecordCollectionItem} from '../types';

export const REMOVE_EVENT_TYPE = 'removeItem';
export const MOVE_UP_EVENT_TYPE = 'moveUpItem';
export const MOVE_DOWN_EVENT_TYPE = 'moveDownItem';

export interface RecordCollectionItemEventMap {
    [REMOVE_EVENT_TYPE]: RecordCollectionItemEventDetail;
    [MOVE_UP_EVENT_TYPE]: RecordCollectionItemEventDetail;
    [MOVE_DOWN_EVENT_TYPE]: RecordCollectionItemEventDetail;
}

export interface RecordCollectionItemEventDetail {
    item: RecordCollectionItem;
}

export interface RecordCollectionItemEvent extends CustomEvent {
    detail: RecordCollectionItemEventDetail;
}