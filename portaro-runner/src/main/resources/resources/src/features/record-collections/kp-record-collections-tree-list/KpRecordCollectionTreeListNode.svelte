<script lang="ts">
    import type {CollectionsTreeListNode} from './kp-record-collections-tree-list.presenter';
    import type {RecordCollectionsEventMap} from './events';
    import {createEventDispatcher} from 'svelte';
    import {isPublicCollection} from '../util';
    import {getLocalization} from 'core/svelte-context/context';
    import {exists} from 'shared/utils/custom-utils';
    import {Kind} from 'shared/constants/portaro.constants';
    import KpDropdownMenuButton from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuButton.svelte';
    import KpDropdownMenuItem from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuItem.svelte';
    import KpCollapsibleGenericPanel from 'shared/ui-widgets/panel/KpCollapsibleGenericPanel.svelte';
    import KpCollapsibleGenericPanelToggle from 'shared/ui-widgets/panel/KpCollapsibleGenericPanelToggle.svelte';
    import Label from 'shared/components/kp-label/Label.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let node: CollectionsTreeListNode;

    const dispatch = createEventDispatcher<RecordCollectionsEventMap>();
    const localize = getLocalization();

    $: canPerformAnyAction = node.canCreateSubcollection || node.editable || node.canMoveUp || node.canMoveDown || node.deletable;
    $: isPublic = isPublicCollection(node.recordCollection);
    $: hasNote = exists(node.recordCollection.note);

    function moveUp() {
        dispatch('moveUpItem', {item: node.recordCollection});
    }

    function moveDown() {
        dispatch('moveDownItem', {item: node.recordCollection});
    }

    function edit() {
        dispatch('editItem', {item: node.recordCollection});
    }

    function create() {
        dispatch('createItem', {item: node.recordCollection});
    }

    function remove() {
        dispatch('removeItem', {item: node.recordCollection});
    }
</script>

<KpCollapsibleGenericPanel>
    <div slot="heading" class="collection-panel-heading" let:open>
        <a href="/#!/record-collections/{node.recordCollection.id}"
           class="collection-name panel-title">{node.recordCollection.name}</a>

        <div class="context-buttons">
            {#if node.showVisibility}
                {#if isPublic}
                    <UIcon icon="eye"
                           label="{localize(/* @kp-localization recordCollections.SharedCollection */ 'recordCollections.SharedCollection')}"
                           additionalClasses="visibility-icon"/>
                {:else}
                    <UIcon icon="eye-crossed"
                           label="{localize(/* @kp-localization recordCollections.PrivateCollection */ 'recordCollections.PrivateCollection')}"
                           additionalClasses="visibility-icon"/>
                {/if}
            {/if}

            {#if node.showCreatorAndDepartment || hasNote}
                <KpCollapsibleGenericPanelToggle isOpened="{open}"/>
            {/if}

            {#if node.showContextMenuButton && canPerformAnyAction}
                <KpDropdownMenuButton buttonSize="md">
                    <svelte:fragment slot="button">
                        <UIcon icon="menu-dots" label="{localize(/* @kp-localization commons.Options */ 'commons.Options')}"/>
                    </svelte:fragment>

                    <svelte:fragment slot="menu">
                        <KpDropdownMenuItem on:click={() => create()} showWhen={node.canCreateSubcollection}>
                            <IconedContent icon="plus" justify="start">
                                {localize(/* @kp-localization recordCollections.NewSubcollection */ 'recordCollections.NewSubcollection')}
                            </IconedContent>
                        </KpDropdownMenuItem>

                        <KpDropdownMenuItem on:click={() => edit()} showWhen={node.editable}>
                            <IconedContent icon="pencil" justify="start">
                                {localize(/* @kp-localization commons.Uprava */ 'commons.Uprava')}
                            </IconedContent>
                        </KpDropdownMenuItem>

                        <KpDropdownMenuItem on:click={() => moveUp()} showWhen={node.canMoveUp}>
                            <IconedContent icon="arrow-up" justify="start">
                                {localize(/* @kp-localization commons.MoveUp */ 'commons.MoveUp')}
                            </IconedContent>
                        </KpDropdownMenuItem>

                        <KpDropdownMenuItem on:click={() => moveDown()} showWhen={node.canMoveDown}>
                            <IconedContent icon="arrow-down" justify="start">
                                {localize(/* @kp-localization commons.MoveDown */ 'commons.MoveDown')}
                            </IconedContent>
                        </KpDropdownMenuItem>

                        <KpDropdownMenuItem on:click|once={() => remove()} showWhen={node.deletable}>
                            <IconedContent icon="trash" justify="start">
                                {localize(/* @kp-localization commons.odebrat */ 'commons.odebrat')}
                            </IconedContent>
                        </KpDropdownMenuItem>
                    </svelte:fragment>
                </KpDropdownMenuButton>
            {/if}
        </div>
    </div>

    <svelte:fragment>
        {#if node.showCreatorAndDepartment}
            <div>
                <strong>{localize(/* @kp-localization commons.autor */ 'commons.autor')}:</strong>
                <Label labeled={node.recordCollection.owner} explicitKind={Kind.KIND_USER}/>
            </div>

            <div>
                <strong>{localize(/* @kp-localization commons.Department */ 'commons.Department')}:</strong>
                <span>{node.recordCollection.department.text}</span>
            </div>
        {/if}

        {#if hasNote}
            <div>
                <strong>{localize(/* @kp-localization recordCollections.Note */ 'recordCollections.Note')}:</strong>
                {node.recordCollection.note}
            </div>
        {/if}
    </svelte:fragment>
</KpCollapsibleGenericPanel>

<style lang="less">
    .collection-panel-heading {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;

        .collection-name {
            font-size: 1.5em;
        }

        .context-buttons {
            display: flex;
            gap: 1ch;
            align-items: center;
        }
    }

    :global(.visibility-icon) {
        // simulate width of icon button (padding is width of a button button - width of icon)
        padding-right: calc((38px - 14px) / 2);
        padding-left: calc((38px - 14px) / 2);
    }
</style>