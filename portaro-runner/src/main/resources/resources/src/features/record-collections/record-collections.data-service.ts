import type {ActionResponse, BasicUser, ConfirmableRequest, Department, FinishedSaveResponse, HierarchyTree, Pageable, UUID} from 'typings/portaro.be.types';
import {ngAsync} from 'shared/utils/ng-@decorators';
import {transferify} from 'shared/utils/data-service-utils';

import type {RecordCollection, RecordCollectionBulkInsertRequest, RecordCollectionCategory, RecordCollectionDetail, RecordCollectionHierarchyTreeNode, RecordCollectionInsertRequest, RecordCollectionItem, RecordCollectionItemEntity, RecordCollectionViewConfiguration, ViewableRecordCollection} from './types';
import type {AjaxService} from 'core/data-services/ajax.service';


export class RecordCollectionsDataService {
    public static serviceName = 'recordCollectionsDataService';

    private static RECORD_COLLECTION_COLLECTIONS_ROUTE = 'record-collections/collections';
    private static RECORD_COLLECTION_ITEMS_ROUTE = 'record-collections/items';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async queryCollectionItems(recordCollection: RecordCollection, pageable: Pageable, ascending = true): Promise<RecordCollectionItem[]> {
        return this.ajaxService
            .createRequest(`${RecordCollectionsDataService.RECORD_COLLECTION_ITEMS_ROUTE}`)
            .get({recordCollection: recordCollection.id, ...pageable, ascending});
    }

    @ngAsync()
    public async insertSingleCollectionItem(request: RecordCollectionInsertRequest): Promise<FinishedSaveResponse<RecordCollectionItem>> {
        return this.ajaxService
            .createRequest(`${RecordCollectionsDataService.RECORD_COLLECTION_ITEMS_ROUTE}`)
            .post(transferify(request));
    }

    @ngAsync()
    public async insertMultipleCollectionItems(request: RecordCollectionBulkInsertRequest): Promise<FinishedSaveResponse<RecordCollectionItem[]>> {
        return this.ajaxService
            .createRequest(`${RecordCollectionsDataService.RECORD_COLLECTION_ITEMS_ROUTE}`)
            .post(transferify(request), {bulk: true});
    }

    @ngAsync()
    public async removeCollectionItem(collectionItemId: UUID): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${RecordCollectionsDataService.RECORD_COLLECTION_ITEMS_ROUTE}/${collectionItemId}`)
            .delete();
    }

    @ngAsync()
    public async swapCollectionItems(collectionItem1: RecordCollectionItem, collectionItem2: RecordCollectionItem): Promise<FinishedSaveResponse<[RecordCollectionItem, RecordCollectionItem]>> {

        return this.ajaxService
            .createRequest(`${RecordCollectionsDataService.RECORD_COLLECTION_ITEMS_ROUTE}`)
            .put(null, transferify({item1: collectionItem1, item2: collectionItem2}));
    }

    @ngAsync()
    public async getCollectionIndex(collection: RecordCollection): Promise<RecordCollectionItemEntity[]> {
        return this.ajaxService
            .createRequest(`${RecordCollectionsDataService.RECORD_COLLECTION_COLLECTIONS_ROUTE}/${collection.id}/index`)
            .get();
    }

    @ngAsync()
    public async removeAllFromCollection(collection: RecordCollection, includingSubCollections?: boolean): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${RecordCollectionsDataService.RECORD_COLLECTION_COLLECTIONS_ROUTE}/${collection.id}/clear`)
            .delete({includingSubCollections});
    }

    @ngAsync()
    public async exportCollectionAsEmail(collection: RecordCollection): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${RecordCollectionsDataService.RECORD_COLLECTION_COLLECTIONS_ROUTE}/${collection.id}/templated-mail`)
            .post(transferify({collection}));
        }

    @ngAsync()
    public async getPageSettings(collectionId: UUID): Promise<RecordCollectionViewConfiguration> { // use just ID instead of collection.id to save one request just to get collection
        return this.ajaxService
            .createRequest(`${RecordCollectionsDataService.RECORD_COLLECTION_COLLECTIONS_ROUTE}/${collectionId}/page-settings`)
            .get();
    }

    public queryCollections(user: BasicUser, categories?: UUID[], viewable?: false): Promise<RecordCollection[]>;
    public queryCollections(user: BasicUser, categories?: UUID[], viewable?: true): Promise<ViewableRecordCollection[]>;
    @ngAsync()
    public async queryCollections(user: BasicUser, categories?: UUID[], viewable = false): Promise<RecordCollection[] | ViewableRecordCollection[]> {
        return this.ajaxService
            .createRequest(RecordCollectionsDataService.RECORD_COLLECTION_COLLECTIONS_ROUTE)
            .get({user: user.id, categories, viewable});
    }

    public async getCollection(id: UUID, viewable?: false): Promise<RecordCollection>;
    public async getCollection(id: UUID, viewable?: true): Promise<ViewableRecordCollection>;
    @ngAsync()
    public async getCollection(id: UUID, viewable = false): Promise<RecordCollection | ViewableRecordCollection> {
        return this.ajaxService
            .createRequest(`${RecordCollectionsDataService.RECORD_COLLECTION_COLLECTIONS_ROUTE}/${id}`)
            .get({viewable});
    }

    @ngAsync()
    public async getCollectionDetail(id: UUID): Promise<RecordCollectionDetail> {
        return this.ajaxService
            .createRequest(`${RecordCollectionsDataService.RECORD_COLLECTION_COLLECTIONS_ROUTE}/${id}/detail`)
            .get();

    }

    @ngAsync()
    public async getCollectionHierarchyTree(id: UUID): Promise<HierarchyTree<UUID, RecordCollectionHierarchyTreeNode>> {
        return this.ajaxService
            .createRequest(`${RecordCollectionsDataService.RECORD_COLLECTION_COLLECTIONS_ROUTE}/${id}/hierarchy`)
            .get();
    }

    @ngAsync()
    public async removeCollection(collection: RecordCollection): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${RecordCollectionsDataService.RECORD_COLLECTION_COLLECTIONS_ROUTE}/${collection.id}`)
            .delete();
    }

    @ngAsync()
    public async createCollection(request: { creator: BasicUser, department: Department, parentCollection?: RecordCollection }): Promise<FinishedSaveResponse<RecordCollection>> {
        return this.ajaxService
            .createRequest(RecordCollectionsDataService.RECORD_COLLECTION_COLLECTIONS_ROUTE)
            .post(transferify(request));
    }

    @ngAsync()
    public async editCollection(request: RecordCollectionEditationRequest & ConfirmableRequest): Promise<FinishedSaveResponse<RecordCollection>> {
        return this.ajaxService
            .createRequest(`${RecordCollectionsDataService.RECORD_COLLECTION_COLLECTIONS_ROUTE}/${request.collection.id}`)
            .post(transferify(request));
    }
}

interface RecordCollectionEditationRequest {
    collection: RecordCollection,
    name: string,
    order: number,
    parentCollection?: RecordCollection | UUID,
    note?: string,
    recordCollectionCategory: RecordCollectionCategory | UUID
}