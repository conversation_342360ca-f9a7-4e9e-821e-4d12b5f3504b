import {SortablePageableList} from 'shared/utils/sortable-reactive-pageable-list';
import type {
    ActionResponse,
    FinishedSaveResponse, Pageable,
    Rec,
    UUID
} from 'typings/portaro.be.types';
import type {State} from 'shared/constants/portaro.constants';
import {states} from 'shared/constants/portaro.constants';
import type {Observable} from 'rxjs';
import {BehaviorSubject, Subject} from 'rxjs';
import {distinctUntilChanged} from 'rxjs/operators';
import type {RecordCollectionItem, RecordCollectionItemEntity, ViewableRecordCollection} from './types';

export interface RecordCollectionDataSource {
    insertSingle(record: Rec): Promise<FinishedSaveResponse<RecordCollectionItem>>;

    insertMultiple(records: Rec[]): Promise<FinishedSaveResponse<RecordCollectionItem[]>>;

    getCollectionItemsIndex(): Promise<RecordCollectionItemEntity[]>;

    query(pageable: Pageable, ascending: boolean): Promise<RecordCollectionItem[]>

    remove(collectionItemId: UUID): Promise<ActionResponse>;

    removeAll(includingSubCollections: boolean): Promise<ActionResponse>;

    swapItems(collectionItems: [RecordCollectionItem, RecordCollectionItem]): Promise<FinishedSaveResponse<[RecordCollectionItem, RecordCollectionItem]>>
}

export type RecordCollectionUpdateType = 'insert' | 'remove' | 'load' | 'update';

export interface RecordCollectionUpdateEvent {
    type: RecordCollectionUpdateType
}

export class RecordCollectionManager {
    private initializedPromise: Promise<void>;
    private recordCollectionLoadedItemsList: SortablePageableList<RecordCollectionItem, UUID>;
    private recordCollectionItemsIndex: RecordCollectionItemEntity[] = [];
    private itemsInProcessingCount = 0;
    private state$: Subject<State> = new BehaviorSubject<State>(states.INITIAL);
    private collectionUpdateEvent$: Subject<RecordCollectionUpdateEvent> = new Subject<RecordCollectionUpdateEvent>();

    constructor(private source: RecordCollectionDataSource, private collection: ViewableRecordCollection) {
        this.initializedPromise = null;
    }

    public getRecordCollection(): ViewableRecordCollection {
        return this.collection;
    }

    public getState$(): Observable<State> {
        return this.state$.pipe(distinctUntilChanged());
    }

    public getCollectionUpdateEvent$(): Observable<RecordCollectionUpdateEvent> {
        return this.collectionUpdateEvent$.asObservable();
    }

    public async initializeManager(): Promise<void> {
        if (this.initializedPromise === null) {
            this.initializedPromise = this.init(); // initialize only for the first initializeManager() call
        }
        await this.initializedPromise;
    }

    public async insertRecord(record: Rec): Promise<ActionResponse> {
        const result = await this.insert(record);
        await this.reloadLoadedItemsListIfAlreadyLoaded();
        return result;
    }

    public async insertRecords(records: Rec[]): Promise<FinishedSaveResponse<RecordCollectionItem[]>>{
        const results = await this.insertAll(records);
        await this.reloadLoadedItemsListIfAlreadyLoaded();
        return results;
    }

    public async removeFirstMatchingRecord(record: Rec): Promise<ActionResponse> {
        const collectionIndexItem = this.recordCollectionItemsIndex.find((indexItem) => indexItem.recordId === record.id);
        const result = await this.remove(record, collectionIndexItem);
        if (this.recordCollectionLoadedItemsList.isPartiallyLoaded) {
            await this.recordCollectionLoadedItemsList.removeItemById(collectionIndexItem.id);
        }
        return result;
    }

    public async removeCollectionItem(item: RecordCollectionItem): Promise<ActionResponse> {
        const collectionIndexItem = this.recordCollectionItemsIndex.find((indexItem) => indexItem.id === item.id);
        const result = await this.remove(item.record, collectionIndexItem);
        if (this.recordCollectionLoadedItemsList.isPartiallyLoaded) {
            await this.recordCollectionLoadedItemsList.removeItemById(collectionIndexItem.id);
        }
        return result;
    }

    public async removeAll(): Promise<ActionResponse> {
        this.startProcessing();

        let result: ActionResponse;
        try {
            result = await this.source.removeAll(false);
            this.recordCollectionItemsIndex = this.recordCollectionItemsIndex.filter((indexItem) => indexItem.recordCollectionId !== this.collection.id);
            await this.reloadLoadedItemsListIfAlreadyLoaded();

        } finally {
            this.endProcessing();
        }

        this.emitCollectionUpdateEvent('remove');
        return result;
    }

    public async removeAllIncludingSubCollections(): Promise<ActionResponse> {
        this.startProcessing();

        let result: ActionResponse;
        try {
            result = await this.source.removeAll(true);
            this.recordCollectionItemsIndex = [];
            this.recordCollectionLoadedItemsList.removeAll();

        } finally {
            this.endProcessing();
        }

        this.emitCollectionUpdateEvent('remove');
        return result;
    }

    public async swapRecordCollectionItems(recordCollectionItem1: RecordCollectionItem, recordCollectionItem2: RecordCollectionItem): Promise<FinishedSaveResponse<[RecordCollectionItem, RecordCollectionItem]>> {
        this.startProcessing();
        this.throwIfNotContained(recordCollectionItem1.record);
        this.throwIfNotContained(recordCollectionItem2.record);

        let result: FinishedSaveResponse<[RecordCollectionItem, RecordCollectionItem]>;
        try {
            result = await this.source.swapItems([recordCollectionItem1, recordCollectionItem2]);
            if (this.recordCollectionLoadedItemsList.isPartiallyLoaded) {
                this.recordCollectionLoadedItemsList.replaceItem(result.savedObject[0]);
                this.recordCollectionLoadedItemsList.replaceItem(result.savedObject[1]);
            }
        } finally {
            this.endProcessing();
        }

        this.emitCollectionUpdateEvent('update');
        return result;
    }

    public getSize(): number {
        return this.recordCollectionItemsIndex.filter((indexItem) => indexItem.recordCollectionId === this.collection.id).length;
    }

    public getSubCollectionsSize(): number {
        return this.recordCollectionItemsIndex.filter((indexItem) => indexItem.recordCollectionId !== this.collection.id).length;
    }

    public isEmpty(): boolean {
        return this.recordCollectionItemsIndex.length === 0;
    }

    public getPageableList(): SortablePageableList<RecordCollectionItem, UUID> {
        return this.recordCollectionLoadedItemsList;
    }

    public contains(record: Rec): boolean {
        return !!this.recordCollectionItemsIndex.find((indexItem) => indexItem.recordId === record.id);
    }

    public getState(): State {
        return this.itemsInProcessingCount === 0 ? states.NORMAL : states.PROCESSING;
    }

    private async init() {
        this.startProcessing();

        try {
            this.recordCollectionItemsIndex = await this.source.getCollectionItemsIndex();
            this.recordCollectionLoadedItemsList = new SortablePageableList<RecordCollectionItem, UUID>({
                loadListItems:(pageable, {isAscending}) => this.source.query(pageable, isAscending),
                loadListSize: () => Promise.resolve({value: this.recordCollectionItemsIndex.length})
            });
        } finally {
            this.endProcessing();
        }

        this.emitCollectionUpdateEvent('load');
    }

    private async insert(record: Rec): Promise<FinishedSaveResponse<RecordCollectionItem>> {
        this.throwIfAlreadyContained(record);
        this.startProcessing();

        let result: FinishedSaveResponse<RecordCollectionItem>;
        try {
            result = await this.source.insertSingle(record);
            this.throwIfAlreadyContained(record);
            const insertedItem = result.savedObject;
            this.recordCollectionItemsIndex.push(this.convertToCollectionItemEntity(insertedItem));
        } finally {
            this.endProcessing();
        }

        this.emitCollectionUpdateEvent('insert');
        return result;
    }

    private async insertAll(records: Rec[]): Promise<FinishedSaveResponse<RecordCollectionItem[]>> {
        records.forEach((record) => this.throwIfAlreadyContained(record));

        this.startProcessing();

        let result: FinishedSaveResponse<RecordCollectionItem[]>;
        try {
            result = await this.source.insertMultiple(records);
            records.forEach((record) => this.throwIfAlreadyContained(record));
            const insertedItems = result.savedObject;
            this.recordCollectionItemsIndex = this.recordCollectionItemsIndex.concat(insertedItems.map((item) => this.convertToCollectionItemEntity(item)));
        } finally {
            this.endProcessing();
        }

        this.emitCollectionUpdateEvent('insert');
        return result;
    }

    private async remove(record: Rec, collectionIndexItem: RecordCollectionItemEntity): Promise<ActionResponse> {
        this.throwIfNotContained(record);
        this.startProcessing();

        let result: ActionResponse;
        try {
            result = await this.source.remove(collectionIndexItem.id);
            this.throwIfNotContained(record);
            this.recordCollectionItemsIndex = this.recordCollectionItemsIndex.filter((indexItem) => indexItem.id !== collectionIndexItem.id);
        } finally {
            this.endProcessing();
        }

        this.emitCollectionUpdateEvent('remove');
        return result;
    }

    private convertToCollectionItemEntity(collectionItem: RecordCollectionItem): RecordCollectionItemEntity {
        return {...collectionItem, recordId: collectionItem.record.id};
    }

    private async reloadLoadedItemsListIfAlreadyLoaded() {
        if (this.recordCollectionLoadedItemsList.isPartiallyLoaded) {
            await this.recordCollectionLoadedItemsList.resetAndReloadFirstPage();
        }
    }

    private startProcessing() {
        this.itemsInProcessingCount++;
        this.emitCurrentState();
    }

    private endProcessing() {
        this.itemsInProcessingCount--;
        this.emitCurrentState();
    }

    private emitCollectionUpdateEvent(type: RecordCollectionUpdateType) {
        this.collectionUpdateEvent$.next({type});
    }

    private emitCurrentState() {
        this.state$.next(this.getState());
    }

    private throwIfAlreadyContained(record: Rec) {
        if (this.contains(record)) {
            throw new Error(`Record: ${JSON.stringify(record)} is already contained in the collection`);
        }
    }

    private throwIfNotContained(record: Rec) {
        if (!this.contains(record)) {
            throw new Error(`Record: ${JSON.stringify(record)} is not contained in the collection`);
        }
    }
}