import type {RecordCollectionsDataService} from './record-collections.data-service';
import type {RecordCollectionDataSource} from './record-collection-manager';
import {RecordCollectionManager} from './record-collection-manager';
import type {Pageable, Rec, UUID} from 'typings/portaro.be.types';
import type {RecordCollectionItem, ViewableRecordCollection} from './types';


export class RecordCollectionManagerFactory {
    public static serviceName = 'recordCollectionManagerFactory';

    /*@ngInject*/
    constructor(private recordCollectionsDataService: RecordCollectionsDataService) {
    }

    public async createAndInitialize(recordCollection: ViewableRecordCollection): Promise<RecordCollectionManager> {
        const manager = this.create(recordCollection);
        await manager.initializeManager();
        return manager;
    }

    private create(recordCollection: ViewableRecordCollection): RecordCollectionManager {
        const dataSource: RecordCollectionDataSource = {
            insertSingle: (record: Rec) =>  this.recordCollectionsDataService.insertSingleCollectionItem({record, recordCollection}),
            insertMultiple: (records: Rec[]) => this.recordCollectionsDataService.insertMultipleCollectionItems({records, recordCollection}),
            getCollectionItemsIndex: () => this.recordCollectionsDataService.getCollectionIndex(recordCollection),
            query: (pageable: Pageable, ascending: boolean) => this.recordCollectionsDataService.queryCollectionItems(recordCollection, pageable, ascending),
            remove: (collectionItemId: UUID) => this.recordCollectionsDataService.removeCollectionItem(collectionItemId),
            removeAll: (includingSubCollections: boolean) => this.recordCollectionsDataService.removeAllFromCollection(recordCollection, includingSubCollections),
            swapItems: (collectionItems: [RecordCollectionItem, RecordCollectionItem]) => this.recordCollectionsDataService.swapCollectionItems(...collectionItems),
        }

        return  new RecordCollectionManager(dataSource, recordCollection);
    }
}