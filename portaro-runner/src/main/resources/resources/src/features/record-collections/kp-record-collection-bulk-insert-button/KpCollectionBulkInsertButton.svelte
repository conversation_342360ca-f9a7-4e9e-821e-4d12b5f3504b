<script lang="ts">
    import type {Rec} from 'typings/portaro.be.types';
    import {KpCollectionBulkInsertButtonPresenter} from './kp-collection-bulk-insert-button.presenter';
    import {getInjector, getLocalization} from 'core/svelte-context/context';

    export let records: Rec[];

    const localize = getLocalization();
    const presenter = getInjector().getByToken<KpCollectionBulkInsertButtonPresenter>(KpCollectionBulkInsertButtonPresenter.presenterName);

    let isDisabled = false;

    async function insert() {
        isDisabled = true;
        try {
            await presenter.insertMultipleToCollection(records);
        } finally {
            isDisabled = false;
        }
    }
</script>

<button type="button"
        class="btn btn-default btn-xs"
        disabled="{isDisabled}"
        on:click={insert}>
    {localize(/* @kp-localization recordCollections.AddSelectedToCollection */ 'recordCollections.AddSelectedToCollection')}
</button>