import type {RecordCollectionsDataService} from '../record-collections.data-service';
import type {UUID} from 'typings/portaro.be.types';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type LoanRequestService from '../../loan/loan-request.service';
import {isInterruptedActionResponse} from '../../../modals/modal-utils';
import type ModalConversationInterceptorService from '../../../modals/modal-conversation-interceptor.service';
import type {RecordCollectionManager} from '../record-collection-manager';
import type {SortablePageableList} from 'shared/utils/sortable-reactive-pageable-list';
import {
    ascendingOrderComparator, byIdOf,
    compositeComparator,
    descendingOrderComparator,
    findFirstIndex
} from 'shared/utils/array-utils';
import type {RecordCollection, RecordCollectionItem} from '../types';


export class KpRecordCollectionPresenter {
    public static presenterName = 'kpRecordCollectionPresenter';

    /*@ngInject*/
    constructor(private recordCollectionsDataService: RecordCollectionsDataService,
                private finishedResponseInteractionService: FinishedResponseInteractionService,
                private loanRequestService: LoanRequestService,
                private modalConversationInterceptor: ModalConversationInterceptorService) {
        this.interceptorRegistration();
    }

    public async exportCollectionRecordsByMail(collection: RecordCollection): Promise<void> {
        try {
            const actionResponse = await this.recordCollectionsDataService.exportCollectionAsEmail(collection);
            return this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
        } catch (exceptionResponse) {
            if (!isInterruptedActionResponse(exceptionResponse.data)) {
                return this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            }
        }
    }

    public async requestCollectionItems(recordCollectionItems: RecordCollectionItem[]) {
        const records = recordCollectionItems.map((collectionItem) => collectionItem.record);
        try {
            const actionResponse = await this.loanRequestService.requestDocuments(records);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
        } catch (errorResult) {
            if (!isInterruptedActionResponse(errorResult.data)) {
                await this.finishedResponseInteractionService.showFailedInModalWindow(errorResult);
            }
        }
    }

    public canMoveUpItem(selectedItem: RecordCollectionItem, pageableList: SortablePageableList<RecordCollectionItem, UUID>): boolean {
        const index = findFirstIndex(pageableList.loadedItems, byIdOf(selectedItem));
        const previous = this.findPreviousItem(selectedItem, pageableList);
        return index >= 1 && index < pageableList.loadedItems.length && selectedItem.recordCollectionId === previous.recordCollectionId;
    }

    public canMoveDownItem(selectedItem: RecordCollectionItem, pageableList: SortablePageableList<RecordCollectionItem, UUID>): boolean {
        const index = findFirstIndex(pageableList.loadedItems, byIdOf(selectedItem));
        const next = this.findNextItem(selectedItem, pageableList);
        return index >= 0 && index < pageableList.loadedItems.length - 1 && selectedItem.recordCollectionId === next.recordCollectionId;
    }

    public findPreviousItem(selectedItem: RecordCollectionItem, pageableList: SortablePageableList<RecordCollectionItem, UUID>): RecordCollectionItem {
        const index = findFirstIndex(pageableList.loadedItems, byIdOf(selectedItem));
        return  pageableList.loadedItems[index - 1];
    }

    public findNextItem(selectedItem: RecordCollectionItem, pageableList: SortablePageableList<RecordCollectionItem, UUID>): RecordCollectionItem {
        const index = findFirstIndex(pageableList.loadedItems, byIdOf(selectedItem));
        return  pageableList.loadedItems[index + 1];
    }

    public async swapItemsPersistently(item1: RecordCollectionItem, item2: RecordCollectionItem, recordCollectionManager: RecordCollectionManager) {
        try {
            await recordCollectionManager.swapRecordCollectionItems(item1, item2);
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e);
            throw e;
        }
    }

    public sortPageableList(pageableList: SortablePageableList<RecordCollectionItem, UUID>) {
        const ascendingCollectionIdComparator = (a: RecordCollectionItem, b: RecordCollectionItem) => a.recordCollectionId.localeCompare(b.recordCollectionId);
        const descendingCollectionIdComparator = (a: RecordCollectionItem, b: RecordCollectionItem) => - ascendingCollectionIdComparator(a, b);

        let comparator: (a: RecordCollectionItem, b: RecordCollectionItem) => number;
        if (pageableList.isSortedInAscendingOrder()) {
            comparator = compositeComparator(ascendingCollectionIdComparator, ascendingOrderComparator);
        } else {
            comparator = compositeComparator(descendingCollectionIdComparator, descendingOrderComparator);
        }
        pageableList.sort(comparator);
    }

    public async removeItem(item: RecordCollectionItem, recordCollectionManager: RecordCollectionManager) {
        try {
            await recordCollectionManager.removeCollectionItem(item);
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e);
            throw e;
        }
    }

    public async removeAll(recordCollectionManager: RecordCollectionManager) {
        try {
            await recordCollectionManager.removeAll();
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e);
            throw e;
        }
    }

    public async removeAllIncludingSubCollections(recordCollectionManager: RecordCollectionManager) {
        try {
            await recordCollectionManager.removeAllIncludingSubCollections();
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e);
            throw e;
        }
    }

    private interceptorRegistration() {
        this.modalConversationInterceptor.registerFieldEnablingConfirmationDialogFromExceptionHandler('LoanRequestDocumentWasLoanedInPastException');
    }
}
