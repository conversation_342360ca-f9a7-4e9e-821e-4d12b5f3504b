<script lang="ts">
    import type {RecordCollectionItemEventMap} from './events';
    import type {RecordCollectionItem, ViewableRecordCollection} from '../types';
    import type {Pageable, UUID} from 'typings/portaro.be.types';
    import type {SortablePageableList} from 'shared/utils/sortable-reactive-pageable-list';
    import {createEventDispatcher, onDestroy, onMount} from 'svelte';
    import {debounceTime, map, skipWhile} from 'rxjs/operators';
    import {getLocalization} from 'core/svelte-context/context';
    import {fly} from 'svelte/transition';
    import {flip} from 'svelte/animate';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {MOVE_DOWN_EVENT_TYPE, MOVE_UP_EVENT_TYPE, REMOVE_EVENT_TYPE} from './events';
    import KpDocumentMini from '../../record/kp-document-mini/KpDocumentMini.svelte';
    import KpLoadingInline from 'shared/components/kp-loading/KpLoadingInline.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import KpDropdownMenuButton from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuButton.svelte';
    import KpDropdownMenuItem from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuItem.svelte';
    import KpDropdownMenuLink from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuLink.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpBarebonesTable from 'shared/ui-widgets/table/barebones/KpBarebonesTable.svelte';

    export let pageableList: SortablePageableList<RecordCollectionItem, UUID>;
    export let recordCollection: ViewableRecordCollection;

    const dispatch = createEventDispatcher<RecordCollectionItemEventMap>();
    const localize = getLocalization();

    let items: RecordCollectionItem[] = [];
    let isLoadedAll: boolean;
    let isLoadingNextPage = false;
    let isLoadingFirstPage = true;
    let isInitialized = false;

    let accentedPositions: boolean[];
    $: accentedPositions = calculateAccentedPositions(items);

    // subscribe for updates after an initial load in case already loaded items are not valid (e.g., incorrect sorting order)
    const listItemsSubscription = pageableList.getState$().pipe(
        skipWhile(() => !isInitialized),
        map((state) => state.loadedItems ?? []),
        debounceTime(100)
    ).subscribe((updatedItems) => updateListItems(updatedItems));

    const listPagesSubscription = pageableList.getState$().subscribe((state) => updateListLoadingState(state.nextPage));

    onMount(async () => {
        items = await pageableList.loadFirstPage();
        isLoadedAll = pageableList.isAllLoaded;
        isLoadingFirstPage = false;
        isInitialized = true;
    });

    onDestroy(() => {
        listItemsSubscription.unsubscribe();
        listPagesSubscription.unsubscribe();
    });

    function remove(item: RecordCollectionItem) {
        dispatch(REMOVE_EVENT_TYPE, {item});
    }

    function moveUp(item: RecordCollectionItem) {
        dispatch(MOVE_UP_EVENT_TYPE, {item});
    }

    function moveDown(item: RecordCollectionItem) {
        dispatch(MOVE_DOWN_EVENT_TYPE, {item});
    }

    function updateListItems(updatedItems: RecordCollectionItem[]) {
        items = updatedItems;
        isLoadedAll = pageableList.isAllLoaded;
        isLoadingFirstPage = false;
    }

    function updateListLoadingState(page: Pageable) {
        // in case of list reload, show loading animation
        if (page.pageNumber === 1) {
            isLoadingFirstPage = true;
        }
    }

    async function loadNextPage() {
        isLoadingNextPage = true;
        await pageableList.loadNextPage();
        isLoadingNextPage = false;
    }

    function calculateAccentedPositions(newItems: RecordCollectionItem[]): boolean[] {
        const result: boolean[] = [];

        for (let i = 0; i < newItems.length; i++) {
            if (i === 0) {
                result[i] = false;
                continue;
            }
            const prev = newItems[i - 1].recordCollectionId;
            const curr = newItems[i].recordCollectionId;

            if (curr !== prev) {
                result[i] = !result[i - 1];
            } else {
                result[i] = result[i - 1];
            }
        }
        return result;
    }

    function isAccented(index: number): boolean {
        return accentedPositions[index];
    }

    function canMoveUp(item: RecordCollectionItem, index: number): boolean {
        return index > 0 && items[index - 1].recordCollectionId === item.recordCollectionId && recordCollection.editable; // if top level collection is editable so are subcollections
    }

    function canMoveDown(item: RecordCollectionItem, index: number): boolean {
        return index < items.length - 1 && items[index + 1].recordCollectionId === item.recordCollectionId && recordCollection.editable; // if top level collection is editable so are subcollections
    }

    function canRemove(): boolean {
        return recordCollection.editable;
    }

    function canShowSubcollectionLink(item: RecordCollectionItem): boolean {
        return recordCollection.id !== item.recordCollectionId;
    }

    function canPerformAnyActionOnItem(item: RecordCollectionItem, index: number): boolean {
        return canMoveUp(item, index) || canMoveDown(item, index) || canRemove() || canShowSubcollectionLink(item);
    }
</script>

{#if isLoadingFirstPage}
    <KpLoadingBlock/>
{:else }
    <KpBarebonesTable responsive colorAccented rowsTopBordered headerFooterDivided>
        <tr slot="header">
            <th>{localize(/* @kp-localization commons.nazevDokumentu */ 'commons.nazevDokumentu')}</th>
            <th>{localize(/* @kp-localization commons.fond */ 'commons.fond')}</th>
            {#if items.some((item, index) => canPerformAnyActionOnItem(item, index))}
                <th>{localize(/* @kp-localization commons.Options */ 'commons.Options')}</th>
            {/if}
        </tr>

        <svelte:fragment slot="body">
            {#each items as item, index (item.id)}
                <tr class:accented-item={isAccented(index)} in:fly={{duration: 250, y: 50, delay: 200}}
                    animate:flip={{duration: 250}}> <!-- TODO: find better workaround for svelte transition+animation bug: https://github.com/sveltejs/svelte/issues/4910 -->
                    <td>
                        <KpDocumentMini document={item.record}/>
                    </td>

                    <td>
                        {pipe(item.record.fond, loc())}
                    </td>

                    <td>
                        {#if canPerformAnyActionOnItem(item, index)}
                            <KpDropdownMenuButton buttonSize="sm">
                                <svelte:fragment slot="button">
                                    <UIcon icon="menu-dots" label="{localize(/* @kp-localization commons.Options */ 'commons.Options')}"/>
                                </svelte:fragment>

                                <svelte:fragment slot="menu">
                                    <KpDropdownMenuItem on:click={() => moveUp(item)} showWhen={canMoveUp(item, index)}>
                                        <IconedContent icon="arrow-up" justify="start">
                                            {localize(/* @kp-localization commons.MoveUp */ 'commons.MoveUp')}
                                        </IconedContent>
                                    </KpDropdownMenuItem>

                                    <KpDropdownMenuItem on:click={() => moveDown(item)} showWhen={canMoveDown(item, index)}>
                                        <IconedContent icon="arrow-down" justify="start">
                                            {localize(/* @kp-localization commons.MoveDown */ 'commons.MoveDown')}
                                        </IconedContent>
                                    </KpDropdownMenuItem>

                                    <KpDropdownMenuItem on:click|once={() => remove(item)} showWhen={canRemove()}>
                                        <IconedContent icon="trash" justify="start">
                                            {localize(/* @kp-localization commons.odebrat */ 'commons.odebrat')}
                                        </IconedContent>
                                    </KpDropdownMenuItem>

                                    <KpDropdownMenuLink href="/#!/record-collections/{item.recordCollectionId}" showWhen={canShowSubcollectionLink(item)}>
                                        <IconedContent icon="arrow-up-right-from-square" justify="start">
                                            {localize(/* @kp-localization recordCollections.ShowSubcollection */ 'recordCollections.ShowSubcollection')}
                                        </IconedContent>
                                    </KpDropdownMenuLink>
                                </svelte:fragment>
                            </KpDropdownMenuButton>
                        {/if}
                    </td>
                </tr>
            {/each}
        </svelte:fragment>
    </KpBarebonesTable>

    {#if !isLoadedAll}
        <KpButton isBlock buttonSize="sm" isDisabled={isLoadingNextPage} on:click={loadNextPage}>
            <span class:hidden={isLoadingNextPage}>
                {localize(/* @kp-localization vysledky.NacistDalsi */ 'vysledky.NacistDalsi')}
            </span>

            <KpLoadingInline size="xs" showWhen={isLoadingNextPage}/>
        </KpButton>
    {/if}
{/if}

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    .hidden {
        display: none;
    }

    .accented-item {
        background-color: @table-bg-accent;
    }
</style>