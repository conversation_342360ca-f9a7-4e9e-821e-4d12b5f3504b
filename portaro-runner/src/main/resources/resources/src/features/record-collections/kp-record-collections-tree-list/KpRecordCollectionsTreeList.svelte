<script lang="ts">
    import type {CollectionsTreeList, CollectionsTreeListNode} from './kp-record-collections-tree-list.presenter';
    import type {RecordCollectionsEvent} from './events';
    import type {ViewableRecordCollection} from '../types';
    import {KpRecordCollectionsTreeListPresenter} from './kp-record-collections-tree-list.presenter';
    import {VirtualScroll} from 'svelte-virtual-scroll-list';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {replaceAll, byIdOf} from 'shared/utils/array-utils';
    import {fade} from 'svelte/transition';
    import {flip} from 'svelte/animate';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import KpRecordCollectionTreeListNode from './KpRecordCollectionTreeListNode.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    const localize = getLocalization();
    const presenter = getInjector().getByToken<KpRecordCollectionsTreeListPresenter>(KpRecordCollectionsTreeListPresenter.presenterName);

    let allCollections: ViewableRecordCollection[];
    let treeList: CollectionsTreeList;

    function constructTreeList() {
        treeList = presenter.getTreeList(allCollections);
    }

    async function loadCollectionsAsTreeList() {
        allCollections = await presenter.loadRecordCollections();
        constructTreeList();
    }

    async function moveUpCollection(event: RecordCollectionsEvent) {
        const [selected, next] = presenter.moveUpCollection(event.detail.item, allCollections);
        constructTreeList(); // optimistic update
        try {
            await presenter.saveSwappedCollections(selected, next); // persist change
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (e) {
            presenter.swapCollectionsOrder(selected, next); // rollback
            constructTreeList();
        }
    }

    async function moveDownCollection(event: RecordCollectionsEvent) {
        const [previous, selected] = presenter.moveDownCollection(event.detail.item, allCollections);
        constructTreeList(); // optimistic update
        try {
            await presenter.saveSwappedCollections(selected, previous); // persist change
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (e) {
            presenter.swapCollectionsOrder(selected, previous); // rollback
            constructTreeList();
        }
    }

    async function removeCollection(event: RecordCollectionsEvent) {
        const allCollectionsIncludingRemovedOne = allCollections;
        allCollections = allCollections.filter((c) => c.id !== event.detail.item.id);
        constructTreeList(); // optimistic update
        try {
            await presenter.removeCollection(event.detail.item); // persist change
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (e) {
            allCollections = allCollectionsIncludingRemovedOne; // rollback
            constructTreeList();
        }
    }

    async function editCollection(event: RecordCollectionsEvent) {
        const originalCollection = event.detail.item;
        const changedCollection = await presenter.editCollection(originalCollection);
        const refreshedCollection = await presenter.refreshSingleCollection(changedCollection);
        allCollections = replaceAll(allCollections, byIdOf(refreshedCollection), refreshedCollection);
        constructTreeList();
        presenter.showWarningIfTreeDepthOverThreshold(changedCollection, allCollections);
        await loadCollectionsAsTreeList(); // in case of recursive update, reload whole tree
        presenter.showRecursiveUpdateInfoIfCategoryChanged(originalCollection, changedCollection, allCollections);
    }

    async function createSubCollection(event: RecordCollectionsEvent) {
        const newCollection = await presenter.createCollection(event.detail.item);
        allCollections.push(await presenter.refreshSingleCollection(newCollection));
        constructTreeList();
        presenter.showWarningIfTreeDepthOverThreshold(newCollection, allCollections);
    }

    async function createRootCollection() {
        const newCollection = await presenter.createCollection(null);
        allCollections.push(await presenter.refreshSingleCollection(newCollection));
        constructTreeList();
    }

    // cast because type error from: svelte-virtual-scroll-list TODO: find better solution
    function castToCollectionsTreeListNode(object: any): CollectionsTreeListNode {
        return object as CollectionsTreeListNode;
    }
</script>

<Flex alignItems="center" gap="m" justifyContent="space-between">
    <KpHeading type="h1">
        {localize(/* @kp-localization recordCollections.Collections */ 'recordCollections.Collections')}
    </KpHeading>

    {#if presenter.canCreateNewCollection()}
        <KpButton buttonStyle="success-new" on:click={createRootCollection}>
            <IconedContent icon="add">
                {localize(/* @kp-localization recordCollections.NewCollection */ 'recordCollections.NewCollection')}
            </IconedContent>
        </KpButton>
    {/if}
</Flex>

{#await loadCollectionsAsTreeList()}
    <KpLoadingBlock/>
{:then}
    {#if treeList.nodes.length > 100}
        <!-- use virtual scroll for large lists -->
        <VirtualScroll data={treeList.nodes}
                       key="id"
                       let:data
                       keeps={50}
                       pageMode={true}>

            {@const node = castToCollectionsTreeListNode(data)}

            <div class="collections-list-node" style="--depth-level: {node.depth}">
                <KpRecordCollectionTreeListNode {node}
                                                on:createItem={createSubCollection}
                                                on:editItem={editCollection}
                                                on:removeItem={removeCollection}
                                                on:moveUpItem={moveUpCollection}
                                                on:moveDownItem={moveDownCollection}/>
            </div>
        </VirtualScroll>
    {:else if treeList.nodes.length > 0}
        <!-- use normal list for small lists -->
        {#each treeList.nodes as item (item.recordCollection.id)}
            <div class="collections-list-node" style="--depth-level: {item.depth}" in:fade={{duration: 250}}
                 animate:flip={{duration: 250}}> <!-- TODO: find better workaround for svelte transition+animation bug: https://github.com/sveltejs/svelte/issues/4910 -->
                <!-- transition must be on element not component, animate has to be on single direct descendant of #each block -->
                <KpRecordCollectionTreeListNode node={item}
                                                on:createItem={createSubCollection}
                                                on:editItem={editCollection}
                                                on:removeItem={removeCollection}
                                                on:moveUpItem={moveUpCollection}
                                                on:moveDownItem={moveDownCollection}/>
            </div>
        {/each}
    {:else}
        <!-- no visible collections -->
        <p class="text-center text-muted">{localize(/* @kp-localization recordCollections.CollectionsFeatureInfo */ 'recordCollections.CollectionsFeatureInfo')}</p>
    {/if}
{/await}

<style lang="less">
    @import (reference) "styles/portaro.media-queries.less";

    .collections-list-node {
        padding-left: calc(30px * var(--depth-level));

        @media screen and (max-width: @screen-sm-min) {
            & {
                padding-left: calc(15px * var(--depth-level));
            }
        }
    }
</style>