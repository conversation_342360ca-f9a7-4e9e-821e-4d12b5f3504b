import {RecordCollectionsDataService} from './record-collections.data-service';
import register from '@kpsys/angularjs-register';
import {KpRecordCollectionPresenter} from './kp-record-collection/kp-record-collection.presenter';
import collectionsRoutes from './record-collections.routes';
import {KpRecordCollectionsTreeListPresenter} from './kp-record-collections-tree-list/kp-record-collections-tree-list.presenter';
import {RecordCollectionsService} from './record-collections.service';
import {RecordCollectionManagerFactory} from './record-collection-manager.factory';
import {
    KpRecordCollectionContentPagePresenter
} from './record-collection-content-page/kp-record-collection-content-page.presenter';
import {KpCollectionBulkInsertButtonPresenter} from './kp-record-collection-bulk-insert-button/kp-collection-bulk-insert-button.presenter';
import {KpCollectionSingleInsertButtonPresenter} from './kp-record-collection-single-insert-button/kp-collection-inser-insert-button.presenter';

export default register('portaro.features.record-collections')
    .config(collectionsRoutes)
    .service(RecordCollectionsDataService.serviceName, RecordCollectionsDataService)
    .service(RecordCollectionsService.serviceName, RecordCollectionsService)
    .service(RecordCollectionManagerFactory.serviceName, RecordCollectionManagerFactory)
    .service(KpRecordCollectionPresenter.presenterName, KpRecordCollectionPresenter)
    .service(KpRecordCollectionsTreeListPresenter.presenterName, KpRecordCollectionsTreeListPresenter)
    .service(KpRecordCollectionContentPagePresenter.presenterName, KpRecordCollectionContentPagePresenter)
    .service(KpCollectionBulkInsertButtonPresenter.presenterName, KpCollectionBulkInsertButtonPresenter)
    .service(KpCollectionSingleInsertButtonPresenter.presenterName, KpCollectionSingleInsertButtonPresenter)
    .name();
