import type {RecordCollectionsDataService} from '../record-collections.data-service';
import type {FinishedSaveResponse, Rec} from 'typings/portaro.be.types';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type {RecordCollectionItem} from '../types';

export class KpCollectionSingleInsertButtonPresenter {
    public static presenterName = 'kpCollectionSingleInsertButtonPresenter';

    /*@ngInject*/
    constructor(private recordCollectionsDataService: RecordCollectionsDataService, private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    public async insertToCollection(record: Rec): Promise<FinishedSaveResponse<RecordCollectionItem>> {
        try {
            const response = await this.recordCollectionsDataService.insertSingleCollectionItem({record, recordCollection: null});
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(response);
            return response;
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e);
            throw e;
        }
    }
}