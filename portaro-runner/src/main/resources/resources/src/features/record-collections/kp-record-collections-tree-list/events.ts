import type {RecordCollection} from '../types';

export const CREATE_EVENT_TYPE = 'createItem';
export const EDIT_EVENT_TYPE = 'editItem';
export const REMOVE_EVENT_TYPE = 'removeItem';
export const MOVE_UP_EVENT_TYPE = 'moveUpItem';
export const MOVE_DOWN_EVENT_TYPE = 'moveDownItem';

export interface RecordCollectionsEventMap {
    [REMOVE_EVENT_TYPE]: EventDetail;
    [MOVE_UP_EVENT_TYPE]: EventDetail;
    [MOVE_DOWN_EVENT_TYPE]: EventDetail;
    [CREATE_EVENT_TYPE]: EventDetail;
    [EDIT_EVENT_TYPE]: EventDetail;
}

export interface EventDetail {
    item: RecordCollection;
}

export interface RecordCollectionsEvent extends CustomEvent {
    detail: EventDetail;
}