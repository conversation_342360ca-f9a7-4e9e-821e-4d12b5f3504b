<script lang="ts">
    import type {HierarchyTreeNode, UUID} from 'typings/portaro.be.types';
    import type {RecordCollectionManager} from '../record-collection-manager';
    import {onMount} from 'svelte';
    import {pipe} from 'core/utils';
    import {fade} from 'svelte/transition';
    import {KpRecordCollectionContentPagePresenter} from './kp-record-collection-content-page.presenter';
    import {getDateFormatter, getInjector, getLocalization} from 'core/svelte-context/context';
    import {exists} from 'shared/utils/custom-utils';
    import KpRecordCollection from '../kp-record-collection/KpRecordCollection.svelte';
    import KpGenericHierarchyTreesView from 'shared/components/kp-generic-hierarchy-trees-view/KpGenericHierarchyTreesView.svelte';
    import KpLoadingInline from 'shared/components/kp-loading/KpLoadingInline.svelte';
    import KpPluralizeText from 'shared/components/kp-pluralize-text/KpPluralizeText.svelte';
    import type {
        RecordCollectionDetail,
        RecordCollectionHierarchyTreeNode,
        RecordCollectionViewConfiguration
    } from '../types';

    import KpPageContainer from 'shared/layouts/containers/KpPageContainer.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    export let recordCollectionManager: RecordCollectionManager;
    export let viewConfiguration: RecordCollectionViewConfiguration;

    const localize = getLocalization();
    const dateFormatter = getDateFormatter();
    const presenter = getInjector().getByToken<KpRecordCollectionContentPagePresenter>(KpRecordCollectionContentPagePresenter.presenterName);

    const recordCollection = recordCollectionManager.getRecordCollection();

    let collectionSize = recordCollectionManager.getSize();
    let subcollectionsSize = recordCollectionManager.getSubCollectionsSize();
    let recordCollectionDetail: RecordCollectionDetail;

    onMount(async () => {
        await loadCollectionDetail();
    });

    async function loadCollectionDetail() {
        recordCollectionDetail = await presenter.getRecordCollectionDetail(recordCollection);
    }

    async function updateCollectionsInformation() {
        collectionSize = recordCollectionManager.getSize();
        subcollectionsSize = recordCollectionManager.getSubCollectionsSize();
        await loadCollectionDetail();
    }

    function recastToRecordCollectionTreeNode(node: HierarchyTreeNode<UUID | number>): RecordCollectionHierarchyTreeNode {
        return node as RecordCollectionHierarchyTreeNode;
    }
</script>

<KpPageContainer id="record-collection-content" additionalClasses="kp-record-collection-content-page">
    <KpHeading type="h1">
        {recordCollectionManager.getRecordCollection().name}
    </KpHeading>

    <div class="collections">
        <div class="back-to-collections-link">
            <a href="/#!/record-collections">
                {localize(/* @kp-localization recordCollections.BackToCollections */ 'recordCollections.BackToCollections')}
            </a>
        </div>

        {#await presenter.getRecordCollectionHierarchyTree(recordCollection) then hierarchyTree}
            <div class="record-collection-hierarchy-panel" transition:fade|global={{duration:200}}>
                <KpGenericHierarchyTreesView hierarchyTrees={[hierarchyTree]} let:node>
                    <!-- if the next line is red then IDEA/Svelte plugin still do not support new features (ignore it) -->
                    {@const collectionTreeNode = recastToRecordCollectionTreeNode(node)}
                    {#if collectionTreeNode.current}
                        <strong>{collectionTreeNode.text}</strong>
                    {:else }
                        <a href="/#!/record-collections/{collectionTreeNode.id}">{collectionTreeNode.text}</a>
                    {/if}
                </KpGenericHierarchyTreesView>
            </div>
        {/await}

        {#if exists(recordCollection.note)}
            <p class="record-collection-note">{recordCollection.note}</p>
        {/if}

        <div class="record-collection-detail">
            <span class="creation-date">
                {localize(/* @kp-localization commons.DatumVytvoreni */ 'commons.DatumVytvoreni')}:

                {#if !recordCollectionDetail}
                    <KpLoadingInline size="xs"/>
                {:else}
                    {pipe(recordCollectionDetail.creationEvent.createDate, dateFormatter('d. M. yyyy'))}
                {/if}
            </span>

            <span class="last-update-date">
                {localize(/* @kp-localization commons.LastUpdateDate */ 'commons.LastUpdateDate')}:

                {#if !recordCollectionDetail}
                    <KpLoadingInline size="xs"/>
                {:else}
                    {pipe(recordCollectionDetail.lastUpdateEvent.createDate, dateFormatter('d. M. yyyy'))}
                {/if}
            </span>
        </div>

        <div class="record-collection-size">
            <span class="collection-size-counter">
                <KpPluralizeText
                    count={collectionSize}
                    zeroCountText={localize(/* @kp-localization recordCollections.zadneZaznamy */ 'recordCollections.zadneZaznamy')}
                    oneCountText={localize(/* @kp-localization recordCollections.1Zaznam */ 'recordCollections.1Zaznam')}
                    twoThreeFourCountText={localize(/* @kp-localization recordCollections.xZaznamy */ 'recordCollections.xZaznamy')}
                    moreText={localize(/* @kp-localization recordCollections.xZaznamu */ 'recordCollections.xZaznamu')}/>
                {localize(/* @kp-localization recordCollections.inCollection */ 'recordCollections.inCollection')}
            </span>

            <span class="sub-collections-size-counter">
                <KpPluralizeText
                    count={subcollectionsSize}
                    zeroCountText={localize(/* @kp-localization recordCollections.zadneZaznamy */ 'recordCollections.zadneZaznamy')}
                    oneCountText={localize(/* @kp-localization recordCollections.1Zaznam */ 'recordCollections.1Zaznam')}
                    twoThreeFourCountText={localize(/* @kp-localization recordCollections.xZaznamy */ 'recordCollections.xZaznamy')}
                    moreText={localize(/* @kp-localization recordCollections.xZaznamu */ 'recordCollections.xZaznamu')}/>

                {localize(/* @kp-localization recordCollections.inSubcollections */ 'recordCollections.inSubcollections')}
            </span>
        </div>

        <KpRecordCollection {recordCollectionManager} {viewConfiguration} on:update={updateCollectionsInformation}/>
    </div>
</KpPageContainer>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    .record-collection-hierarchy-panel {
        margin: @padding-large-vertical 0;
        padding: @panel-heading-padding;
        border-radius: @border-radius-base;
        border: 1px solid #d3d3d3;
        box-shadow: 2px 1px 6px #d3d3d3;
    }

    .record-collection-detail, .record-collection-size {
        margin-bottom: 10px;
        display: flex;
        flex-direction: column;
    }
</style>