import type {StateProvider} from '@uirouter/angularjs';
import type {RecordCollectionsDataService} from './record-collections.data-service';
import type {Transition} from '@uirouter/angularjs';
import type {RecordCollectionManagerFactory} from './record-collection-manager.factory';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';


/*@ngInject*/
export default function collectionsRoutes($stateProvider: StateProvider) {
    let recordCollectionsModule: { default: any; };
    let recordCollectionContentModule: { default: any; };
    $stateProvider
        .state({
            name: 'record-collections',
            url: '/record-collections',
            component:  KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => recordCollectionsModule.default,
            },
            lazyLoad: async () => {
                recordCollectionsModule = await import(/* webpackChunkName: "record-collections" */ './record-collections-page/KpRecordCollectionsPage.svelte');
                return null;
            }
        })
        .state({
            name: 'record-collection-content',
            url: '/record-collections/:collectionId',
            component:  KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => recordCollectionContentModule.default,

                /*@ngInject*/
                props: async (recordCollectionsDataService: RecordCollectionsDataService, recordCollectionManagerFactory: RecordCollectionManagerFactory, $transition$: Transition) => {
                    const viewConfiguration = await recordCollectionsDataService.getPageSettings($transition$.params().collectionId);
                    const collection = await recordCollectionsDataService.getCollection($transition$.params().collectionId, true);
                    const recordCollectionManager = await recordCollectionManagerFactory.createAndInitialize(collection);
                    return {viewConfiguration, recordCollectionManager};
                }
            },
            lazyLoad: async () => {
                recordCollectionContentModule = await import(/* webpackChunkName: "record-collection-content" */ './record-collection-content-page/KpRecordCollectionContentPage.svelte');
                return null;
            }
        });
}