import type {
    HierarchyTree,
    UUID
} from 'typings/portaro.be.types';
import type {RecordCollectionsDataService} from '../record-collections.data-service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type {RecordCollection, RecordCollectionDetail, RecordCollectionHierarchyTreeNode} from '../types';

export class KpRecordCollectionContentPagePresenter {
    public static presenterName = 'kpRecordCollectionContentPagePresenter';

    /*@ngInject*/
    constructor(private recordCollectionsDataService: RecordCollectionsDataService,
                private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    public async getRecordCollectionDetail(recordCollection: RecordCollection): Promise<RecordCollectionDetail> {
        try {
            return await this.recordCollectionsDataService.getCollectionDetail(recordCollection.id);
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e);
            throw e;
        }
    }

    public async getRecordCollectionHierarchyTree(recordCollection: RecordCollection): Promise<HierarchyTree<UUID, RecordCollectionHierarchyTreeNode>> {
        try {
            return await this.recordCollectionsDataService.getCollectionHierarchyTree(recordCollection.id);
        } catch (e) {
            this.finishedResponseInteractionService.showFailedResponseInToast(e);
            throw e;
        }
    }
}