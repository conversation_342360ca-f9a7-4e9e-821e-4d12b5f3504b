<script lang="ts">
    import type {Inventory} from '../types';
    import type {ButtonStyle} from 'shared/ui-widgets/types';
    import {pipe} from 'core/utils';
    import {inline, loc, prop} from 'shared/utils/pipes';
    import {getDateFormatter, getInjector, getLocalization} from 'core/svelte-context/context';
    import {InventoryPresenter} from '../inventory.presenter';
    import {isInventoryClosed, isInventoryCreated, isInventoryOpened} from '../utils';
    import {Kind} from 'shared/constants/portaro.constants';
    import {exists} from 'shared/utils/custom-utils';
    import KpCollapsibleMenuWrapper from 'shared/ui-widgets/menu-wrapper/KpCollapsibleMenuWrapper.svelte';
    import KpButtonStyleAnchor from 'shared/ui-widgets/button/KpButtonStyleAnchor.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import Label from 'shared/components/kp-label/Label.svelte';
    import KpChipTag from 'shared/ui-widgets/chip/KpChipTag.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let inventory: Inventory;

    const dateFormatter = getDateFormatter();
    const localize = getLocalization();
    const presenter = getInjector().getByToken<InventoryPresenter>(InventoryPresenter.presenterName);

    function showCapturingForm() {
        presenter.showCapturingFormForInventory(inventory);
    }

    function updateInventory() {
        presenter.updateInventory(inventory);
    }

    function deleteInventory() {
        presenter.deleteInventoryAndRefresh(inventory);
    }

    function getInventoryLabelStyle(inv: Inventory): ButtonStyle {
        if (isInventoryCreated(inv)) {
            return 'info';
        }

        if (isInventoryOpened(inv)) {
            return 'success-new';
        }

        if (isInventoryClosed(inv)) {
            return 'danger-new';
        }

        return 'default';
    }
</script>

<KpCollapsibleMenuWrapper>
    <div slot="main" class="list-item-inventory">
        <div class="list-item-inventory-heading">
            <h2 class="unset-style inventory-name">
                <a href="/#!/inventories/{inventory.id}">{pipe(inventory, loc())}</a>
            </h2>

            <KpChipTag chipStyle={getInventoryLabelStyle(inventory)}>
                {pipe(inventory.inventoryState, loc())}
            </KpChipTag>
        </div>

        <div class="list-item-inventory-main-content">
            <div>
                <div>
                    <span class="text-muted">{localize(/* @kp-localization commons.Vytvoreno */ 'commons.Vytvoreno')}</span>
                    {pipe(inventory.createDate, dateFormatter('d.M.yyyy'))}
                </div>
                {#if exists(inventory.date)}
                    <div>
                        <span class="text-muted">{localize(/* @kp-localization commons.datum */ 'commons.datum')}</span>
                        {pipe(inventory.date, dateFormatter('d.M.yyyy'))}
                    </div>
                {/if}
                {#if exists(inventory.closeDate)}
                    <div>
                        <span class="text-muted">{localize(/* @kp-localization inventory.ClosedAt */ 'inventory.ClosedAt')}</span>{
                        pipe(inventory.closeDate, dateFormatter('d.M.yyyy'))}
                    </div>
                {/if}
                <div>
                    <span class="text-muted">{localize(/* @kp-localization inventory.CreatedBy */ 'inventory.CreatedBy')}</span>
                    <Label labeled={inventory.creator} explicitKind={Kind.KIND_USER}/>
                </div>
                <div>
                    <span class="text-muted">{localize(/* @kp-localization inventory.LastUpdatedBy */ 'inventory.LastUpdatedBy')}</span>
                    <Label labeled={inventory.lastEditor} explicitKind={Kind.KIND_USER}/>
                </div>
            </div>

            <div>
                {#if exists(inventory.department)}
                    <div>
                        <span class="text-muted">{localize(/* @kp-localization inventory.AtDepartment */ 'inventory.AtDepartment')}</span>
                        {pipe(inventory.department, loc())}
                    </div>
                {/if}

                {#if exists(inventory.fond)}
                    <div>
                        <span class="text-muted">{localize(/* @kp-localization commons.fond */ 'commons.fond')}</span>
                        {pipe(inventory.fond, loc())}
                    </div>
                {/if}
            </div>
        </div>

        <div class="list-item-inventory-locations">
            {#if inventory.locations.length > 0}
                <div>
                    <span class="text-muted">{localize(/* @kp-localization exemplar.OnLocations */ 'exemplar.OnLocations')}</span>
                    {pipe(inventory.locations, prop('text'), inline(', '))}
                </div>
            {:else }
                <div>{localize(/* @kp-localization inventory.AtAllLocations */ 'inventory.AtAllLocations')}</div>
            {/if}
        </div>
    </div>

    <div slot="menu" let:collapsed>
        <li class="collapsible-menu-item">
            <KpButtonStyleAnchor noWrap isBlock={true} href="/#!/inventories/{inventory.id}/matches">
                <UIcon icon="list"/>
                <span class:sr-only={collapsed}>{localize(/* @kp-localization inventory.Results */ 'inventory.Results')}</span>
            </KpButtonStyleAnchor>
        </li>
        {#if !isInventoryClosed(inventory)}
            <li class="collapsible-menu-item">
                <KpButton noWrap isBlock={true} on:click={showCapturingForm}>
                    <UIcon icon="barcode"/>
                    <span class:sr-only={collapsed}>{localize(/* @kp-localization inventory.ScanToInventory */ 'inventory.ScanToInventory')}</span>
                </KpButton>
            </li>

            <li class="collapsible-menu-item">
                <KpButton noWrap isBlock={true} buttonSize="xs" on:click={updateInventory}>
                    <UIcon icon="edit"/>
                    <span class:sr-only={collapsed}>{localize(/* @kp-localization commons.edit */ 'commons.edit')}</span>
                </KpButton>
            </li>

            <li class="collapsible-menu-item">
                <KpButton noWrap isBlock={true} buttonSize="xs" on:click={deleteInventory}>
                    <UIcon icon="trash"/>
                    <span class:sr-only={collapsed}>{localize(/* @kp-localization commons.Smazat */ 'commons.Smazat')}</span>
                </KpButton>
            </li>
        {/if}
    </div>
</KpCollapsibleMenuWrapper>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .list-item-inventory-heading {
        display: flex;
        align-items: center;
        gap: @spacing-s;
    }

    .inventory-name {
        font-size: 1.2em;
    }

    .list-item-inventory-main-content {
        margin: 0.5em 0 0.5em 0;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1em;
    }
</style>