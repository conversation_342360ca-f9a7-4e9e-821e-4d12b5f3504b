<script lang="ts">
    import type {Inventory} from 'src/features/inventory/types';
    import {Kind, SearchType} from 'shared/constants/portaro.constants';
    import {MatchesPageService} from 'src/features/inventory/matches-page/matches-page.service';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import KpSearchPageContainerWithContextActions from 'src/features/search/search-page-container-with-context-actions/KpSearchPageContainerWithContextActions.svelte';
    import KpButtonStyleAnchor from 'shared/ui-widgets/button/KpButtonStyleAnchor.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let inventory: Inventory;

    const service = getInjector().getByClass(MatchesPageService);
    const localize = getLocalization();
    const staticParams = {
        type: SearchType.TYPE_MATCH_SEARCH,
        kind: [Kind.KIND_MATCH],
        inventory: inventory.id,
        title: service.getSearchTitle(inventory)
    };
</script>

<KpSearchPageContainerWithContextActions {staticParams} pageClass="kp-matches-page matches">
    <svelte:fragment slot="context-actions">
        <KpButtonStyleAnchor href="/#!/inventories/{inventory.id}">
            <IconedContent icon="arrow-small-left">
                {localize(/* @kp-localization commons.zpet */ 'commons.zpet')}
            </IconedContent>
        </KpButtonStyleAnchor>
    </svelte:fragment>
</KpSearchPageContainerWithContextActions>
