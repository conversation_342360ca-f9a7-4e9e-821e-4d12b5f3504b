import type {InventoryDetailViewConfiguration} from './types';
import type {StateProvider, Transition} from '@uirouter/angularjs';
import type {InventoryDataService} from './inventory.data-service';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

export const INVENTORY_DETAIL_VIEW_CONFIGURATION = 'inventory-detail-view-configuration';

/*@ngInject*/
export function inventoryRoutes($stateProvider: StateProvider) {
    let inventoryModule: {default: any;};
    let inventoriesModule: {default: any;};
    let capturesModule: {default: any;};
    let matchesModule: {default: any;};

    $stateProvider
        .state('inventories', {
            url: '/inventories',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => inventoriesModule.default
            },
            lazyLoad: async () => {
                inventoriesModule = await import(/* webpackChunkName: "inventories-page" */ './inventories-page/KpInventoriesPage.svelte');
                return null;
            }
        });

    $stateProvider
        .state('captures', {
            url: '/inventories/:inventoryId/captures',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => capturesModule.default,

                /*@ngInject*/
                props: async (inventoryDataService: InventoryDataService, $transition$: Transition) => {
                    const inventory = await inventoryDataService.getInventory($transition$.params().inventoryId);
                    return {inventory};
                }
            },
            lazyLoad: async () => {
                capturesModule = await import(/* webpackChunkName: "captures-page" */ './captures-page/KpCapturesPage.svelte');
                return null;
            }
        });

    $stateProvider
        .state('matches', {
            url: '/inventories/:inventoryId/matches',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => matchesModule.default,

                /*@ngInject*/
                props: async (inventoryDataService: InventoryDataService, $transition$: Transition) => {
                    const inventory = await inventoryDataService.getInventory($transition$.params().inventoryId);
                    return {inventory};
                }
            },
            lazyLoad: async () => {
                matchesModule = await import(/* webpackChunkName: "matches-page" */ './matches-page/KpMatchesPage.svelte');
                return null;
            }
        });

    $stateProvider
        .state({
            name: 'inventory',
            url: '/inventories/:inventoryId',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => inventoryModule.default,
                /*@ngInject*/
                props: async (inventoryDataService: InventoryDataService, $transition$: Transition) => {
                    const inventory = await inventoryDataService.getInventory($transition$.params().inventoryId);
                    return {inventory};
                },
                /*@ngInject*/
                additionalContext: async (inventoryDataService: InventoryDataService, $transition$: Transition) => {
                    const detailViewConfiguration = await inventoryDataService.getDetailViewConfiguration($transition$.params().inventoryId);
                    return new Map<string, InventoryDetailViewConfiguration>([[INVENTORY_DETAIL_VIEW_CONFIGURATION, detailViewConfiguration]]);
                }

            },
            lazyLoad: async () => {
                inventoryModule = await import(/* webpackChunkName: "inventory-page" */ './inventory-page/KpInventoryPage.svelte');
                return null;
            }
        });
}