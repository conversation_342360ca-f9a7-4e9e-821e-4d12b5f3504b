import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type ReloaderService from 'shared/services/reloader.service';
import type {InventoryDataService} from './inventory.data-service';
import type {Inventory} from './types';
import {Kind, SearchType} from 'shared/constants/portaro.constants';
import type {ActionResponseWithText, Exemplar, Valuable, AsyncProcessStatus} from 'typings/portaro.be.types';
import {isInterruptedActionResponse} from '../../modals/modal-utils';
import type {Capture} from './types';
import {lastValueFrom} from 'rxjs';
import type {ObservableCaptureDataService} from './observable-capture.data-service';
import type {ModalDialogService} from 'shared/modal-dialogs/modal-dialog.service';
import type {LocalizationService} from 'shared/providers/localization.provider';
import type WalkerService from 'shared/services/walker.service';

export class InventoryPresenter {
    public static presenterName = 'inventoryPresenter';

    /*@ngInject*/
    constructor(private finishedResponseInteractionService: FinishedResponseInteractionService,
                private reloader: ReloaderService,
                private walker: WalkerService,
                private inventoryDataService: InventoryDataService,
                private modalDialogService: ModalDialogService,
                private observableCaptureDataService: ObservableCaptureDataService,
                private localizationService: LocalizationService) {
    }

    public async createInventory(): Promise<Inventory> {
        try {
            const actionResponse =  await this.inventoryDataService.create();
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
            await this.refreshPage();
            return actionResponse.savedObject;
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async updateInventory(inventory: Inventory): Promise<Inventory> {
        try {
            const actionResponse =  await this.inventoryDataService.update(inventory);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
            await this.refreshPage();
            return actionResponse.savedObject;
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async deleteInventoryAndRefresh(inventory: Inventory): Promise<void> {
        await this.deleteInventory(inventory);
        await this.refreshPage();
    }

    public async deleteInventoryAndOpenInventories(inventory: Inventory): Promise<void> {
        await this.deleteInventory(inventory);
        this.walker.newSpaPage('/inventories');
    }

    private async deleteInventory(inventory: Inventory): Promise<void> {
        try {
            const actionResponse =  await this.inventoryDataService.delete(inventory);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async startInventoryExecution(inventory: Inventory): Promise<void> {
        try {
            await this.modalDialogService.openModalWindow('confirmationDialog', {text: this.localizationService.get(/* @kp-localization inventory.ReallyStartExecution */ 'inventory.ReallyStartExecution')})
            const actionResponse =  await this.inventoryDataService.startExecution(inventory);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async getInventoryExecutionStatus(inventory: Inventory): Promise<Valuable<AsyncProcessStatus<number>>> {
        try {
            return await this.inventoryDataService.getExecutionStatus(inventory);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async cancelInventoryExecution(inventory: Inventory): Promise<void> {
        try {
            await this.modalDialogService.openModalWindow('confirmationDialog', {text: this.localizationService.get(/* @kp-localization inventory.ReallyCancelExecution */ 'inventory.ReallyCancelExecution')})
            const actionResponse =  await this.inventoryDataService.cancelExecution(inventory);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async closeInventory(inventory: Inventory): Promise<void> {
        try {
            const actionResponse =  await this.inventoryDataService.close(inventory);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
            await this.refreshPage();
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async startInventoryDiscardion(inventory: Inventory): Promise<void> {
        try {
            await this.modalDialogService.openModalWindow('confirmationDialog', {text: this.localizationService.get(/* @kp-localization inventory.ReallyStartDiscardion */ 'inventory.ReallyStartDiscardion')})
            const actionResponse =  await this.inventoryDataService.startDiscardion(inventory);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async getInventoryDiscardionStatus(inventory: Inventory): Promise<Valuable<AsyncProcessStatus<number>>> {
        try {
            return await this.inventoryDataService.getDiscardionStatus(inventory);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async cancelInventoryDiscardion(inventory: Inventory): Promise<void> {
        try {
            await this.modalDialogService.openModalWindow('confirmationDialog', {text: this.localizationService.get(/* @kp-localization inventory.ReallyCancelDiscardion */ 'inventory.ReallyCancelDiscardion')})
            const actionResponse =  await this.inventoryDataService.cancelDiscardion(inventory);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    public async showCapturingFormForInventory(inventory: Inventory) {
        const modalModel = {
            staticSearchParams: {
                kind: [Kind.KIND_EXEMPLAR],
                type: SearchType.TYPE_SEARCH_SELECTION
            }
        };

        while (true) {
            try {
                const exemplar = await this.modalDialogService.openModalWindowAndGetPayload('searchSelection', modalModel) as Exemplar;
                const result = await this.captureExemplarToInventory(exemplar, inventory);
                await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(result);
            } catch (exceptionResponse) {
                if (isInterruptedActionResponse(exceptionResponse)) {
                    break;
                } else {
                    this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
                }
            }
        }
    }

    public async refreshPage() {
        await this.reloader.reloadRoute();
    }

    private captureExemplarToInventory(ex: Exemplar, inventory: Inventory): Promise<ActionResponseWithText> {
        const request: Capture = {
            id: null,
            inventory,
            identifier: ex.designation, // TODO: change to user's entered code (that will probably require some search-selection-modal refactoring)
            exemplar: ex.id,
            quantity: ex.quantity,
            captureWay: 0
        };

        return lastValueFrom(this.observableCaptureDataService.putCapture(request));
    }
}