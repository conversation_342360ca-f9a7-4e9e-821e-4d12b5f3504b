<script lang="ts">
    import type {Inventory} from 'src/features/inventory/types';
    import {Kind, SearchType} from 'shared/constants/portaro.constants';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {CapturesPageService} from 'src/features/inventory/captures-page/captures-page.service';
    import KpSearchPageContainerWithContextActions from 'src/features/search/search-page-container-with-context-actions/KpSearchPageContainerWithContextActions.svelte';
    import KpButtonStyleAnchor from 'shared/ui-widgets/button/KpButtonStyleAnchor.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let inventory: Inventory;

    const service = getInjector().getByClass(CapturesPageService);
    const localize = getLocalization();
    const staticParams = {
        kind: [Kind.KIND_CAPTURE],
        type: SearchType.TYPE_CAPTURE_SEARCH,
        inventory: inventory.id,
        title: service.getSearchTitle(inventory)
    };
</script>

<KpSearchPageContainerWithContextActions {staticParams} pageClass="kp-captures-page captures">
    <svelte:fragment slot="context-actions">
        <KpButtonStyleAnchor href="/#!/inventories/{inventory.id}">
            <IconedContent icon="arrow-small-left">
                {localize(/* @kp-localization commons.zpet */ 'commons.zpet')}
            </IconedContent>
        </KpButtonStyleAnchor>
    </svelte:fragment>
</KpSearchPageContainerWithContextActions>
