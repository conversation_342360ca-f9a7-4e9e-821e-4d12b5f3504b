import register from '@kpsys/angularjs-register';

import {InventoryDataService} from './inventory.data-service';
import {inventoryRoutes} from './inventory.routes';
import {ObservableCaptureDataService} from './observable-capture.data-service';
import {InventoryPresenter} from './inventory.presenter';
import {MatchDataService} from './match.data-service';
import {MatchPresenter} from './match.presenter';
import {InventoriesPageService} from 'src/features/inventory/inventories-page/inventories-page.service';
import {MatchesPageService} from 'src/features/inventory/matches-page/matches-page.service';
import {CapturesPageService} from 'src/features/inventory/captures-page/captures-page.service';

/**
 * @ngdoc module
 * @name portaro.features.inventory
 * @module portaro.features.inventory
 */
export default register('portaro.features.inventory')
    .config(inventoryRoutes)
    .service(InventoryDataService.serviceName, InventoryDataService)
    .service(ObservableCaptureDataService.serviceName, ObservableCaptureDataService)
    .service(MatchDataService.serviceName, MatchDataService)
    .service(InventoryPresenter.presenterName, InventoryPresenter)
    .service(MatchPresenter.presenterName, MatchPresenter)
    .service(InventoriesPageService.serviceName, InventoriesPageService)
    .service(MatchesPageService.serviceName, MatchesPageService)
    .service(CapturesPageService.serviceName, CapturesPageService)
    .name();
