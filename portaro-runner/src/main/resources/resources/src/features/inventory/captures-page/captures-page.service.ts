import type {Inventory} from 'src/features/inventory/types';
import type {LocalizationService} from 'shared/providers/localization.provider';

export class CapturesPageService {
    public static serviceName = 'capturesPageService';

    /*@ngInject*/
    constructor(private localizationService: LocalizationService) {
    }

    public getSearchTitle(inventory: Inventory): string {
        const msg = this.localizationService.get(/* @kp-localization inventory.ScannedCodes */ 'inventory.ScannedCodes');
        return `${msg} - ${inventory.text}`;
    }
}