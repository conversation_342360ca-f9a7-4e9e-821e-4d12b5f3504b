import {ngAsync} from 'shared/utils/ng-@decorators';
import {transferify} from 'shared/utils/data-service-utils';
import type {Inventory, InventoryDetailViewConfiguration} from './types';
import type {AsyncProcessStatus, FinishedActionResponse, FinishedSaveResponse, Valuable} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';

export class InventoryDataService {
    public static serviceName = 'inventoryDataService';

    private static readonly INVENTORIES_ROUTE = 'inventories';
    private static readonly CLOSE_ROUTE = 'close';
    private static readonly EXECUTION_ROUTE = 'execution';
    private static readonly DISCARDION_ROUTE = 'discardion';
    private static readonly DETAIL_VIEW_CONFIGURATION_ROUTE = 'detail-view-configuration';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async getInventory(id: number): Promise<Inventory> {
        return this.ajaxService
            .createRequest(`${InventoryDataService.INVENTORIES_ROUTE}/${id}`)
            .get();
    }

    @ngAsync()
    public async create(): Promise<FinishedSaveResponse<Inventory>> {
        return this.ajaxService
            .createRequest(`${InventoryDataService.INVENTORIES_ROUTE}`)
            .post({});
    }

    @ngAsync()
    public async update(inventory: Inventory): Promise<FinishedSaveResponse<Inventory>> {
        return this.ajaxService
            .createRequest(`${InventoryDataService.INVENTORIES_ROUTE}/${inventory.id}`)
            .post(transferify({inventory, ...inventory, confirmed: false}));
    }

    @ngAsync()
    public async delete(inventory: Inventory): Promise<FinishedActionResponse> {
        return this.ajaxService
            .createRequest(`${InventoryDataService.INVENTORIES_ROUTE}/${inventory.id}`)
            .delete();
    }

    @ngAsync()
    public async startExecution(inventory: Inventory): Promise<FinishedActionResponse> {
        return this.ajaxService
            .createRequest(`${InventoryDataService.INVENTORIES_ROUTE}/${inventory.id}/${InventoryDataService.EXECUTION_ROUTE}`)
            .post({});
    }

    @ngAsync()
    public async getExecutionStatus(inventory: Inventory): Promise<Valuable<AsyncProcessStatus<number>>> {
        return this.ajaxService
            .createRequest(`${InventoryDataService.INVENTORIES_ROUTE}/${inventory.id}/${InventoryDataService.EXECUTION_ROUTE}`)
            .get();
    }

    @ngAsync()
    public async cancelExecution(inventory: Inventory): Promise<FinishedActionResponse> {
        return this.ajaxService
            .createRequest(`${InventoryDataService.INVENTORIES_ROUTE}/${inventory.id}/${InventoryDataService.EXECUTION_ROUTE}`)
            .delete();
    }

    @ngAsync()
    public async close(inventory: Inventory): Promise<FinishedActionResponse> {
        return this.ajaxService
            .createRequest(`${InventoryDataService.INVENTORIES_ROUTE}/${inventory.id}/${InventoryDataService.CLOSE_ROUTE}`)
            .post({});
    }

    @ngAsync()
    public async startDiscardion(inventory: Inventory): Promise<FinishedSaveResponse<Inventory>> {
        return this.ajaxService
            .createRequest(`${InventoryDataService.INVENTORIES_ROUTE}/${inventory.id}/${InventoryDataService.DISCARDION_ROUTE}`)
            .post(transferify({inventory}));
    }

    @ngAsync()
    public async getDiscardionStatus(inventory: Inventory): Promise<Valuable<AsyncProcessStatus<number>>> {
        return this.ajaxService
            .createRequest(`${InventoryDataService.INVENTORIES_ROUTE}/${inventory.id}/${InventoryDataService.DISCARDION_ROUTE}`)
            .get();
    }

    @ngAsync()
    public async cancelDiscardion(inventory: Inventory): Promise<FinishedActionResponse> {
        return this.ajaxService
            .createRequest(`${InventoryDataService.INVENTORIES_ROUTE}/${inventory.id}/${InventoryDataService.DISCARDION_ROUTE}`)
            .delete();
    }

    @ngAsync()
    public async getDetailViewConfiguration(id: number): Promise<InventoryDetailViewConfiguration> {
        return this.ajaxService
            .createRequest(`${InventoryDataService.INVENTORIES_ROUTE}/${id}/${InventoryDataService.DETAIL_VIEW_CONFIGURATION_ROUTE}`)
            .get();
    }
}