<script lang="ts">
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {InventoriesPageService} from 'src/features/inventory/inventories-page/inventories-page.service';
    import {Kind, SearchType} from 'shared/constants/portaro.constants';
    import KpSearchPageContainerWithContextActions from 'src/features/search/search-page-container-with-context-actions/KpSearchPageContainerWithContextActions.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';

    const service = getInjector().getByClass(InventoriesPageService);
    const localize = getLocalization();
    const staticParams = {
        type: SearchType.TYPE_INVENTORY_SEARCH,
        kind: [Kind.KIND_INVENTORY]
    };
</script>

<KpSearchPageContainerWithContextActions {staticParams} pageClass="kp-inventories-page inventories">
    <svelte:fragment slot="context-actions">
        <KpButton dataQa="create-inventory-button" on:click={() => service.createInventory()}>
            {localize(/* @kp-localization commons.Novy */ 'commons.Novy')}
        </KpButton>
    </svelte:fragment>
</KpSearchPageContainerWithContextActions>