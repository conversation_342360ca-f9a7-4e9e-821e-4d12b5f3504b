<script lang="ts">
    import type {MatchState} from '../types';
    import type {ButtonStyle} from 'shared/ui-widgets/types';
    import KpChipTag from 'shared/ui-widgets/chip/KpChipTag.svelte';
    import {
        isDecreased,
        isFound,
        isInExchangeFond,
        isLended,
        isManuallyFixed,
        isNotFound,
        isNotInventoried
    } from '../utils';

    export let matchState: MatchState;

    function getChipStyle(state: MatchState): ButtonStyle {
        if (isNotFound(state)) {
            return 'danger-new';
        }

        if (isFound(state) || isLended(state)) {
            return 'success-new';
        }

        if (isDecreased(state) || isManuallyFixed(state)) {
            return 'warning';
        }

        if (isInExchangeFond(state)) {
            return 'info';
        }

        if (isNotInventoried(state)) {
            return 'default';
        }

        return 'default';
    }
</script>

<div class="match-state">
    <div>
        <KpChipTag chipStyle={getChipStyle(matchState)}>
            <span class="label-text">{matchState.text}</span>
        </KpChipTag>
    </div>

    {#if $$slots.default}
        <div class="description text-muted">
            <slot/>
        </div>
    {/if}
</div>

<style lang="less">
    .match-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
    }

    .description {
        font-size: 0.7em;
    }

    .label-text {
        text-transform: uppercase;
    }
</style>