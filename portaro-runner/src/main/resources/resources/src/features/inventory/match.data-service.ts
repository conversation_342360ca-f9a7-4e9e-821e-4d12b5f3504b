import {ngAsync} from 'shared/utils/ng-@decorators';
import type {Match} from './types';
import type {FinishedSaveResponse} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';

export class MatchDataService {
    public static serviceName = 'matchDataService';

    private static readonly MATCHES_ROUTE = 'matches';
    private static readonly MANUAL_FIX_ROUTE = 'manual-fix';
    private static readonly REVERT_MANUAL_FIX_ROUTE = 'revert-manual-fix';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async manualFix(match: Match): Promise<FinishedSaveResponse<Match>> {
        return this.ajaxService
            .createRequest(`${MatchDataService.MATCHES_ROUTE}/${match.id}/${MatchDataService.MANUAL_FIX_ROUTE}`)
            .put(null);
    }

    @ngAsync()
    public async revertManualFix(match: Match): Promise<FinishedSaveResponse<Match>> {
        return this.ajaxService
            .createRequest(`${MatchDataService.MATCHES_ROUTE}/${match.id}/${MatchDataService.REVERT_MANUAL_FIX_ROUTE}`)
            .put(null);
    }
}