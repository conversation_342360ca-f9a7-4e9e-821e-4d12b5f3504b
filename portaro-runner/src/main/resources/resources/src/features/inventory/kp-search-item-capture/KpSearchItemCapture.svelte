<script lang="ts">
    import type {Capture} from '../types';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';

    export let capture: Capture;
</script>

<div class="list-item-capture">
    <div>
        <strong>{capture.identifier}</strong>
    </div>

    <div>
        <span>{pipe(capture.captureWay, loc())}</span>
    </div>
</div>

<style lang="less">
    .list-item-capture {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1em;
    }
</style>