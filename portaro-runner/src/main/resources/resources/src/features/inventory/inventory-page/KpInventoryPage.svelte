<script lang="ts">
    import type {Inventory, InventoryDetailViewConfiguration} from '../types';
    import type {AsyncProcessStatus} from 'typings/portaro.be.types';
    import type {ButtonStyle} from 'shared/ui-widgets/types';
    import {getDateFormatter, getInjector, getLocalization, getLogger} from 'core/svelte-context/context';
    import {getContext, onDestroy, onMount, tick} from 'svelte';
    import {isInventoryClosed, isInventoryCreated, isInventoryOpened} from '../utils';
    import {InventoryPresenter} from '../inventory.presenter';
    import {pipe} from 'core/utils';
    import {inline, loc, prop} from 'shared/utils/pipes';
    import {Kind} from 'shared/constants/portaro.constants';
    import {INVENTORY_DETAIL_VIEW_CONFIGURATION} from '../inventory.routes';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpButtonStyleAnchor from 'shared/ui-widgets/button/KpButtonStyleAnchor.svelte';
    import KpAsyncProcessStatusPanel from 'shared/components/kp-async-process-status-panel/KpAsyncProcessStatusPanel.svelte';
    import KpLinksMenu from 'shared/components/kp-links-menu/KpLinksMenu.svelte';
    import KpVerticalButtonsPanel from 'shared/ui-widgets/vertical-buttons-panel/KpVerticalButtonsPanel.svelte';
    import Label from 'shared/components/kp-label/Label.svelte';
    import KpChipTag from 'shared/ui-widgets/chip/KpChipTag.svelte';
    import KpPageContainer from 'shared/layouts/containers/KpPageContainer.svelte';
    import {
        exists,
        isAsyncProcessRunning,
        isAsyncProcessStopped,
        isNull,
        isNullOrUndefined
    } from 'shared/utils/custom-utils';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    export let inventory: Inventory;

    const dateFormatter = getDateFormatter();
    const localize = getLocalization();
    const logger = getLogger();
    const presenter = getInjector().getByToken<InventoryPresenter>(InventoryPresenter.presenterName);
    const viewConfiguration = getContext<InventoryDetailViewConfiguration>(INVENTORY_DETAIL_VIEW_CONFIGURATION);

    let inventoryExecutionProcessStatus: AsyncProcessStatus<number> | null = null;
    let discardionProcessStatus: AsyncProcessStatus<number> | null = null;
    let intervalHandle: number | null = null;

    onMount(() => {
        startUpdates();
    });

    onDestroy(() => {
        cancelStatusUpdates();
    });

    let isRunnable = false;
    $: isRunnable = (isInventoryCreated(inventory) || isInventoryOpened(inventory))
        && (isNullOrUndefined(inventoryExecutionProcessStatus) || isAsyncProcessStopped(inventoryExecutionProcessStatus))
        && (isNullOrUndefined(discardionProcessStatus) || isAsyncProcessStopped(discardionProcessStatus));

    let isExecuting = false;
    $: isExecuting = exists(inventoryExecutionProcessStatus) && isAsyncProcessRunning(inventoryExecutionProcessStatus);

    let isClosable = false;
    $: isClosable = isInventoryOpened(inventory);

    let isDiscardable = false;
    $: isDiscardable = isInventoryOpened(inventory)
        && (isNullOrUndefined(inventoryExecutionProcessStatus) || isAsyncProcessStopped(inventoryExecutionProcessStatus))
        && (isNullOrUndefined(discardionProcessStatus) || isAsyncProcessStopped(discardionProcessStatus));

    let isDiscarding = false;
    $: isDiscarding = exists(discardionProcessStatus) && isAsyncProcessRunning(discardionProcessStatus);

    let isNotClosed = false;
    $: isNotClosed = !isInventoryClosed(inventory);

    function showCapturingForm() {
        presenter.showCapturingFormForInventory(inventory);
    }

    async function startUpdates() {
        if (await tryToStartStatusUpdatesSubscription(startExecutionStatusUpdates)) {
            return;
        }
        await tryToStartStatusUpdatesSubscription(startDiscardionStatusUpdates);
    }

    async function tryToStartStatusUpdatesSubscription(startStatusUpdateFunction: () => Promise<void>): Promise<boolean> {
        await startStatusUpdateFunction();
        return exists(intervalHandle);
    }

    function updateInventory() {
        presenter.updateInventory(inventory);
    }

    function deleteInventory() {
        presenter.deleteInventoryAndOpenInventories(inventory);
    }

    async function startInventoryExecution() {
        await presenter.startInventoryExecution(inventory);
        await startExecutionStatusUpdates();
    }

    async function cancelInventoryExecution() {
        await presenter.cancelInventoryExecution(inventory);
    }

    async function closeInventory() {
        await presenter.closeInventory(inventory);
    }

    async function startDiscardion() {
        await presenter.startInventoryDiscardion(inventory);
        await startDiscardionStatusUpdates();
    }

    async function cancelDiscardion() {
        await presenter.cancelInventoryDiscardion(inventory);
    }

    async function fetchInventoryExecutionStatus(): Promise<boolean> {
        try {
            const {value: updatedExecutionStatus} = await presenter.getInventoryExecutionStatus(inventory);
            const previousExecutionStatus = inventoryExecutionProcessStatus;
            inventoryExecutionProcessStatus = updatedExecutionStatus;
            if (isNull(updatedExecutionStatus) || isAsyncProcessStopped(updatedExecutionStatus)) {
                cancelStatusUpdates();
                refreshPageOnNextTickIfProcessHasStopped(previousExecutionStatus, updatedExecutionStatus);
                return false;
            }
            return true;
        } catch (error) {
            cancelStatusUpdates();
            logger.error(error);
            return false;
        }
    }

    async function startExecutionStatusUpdates() {
        if (exists(intervalHandle)) {
            return; // already started
        }

        const shouldContinueFetching = await fetchInventoryExecutionStatus(); // pull first status immediately
        if (!shouldContinueFetching) {
            return; // stop further fetching
        }
        intervalHandle = window.setInterval(() => {
            fetchInventoryExecutionStatus();
        }, 3000); // pull after timeout in regular intervals
    }

    function cancelStatusUpdates() {
        if (isNullOrUndefined(intervalHandle)) {
            return;
        }
        window.clearInterval(intervalHandle);
        intervalHandle = null;
    }

    async function fetchDiscardionStatus(): Promise<boolean> {
        try {
            const {value: updatedDiscardionStatus} = await presenter.getInventoryDiscardionStatus(inventory);
            const previousDiscardionStatus = discardionProcessStatus;
            discardionProcessStatus = updatedDiscardionStatus;
            if (isNull(updatedDiscardionStatus) || isAsyncProcessStopped(updatedDiscardionStatus)) {
                cancelStatusUpdates();
                refreshPageOnNextTickIfProcessHasStopped(previousDiscardionStatus, updatedDiscardionStatus);
                return false;
            }
            return true;
        } catch (error) {
            cancelStatusUpdates();
            logger.error(error);
            return false;
        }
    }

    async function startDiscardionStatusUpdates() {
        if (exists(intervalHandle)) {
            return; // already started
        }

        const shouldContinueFetching = await fetchDiscardionStatus(); // pull first status immediately
        if (!shouldContinueFetching) {
            return; // stop further fetching
        }
        intervalHandle = window.setInterval(() => {
            fetchDiscardionStatus();
        }, 3000); // pull after timeout in regular intervals
    }

    function refreshPageOnNextTickIfProcessHasStopped(previousStatus: AsyncProcessStatus<number> | null, updatedStatus: AsyncProcessStatus<number> | null) {
        if (isNullOrUndefined(previousStatus) || isNullOrUndefined(updatedStatus)) {
            return;
        }
        if (isAsyncProcessRunning(previousStatus) && isAsyncProcessStopped(updatedStatus)) {
            tick().then(() => presenter.refreshPage());
        }
    }

    function getInventoryLabelStyle(inv: Inventory): ButtonStyle {
        if (isInventoryCreated(inv)) {
            return 'info';
        }

        if (isInventoryOpened(inv)) {
            return 'success-new';
        }

        if (isInventoryClosed(inv)) {
            return 'danger-new';
        }

        return 'default';
    }
</script>

<KpPageContainer additionalClasses="kp-inventory-page" gap="6px" id="inventory">
    <div class="row">
        <div class="col-sm-8 main-panel-hlavni">
            <div class="heading-row">
                <KpHeading type="h1">
                    {pipe(inventory, loc())}
                </KpHeading>

                <div class="label-container">
                    <KpChipTag chipStyle="{getInventoryLabelStyle(inventory)}">
                        {pipe(inventory.inventoryState, loc())}
                    </KpChipTag>

                    {#if exists(inventoryExecutionProcessStatus) && isAsyncProcessRunning(inventoryExecutionProcessStatus)}
                        <KpChipTag chipStyle="warning">
                            {localize(/* @kp-localization inventory.ExecutionInProgress */ 'inventory.ExecutionInProgress')}
                        </KpChipTag>
                    {/if}

                    {#if exists(discardionProcessStatus) && isAsyncProcessRunning(discardionProcessStatus)}
                        <KpChipTag chipStyle="warning">
                            {localize(/* @kp-localization inventory.DiscardionInProgress */ 'inventory.DiscardionInProgress')}
                        </KpChipTag>
                    {/if}
                </div>
            </div>

            <div class="inventory-metadata-container">
                <div>{localize(/* @kp-localization commons.Vytvoreno */ 'commons.Vytvoreno')} {pipe(inventory.createDate, dateFormatter('d.M.yyyy'))}</div>

                {#if exists(inventory.date)}
                    <div>{localize(/* @kp-localization commons.datum */ 'commons.datum')} {pipe(inventory.date, dateFormatter('d.M.yyyy'))}</div>
                {/if}

                {#if exists(inventory.closeDate)}
                    <div>{localize(/* @kp-localization inventory.ClosedAt */ 'inventory.ClosedAt')} {pipe(inventory.closeDate, dateFormatter('d.M.yyyy'))}</div>
                {/if}

                <div>{localize(/* @kp-localization inventory.CreatedBy */ 'inventory.CreatedBy')}
                    <Label labeled={inventory.creator} explicitKind={Kind.KIND_USER}/>
                </div>
                <div>{localize(/* @kp-localization inventory.LastUpdatedBy */ 'inventory.LastUpdatedBy')}
                    <Label labeled={inventory.lastEditor} explicitKind={Kind.KIND_USER}/>
                </div>
            </div>

            <div class="inventory-settings-container">
                <div>
                    {#if exists(inventory.department)}
                        <div>{localize(/* @kp-localization inventory.AtDepartment */ 'inventory.AtDepartment')} {pipe(inventory.department, loc())}</div>
                    {/if}

                    {#if exists(inventory.fond)}
                        <div>{localize(/* @kp-localization commons.fond */ 'commons.fond')} {pipe(inventory.fond, loc())}</div>
                    {/if}

                    {#if inventory.includeIssues}
                        <div>
                            <span>{localize(/* @kp-localization inventory.IncludingIssues */ 'inventory.IncludingIssues')}</span>
                        </div>
                    {/if}

                    {#if inventory.includeExchangeSets}
                        <div>
                            <span>{localize(/* @kp-localization inventory.IncludingExchangeSets */ 'inventory.IncludingExchangeSets')}</span>
                        </div>
                    {/if}

                    {#if exists(inventory.firstAccessNumber) || exists(inventory.lastAccessNumber)}
                        <div>
                            {localize(/* @kp-localization exemplar.AccessNumber.abbr */ 'exemplar.AccessNumber.abbr')}
                            {#if exists(inventory.firstAccessNumber)}
                                <span>
                                    <span class="text-muted">{localize(/* @kp-localization commons.from */ 'commons.from')}</span>
                                    {inventory.firstAccessNumber}
                                </span>
                            {/if}
                            {#if exists(inventory.lastAccessNumber)}
                                <span>
                                    <span class="text-muted">{localize(/* @kp-localization commons.to */ 'commons.to')}</span>
                                    {inventory.lastAccessNumber}
                                </span>
                            {/if}
                        </div>
                    {/if}

                    {#if exists(inventory.signaturePrefix) || exists(inventory.lastSignature)}
                        <div>
                            {localize(/* @kp-localization exemplar.Signature.abbr */ 'exemplar.Signature.abbr')}
                            {#if exists(inventory.signaturePrefix)}
                                <span>
                                    <span class="text-muted">prefix</span>
                                    {inventory.signaturePrefix}
                                </span>
                            {/if}

                            {#if exists(inventory.lastSignature)}
                                <span>
                                    <span class="text-muted">{localize(/* @kp-localization commons.to */ 'commons.to')}</span>
                                    {inventory.lastSignature}
                                </span>
                            {/if}
                        </div>
                    {/if}

                </div>

                <div>
                    {#if inventory.locations.length > 0}
                        <div>
                            {localize(/* @kp-localization exemplar.OnLocations */ 'exemplar.OnLocations')}
                            {pipe(inventory.locations, prop('text'), inline(', '))}
                        </div>
                    {:else }
                        <div>{localize(/* @kp-localization inventory.AtAllLocations */ 'inventory.AtAllLocations')}</div>
                    {/if}
                </div>
            </div>

            {#if exists(inventoryExecutionProcessStatus)}
                <KpAsyncProcessStatusPanel asyncProcessStatus={inventoryExecutionProcessStatus}/>
            {/if}

            {#if exists(discardionProcessStatus)}
                <KpAsyncProcessStatusPanel asyncProcessStatus={discardionProcessStatus}/>
            {/if}
        </div>

        <aside class="col-sm-4 main-panel-pravy">
            <div style="margin-bottom: 20px;">
                <KpButtonStyleAnchor isBlock href="/#!/inventories">
                    <IconedContent icon="arrow-small-left">
                        {localize(/* @kp-localization detail.zpetNaVyhledane */ 'detail.zpetNaVyhledane')}
                    </IconedContent>
                </KpButtonStyleAnchor>
            </div>

            <KpVerticalButtonsPanel additionalClasses="inventory-management-panel">
                <svelte:fragment slot="heading">{localize(/* @kp-localization inventory.InventoryManagement */ 'inventory.InventoryManagement')}</svelte:fragment>

                <KpButtonStyleAnchor isBlock={true}
                                     href="/#!/inventories/{inventory.id}/captures">{localize(/* @kp-localization inventory.ScannedCodes */ 'inventory.ScannedCodes')}</KpButtonStyleAnchor>
                <KpButtonStyleAnchor isBlock={true} href="/#!/inventories/{inventory.id}/matches">{localize(/* @kp-localization inventory.Results */ 'inventory.Results')}</KpButtonStyleAnchor>

                {#if isNotClosed}
                    <KpButton isDisabled={isExecuting || isDiscarding} isBlock={true}
                              on:click={showCapturingForm}>{localize(/* @kp-localization inventory.ScanToInventory */ 'inventory.ScanToInventory')}</KpButton>
                    <KpButton isDisabled={isExecuting || isDiscarding} isBlock={true} on:click={updateInventory}>{localize(/* @kp-localization commons.edit */ 'commons.edit')}</KpButton>
                    <KpButton isDisabled={isExecuting || isDiscarding} isBlock={true} on:click={deleteInventory}>{localize(/* @kp-localization commons.Smazat */ 'commons.Smazat')}</KpButton>
                {/if}

                {#if isClosable}
                    <KpButton isDisabled={isExecuting || isDiscarding} isBlock={true}
                              on:click={closeInventory}>{localize(/* @kp-localization inventory.CloseInventory */ 'inventory.CloseInventory')}</KpButton>
                {/if}

                {#if isRunnable}
                    <KpButton isBlock={true} on:click={startInventoryExecution}>{localize(/* @kp-localization inventory.StartInventoryExecution */ 'inventory.StartInventoryExecution')}</KpButton>
                {/if}

                {#if isExecuting}
                    <KpButton isBlock={true} buttonStyle="primary"
                              on:click={cancelInventoryExecution}>{localize(/* @kp-localization inventory.StopInventoryExecution */ 'inventory.StopInventoryExecution')}</KpButton>
                {/if}

                {#if isDiscardable}
                    <KpButton isBlock={true} on:click={startDiscardion}>
                        {localize(/* @kp-localization inventory.StartDiscardion */ 'inventory.StartDiscardion')}
                    </KpButton>
                {/if}

                {#if isDiscarding}
                    <KpButton isBlock={true} buttonStyle="primary" on:click={cancelDiscardion}>
                        {localize(/* @kp-localization inventory.StopDiscardion */ 'inventory.StopDiscardion')}
                    </KpButton>
                {/if}

                {#if viewConfiguration.exportsEnabled}
                    <KpLinksMenu links={viewConfiguration.exportLinks}
                                 dropdownButtonText={'Export'}
                                 singleButtonOverwriteLinkText={'Export'}
                                 isBlock={true}/>

                    <KpLinksMenu links={viewConfiguration.printExportLinks}
                                 dropdownButtonText={localize(/* @kp-localization commons.tisk */ 'commons.tisk')}
                                 singleButtonOverwriteLinkText={localize(/* @kp-localization commons.tisk */ 'commons.tisk')}
                                 isBlock={true}
                                 forceNewWindow={true}/>
                {/if}
            </KpVerticalButtonsPanel>
        </aside>
    </div>
</KpPageContainer>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.media-queries.less";

    .heading-row {
        display: flex;
        align-items: center;
        gap: @spacing-s;
    }

    .main-panel-hlavni {
        display: flex;
        flex-direction: column;
        gap: 1em;
    }

    .label-container {
        display: inline-flex;
        flex-direction: column;
        align-items: flex-start;
        gap: @spacing-s;
    }

    .inventory-settings-container {
        display: flex;
        flex-flow: row wrap;
        gap: 1em;

        & > * {
            flex: 1 0;

            @media (max-width: @screen-sm-max) {
                flex-basis: 100%;
            }
        }
    }
</style>