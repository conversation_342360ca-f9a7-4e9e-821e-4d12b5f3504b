import type {ActionResponseWithText} from 'typings/portaro.be.types';
import type {ObservableAjaxService} from 'core/data-services/observable-ajax.service';
import type {Observable} from 'rxjs';
import {transferify} from 'shared/utils/data-service-utils';
import type {Capture} from './types';

export class ObservableCaptureDataService {

    public static serviceName = 'observableCaptureDataService';
    private baseUri: string;

    /*@ngInject*/
    constructor(private observableAjaxService: ObservableAjaxService) {
        this.baseUri = 'captures';
    }

    public putCapture(capture: Capture): Observable<ActionResponseWithText> {
        return this.observableAjaxService
            .forUri(this.baseUri)
            .withBody(transferify(capture))
            .put<ActionResponseWithText>();
    }

}
