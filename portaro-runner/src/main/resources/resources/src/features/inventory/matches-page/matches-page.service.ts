import type {Inventory} from 'src/features/inventory/types';
import type {LocalizationService} from 'shared/providers/localization.provider';

export class MatchesPageService {
    public static serviceName = 'matchesPageService';

    /*@ngInject*/
    constructor(private localizationService: LocalizationService) {
    }

    public getSearchTitle(inventory: Inventory): string {
        const msg = this.localizationService.get(/* @kp-localization inventory.Results */ 'inventory.Results');
        return `${msg} - ${inventory.text}`;
    }
}