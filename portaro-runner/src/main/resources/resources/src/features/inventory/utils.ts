import type {Inventory, MatchState} from './types';


export function isInventoryCreated(inventory: Inventory): boolean {
    return inventory.inventoryState.id === 0;
}

export function isInventoryOpened(inventory: Inventory): boolean {
    return inventory.inventoryState.id === 1;
}

export function isInventoryClosed(inventory: Inventory): boolean {
    return inventory.inventoryState.id === 2;
}

export function isNotFound(matchState: MatchState): boolean {
    return matchState.id === 0;
}

export function isFound(matchState: MatchState): boolean {
    return matchState.id === 1;
}

export function isLended(matchState: MatchState): boolean {
    return matchState.id === 2;
}

export function isDecreased(matchState: MatchState): boolean {
    return matchState.id === 3;
}

export function isManuallyFixed(matchState: MatchState): boolean {
    return matchState.id === 4;
}

export function isInExchangeFond(matchState: MatchState): boolean {
    return matchState.id === 5;
}

export function isNotInventoried(matchState: MatchState): boolean {
    return matchState.id === 6;
}