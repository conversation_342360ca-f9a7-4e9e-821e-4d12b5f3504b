<script lang="ts">
    import type {Match} from '../types';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {isManuallyFixed, isNotFound} from '../utils';
    import {MatchPresenter} from '../match.presenter';
    import {exists} from 'shared/utils/custom-utils';
    import KpMatchStateLabel from './KpMatchStateLabel.svelte';
    import KpCollapsibleMenuWrapper from 'shared/ui-widgets/menu-wrapper/KpCollapsibleMenuWrapper.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let match: Match;

    const localize = getLocalization();
    const presenter = getInjector().getByToken<MatchPresenter>(MatchPresenter.presenterName);

    async function setMatchAsManuallyFixed() {
        match = await presenter.setMatchAsManuallyFixed(match);
    }

    async function revertMatchBackFromManuallyFixed() {
        match = await presenter.revertMatchBackFromManuallyFixed(match);
    }
</script>

<KpCollapsibleMenuWrapper>
    <div slot="main" class="list-item-match">
        <h2 class="unset-style record-name">
            {pipe(match.record, loc())}
        </h2>
        <div class="exemplar-info">
            <div class="other">
                <span>{pipe(match.location, loc())}</span>
                {#if exists(match.customValue)}
                    <span>{pipe(match.customValue, loc())}</span>
                {/if}
                <span>{pipe(match.loanCategory, loc())}</span>
            </div>
            <div class="identifiers">
                {#if exists(match.accessNumber)}
                    <span>{localize(/* @kp-localization exemplar.AccessNumber.abbr */ 'exemplar.AccessNumber.abbr')}: {match.accessNumber}</span>
                {/if}
                {#if exists(match.barCode)}
                    <span>{localize(/* @kp-localization exemplar.BarCode.abbr */ 'exemplar.BarCode.abbr')}: {match.barCode}</span>
                {/if}
                {#if exists(match.signature)}
                    <span>{localize(/* @kp-localization exemplar.Signature.abbr */ 'exemplar.Signature.abbr')}: {match.signature}</span>
                {/if}
            </div>
        </div>
        <div class="result">
            <KpMatchStateLabel matchState={match.previousMatchState}>
                {localize(/* @kp-localization inventory.InLastRevision */ 'inventory.InLastRevision')}
            </KpMatchStateLabel>
            <UIcon icon="angle-small-right"/>
            <KpMatchStateLabel matchState={match.matchState}>
                {localize(/* @kp-localization inventory.InThisRevision */ 'inventory.InThisRevision')}
            </KpMatchStateLabel>
        </div>
    </div>
    <div slot="menu" let:collapsed>
        {#if isNotFound(match.matchState)}
            <li class="collapsible-menu-item">
                <KpButton noWrap isBlock={true} buttonSize="xs" on:click={setMatchAsManuallyFixed}>
                    <UIcon icon="edit"/>
                    <span class:sr-only={collapsed}>{localize(/* @kp-localization inventory.ManuallyFix */ 'inventory.ManuallyFix')}</span>
                </KpButton>
            </li>
        {/if}
        {#if isManuallyFixed(match.matchState)}
            <li class="collapsible-menu-item">
                <KpButton noWrap isBlock={true} buttonSize="xs" on:click={revertMatchBackFromManuallyFixed}>
                    <UIcon icon="edit"/>
                    <span class:sr-only={collapsed}>{localize(/* @kp-localization inventory.RevertManualFix */ 'inventory.RevertManualFix')}</span>
                </KpButton>
            </li>
        {/if}
    </div>
</KpCollapsibleMenuWrapper>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    .list-item-match {
        display: grid;
        column-gap: 1em;
        @media (max-width: @screen-sm-max) {
            grid-template-columns: 1fr 1fr;
        }
        @media (min-width: @screen-md-min) {
            grid-template-columns: 2fr 1fr;
        }
    }

    .record-name {
        font-weight: bold;
        grid-column: 1 / -1; // entire row
        overflow: hidden;
        text-overflow: ellipsis;
        @media (max-width: @screen-xs-max) {
            /* https://albertwalicki.com/learn/solutions/how-to-limit-text-to-n-lines */
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2; /* number of lines to show */
            line-clamp: 2;
        }
        @media (min-width: @screen-sm-min) {
            white-space: nowrap;
        }
    }

    .exemplar-info {
        display: grid;
        @media (max-width: @screen-sm-max) {
            grid-template-columns: 1fr;
        }
        @media (min-width: @screen-md-min) {
            grid-template-columns: 1fr 1fr;
            column-gap: 2em;
        }

        .other, .identifiers {
            display: flex;
            flex-direction: column;
        }
    }

    .result {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
    }
</style>