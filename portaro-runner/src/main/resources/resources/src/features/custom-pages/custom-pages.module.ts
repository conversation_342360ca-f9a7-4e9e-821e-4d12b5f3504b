import register from '@kpsys/angularjs-register';
import {CustomPagesDataService} from './custom-pages.data-service';
import {CustomPagesService} from 'src/features/custom-pages/custom-pages.service';
import {customPagesRoutes} from './custom-pages.routes';

export default register('portaro.features.customPages')
    .service(CustomPagesDataService.serviceName, CustomPagesDataService)
    .service(CustomPagesService.serviceName, CustomPagesService)
    .config(customPagesRoutes)
    .name();