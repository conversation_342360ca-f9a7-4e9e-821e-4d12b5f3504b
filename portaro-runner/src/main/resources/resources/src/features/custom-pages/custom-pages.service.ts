import type {CustomPageResponse} from './types';
import type {CustomPagesDataService} from 'src/features/custom-pages/custom-pages.data-service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';

export class CustomPagesService {
    public static serviceName = 'customPagesService';

    /*@ngInject*/
    constructor(private customPagesDataService: CustomPagesDataService,
                private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    public async getPageData(page: string): Promise<CustomPageResponse | null> {
        try {
            return await this.customPagesDataService.getPageData(page);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            return null;
        }
    }
}