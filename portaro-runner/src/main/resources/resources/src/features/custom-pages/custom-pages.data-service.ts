import type {CustomPageResponse} from './types';
import type {AjaxService} from 'src/core/data-services/ajax.service';

export class CustomPagesDataService {
    public static serviceName = 'customPagesDataService';

    public static readonly CUSTOM_PAGE_ROUTE = 'custom-pages';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    public async getPageData(page: string): Promise<CustomPageResponse> {
        return this.ajaxService
            .createRequest(`${CustomPagesDataService.CUSTOM_PAGE_ROUTE}/${page}`)
            .get();
    }
}