import type {StateProvider, Transition} from '@uirouter/angularjs';
import type {CustomPagesService} from './custom-pages.service';
import type WalkerService from 'shared/services/walker.service';
import {KpSvelteComponentWrapperComponent} from 'src/core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';
import {exists} from 'src/shared/utils/custom-utils';

/*@ngInject*/
export function customPagesRoutes($stateProvider: StateProvider) {
    let customPagesModule: {default: any;};

    $stateProvider
        .state({
            name: 'custom-pages',
            url: '/pages/{page}',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => customPagesModule.default,

                /*@ngInject*/
                props: async ($transition$: Transition, customPagesService: CustomPagesService, walker: WalkerService) => {
                    const pageData = await customPagesService.getPageData($transition$.params().page);

                    if (!exists(pageData)) {
                        await walker.newSpaPage('');
                        return null;
                    }

                    return {pageData};
                }
            },
            lazyLoad: async () => {
                customPagesModule = await import(/* webpackChunkName: "custom-pages" */ './KpCustomPage.svelte');
                return null;
            }
        });
}