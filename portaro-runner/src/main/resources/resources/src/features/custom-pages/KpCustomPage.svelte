<script lang="ts">
    import type {CustomPageResponse} from 'src/features/custom-pages/types';
    import {exists} from 'shared/utils/custom-utils';
    import KpPageContainer from 'shared/layouts/containers/KpPageContainer.svelte';

    export let pageData: CustomPageResponse;
</script>

<svelte:head>
    {#if exists(pageData)}
        <title>{pageData.pageName}</title>
    {/if}
</svelte:head>

{#if exists(pageData)}
    <KpPageContainer additionalClasses="kp-custom-page-{pageData.page}">
        {@html pageData.htmlContext}
    </KpPageContainer>
{/if}