import type {ThesaurusItemType, ThesaurusRecordDescriptor} from './types';
import type {RecordDescriptor} from 'typings/portaro.be.types';
import type RecordDataService from '../record/record.data-service';

export class KpThesaurusService {
    public static serviceName = 'kpThesaurusService';

    /*@ngInject*/
    constructor(private recordDataService: RecordDataService) {
    }

    public async getHierarchyRoot(): Promise<ThesaurusRecordDescriptor> {
        return this.decorateRecordDescriptor(await this.recordDataService.getHierarchyRoot(), 'ROOT');
    }

    public async loadChildren(record: ThesaurusRecordDescriptor): Promise<ThesaurusRecordDescriptor[]> {
        const seesLoad = await this.loadSees(record);
        const seeAlsosLoad = await this.loadSeeAlsos(record);
        const subauthoritiesLoad = await this.loadSubauthorities(record);

        const [sees, seeAlsos, subauthorities] = await Promise.all([subauthoritiesLoad, seesLoad, seeAlsosLoad]);

        return [...sees, ...seeAlsos, ...subauthorities];
    }

    private async loadSees(record: ThesaurusRecordDescriptor): Promise<ThesaurusRecordDescriptor[]> {
        const records = await this.recordDataService.query({seenRecord: record.id});
        return records.map((r) => this.decorateRecordDescriptor(r, 'SEE'));
    }

    private async loadSeeAlsos(record: ThesaurusRecordDescriptor): Promise<ThesaurusRecordDescriptor[]> {
        const records = await this.recordDataService.query({seenAlsoRecord: record.id});
        return records.map((r) => this.decorateRecordDescriptor(r, 'SEE_ALSO'));
    }

    private async loadSubauthorities(record: ThesaurusRecordDescriptor): Promise<ThesaurusRecordDescriptor[]> {
        const records = await this.recordDataService.query({superRecord: record.id});
        return records.map((r) => this.decorateRecordDescriptor(r, 'SUBAUTHORITY'));
    }

    private decorateRecordDescriptor(record: RecordDescriptor, type: ThesaurusItemType): ThesaurusRecordDescriptor {
        return {
            ...record,
            type
        }
    }
}