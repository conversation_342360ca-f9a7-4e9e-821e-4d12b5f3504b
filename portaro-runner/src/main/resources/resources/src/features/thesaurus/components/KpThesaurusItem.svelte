<script lang="ts">
    import type {ThesaurusRecordDescriptor} from '../types';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {KpThesaurusService} from '../kp-thesaurus.service';
    import {onMount} from 'svelte';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {slide} from 'svelte/transition';
    import {exists} from 'shared/utils/custom-utils';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import KpLoadingInline from 'shared/components/kp-loading/KpLoadingInline.svelte';

    export let thesaurusItem: ThesaurusRecordDescriptor;

    const localize = getLocalization();
    const service = getInjector().getByToken<KpThesaurusService>(KpThesaurusService.serviceName);
    const slideAnimParams = {axis: 'y', duration: 250} as const;
    const emptySlideInAnimParams = {axis: 'x', duration: 250} as const;

    let children: ThesaurusRecordDescriptor[] | null = null;
    let childrenLoading = false;
    let childrenShowing = false;
    $: isEmpty = exists(children) && !childrenLoading && children.length === 0;
    $: childrenOpened = childrenShowing && !isEmpty && exists(children);

    onMount(async () => {
        if (thesaurusItem.type === 'ROOT') {
            children = await service.loadChildren(thesaurusItem);
            childrenShowing = true;
        }
    });

    const handleShowChildrenClick = async () => {
        if (!exists(children) && !childrenLoading) {
            childrenLoading = true;
            children = await service.loadChildren(thesaurusItem);
            childrenLoading = false;
            childrenShowing = true;
            return;
        }

        childrenShowing = !childrenShowing;
    }
</script>

<div class="kp-thesaurus-item">
    <div class="inner-row type-{thesaurusItem.type.toLowerCase()}">
        {#if thesaurusItem.type === 'SEE'}
            {localize(/* @kp-localization commons.viz */ 'commons.viz')}: {pipe(thesaurusItem, loc())}
            <a class="text-muted" href="/#!/records/{thesaurusItem.id}">
                <UIcon icon="zoom-in"/>
            </a>
        {/if}

        {#if thesaurusItem.type === 'SEE_ALSO'}
            {localize(/* @kp-localization commons.vizTez */ 'commons.vizTez')}: {pipe(thesaurusItem, loc())}
            <a class="text-muted" href="/#!/records/{thesaurusItem.id}">
                <UIcon icon="zoom-in"/>
            </a>
        {/if}

        {#if thesaurusItem.type === 'SUBAUTHORITY'}
            <button class="thesaurus-item-btn"
                    class:children-opened={childrenOpened}
                    on:click={handleShowChildrenClick}>

                {pipe(thesaurusItem, loc())}

                <div class="arrow-container">
                    <UIcon icon="angle-small-down"/>
                </div>
            </button>

            {#if childrenLoading}
                <KpLoadingInline size="xs"/>
            {/if}

            {#if isEmpty}
                <span class="text-muted empty-label" in:slide={emptySlideInAnimParams}>
                    {localize(/* @kp-localization commons.NoRecordFound */ 'commons.NoRecordFound')}
                </span>
            {/if}

            <a class="text-muted" href="/#!/records/{thesaurusItem.id}">
                <UIcon icon="zoom-in"/>
            </a>
        {/if}
    </div>

    {#if childrenOpened}
        <ul class="subauthorities"
            class:not-root-item={thesaurusItem.type !== 'ROOT'}
            transition:slide={slideAnimParams}>

            {#each children as record}
                <svelte:self thesaurusItem="{record}"/>
            {/each}
        </ul>
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .kp-thesaurus-item {
        display: flex;
        flex-direction: column;

        .inner-row {
            display: flex;
            align-items: center;
            gap: @spacing-m;

            &:not(.type-subauthority) {
                padding-left: @spacing-xs;
            }

            &:empty {
                display: none;
            }

            .thesaurus-item-btn {
                outline: none;
                border: 1px solid @themed-border-default;
                padding: @spacing-sm @spacing-ml;
                border-radius: @border-radius-default;
                background-color: @themed-panel-bg;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: @spacing-sm;
                transition: border 0.3s ease-in-out, background-color 0.3s ease-in-out;

                &:hover {
                    border: 1px solid var(--accent-blue-new);
                    background-color: #EAEEFF;
                }

                .arrow-container {
                    transition: rotate 0.2s ease-in-out, color 0.2s ease-in-out;
                }

                &.children-opened .arrow-container {
                    color: var(--accent-blue-new);
                    rotate: 180deg;
                }
            }

            .empty-label {
                text-wrap: nowrap;
            }
        }

        .subauthorities {
            display: flex;
            flex-direction: column;
            gap: @spacing-ml;

            &.not-root-item {
                margin-top: @spacing-m;
                padding-left: @spacing-ml;
                margin-left: @spacing-ml;
                border-left: 2px solid var(--accent-blue-new);
            }
        }
    }
</style>