import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export function thesaurusRoutes($stateProvider: StateProvider) {
    let thesaurusModule: { default: any; };
    $stateProvider
        .state({
            name: 'thesaurus',
            url: '/thesaurus',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => thesaurusModule.default,
            },
            lazyLoad: async () => {
                thesaurusModule = await import(/* webpackChunkName: "thesaurus" */ './KpThesaurusPage.svelte');
                return null;
            }
        });
}