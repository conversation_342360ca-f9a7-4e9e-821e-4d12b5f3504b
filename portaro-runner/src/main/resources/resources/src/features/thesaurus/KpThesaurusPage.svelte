<script lang="ts">
    import type {ThesaurusRecordDescriptor} from './types';
    import {getInjector} from 'core/svelte-context/context';
    import {onMount} from 'svelte';
    import {KpThesaurusService} from './kp-thesaurus.service';
    import KpLoadablePageContainer from 'shared/layouts/containers/KpLoadablePageContainer.svelte';
    import ThesaurusHeadingPart from './parts/ThesaurusHeadingPart.svelte';
    import ThesaurusItemsPart from './parts/ThesaurusItemsPart.svelte';

    const service = getInjector().getByToken<KpThesaurusService>(KpThesaurusService.serviceName);

    let loading = true;
    let thesaurusRoot: ThesaurusRecordDescriptor;

    onMount(async () => {
        thesaurusRoot = await service.getHierarchyRoot();
        loading = false;
    });
</script>

<KpLoadablePageContainer pageClass="kp-thesaurus-page" {loading}>
    <ThesaurusHeadingPart/>
    <ThesaurusItemsPart {thesaurusRoot}/>
</KpLoadablePageContainer>