<script lang="ts">
    import type {FormControlOptions} from 'shared/value-editors/internal/forms/types';
    import type {ValueEditorValidations} from 'shared/value-editors/kp-value-editor/types';
    import type {EditorsLocalizationFunction} from 'shared/value-editors/localizations/types';
    import type {Rec, RecordSearchParams} from 'typings/portaro.be.types';
    import type {TextValueEditorOptions} from 'shared/value-editors/internal/editors/text/types';
    import type {ValueEditorSize} from 'shared/value-editors/types';
    import type {FormControl} from 'shared/value-editors/internal/forms/form-control';
    import {getContext, onDestroy} from 'svelte';
    import {FormControlBuilder, defaultUpdateTriggers} from 'shared/value-editors/internal/forms/form-control';
    import {validatorFactory} from 'shared/value-editors/internal/editors/search-or-edit/utils';
    import {emptyAsNullFormatter, emptyAsNullParser, trim} from 'shared/value-editors/internal/editors/_shared/editors-utils';
    import {getInjector, getLogger} from 'core/svelte-context/context';
    import {createInputDescription} from 'shared/value-editors/internal/description/utils';
    import {touched} from 'shared/value-editors/internal/shared/use.touched';
    import {required} from 'shared/value-editors/internal/forms/use.required';
    import {style} from 'svelte-forms';
    import {focusOnMount} from 'shared/value-editors/internal/shared/use.focus-on-mount';
    import {ariaInvalid} from 'shared/value-editors/internal/errors/use.aria-invalid';
    import {ariaDescribedby} from 'shared/value-editors/internal/description/use.aria-describedby';
    import {ariaErrormessage} from 'shared/value-editors/internal/errors/use.aria-errormessage';
    import {createFloatingActions} from 'svelte-floating-ui';
    import {floatingUiCommonSettings} from 'shared/utils/floating-ui-common-settings';
    import {SearchManagerBuilderFactoryService} from 'src/features/search/search-manager/search-manager-factory.service';
    import {formControls} from 'shared/value-editors/internal/forms/use.form-controls';
    import {focusOutside} from 'shared/value-editors/internal/editors/multiple-acceptable/use.focus-outside';
    import {cleanup, exists} from 'shared/utils/custom-utils';
    import {filter, map, tap} from 'rxjs/operators';
    import {Kind, SearchType} from 'shared/constants/portaro.constants';
    import {EDITORS_LOCALIZE} from 'shared/value-editors/context-keys';
    import KpInlineRecordSearchPopover from 'src/features/record/inline-record-search/KpInlineRecordSearchPopover.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import Description from 'shared/value-editors/internal/description/Description.svelte';
    import ErrorMessages from 'shared/value-editors/internal/errors/ErrorMessages.svelte';
    import type {
        InlineRecordSearchValueEditorLocalizations,
        InlineRecordSearchValueEditorOptions
    } from 'src/features/record/inline-record-search/types';

    export let model: Rec;
    export let formControlOptions: FormControlOptions;
    export let editorId: string;
    export let editorName: string;
    export let placeholder: string;
    export let isDisabled: boolean;
    export let isFocused: boolean;
    export let forceShowErrors: boolean;
    export let size: ValueEditorSize;

    export let options: InlineRecordSearchValueEditorOptions;
    export let validations: ValueEditorValidations;

    const logger = getLogger();
    const searchManager = getInjector().getByClass(SearchManagerBuilderFactoryService)
        .createBuilder<RecordSearchParams, Rec>()
        .withStaticParams({
            pageSize: 6,
            ...options.searchParams,
            facetsEnabled: false,
            datasource: 'local',
            kind: [Kind.KIND_RECORD],
            type: SearchType.TYPE_SEARCH_SELECTION
        })
        .createLocalSearch(false);

    const localize: EditorsLocalizationFunction<InlineRecordSearchValueEditorLocalizations> = getContext(EDITORS_LOCALIZE);

    const recordFormControl = FormControlBuilder.for(`${editorId}-record`, `${editorName}-record`, model)
        .withValidators(validatorFactory(validations))
        .withModifiers([emptyAsNullParser(options)])
        .withFormatters([emptyAsNullFormatter(options, null)])
        .withOptions(formControlOptions)
        .build(logger);

    const queryDebounceOptions: FormControlOptions = {updateTriggers: [{...defaultUpdateTriggers.INPUT, debounceValue: 500}]};
    const queryEditorOptions: TextValueEditorOptions = {trim: true, emptyAsNull: false};
    const queryFormControl = FormControlBuilder.for<string>(`${editorId}-query`, `${editorName}-query`, model?.text ?? '')
        .withOptions(queryDebounceOptions)
        .withModifiers([trim(queryEditorOptions), emptyAsNullParser(queryEditorOptions)])
        .withFormatters([emptyAsNullFormatter(queryEditorOptions)])
        .build(logger);

    const querySubscription = queryFormControl
        .getModelInputChangeEvent$()
        .pipe(
            map((event) => event.detail),
            tap(resetSearchIfPopoverIsHidden), // because of input debouncing, events can arrive after the popover is already hidden
            filter(() => popoverIsDisplayed) // because of input debouncing, events can arrive after the popover is already hidden
        )
        .subscribe(search);

    const modelSubscription = recordFormControl.getModelValue$().subscribe((value) => model = value);  // two-way data binding -> sending updated value up to a parent
    $: recordFormControl.setModelValue(model); // two-way data binding -> receiving new model value from parent
    $: queryFormControl.setModelValue(model?.text ?? '');

    const inputDescription = createInputDescription({editorId, editorName, description: localize('description')});

    const [floatingRef, floatingContent] = createFloatingActions(floatingUiCommonSettings('bottom-start'));

    $: modelIsSelected = exists(model);
    let popoverIsDisplayed = false;

    onDestroy(() => cleanup(modelSubscription, querySubscription));

    function setToEmpty() {
        recordFormControl.setViewValue(null);
        resetSearch();
    }

    function search(query: string) {
        searchManager.newSearchWithPartialParams({q: query});
    }

    function showPopover() {
        if (popoverIsDisplayed || modelIsSelected) {
            return;
        }
        popoverIsDisplayed = true;
        search('');
    }

    function hidePopover() {
        if (!popoverIsDisplayed) {
            return;
        }
        popoverIsDisplayed = false;
        resetSearch();
    }

    function resetSearchIfPopoverIsHidden() {
        if (popoverIsDisplayed) {
            return;
        }
        resetSearch();
    }

    function selectItem(record: Rec) {
        hidePopover();
        recordFormControl.setViewValue(record);
    }

    function resetSearch() {
        queryFormControl.setModelValue('');
        searchManager.reset();
    }

    export function getFormController(): FormControl<Rec> {
        return recordFormControl;
    }
</script>

<div class="inline-record-search-value-editor"
     use:focusOutside={{ignoreLostFocusCause: false}}
     on:focus-outside={hidePopover}>

    <div class="input-group input-group-{size}">

        <input id={editorId}
               class="form-control input-{size}"
               name={editorName}
               type="text"
               {placeholder}
               disabled={isDisabled}
               readonly="{modelIsSelected}"
               on:focus={showPopover}

               use:formControls={queryFormControl}
               use:required={validations.required}
               use:focusOnMount={isFocused}
               use:style={{field: recordFormControl.getFieldStateAsStore()}}
               use:touched={{formControl: recordFormControl}}

               use:ariaInvalid={recordFormControl}
               use:ariaErrormessage={recordFormControl}
               use:ariaDescribedby={inputDescription}
               use:floatingRef
               autocomplete="off"
               data-main-input/>

        <div class="input-group-btn">
            <KpButton {size} use="{[[touched, {formControl: recordFormControl}]]}" isDisabled={isDisabled || !modelIsSelected} on:click={setToEmpty}>
                <IconedContent icon="trash">
                    {localize('delete')}
                </IconedContent>
            </KpButton>
        </div>
    </div>

    <ErrorMessages formController={recordFormControl} {forceShowErrors} {size}/>
    <Description {inputDescription}/>
    <KpInlineRecordSearchPopover {searchManager}
                                 open="{popoverIsDisplayed}"
                                 use={[floatingContent]}
                                 onSelect="{selectItem}"/>
</div>
