<script lang="ts">
    import type {Rec, RecordSearchParams} from 'typings/portaro.be.types';
    import type {ActionArray} from 'shared/utils/svelte-actions-utils';
    import type {SearchManager} from 'src/features/search/search-manager/search-manager';
    import {tick} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {getLocalization} from 'core/svelte-context/context';
    import KpPopoverPanel from 'shared/ui-widgets/popover/KpPopoverPanel.svelte';
    import KpPageableSearchResults from 'src/features/search/kp-search-results/KpPageableSearchResults.svelte';
    import SelectableItemsContainer from 'src/modals/search-selection-modal/SelectableItemsContainer.svelte';
    import KpSelectableRecordCard from 'shared/value-editors/internal/editors/search-or-edit/templates/KpSelectableRecordCard.svelte';
    import Checkbox from 'shared/ui-widgets/checkbox/Checkbox.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';
    import DataViewCreateRecordButton from 'src/features/erp-sutor/pages/data-view/DataViewCreateRecordButton.svelte';

    export let searchManager: SearchManager<Rec, RecordSearchParams>;
    export let open = false;
    export let use: ActionArray = [];
    export let onSelect: (item: Rec) => void;

    const localize = getLocalization();

    $: itemIsSelected = exists(selectedItem);
    $: isSubmittable = itemIsSelected;

    let selectedItem: Rec | null = null;

    async function select(item: Rec) {
        selectedItem = item;
        await tick(); // wait for update of reactive values denpended on 'selectedItem`
        submitSelectedItem();
    }

    function submitSelectedItem() {
        if (isSubmittable) {
            onSelect(selectedItem);
        }
    }

    function searchInAllLocalContexts(checked: boolean) {
        searchManager.newSearchWithPartialParams({datasource: checked ? 'local-root' : 'local'});
    }

    function hasRootFondParam() {
        return exists(searchManager.getCompleteParams().rootFond) && searchManager.getCompleteParams().rootFond.length >= 1;
    }

    function getRootFondId(): number {
        const rootFond = searchManager.getCompleteParams().rootFond[0];
        if (!exists(rootFond)) {
            throw new Error('Root fond is not set');
        }

        if (typeof rootFond === 'number') {
            return rootFond;
        }

        return rootFond.id;
    }

    const handleNewRecordCreated = (event: CustomEvent<Rec>) => {
        select(event.detail);
    };
</script>

<KpPopoverPanel popoverMode="manual" additionalClasses="record-search-popover" {use} bind:open let:togglePopover>
    <KpPageableSearchResults pagination="{searchManager.getPagination()}"
                             loadingSize="sm"
                             showPageButtons="{false}"
                             showEmptyResultsMessage="{false}"
                             let:paginationData>
        <SelectableItemsContainer items="{paginationData.items}"
                                  on:select={(event) => {select(event.detail); togglePopover();}}
                                  let:item>
            <KpSelectableRecordCard value="{item}" isSelected="{item === selectedItem}"/>
        </SelectableItemsContainer>
    </KpPageableSearchResults>

    <Flex alignItems="center" gap="m">
        <label for="search-all-local-contexts-checkbox">
            <Checkbox id="search-all-local-contexts-checkbox" on:change={(event) => searchInAllLocalContexts(event.detail.checked)}/>
            {localize(/* @kp-localization dataset.SearchEverywhere */ 'dataset.SearchEverywhere')}
        </label>

        {#if hasRootFondParam()}
            <Spacer flex="1"/>

            <DataViewCreateRecordButton buttonLabel="Vytvořit nový záznam"
                                        buttonSize="xs"
                                        rootFondId="{getRootFondId()}"
                                        on:record-created={handleNewRecordCreated}/>
        {/if}
    </Flex>
</KpPopoverPanel>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    :global {
        .record-search-popover {
            .content {
                width: auto;

                @media (min-width: @screen-sm-min) {
                    width: 600px;
                }
            }
        }
    }
</style>