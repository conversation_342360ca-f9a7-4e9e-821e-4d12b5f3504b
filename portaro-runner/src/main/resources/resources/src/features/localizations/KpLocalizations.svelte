<script lang="ts">
    import type {Category, Language, Localization} from 'typings/portaro.be.types';
    import type {LocalizationsSearchUrlParams} from './types';
    import type {Subscription} from 'rxjs';
    import {onDestroy, onMount} from 'svelte';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {KpLocalizationsPresenter} from './kp-localizations.presenter';
    import {cleanup, exists} from 'shared/utils/custom-utils';
    import {removeAll, byReference} from 'shared/utils/array-utils';
    import {fade} from 'svelte/transition';
    import KpClassicTable from 'shared/ui-widgets/table/classic/KpClassicTable.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpDropdownMenuItem from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuItem.svelte';
    import KpDropdownMenuButton from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuButton.svelte';
    import KpSearchBar from 'shared/ui-widgets/search/KpSearchBar.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import KpLoadablePageContainer from 'shared/layouts/containers/KpLoadablePageContainer.svelte';
    import KpHorizontalSeparator from 'shared/ui-widgets/separator/KpHorizontalSeparator.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    const localize = getLocalization();
    const presenter = getInjector().getByToken<KpLocalizationsPresenter>(KpLocalizationsPresenter.presenterName);
    const fadeInAnimParams = {duration: 250};

    let loading = true;
    let loadingMessages = true;
    let searchQuery = '';
    let categories: Category[];
    let languages: Language[];
    let messages: Localization[] = [];
    let activeCategories = {};
    let showingSearchResults = false;
    let subscription: Subscription;

    onMount(async () => {
        [categories, languages] = await presenter.loadCategoriesAndLanguages();
        // eslint-disable-next-line @typescript-eslint/no-misused-promises
        subscription = presenter.getUrlState$().subscribe(handleUrlStateChange);
        loading = false;
    });

    onDestroy(() => {
        cleanup(subscription);
    });

    async function createNewMessage() {
        const response = await presenter.createNewMessage();
        addMessage(response);
    }

    function addMessage(message: Localization) {
        convertLoadedMessage(message);
        messages = messages.filter((element) => {
            return !(element.depId === message.depId && element.code === message.code);
        });
        messages.push(message);
        messages.sort((a, b) => a.code.localeCompare(b.code));
    }

    async function editMessage(message: Localization) {
        const response = await presenter.editMessage(message);
        addMessage(response);
    }

    async function deleteMessage(message: Localization) {
        await presenter.deleteMessage(message);
        messages = removeAll(messages, byReference(message));
    }

    function handleSearch(event: CustomEvent<string>) {
        searchQuery = event.detail;
        presenter.search(searchQuery);
    }

    function handleSearchReset() {
        searchQuery = '';
        presenter.search(null);
    }

    function showMessages(localizationMessages: Localization[]) {
        activeCategories = {};
        messages = localizationMessages;
        messages.forEach(convertLoadedMessage);
    }

    function convertLoadedMessage(message: Localization) {
        const category = presenter.getCategoryFromMessageCode(message.code);
        activeCategories[category] = true;
        languages.forEach((language) => {
            const languageId = language.id;
            const translation = message.translations[languageId];
            message.translations[languageId] = {
                code: message.code,
                depId: message.depId,
                language: languageId,
                value: translation
            };
        });
    }

    const handleUrlStateChange = async (params: LocalizationsSearchUrlParams) => {
        if (searchQuery !== params.q) {
            searchQuery = params.q;
        }

        loadingMessages = true;
        const messagesResult = await presenter.queryBySearchParams(params);
        showingSearchResults = exists(params.q) && params.q.length > 0;
        showMessages(messagesResult);
        loadingMessages = false;
    };
</script>

<KpLoadablePageContainer pageClass="kp-localizations messages">
    <div class="title-row">
        <KpHeading type="h1">
            {localize(/* @kp-localization commons.lokalizacniHlasky */ 'commons.lokalizacniHlasky')}
        </KpHeading>

        <KpButton buttonStyle="success-new"
                  buttonSize="md"
                  on:click={createNewMessage}>

            <IconedContent icon="add">
                {localize(/* @kp-localization localization.CreateNewMessage */ 'localization.CreateNewMessage')}
            </IconedContent>
        </KpButton>
    </div>

    <KpHorizontalSeparator/>

    <KpSearchBar minlength="{2}"
                 placeholder="{localize(/* @kp-localization localization.EnterPartOfTextOrCode */ 'localization.EnterPartOfTextOrCode')}"
                 on:search={handleSearch}
                 on:reset={handleSearchReset}/>

    {#if loading}
        <KpLoadingBlock size="sm"/>
    {/if}

    {#if categories && !loading}
        <div class="categories-list" in:fade={fadeInAnimParams}>
            {#each categories as category(category.id)}
                <KpButton buttonSize="sm"
                          buttonStyle="{activeCategories[category.id] ? 'brand-orange-new' : 'default'}"
                          on:click={() => presenter.showCategory(category)}>
                    {category.id}
                </KpButton>
            {/each}
        </div>
    {/if}

    {#if !loading && loadingMessages}
        <KpLoadingBlock size="xs"/>
    {/if}

    {#if languages && !loadingMessages}
        <KpHorizontalSeparator/>

        <div class="localizations-table-container" in:fade={fadeInAnimParams}>
            {#if !showingSearchResults}
                {@const title = Object.keys(activeCategories)[0]}
                {#if title}
                    <h2>{title}</h2>
                {/if}
            {:else}
                <KpHeading type="h2">Výsledky pro hledání '{searchQuery}'</KpHeading>
            {/if}

            <KpClassicTable additionalClasses="kp-localizations-table"
                            additionalContainerClasses="kp-localizations-table-container"
                            responsive
                            horizontallyDivided
                            hoverRows
                            colorAccented
                            stripedRows
                            columnHeadersCentered>

                <tr slot="header">
                    <th>{localize(/* @kp-localization localization.MessageCode */ 'localization.MessageCode')}</th>
                    <th>{localize(/* @kp-localization department.Department.abbr */ 'department.Department.abbr')}</th>
                    {#each languages as lang(lang.id)}
                        <th>{lang.id}</th>
                    {/each}
                    <th>{localize(/* @kp-localization commons.Options */ 'commons.Options')}</th>
                </tr>

                <svelte:fragment slot="body">
                    {#each messages as message(`dep:${message.depId};code:${message.code}`)}
                        <tr>
                            <td class="nowrap data-message-code">{message.code}</td>
                            <td>{message.depId}</td>

                            {#each presenter.getTranslationsByLanguages(message, languages) as translation(translation.language + translation.code)}
                                <td>{translation.value ?? ''}</td>
                            {/each}

                            <td class="data-actions">
                                <KpDropdownMenuButton buttonSize="sm">
                                    <svelte:fragment slot="button">
                                        <UIcon icon="menu-dots"/>
                                    </svelte:fragment>

                                    <svelte:fragment slot="menu">
                                        <KpDropdownMenuItem on:click={() => editMessage(message)}>
                                            <IconedContent icon="edit">
                                                {localize( /* @kp-localization commons.edit */ 'commons.edit')}
                                            </IconedContent>
                                        </KpDropdownMenuItem>

                                        <KpDropdownMenuItem on:click={() => deleteMessage(message)}>
                                            <IconedContent icon="trash">
                                                {localize( /* @kp-localization localization.DeleteText */ 'localization.DeleteText')}
                                            </IconedContent>
                                        </KpDropdownMenuItem>
                                    </svelte:fragment>
                                </KpDropdownMenuButton>
                            </td>
                        </tr>
                    {/each}

                    {#if messages.length === 0}
                        <tr>
                            <td class="text-muted text-center" colspan="{3 + languages.length}">
                                {localize( /* @kp-localization vysledky.nebylyNalezenyZadneZaznamy */ 'vysledky.nebylyNalezenyZadneZaznamy')}
                            </td>
                        </tr>
                    {/if}
                </svelte:fragment>
            </KpClassicTable>
        </div>
    {/if}
</KpLoadablePageContainer>

<style lang="less">
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.variables.less";

    @page-top-spacing: 32px;
    @vertical-spacing: 24px;
    @vertical-spacing-small: 16px;

    .title-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .categories-list {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
    }

    .localizations-table-container {
        display: flex;
        flex-direction: column;
        gap: @vertical-spacing-small;
    }

    :global {
        .kp-localizations {
            .kp-localizations-table-container {
                border-radius: @border-radius-default;
                border: 1px solid @themed-border-default;
            }

            .kp-localizations-table {
                tr:first-child > td {
                    border-top: none;
                }

                tr > th {
                    height: 42px;
                }

                tr > th,
                tr > td {
                    padding: @spacing-sm @spacing-m !important;
                    vertical-align: middle !important;
                    text-align: center;
                }

                .data-message-code {
                    text-align: left;
                }

                .data-actions {
                    text-align: right;
                }
            }
        }
    }
</style>