import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type LocalizationsDataService from './localizations-data.service';
import {ngAsync} from 'shared/utils/ng-@decorators';
import type {Category, Language, Localization} from 'typings/portaro.be.types';
import type {AnyObject} from 'typings/portaro.fe.types';
import type {LocalizationsSearchParams} from './types';

export default class LocalizationsService {
    public static readonly serviceName = 'localizationsService';

    /*@ngInject*/
    constructor(private localizationsDataService: LocalizationsDataService,
                private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    @ngAsync()
    public async query(filterParam: LocalizationsSearchParams): Promise<Localization[]> {
        return this.localizationsDataService.query(filterParam);
    }

    @ngAsync()
    public async createNewMessage(message: Localization | AnyObject): Promise<Localization> {
        try {
            const response = await this.localizationsDataService.createMessage(message);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(response);
            return response.savedObject;
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async editMessage(message: Localization | AnyObject): Promise<Localization> {
        try {
            const response = await this.localizationsDataService.editMessage(message);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(response);
            return response.savedObject;
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async deleteMessage(message: Localization): Promise<void> {
        try {
            await this.localizationsDataService.deleteMessage(message.code, message.depId);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async getCategories(): Promise<Category[]> {
        try {
            return await this.localizationsDataService.getCategories();
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async getLanguages(): Promise<Language[]> {
        try {
            return await this.localizationsDataService.getLanguages();
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }
}