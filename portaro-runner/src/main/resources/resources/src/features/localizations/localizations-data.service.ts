import {ngAsync} from 'shared/utils/ng-@decorators';
import type {Category, FinishedSaveResponse, Language, Localization} from 'typings/portaro.be.types';
import type {AnyObject} from 'typings/portaro.fe.types';
import type {LocalizationsSearchParams} from './types';
import type {AjaxService} from 'core/data-services/ajax.service';

export default class LocalizationsDataService {
    public static serviceName = 'localizationsDataService';

    private readonly REQUEST_ROUTE = 'localizations';
    private readonly CATEGORIES_ROUTE = 'localizations/categories';
    private readonly LANGUAGES_ROUTE = 'localizations/languages';
    private readonly CREATE_ROUTE = 'localizations/create';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async query(filterParam: LocalizationsSearchParams): Promise<Localization[]> {
        return this.ajaxService
            .createRequest(`${this.REQUEST_ROUTE}`)
            .get(filterParam);
    }

    @ngAsync()
    public async getCategories(): Promise<Category[]> {
        return this.ajaxService
            .createRequest(`${this.CATEGORIES_ROUTE}`)
            .get();
    }

    @ngAsync()
    public async getLanguages(): Promise<Language[]> {
        return this.ajaxService
            .createRequest(`${this.LANGUAGES_ROUTE}`)
            .get();
    }

    @ngAsync()
    public async createMessage(message: Localization | AnyObject): Promise<FinishedSaveResponse<Localization>> {
        return this.ajaxService
            .createRequest(`${this.CREATE_ROUTE}`)
            .post(message);
    }

    @ngAsync()
    public async editMessage(message: Localization | AnyObject): Promise<FinishedSaveResponse<Localization>> {
        return this.ajaxService
            .createRequest(`${this.REQUEST_ROUTE}`)
            .post(message);
    }

    @ngAsync()
    public async deleteMessage(code: string, depId: number): Promise<void> {
        return this.ajaxService
            .createRequest(`${this.REQUEST_ROUTE}/${code}/${depId}`)
            .delete();
    }
}

