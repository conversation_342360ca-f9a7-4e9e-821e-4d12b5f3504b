import type LocalizationsService from './localizations.service';
import type {UrlStateManagerService} from 'shared/state-manager/url-state-manager.service';
import type {Category, Language, Localization, Translation} from 'typings/portaro.be.types';
import type {ConvertedLocalization, LocalizationsSearchUrlParams} from './types';

export class KpLocalizationsPresenter {
    public static presenterName = 'kpLocalizationsPresenter';

    /*@ngInject*/
    constructor(private localizationsService: LocalizationsService,
                private urlStateManagerService: UrlStateManagerService<LocalizationsSearchUrlParams>) {
    }

    public loadCategoriesAndLanguages(): Promise<[categories: Category[], languages: Language[]]> {
        return Promise.all([
            this.localizationsService.getCategories(),
            this.localizationsService.getLanguages()
        ]);
    }

    public getUrlState$() {
        return this.urlStateManagerService.getState$();
    }

    public showCategory(category: Category) {
        this.urlStateManagerService.requestChangeState({category: category.id});
    }

    public search(query: string | null) {
        this.urlStateManagerService.requestChangeState({q: query});
    }

    public queryBySearchParams(params: LocalizationsSearchUrlParams): Promise<Localization[]> {
        return Object.keys(params).length === 0 ? Promise.resolve([] as Localization[]) : this.localizationsService.query(params);
    }

    public getTranslationsByLanguages(message: Localization, languages: Language[]): Translation[] {
        return languages.map((lang) => {
            return message.translations[lang.id];
        });
    }

    public getCategoryFromMessageCode(messageCode: string): string {
        return messageCode.split('.', 1)[0];
    }

    public createNewMessage(): Promise<Localization> {
        return this.localizationsService.createNewMessage({});
    }

    public editMessage(message: Localization): Promise<Localization> {
        return this.localizationsService.editMessage({
            ...this.convertLocalizationForServer(message),
            confirmed: false
        });
    }

    public async deleteMessage(message: Localization): Promise<void> {
        await this.localizationsService.deleteMessage(message);
    }

    private convertLocalizationForServer(localization: Localization): ConvertedLocalization {
        const convertedLocalization: ConvertedLocalization = {
            code: localization.code,
            department: localization.depId
        }

        Object.keys(localization.translations).forEach((key) => convertedLocalization[key] = localization.translations[key]?.value);

        return convertedLocalization;
    }
}