import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function localizationsRoutes($stateProvider: StateProvider) {
    let localizationModule: { default: any; };
    $stateProvider
        .state({
            name: 'localizations',
            url: '/localizations?q&category',
            params: {q: {dynamic: true}, category: {dynamic: true}},
            component:  KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => localizationModule.default,
            },
            lazyLoad: async () => {
                localizationModule = await import(/* webpackChunkName: "localizations" */ './KpLocalizations.svelte');
                return null;
            }
        });
}