import register from '@kpsys/angularjs-register';
import localizationRoutes from './localizations.routes';
import LocalizationsDataService from './localizations-data.service';
import LocalizationsService from './localizations.service';
import {KpLocalizationsPresenter} from './kp-localizations.presenter';

export default register('portaro.features.localizations')
    .config(localizationRoutes)
    .service(LocalizationsDataService.serviceName, LocalizationsDataService)
    .service(LocalizationsService.serviceName, LocalizationsService)
    .service(KpLocalizationsPresenter.presenterName, KpLocalizationsPresenter)
    .name();