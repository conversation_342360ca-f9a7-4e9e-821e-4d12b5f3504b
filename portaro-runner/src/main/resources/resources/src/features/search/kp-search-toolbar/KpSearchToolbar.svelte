<script lang="ts" generics="ITEM">
    import type {Readable} from 'svelte/store';
    import type {Fond} from 'typings/portaro.be.types';
    import type {Table} from '@tanstack/svelte-table';
    import {exists} from 'shared/utils/custom-utils';
    import {createSearchToolbarContext} from 'src/features/search/kp-search-toolbar/search-toolbar.context';
    import SearchToolbarFacets from './SearchToolbarFacets.svelte';
    import SearchToolbarSortings from './SearchToolbarSortings.svelte';
    import SearchToolbarSearchBar from './SearchToolbarSearchBar.svelte';
    import SearchToolbarFondSelector from './SearchToolbarFondSelector.svelte';
    import SearchToolbarGridColumnsVisibility from './SearchToolbarGridColumnsVisibility.svelte';
    import KpLoadingInline from 'shared/components/kp-loading/KpLoadingInline.svelte';

    export let fond: Fond | null = null;
    export let relatedFonds: Fond[] | null = null;
    export let table$: Readable<Table<ITEM>> | null = null;
    export let searchType: 'record-name' | 'query' = 'query';
    export let showLoadingIndicator = false;

    createSearchToolbarContext();
</script>

<div class="kp-search-toolbar">
    {#if exists(fond)}
        <SearchToolbarFondSelector {fond} {relatedFonds} on:fond-change/>
    {/if}

    <SearchToolbarFacets/>
    <SearchToolbarSortings/>

    {#if table$}
        <SearchToolbarGridColumnsVisibility {table$}/>
    {/if}

    <SearchToolbarSearchBar {searchType}/>

    <KpLoadingInline size="xxs" showWhen="{showLoadingIndicator}"/>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .kp-search-toolbar {
        display: flex;
        flex-wrap: wrap;
        font-size: @font-size-sm;
        padding: 0 @spacing-xs;
        column-gap: @spacing-l;
        row-gap: @spacing-sm;
    }

    :global {
        .kp-search-toolbar {
            .uicon:not(.dropdown-angle-arrow) {
                font-size: @font-size-default;
            }
        }
    }
</style>