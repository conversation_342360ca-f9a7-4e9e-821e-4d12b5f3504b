<script lang="ts">
    import {getDiscountApprovingContext} from 'src/features/discount-approving/lib/discount-approving-context';
    import {isNullOrUndefined} from 'shared/utils/custom-utils';
    import {fly} from 'svelte/transition';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import DiscountApprovingDetail from 'src/features/discount-approving/parts/detail/DiscountApprovingDetail.svelte';

    const {openedDiscountApprovingPerson: openedPerson} = getDiscountApprovingContext();
</script>

{#key $openedPerson}
    <div class="discount-approving-detail-part"
         class:no-discount-person={isNullOrUndefined($openedPerson)}
         in:fly={{y: 10, duration: 250}}>

        {#if isNullOrUndefined($openedPerson)}
            <IconedContent align="center" justify="center" orientation="vertical" icon="info">
                <span class="text-muted"><PERSON><PERSON> budou zobrazeny podrobnosti o výkazu po rozkliknutí</span>
            </IconedContent>
        {:else}
            <DiscountApprovingDetail person="{$openedPerson}"/>
        {/if}
    </div>
{/key}

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .discount-approving-detail-part {
        display: flex;
        width: 100%;
        height: 100%;
        flex-direction: column;

        &.no-discount-person {
            align-items: center;
            justify-content: center;
        }
    }
</style>