<script lang="ts">
    import {DiscountApprovingService} from 'src/features/discount-approving/lib/discount-approving.service';
    import {createDiscountApprovingContext} from 'src/features/discount-approving/lib/discount-approving-context';
    import {getInjector} from 'core/svelte-context/context';
    import DiscountApprovingTablePart from 'src/features/discount-approving/parts/table/DiscountApprovingTablePart.svelte';
    import DiscountApprovingDetailPart from 'src/features/discount-approving/parts/detail/DiscountApprovingDetailPart.svelte';
    import ErpHorizontalTwoSplitLayout from 'src/features/erp/components/erp-split-layouts/ErpHorizontalTwoSplitLayout.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    const service = getInjector().getByClass(DiscountApprovingService);

    createDiscountApprovingContext(service);
</script>

<ErpHorizontalTwoSplitLayout pageClass="discount-approving-page" withoutLogo>
    <KpHeading type="h1" slot="heading">Schvalování žádostí o slevu</KpHeading>

    <svelte:fragment slot="left-panel">
        <DiscountApprovingTablePart/>
    </svelte:fragment>

    <svelte:fragment slot="right-panel">
        <DiscountApprovingDetailPart/>
    </svelte:fragment>
</ErpHorizontalTwoSplitLayout>

<style lang="less">
    @import (reference) "styles/portaro-erp.less";

    :global {
        &:has(.discount-approving-page) {
            margin-bottom: 0;
            min-height: 100%;
            .flex-grow();
        }

        kp-svelte-component-wrapper:has(.discount-approving-page) {
            .flex-grow();
        }
    }
</style>