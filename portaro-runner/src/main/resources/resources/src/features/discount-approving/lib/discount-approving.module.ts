import register from '@kpsys/angularjs-register';
import discountApprovingRoutes from './discount-approving.routes';
import {DiscountApprovingDataService} from './discount-approving.data-service';
import {DiscountApprovingService} from './discount-approving.service';

export default register('portaro.features.discount-approving')
    .config(discountApprovingRoutes)
    .service(DiscountApprovingDataService.serviceName, DiscountApprovingDataService)
    .service(DiscountApprovingService.serviceName, DiscountApprovingService)
    .name();