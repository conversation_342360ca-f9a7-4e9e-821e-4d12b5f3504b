<script lang="ts">
    import KpBarebonesTableSelectableRow from 'shared/ui-widgets/table/barebones/KpBarebonesTableSelectableRow.svelte';
    import type {Person} from 'typings/portaro.be.types';
    import {getReaderCategory, getRegistrationDate, getRegistrationExpirationDate} from 'shared/utils/user-utils';
    import {getDiscountApprovingContext} from 'src/features/discount-approving/lib/discount-approving-context';
    import {getDateFormatter} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import {Kind} from 'shared/constants/portaro.constants';
    import Label from 'shared/components/kp-label/Label.svelte';

    export let person: Person;

    const context = getDiscountApprovingContext();
    const dateFormatter = getDateFormatter();

    const {openedDiscountApprovingPerson: openedPerson} = getDiscountApprovingContext();
</script>

<KpBarebonesTableSelectableRow on:click={() => context.setOpenedDiscountApprovingPerson(person)}
                               selected="{$openedPerson?.id === person.id}">

    <td>
        <Label labeled={person} explicitKind={Kind.KIND_USER} additionalClasses="user-printable-name"/>
    </td>

    <td>
        {getReaderCategory(person)?.name}
    </td>

    <td>
        {pipe(getRegistrationDate(person), dateFormatter('d.M.yyyy')) ?? ''}
    </td>

    <td>
        {pipe(getRegistrationExpirationDate(person), dateFormatter('d.M.yyyy')) ?? ''}
    </td>
</KpBarebonesTableSelectableRow>