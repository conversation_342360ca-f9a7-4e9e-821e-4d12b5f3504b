<script lang="ts">
    import type {Person, User, UserSearchParams} from 'typings/portaro.be.types';
    import type {SearchOrEditValueEditorOptions} from 'shared/value-editors/internal/editors/search-or-edit/types';
    import type {SearchManager} from 'src/features/search/search-manager/search-manager';
    import {Kind, SearchType, UserType} from 'shared/constants/portaro.constants';
    import {getDiscountApprovingContext} from 'src/features/discount-approving/lib/discount-approving-context';
    import {onDestroy, onMount} from 'svelte';
    import {isFunction} from 'shared/utils/custom-utils';
    import {DISCOUNT_APPROVING_SETTINGS} from 'src/features/discount-approving/lib/discount-approving.constants';
    import KpSearchContext from 'src/features/search/kp-search-context/KpSearchContext.svelte';
    import KpPageableSearchResults from 'src/features/search/kp-search-results/KpPageableSearchResults.svelte';
    import KpBarebonesTable from 'shared/ui-widgets/table/barebones/KpBarebonesTable.svelte';
    import KpSearchToolbar from 'src/features/search/kp-search-toolbar/KpSearchToolbar.svelte';
    import DiscountApprovingItem from 'src/features/discount-approving/parts/table/DiscountApprovingItem.svelte';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    const context = getDiscountApprovingContext();

    let getSearchManager: () => SearchManager<User>;

    function createStaticParams(): UserSearchParams {
        return {
            type: SearchType.TYPE_USER_SEARCH,
            kind: [Kind.KIND_USER],
            pageSize: 50,
            userType: UserType.PERSON_READER,
            userServiceProp: {
                service: DISCOUNT_APPROVING_SETTINGS.discountServicePropsService,
                name: DISCOUNT_APPROVING_SETTINGS.discountServicePropsApprovalStateName,
                value: DISCOUNT_APPROVING_SETTINGS.discountServicePropsApprovalStateDisplayedValue
            }
        };
    }

    onMount(() => {
        context.eventBus.addEventListener(DISCOUNT_APPROVING_SETTINGS.discountRefreshEvent, handleDiscountRefresh);
    });

    onDestroy(() => {
        context.eventBus.removeEventListener(DISCOUNT_APPROVING_SETTINGS.discountRefreshEvent, handleDiscountRefresh);
    });

    const handleDiscountRefresh = () => {
        if (!isFunction(getSearchManager)) {
            return;
        }

        const searchManager = getSearchManager();
        searchManager.refreshSearch();
        searchManager.newSearch();
        context.setOpenedDiscountApprovingPerson(null);
    };

    const setCustomUser = (person: Person) => {
        context.setOpenedDiscountApprovingPerson(person);
    };

    const createValueEditorOptions = (): SearchOrEditValueEditorOptions<string> => {
        return {
            modelComponent: null,
            searchParams: {
                kind: 'user',
                type: 'user-search'
            }
        };
    };
</script>

<div class="custom-user-finder-container">
    <KpValueEditor type="user"
                   options="{createValueEditorOptions()}"
                   localizations="{{search: 'Načtení uživatele bez žádosti o slevu', searchOther: 'Načtení uživatele bez žádosti o slevu'}}"
                   model="{null}"
                   on:model-change={(event) => setCustomUser(event.detail)}/>
</div>

<KpSearchContext staticParams="{createStaticParams()}" localSearch let:searchManager bind:getSearchManager>
    <Flex class="discount-approving-table-container" direction="column" width="100%" gap="xl">
        <KpSearchToolbar/>

        <KpPageableSearchResults noRefreshLoading pagination="{searchManager.getPagination()}" let:paginationData>
            <KpBarebonesTable fontSize="12px">
                <tr slot="header">
                    <th>Jméno a přijmení</th>
                    <th>Čtenářská kategorie</th>
                    <th>Začátek registrace</th>
                    <th>Konec registrace</th>
                </tr>

                <svelte:fragment slot="body">
                    {#each paginationData.items as person(person.id)}
                        <DiscountApprovingItem person={person}/>
                    {/each}
                </svelte:fragment>
            </KpBarebonesTable>
        </KpPageableSearchResults>
    </Flex>
</KpSearchContext>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .custom-user-finder-container {
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
</style>