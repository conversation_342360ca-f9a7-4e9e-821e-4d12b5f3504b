import type {AjaxService} from 'src/core/data-services/ajax.service';
import type {DiscountApprovedRequest, DiscountRejectedRequest} from 'src/features/discount-approving/lib/types';
import {transferify} from 'shared/utils/data-service-utils';
import type {ActionResponse} from 'typings/portaro.be.types';

export class DiscountApprovingDataService {
    public static serviceName = 'discountApprovingDataService';

    public static readonly APPROVED_ROUTE = '/discount/approved';
    public static readonly REJECTED_ROUTE = '/discount/rejected';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    public async approved(discountApproved: DiscountApprovedRequest): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(DiscountApprovingDataService.APPROVED_ROUTE)
            .post(transferify(discountApproved));
    }

    public async rejected(discountRejected: DiscountRejectedRequest): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(DiscountApprovingDataService.REJECTED_ROUTE)
            .post(transferify(discountRejected));
    }

}