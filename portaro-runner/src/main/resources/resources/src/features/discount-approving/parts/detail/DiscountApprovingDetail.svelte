<script lang="ts">
    import type {Document, Person} from 'typings/portaro.be.types';
    import type {DateValueEditorOptions, DateValueEditorValidations} from 'shared/value-editors/internal/editors/date/types';
    import {getDiscountApprovingContext} from 'src/features/discount-approving/lib/discount-approving-context';
    import {isReaderAccountValid} from 'shared/utils/user-utils';
    import {onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {Kind} from 'shared/constants/portaro.constants';
    import {getLocalization} from 'core/svelte-context/context';
    import {DateTime} from 'luxon';
    import KpMediaViewerInline from 'src/features/media-viewer/KpMediaViewerInline.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import Label from 'shared/components/kp-label/Label.svelte';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import KpVerticalSeparator from 'shared/ui-widgets/separator/KpVerticalSeparator.svelte';
    import {DISCOUNT_APPROVING_SETTINGS} from 'src/features/discount-approving/lib/discount-approving.constants';
    import InfoBanner from 'shared/ui-widgets/banner/InfoBanner.svelte';

    export let person: Person;

    const context = getDiscountApprovingContext();
    const localize = getLocalization();

    let saving = false;
    let personWasEdited = false;
    let personRecord: Document | null = null;

    const discountState = context.service.getUserDiscountState(person);

    $: cannotBeSubmitted = !(
        personWasEdited ||
        (discountState.student && DateTime.fromISO(discountState.studentValidityEndDate) >= DateTime.now()) ||
        (discountState.blind && DateTime.fromISO(discountState.blindValidityEndDate) >= DateTime.now()) ||
        (discountState.ztp && DateTime.fromISO(discountState.ztpValidityEndDate) >= DateTime.now()) ||
        (discountState.teacher && DateTime.fromISO(discountState.teacherValidityEndDate) >= DateTime.now()) ||
        (discountState.retiree && DateTime.fromISO(discountState.retireeValidityEndDate) >= DateTime.now())
    );

    onMount(async () => {
        personRecord = await context.service.getPersonRecord(person);
    });

    const handleApproved = async () => {
        saving = true;
        await context.service.approved({user: person, ...discountState});
        refreshUsers();
        saving = false;
    };

    const handleRejected = async () => {
        saving = true;
        await context.service.rejected({user: person});
        refreshUsers();
        saving = false;
    };

    function refreshUsers() {
        context.eventBus.dispatchEvent(new CustomEvent<void>(DISCOUNT_APPROVING_SETTINGS.discountRefreshEvent));
    }

    async function editUser() {
        await context.service.editUser(person);
        personWasEdited = true;
    }

    function createDateEditorValidation(): DateValueEditorValidations {
        return {minDate: new Date(new Date().setHours(0, 0, 0, 0)).toISOString()};
    }

    function createDateEditorOptions(): DateValueEditorOptions {
        return {maximumGranularity: 'date-and-time', viewFormat: 'd.L.y H:mm'};
    }
</script>

<div class="discount-approving-detail">
    {#if isReaderAccountValid(person)}
        <div>
            <InfoBanner bannerType="error">
                <svelte:fragment slot="text">
                    Čtenář má aktivní registraci. Při aktivní registraci nelze automaticky změnit kategorii. Změna se provede až po vypršení registrace uživatele.
                </svelte:fragment>
            </InfoBanner>
        </div>
    {/if}
    <div class="heading-container">
        <h1>
            <strong>
                <Label labeled={person} explicitKind={Kind.KIND_USER} additionalClasses="user-printable-name"/>
            </strong>
        </h1>

        <div class="state-buttons-container">
            <KpButton buttonStyle="danger-new" isDisabled="{saving}" on:click={handleRejected}>
                <IconedContent icon="undo-alt">
                    Zamítnout slevu
                </IconedContent>
            </KpButton>

            <KpButton buttonStyle="success-new" isDisabled="{saving || cannotBeSubmitted}" on:click={handleApproved}>
                <IconedContent icon="check">
                    Schválit slevu
                </IconedContent>
            </KpButton>
        </div>
    </div>

    <div class="discount-approving-editors-container">
        <span class="text-muted">Potvrzení slevy:</span>
        <div class="discount-approving-editors-all-actions-container">
            <div class="discount-approving-editors-all-editor-actions">
                <div>
                    <p class="editors-label">
                        Student:
                    </p>
                    <div class="editors-container">
                        <label for="discount-student-boolean">Validní:</label>
                        <KpValueEditor editorId="discount-student-boolea" type="boolean"
                                       bind:model="{discountState.student}"/>
                        <label for="discount-student-date">Validní do:</label>
                        <KpValueEditor editorId="discount-student-date" type="date"
                                       options="{createDateEditorOptions()}"
                                       validations="{createDateEditorValidation()}"
                                       bind:model="{discountState.studentValidityEndDate}"/>
                    </div>
                </div>
                <div>
                    <p class="editors-label">
                        Učitel:
                    </p>
                    <div class="editors-container">
                        <label for="discount-teacher-boolean">Validní:</label>
                        <KpValueEditor editorId="discount-teacher-boolean" type="boolean"
                                       bind:model="{discountState.teacher}"/>
                        <label for="discount-teacher-date">Validní do:</label>
                        <KpValueEditor editorId="discount-teacher-date"
                                       type="date"
                                       options="{createDateEditorOptions()}"
                                       validations="{createDateEditorValidation()}"
                                       bind:model="{discountState.teacherValidityEndDate}"/>
                    </div>
                </div>
                <div>
                    <p class="editors-label">
                        ZTP:
                    </p>
                    <div class="editors-container">
                        <label for="discount-ztp-boolean">Validní:</label>
                        <KpValueEditor editorId="discount-ztp-boolean" type="boolean"
                                       bind:model="{discountState.ztp}"/>
                        <label for="discount-ztp-date">Validní do:</label>
                        <KpValueEditor editorId="discount-ztp-date" type="date"
                                       options="{createDateEditorOptions()}"
                                       validations="{createDateEditorValidation()}"
                                       bind:model="{discountState.ztpValidityEndDate}"/>
                    </div>
                </div>
                <div>
                    <p class="editors-label">
                        Nevidomý:
                    </p>
                    <div class="editors-container">
                        <label for="discount-blind-boolean">Validní:</label>
                        <KpValueEditor editorId="discount-blind-boolean" type="boolean"
                                       bind:model="{discountState.blind}"/>
                        <label for="discount-blind-date">Validní do:</label>
                        <KpValueEditor editorId="discount-blind-date" type="date"
                                       options="{createDateEditorOptions()}"
                                       validations="{createDateEditorValidation()}"
                                       bind:model="{discountState.blindValidityEndDate}"/>
                    </div>
                </div>
                <div>
                    <p class="editors-label">
                        Důchodce:
                    </p>
                    <div class="editors-container">
                        <label for="discount-retiree-boolean">Validní:</label>
                        <KpValueEditor editorId="discount-retiree-boolean" type="boolean"
                                       bind:model="{discountState.retiree}"/>
                        <label for="discount-retiree-date">Validní do:</label>
                        <KpValueEditor editorId="discount-retiree-date" type="date"
                                       options="{createDateEditorOptions()}"
                                       validations="{createDateEditorValidation()}"
                                       bind:model="{discountState.retireeValidityEndDate}"/>
                    </div>
                </div>
            </div>
            <KpVerticalSeparator/>
            <div class="discount-approving-user-editation">
                <p class="editors-label">
                    Editace uživatele:
                </p>
                <KpButton isBlock
                          dataQa="edit-user-button"
                          isDisabled="{person.deleted}"
                          on:click={() => editUser()}>

                    {localize(/* @kp-localization commons.editovat */ 'commons.editovat')}
                </KpButton>
            </div>
        </div>
    </div>

    {#if exists(personRecord)}
        <div class="identifiers-container column-container">
            <span class="text-muted">Doklady:</span>
            <div class="verbis-viewer-container">
                <KpMediaViewerInline record={personRecord} editMode={false}/>
            </div>
        </div>
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .discount-approving-detail {
        display: flex;
        flex-direction: column;
        gap: 32px;
    }

    .identifiers-container .verbis-viewer-container {
        display: flex;
        position: relative;
        height: 600px;
        border-top: 1px solid @themed-border-default;
        border-bottom: 1px solid @themed-border-default;
        margin-left: calc(@spacing-xl * -1);
        margin-right: calc(@spacing-xxl * -1);
        width: calc(@spacing-xl + 100% + @spacing-xxl);
    }

    .discount-approving-editors-container {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .discount-approving-editors-all-actions-container {
            display: flex;
            flex: 1 1 0;
            gap: 32px;
            justify-content: space-between;

            .discount-approving-editors-all-editor-actions {
                display: flex;
                flex-direction: column;
                gap: 10px;
                flex: 1 1 0;

                .editors-label {
                    font-weight: bolder;
                    font-size: @font-size-large;
                    margin: 0;
                }

                .editors-container {
                    display: flex;
                    align-items: center;
                    gap: @spacing-m;
                }

            }

            .discount-approving-user-editation {
                min-width: 300px;
                display: flex;
                flex: 0 1 0;
                flex-direction: column;
            }
        }
    }

    .heading-container {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .state-buttons-container {
            display: flex;
            align-items: center;
            gap: @spacing-s;
        }
    }

</style>