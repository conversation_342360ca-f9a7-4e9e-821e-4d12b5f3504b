import type {Readable} from 'svelte/store';
import {writable} from 'svelte/store';
import {getContext, hasContext, setContext} from 'svelte';
import type {Person} from 'typings/portaro.be.types';
import type {DiscountApprovingService} from 'src/features/discount-approving/lib/discount-approving.service';

const discountApprovingContextKey = 'discount-approving-ctx';

interface DiscountApprovingContext {
    service: DiscountApprovingService;
    eventBus: EventTarget;
    openedDiscountApprovingPerson: Readable<Person | null>;
    setOpenedDiscountApprovingPerson: (person: Person | null) => void;
}

export function createDiscountApprovingContext(service: DiscountApprovingService): DiscountApprovingContext {
    const openedDiscountApprovingPerson = writable<Person | null>(null);
    const eventBus = new EventTarget();

    return setContext<DiscountApprovingContext>(discountApprovingContextKey, {
        service,
        eventBus,
        openedDiscountApprovingPerson,
        setOpenedDiscountApprovingPerson: (openedDiscountApproving) => openedDiscountApprovingPerson.set(openedDiscountApproving),

    });
}

export function getDiscountApprovingContext(): DiscountApprovingContext {
    if (!hasContext(discountApprovingContextKey)) {
        throw new Error('Discount approving context does not exist! Use `createDiscountApprovingContext` function to create it in a parent component.');
    }

    return getContext<DiscountApprovingContext>(discountApprovingContextKey);
}