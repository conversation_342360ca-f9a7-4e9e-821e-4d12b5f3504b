import type {User} from 'typings/portaro.be.types';

export interface DiscountApprovedRequest {
    user: User,
    ztp: boolean,
    ztpValidityEndDate: string,
    student: boolean,
    studentValidityEndDate: string,
    teacher: boolean,
    teacherValidityEndDate: string,
    blind: boolean,
    blindValidityEndDate: string,
    retiree: boolean,
    retireeValidityEndDate: string,
}

export interface DiscountRejectedRequest {
    user: User
}

export interface DiscountApprovalState{
    ztp: boolean,
    ztpValidityEndDate: string,
    student: boolean,
    studentValidityEndDate: string,
    teacher: boolean,
    teacherValidityEndDate: string,
    blind: boolean,
    blindValidityEndDate: string,
    retiree: boolean,
    retireeValidityEndDate: string,
}