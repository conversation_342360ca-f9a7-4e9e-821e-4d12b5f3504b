import type FinishedResponseInteractionService from 'src/shared/services/finished-response-interaction.service';
import type RecordDataService from 'src/features/record/record.data-service';
import {exists} from 'shared/utils/custom-utils';
import type {Document, Person, User, UserServiceProperty} from 'typings/portaro.be.types';
import type UserService from 'src/features/user/user.service';
import type {DiscountApprovingDataService} from 'src/features/discount-approving/lib/discount-approving.data-service';
import type {DiscountApprovalState, DiscountApprovedRequest, DiscountRejectedRequest} from 'src/features/discount-approving/lib/types';
import {DateTime} from 'luxon';
import {DISCOUNT_APPROVING_SETTINGS, DiscountTypes} from 'src/features/discount-approving/lib/discount-approving.constants';

export class DiscountApprovingService {
    public static serviceName = 'discountApprovingService';

    /*@ngInject*/
    constructor(private discountApprovingDataService: DiscountApprovingDataService,
                private recordDataService: RecordDataService,
                private userService: UserService,
                private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    public async getPersonRecord(person: Person): Promise<Document> {
        if (exists(person.rid)) {
            try {
                return await this.recordDataService.getById(person.rid) as Document;
            } catch (exceptionResponse) {
                this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
                return null;
            }
        }
        return null;
    }

    public async editUser(user: User) {
        await this.userService.editUserByForm(user);
    }

    public async approved(discountApproved: DiscountApprovedRequest) {
        try {
            const actionResponse = await this.discountApprovingDataService.approved(discountApproved);
            await this.finishedResponseInteractionService.showSuccessResponseInModalWindow(actionResponse);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
        }
    }

    public async rejected(discountRejected: DiscountRejectedRequest) {
        try {
            const actionResponse = await this.discountApprovingDataService.rejected(discountRejected);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
        }
    }

    public getUserDiscountState(person: Person): DiscountApprovalState {
        const defaultEndValidity = DateTime.now().plus({ year: 1 }).toISO();
        const lifeTimeEndValidity = DateTime.now().plus({ year: 80 }).toISO();

        const findProperty = (name: string): UserServiceProperty | undefined =>
            person.userServiceProperties.find((p) => p.service === DISCOUNT_APPROVING_SETTINGS.discountServicePropsService && p.name === name);

        return {
            ztp: findProperty(DiscountTypes.ZTP)?.value === 'true' || false,
            ztpValidityEndDate: findProperty(DiscountTypes.ZTP)?.validityEndDate ? findProperty(DiscountTypes.ZTP).validityEndDate : defaultEndValidity,

            student: findProperty(DiscountTypes.STUDENT)?.value === 'true' || false,
            studentValidityEndDate: findProperty(DiscountTypes.STUDENT)?.validityEndDate ? findProperty(DiscountTypes.STUDENT).validityEndDate : defaultEndValidity,

            teacher: findProperty(DiscountTypes.TEACHER)?.value === 'true' || false,
            teacherValidityEndDate: findProperty(DiscountTypes.TEACHER)?.validityEndDate ? findProperty(DiscountTypes.TEACHER).validityEndDate : defaultEndValidity,

            blind: findProperty(DiscountTypes.BLIND)?.value === 'true' || false,
            blindValidityEndDate: findProperty(DiscountTypes.BLIND)?.validityEndDate ? findProperty(DiscountTypes.BLIND).validityEndDate : lifeTimeEndValidity,

            retiree: findProperty(DiscountTypes.RETIREE)?.value === 'true' || false,
            retireeValidityEndDate: findProperty(DiscountTypes.RETIREE)?.validityEndDate ? findProperty(DiscountTypes.RETIREE).validityEndDate : lifeTimeEndValidity,
        };
    }
}