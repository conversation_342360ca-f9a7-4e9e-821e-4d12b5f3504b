import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'src/core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function discountApprovingRoutes($stateProvider: StateProvider) {
    let discountStationListModule: {default: any;};

    $stateProvider
        .state({
            name: 'discount-approving',
            url: '/discount-approving',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => discountStationListModule.default
            },
            lazyLoad: async () => {
                discountStationListModule = await import(/* webpackChunkName: "discount-approving" */ '../KpDiscountApprovingPage.svelte');
                return null;
            }
        });
}