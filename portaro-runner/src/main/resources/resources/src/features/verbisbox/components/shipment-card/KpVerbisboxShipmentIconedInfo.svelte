<script lang="ts">
    import type {UIcons} from 'shared/ui-widgets/uicons/types';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let icon: UIcons;
    export let startAligned = false;
    export let redHighlighted = false;
</script>

<div class="verbisbox-shipment-iconed-info"
     class:start-aligned={startAligned}>

    <IconedContent {icon}
                   gap="10px"
                   iconColor="{redHighlighted ? 'var(--danger-red)' : 'inherit'}"
                   align="{startAligned ? 'start' : 'center'}"
                   textAlign="start"
                   justify="start">

        <small class="text-muted"
               class:start-aligned={startAligned}
               class:red-highlighted={redHighlighted}>

            <slot/>
        </small>
    </IconedContent>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    small.start-aligned {
        display: flex;
        flex-direction: column;
        align-items: start;
        gap: @spacing-s;
    }

    :global {
        .verbisbox-shipment-iconed-info.start-aligned {
            span.uicon {
                margin-top: 2px;
            }
        }
    }
</style>