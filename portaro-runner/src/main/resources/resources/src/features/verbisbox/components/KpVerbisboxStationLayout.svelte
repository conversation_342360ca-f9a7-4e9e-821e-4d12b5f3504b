<script lang="ts">
    import type {PhysicalBox, StationLayout, ViewableStationState} from '../types';
    import {BoxCapability} from '../types';
    import KpVerbisboxSingleBoxPanel from './KpVerbisboxSingleBoxPanel.svelte';
    import KpVerbisboxStationInfoPanel from './KpVerbisboxStationInfoPanel.svelte';
    import KpVerbisboxControlBox from './KpVerbisboxControlBox.svelte';
    import {mediaQueryScreenMdMin} from 'shared/ui-widgets/constants';

    export let layout: StationLayout;
    export let state: ViewableStationState;

    let windowWidth = 0;

    function getMaxColumn(stationLayout: StationLayout) {
        return Math.max(...stationLayout.boxes.map((box) => box.position.column));
    }

    function getColumnsCount(stationLayout: StationLayout) {
        return getMaxColumn(stationLayout) + 1;
    }

    function getGridHeightInRows(stationLayout: StationLayout, innerWindowWidth: number) {
        return Math.max(...getColumns(stationLayout, innerWindowWidth).map((column) => getGridSizeOfColumn(column)));
    }

    function getColumns(stationLayout: StationLayout, innerWindowWidth: number): PhysicalBox[][] {
        const maxColumn = getMaxColumn(stationLayout);
        const listOfColumns: PhysicalBox[][] = Array.from({length: maxColumn + 1}, () => []);
        stationLayout.boxes.forEach((box) => {
            const {row, column} = box.position;
            listOfColumns[column][row] = box;
        });

        if (innerWindowWidth > mediaQueryScreenMdMin) {
            return listOfColumns;
        }

        return listOfColumns.map((column) => {
            return column.toSorted((a, b) => b.position.row - a.position.row);
        });
    }

    function getBoxesByRow(stationLayout: StationLayout, rowNumber: number): PhysicalBox[] {
        return stationLayout.boxes.filter((box) => box.position.row === rowNumber);
    }

    function getGridSizeOfColumn(boxesFromSameColumn: PhysicalBox[]) {
        return boxesFromSameColumn
            .map((box) => getBoxSize(box))
            .reduce((sumOfSizes, boxSize) => sumOfSizes + boxSize, 0);
    }

    function getBaseBoxHeight(): number {
        return Math.min(...layout.boxes.map((box) => box.size.height))
    }

    function getBoxSize(box: PhysicalBox) {
        const baseBoxHeight = getBaseBoxHeight();
        if (box.size.height % baseBoxHeight === 0) {
            return box.size.height / baseBoxHeight; // if box height is factor of base height size is used factor
        }
        return 1; // other boxes will behave as if size is 1
    }

    function getBoxColumnOffset(box: PhysicalBox, columns: PhysicalBox[][]) {
        if (box.position.row === 0) {
            return 0; // first box has 0 offset
        }
        const column = columns[box.position.column];
        const boxesBelow = column.filter((_, index) => index < box.position.row);
        return getGridSizeOfColumn(boxesBelow);
    }

    function isControlBox(box: PhysicalBox): boolean {
        return box.capabilities.includes(BoxCapability.CONTROL) || box.capabilities.includes(BoxCapability.DISPLAY);
    }

    function getBoxState(physicalBox: PhysicalBox, stationState: ViewableStationState) {
        return stationState.boxes.find((boxState) => boxState.id === physicalBox.id);
    }

    function moveToOneBasedIndex(zeroBasedIndex: number) {
        // css grid starts order at 1
        return zeroBasedIndex + 1;
    }

    function getBoxOffset(box: PhysicalBox, innerWindowWidth: number): number {
        return getGridHeightInRows(layout, innerWindowWidth) - getBoxColumnOffset(box, getColumns(layout, innerWindowWidth)) - getBoxSize(box);
    }

    function getBoxColumn(box: PhysicalBox): number {
        return box.position.column;
    }

    function getBoxAspectRatio(box: PhysicalBox) {
        return box.size.width / box.size.height;
    }

    function isMultiRowBox(box: PhysicalBox): boolean {
        return getBoxSize(box) > 1 && getBoxesByRow(layout, box.position.row).map((boxInSameRow) => getBoxSize(boxInSameRow)).some((boxSize) => boxSize === 1);
    }
</script>

<svelte:window bind:innerWidth={windowWidth}/>

<div class="verbisbox-station-layout-container">
    <KpVerbisboxStationInfoPanel stationHealth={state.health} --columns-count={getColumnsCount(layout)}/>

    <div class="station-layout-container"
         style:--columns-count={getColumnsCount(layout)}
         style:--rows={getGridHeightInRows(layout, windowWidth)}>

        {#each getColumns(layout, windowWidth) as rowsInColumn}
            {#each rowsInColumn as box (box.id)}
                <div class="box-container"
                     title="column: {moveToOneBasedIndex(getBoxColumn(box))}, row: {moveToOneBasedIndex(getBoxOffset(box, windowWidth))}, size: {getBoxSize(box)}"
                     style:--column={moveToOneBasedIndex(getBoxColumn(box))}
                     style:--offset={moveToOneBasedIndex(getBoxOffset(box, windowWidth))}
                     style:--size={getBoxSize(box)}
                     style:--aspect-ratio={getBoxAspectRatio(box)}
                     class:multi-row-box={isMultiRowBox(box)}>

                    {#if !isControlBox(box)}
                        <KpVerbisboxSingleBoxPanel label={box.label} boxState={getBoxState(box, state)}/>
                    {:else}
                        <KpVerbisboxControlBox display={state.display}/>
                    {/if}
                </div>
            {/each}
        {/each}
    </div>
</div>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    @dark: #5f5e6e;
    @light: #f5f5f5;

    .verbisbox-station-layout-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;

        .station-layout-container {
            width: 100%;
            background-color: @dark;
            border: 8px solid @dark;

            @media (max-width: @screen-md-min) {
                display: flex;
                flex-direction: column;
                gap: 6px;
                max-width: none;
            }

            @media (min-width: @screen-md-min) {
                display: grid;
                grid-template-columns: repeat(var(--columns-count), 1fr);
                gap: 8px 6px;
                place-items: stretch;
                align-content: end;
                max-width: calc(var(--columns-count) * 25%);
            }

            @media (min-width: @screen-md-min) and (max-width: @screen-md-max) {
                font-size: 10px;
            }

            @media (min-width: @screen-lg-min) {
                font-size: 12px;
            }

            .box-container {
                background-color: @light;
                box-shadow: 1px 1px 2px 1px black;

                @media (min-width: @screen-md-min) {
                    grid-column: var(--column) / span 1;
                    grid-row: var(--offset) / span var(--size);

                    &:not(.multi-row-box) {
                        aspect-ratio: var(--aspect-ratio);
                    }
                }
            }
        }
    }
</style>