import type {StateProvider} from '@uirouter/angularjs';
import type {Transition} from '@uirouter/angularjs';
import type {VerbisboxService} from './verbisbox.service';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function verbisboxRoutes($stateProvider: StateProvider) {
    let verbisboxStationListModule: { default: any; };
    $stateProvider
        .state({
            name: 'verbisbox-station-list',
            url: '/verbisbox',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => verbisboxStationListModule.default,
            },
            lazyLoad: async () => {
                verbisboxStationListModule = await import(/* webpackChunkName: "verbisbox-station-list" */ './pages/KpVerbisboxStationListPage.svelte');
                return null;
            }
        });

    let verbisboxStationModule: { default: any; };
    $stateProvider
        .state({
            name: 'verbisbox-station',
            url: '/verbisbox/:stationId',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => verbisboxStationModule.default,

                /*@ngInject*/
                props: async (verbisboxService: VerbisboxService, $transition$: Transition) => {
                    const station = await verbisboxService.getStation($transition$.params().stationId);
                    const layout = await verbisboxService.getLayout(station);
                    return {station, layout};
                }
            },
            lazyLoad: async () => {
                verbisboxStationModule = await import(/* webpackChunkName: "verbisbox-station" */ './pages/KpVerbisboxPage.svelte');
                return null;
            }
        });
}