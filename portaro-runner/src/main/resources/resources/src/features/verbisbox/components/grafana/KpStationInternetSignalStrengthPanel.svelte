<script lang="ts">
    import KpStationsMetricsPanel from './general/KpStationsMetricsPanel.svelte';
    import {
        DEFAULT_PANEL_HEIGHT,
        DEFAULT_PANEL_REFRESH,
        DEFAULT_PANEL_THEME,
        DEFAULT_PANEL_WIDTH,
        STATION_METRICS_INTERNET_SIGNAL_STRENGTH_PANEL_ID,
    } from './general/grafana.constants';

    export let stationIds: string[];
    export let refresh = DEFAULT_PANEL_REFRESH;
    export let from: Date = null;
    export let to: Date = null;
    export let theme: 'dark' | 'light' = DEFAULT_PANEL_THEME;

    export let width = DEFAULT_PANEL_WIDTH;
    export let height = DEFAULT_PANEL_HEIGHT;
</script>

<KpStationsMetricsPanel panelId={STATION_METRICS_INTERNET_SIGNAL_STRENGTH_PANEL_ID}
                        variables={{station: stationIds}}
                        {refresh}
                        {from}
                        {to}
                        {theme}
                        {width}
                        {height}/>
