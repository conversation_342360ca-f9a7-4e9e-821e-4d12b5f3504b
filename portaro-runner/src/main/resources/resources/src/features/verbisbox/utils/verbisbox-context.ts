import type {Writable, Readable} from 'svelte/store';
import type {Box, BoxStation} from '../types';
import {getContext, setContext} from 'svelte';

export interface VerbisboxContext {
    isPickingSelectionEnabled$: Readable<boolean>;
    isNonPickingSelectionEnabled$: Readable<boolean>;
    selectedBoxes$: Writable<Box[]>;
    stationName: string;
    station: BoxStation;
}

export function setVerbisboxContext(context: VerbisboxContext) {
    setContext(VERBISBOX_CONTEXT_KEY, context);
}

export function getVerbisboxContext(): VerbisboxContext {
    return getContext(VERBISBOX_CONTEXT_KEY);
}

const VERBISBOX_CONTEXT_KEY = 'VERBISBOX_CONTEXT_KEY';