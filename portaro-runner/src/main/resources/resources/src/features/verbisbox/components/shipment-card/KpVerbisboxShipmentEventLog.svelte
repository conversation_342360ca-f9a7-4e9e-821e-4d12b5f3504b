<script lang="ts">
    import type {ViewableShipmentInsight} from '../../types';
    import {getDateFormatter, getLocalization} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import KpClassicTable from 'shared/ui-widgets/table/classic/KpClassicTable.svelte';
    import KpCollapsibleGenericPanel from 'shared/ui-widgets/panel/KpCollapsibleGenericPanel.svelte';
    import KpCollapsibleGenericPanelToggle from 'shared/ui-widgets/panel/KpCollapsibleGenericPanelToggle.svelte';

    export let shipment: ViewableShipmentInsight;

    const dateFormatter = getDateFormatter();
    const localize = getLocalization();
</script>

<KpCollapsibleGenericPanel additionalClasses="verbisbox-shipment-event-log-panel">
    <div class="event-log-heading" slot="heading" let:open>
        <span>
            {localize(/* @kp-localization verbisbox.shipment.Events */ 'verbisbox.shipment.Events')}
        </span>

        <KpCollapsibleGenericPanelToggle isOpened="{open}"/>
    </div>

    <KpClassicTable stripedRows
                    hoverRows
                    colorAccented
                    horizontallyDivided
                    responsive>

        <tr slot="header">
            <th>{localize(/* @kp-localization verbisbox.shipment.EventType */ 'verbisbox.shipment.EventType')}</th>
            <th>{localize(/* @kp-localization verbisbox.shipment.EventDate */ 'verbisbox.shipment.EventDate')}</th>
        </tr>

        <svelte:fragment slot="body">
            {#each shipment.events as event}
                <tr>
                    <td>{event.type}</td>
                    <td>{pipe(event.date.date, dateFormatter('dd. MM. yyyy HH:mm'))}</td>
                </tr>
            {/each}
        </svelte:fragment>
    </KpClassicTable>
</KpCollapsibleGenericPanel>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";
    @import (reference) "styles/portaro.variables.less";

    .event-log-heading {
        display: flex;
        align-items: center;

        span {
            flex: 1;
        }
    }

    :global {
        .verbisbox-shipment-event-log-panel .panel-body {
            padding: 0 !important;
        }
    }

    tr {
        th, td {
            padding: @table-cell-padding @spacing-ml !important;
        }
    }
</style>