<script lang="ts">
    import type {BoxStation, ViewableShipmentInsight} from '../types';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {VerbisboxService} from '../verbisbox.service';
    import {exists} from 'shared/utils/custom-utils';
    import KpVerbisboxShipmentCard from './shipment-card/KpVerbisboxShipmentCard.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';

    export let station: BoxStation;

    const localize = getLocalization();
    const presenter = getInjector().getByToken<VerbisboxService>(VerbisboxService.presenterName);

    let activeShipments: ViewableShipmentInsight[];
    presenter.getActiveShipments(station).then((value) => activeShipments = value);

    async function handleReloadShipments() {
        activeShipments = await presenter.getActiveShipments(station);
    }
</script>

<div class="verbisbox-active-shipments-container">
    <h2>{localize(/* @kp-localization verbisbox.ActiveShipments */ 'verbisbox.ActiveShipments')}</h2>

    {#if exists(activeShipments)}
        {#if activeShipments.length === 0}
            <div class="no-shipments text-muted text-center">
                {localize(/* @kp-localization verbisbox.NoActiveShipments */ 'verbisbox.NoActiveShipments')}
            </div>
        {:else}
            <div class="verbisbox-active-shipments-list">
                {#each activeShipments as shipment}
                    <KpVerbisboxShipmentCard {shipment} on:reload-shipments={() => handleReloadShipments()}/>
                {/each}
            </div>
        {/if}
    {:else}
        <KpLoadingBlock/>
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .verbisbox-active-shipments-container {
        display: flex;
        flex-direction: column;
        gap: @spacing-ml;
        width: 100%;

        h2 {
            margin: 0;
        }

        .verbisbox-active-shipments-list {
            display: flex;
            flex-direction: column;
            gap: @spacing-l;
        }
    }
</style>