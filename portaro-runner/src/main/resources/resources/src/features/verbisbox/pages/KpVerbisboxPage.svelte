<script lang="ts">
    import type {BoxStation, StationLayout} from '../types';
    import KpVerbisboxStation from '../components/KpVerbisboxStation.svelte';
    import KpPageContainer from 'shared/layouts/containers/KpPageContainer.svelte';

    export let station: BoxStation;
    export let layout: StationLayout;
</script>

<KpPageContainer id="verbisbox-station" additionalClasses="kp-verbisbox-page">
    <KpVerbisboxStation {station} {layout} stationName={station.text}/>
</KpPageContainer>