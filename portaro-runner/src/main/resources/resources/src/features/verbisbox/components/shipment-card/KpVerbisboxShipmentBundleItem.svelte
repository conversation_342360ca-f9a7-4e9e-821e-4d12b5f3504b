<script lang="ts">
    import type {ViewableItemInsight} from '../../types';
    import {exists} from 'shared/utils/custom-utils';
    import {pipe} from 'core/utils';
    import {getDateFormatter, getInjector, getLocalization} from 'core/svelte-context/context';
    import {VerbisboxService} from '../../verbisbox.service';
    import {createEventDispatcher} from 'svelte';
    import {getVerbisboxContext} from '../../utils/verbisbox-context';
    import {Kind} from 'shared/constants/portaro.constants';
    import KpCover from '../../../record/kp-cover/KpCover.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import Label from 'shared/components/kp-label/Label.svelte';
    import KpVerbisboxShipmentIconedInfo from './KpVerbisboxShipmentIconedInfo.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let item: ViewableItemInsight;

    const localize = getLocalization();
    const dateFormatter = getDateFormatter();
    const dispatch = createEventDispatcher();
    const presenter = getInjector().getByToken<VerbisboxService>(VerbisboxService.presenterName);
    const station = getVerbisboxContext().station;

    function isItemMoveable(changeableItem: ViewableItemInsight) {
        return !(exists(changeableItem.insert) || exists(changeableItem.cancel));
    }

    $: itemMoveable = isItemMoveable(item);

    const handleMoveToNewBundleClock = async () => {
        await presenter.moveToNewBundle(station, item, null);
        dispatch('reload-shipments', station);
    }
</script>

<div class="verbisbox-shipment-bundle-item"
     class:red-highlighted-background={exists(item.cancel)}>

    {#if exists(item.record)}
        <div class="cover-container">
            <KpCover record={item.record} fillTo="height"/>
        </div>
    {/if}

    <div class="item-info-container">
        <span>
            {#if exists(item.record)}
                <Label labeled="{item.record}" explicitKind={Kind.KIND_RECORD}/>
            {:else}
                {item.text ?? item.id}
            {/if}

            {#if exists(item.cancel)}
                <strong class="red-highlighted">({localize(/* @kp-localization verbisbox.shipment.StateCancelled */ 'verbisbox.shipment.StateCancelled')})</strong>
            {/if}
        </span>

        {#if !exists(item.identifiers) || item.identifiers.length === 0}
            <small class="red-highlighted no-identifiers">
                {localize(/* @kp-localization verbisbox.item.NoIdentifiers */ 'verbisbox.item.NoIdentifiers')}
            </small>
        {/if}

        <KpVerbisboxShipmentIconedInfo icon="clock" startAligned>
            <span>
                {localize(/* @kp-localization verbisbox.shipment.DateCreated */ 'verbisbox.shipment.DateCreated')}:
                {pipe(item.create.date, dateFormatter('dd. MM. yyyy HH:mm'))}
            </span>

            {#if exists(item.insert)}
                <span>
                    {localize(/* @kp-localization verbisbox.shipment.DateInserted */ 'verbisbox.shipment.DateInserted')}:
                    {pipe(item.insert.date, dateFormatter('dd. MM. yyyy HH:mm'))}
                </span>
            {/if}

            {#if exists(item.cancel)}
                <span class="red-highlighted">
                    {localize(/* @kp-localization verbisbox.shipment.DateCancelled */ 'verbisbox.shipment.DateCancelled')}:
                    {pipe(item.cancel.date, dateFormatter('dd. MM. yyyy HH:mm'))}
                </span>
            {/if}
        </KpVerbisboxShipmentIconedInfo>
    </div>

    {#if itemMoveable}
        <KpButton buttonStyle="brand-orange-new" on:click={handleMoveToNewBundleClock}>
            <IconedContent icon="box">
                {localize(/* @kp-localization verbisbox.item.ChangeBundle */ 'verbisbox.item.ChangeBundle')}
            </IconedContent>
        </KpButton>
    {/if}
</div>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";
    @import (reference) "styles/portaro.variables.less";

    .verbisbox-shipment-bundle-item {
        display: flex;
        gap: @spacing-sm;
        padding: @spacing-m @spacing-ml;
        align-items: center;

        &.red-highlighted-background {
            background-color: #FCEAEC;
        }

        .cover-container {
            height: 60px;
        }

        .item-info-container {
            display: flex;
            flex-direction: column;
            gap: @spacing-s;
            flex: 1;

            .no-identifiers {
                margin-top: calc(@spacing-s * -1);
            }
        }
    }
</style>