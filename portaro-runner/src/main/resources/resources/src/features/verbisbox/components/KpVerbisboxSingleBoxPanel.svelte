<script lang="ts">
    import type {ViewableBox, ViewableItemInsight} from '../types';
    import {BoxDoorState} from '../types';
    import {exists, isNullOrUndefined} from 'shared/utils/custom-utils';
    import {getDateFormatter, getLocalization} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import {DateTime} from 'luxon';
    import {clickOutside} from 'shared/svelte-actions/use.click-outside';
    import {enforceFocusWithin} from 'shared/svelte-actions/use.enforce-focus-within';
    import {fade} from 'svelte/transition';
    import {getVerbisboxContext} from '../utils/verbisbox-context';
    import {format} from 'shared/utils/string-utils';
    import {zoomFrom} from '../utils/transitions';
    import {disableScrolling} from 'shared/svelte-actions/use.disable-scrolling';
    import {Kind} from 'shared/constants/portaro.constants';
    import KpShipmentItems from './shipment-card/KpVerbisboxShipmentBundleItemsList.svelte';
    import KpPluralizeText from 'shared/components/kp-pluralize-text/KpPluralizeText.svelte';
    import Checkbox from 'shared/ui-widgets/checkbox/Checkbox.svelte';
    import Label from 'shared/components/kp-label/Label.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let label: string;
    export let boxState: ViewableBox;

    const dateFormatter = getDateFormatter();
    const localize = getLocalization();

    const {selectedBoxes$, isPickingSelectionEnabled$, isNonPickingSelectionEnabled$} = getVerbisboxContext();

    let hasItems: boolean = exists(boxState.items) && boxState.items.length > 0;
    $: hasItems = exists(boxState.items) && boxState.items.length > 0;

    let statusMessage: string;
    $: statusMessage = getStatusMessage(!hasItems, boxState.insertDate);

    let statusMessageWithDuration: string;
    $: statusMessageWithDuration = `${statusMessage} ${getStatusDuration(!hasItems, boxState)}`;

    let validItems: ViewableItemInsight[];
    $: validItems = filterOutCancelledItems(boxState.items ?? []);

    let clickable: boolean;
    $: clickable = hasItems && !$isPickingSelectionEnabled$ && !$isNonPickingSelectionEnabled$;

    function getStatusMessage(isEmpty: boolean, insertDate: string): string {
        if (isEmpty) {
            return localize(/* @kp-localization verbisbox.boxStatus.Empty */ 'verbisbox.boxStatus.Empty');
        }
        if (exists(insertDate)) {
            return localize(/* @kp-localization verbisbox.boxStatus.WaitingForPickup */ 'verbisbox.boxStatus.WaitingForPickup');
        }
        return localize(/* @kp-localization verbisbox.boxStatus.WaitingForInsert */ 'verbisbox.boxStatus.WaitingForInsert');
    }

    function getStatusDuration(isEmpty: boolean, state: ViewableBox): string {
        if (isEmpty) {
            return '';
        }
        if (exists(state.insertDate)) {
            return formatDurationSinceDateTime(parseDate(state.insertDate));
        }
        return formatDurationSinceDateTime(parseDate(state.assignDate));
    }

    function isAfterPickupDeadline(deadlineDateInISO: string): boolean {
        if (isNullOrUndefined(deadlineDateInISO)) {
            return false;
        }
        const deadlineDateTime = parseDate(deadlineDateInISO);
        return DateTime.now().diff(deadlineDateTime).toMillis() > 0;
    }

    function formatDurationSinceDateTime(dateTime: DateTime): string {
        const timeDurationSinceInsert = DateTime.now().diff(dateTime);
        const {
            minutes,
            days,
            hours
        } = timeDurationSinceInsert.shiftTo('days', 'hours', 'minutes', 'seconds').toObject(); // shift to seconds to get minutes as whole number

        if (days > 0) {
            return format(days.toString(10))(localize(/* @kp-localization commons.time.xDaysShort */ 'commons.time.xDaysShort'));
        }
        if (hours > 0) {
            return format(hours.toString(10))(localize(/* @kp-localization commons.time.xHoursShort */ 'commons.time.xHoursShort'));
        }
        return format(minutes.toString(10))(localize(/* @kp-localization commons.time.xMinutesShort */ 'commons.time.xMinutesShort'));
    }

    function filterOutCancelledItems(items: ViewableItemInsight[]): ViewableItemInsight[] {
        return items.filter((item) => isNullOrUndefined(item.cancel));
    }

    function parseDate(dateInISO: string): DateTime {
        return DateTime.fromISO(dateInISO);
    }

    let isDetailViewOpened = false;

    function close() {
        isDetailViewOpened = false;
    }

    function toggleSelect(event: CustomEvent<{checked: boolean}>) {
        if (event.detail.checked) {
            selectedBoxes$.update((selectedBoxes) => [...selectedBoxes, boxState]);
        } else {
            selectedBoxes$.update((selectedBoxes) => selectedBoxes.filter((selectedBox) => selectedBox.id !== boxState.id));
        }
    }

    function open() {
        if (clickable) {
            isDetailViewOpened = true;
        }
    }

    let singleBoxPanel: HTMLElement;
</script>

<svelte:body use:disableScrolling={isDetailViewOpened}/>

<!-- svelte-ignore a11y-click-events-have-key-events svelte-ignore a11y-no-static-element-interactions sr-only button inside the panel is used for keyboard control -->
<div class="single-box-panel" class:clickable on:click={open} bind:this={singleBoxPanel}>
    <div class="box-header">
        <span class="box-label">{label}</span>

        {#key boxState.doorState}
            <div class="door-state"
                 class:sr-only={boxState.doorState === BoxDoorState.CLOSED}
                 transition:fade={{duration: 250}}>

                <span class="warning-icon">
                    <UIcon icon="triangle-warning"/>
                </span>

                <span>
                    {#if boxState.doorState === BoxDoorState.OPEN}
                        {localize(/* @kp-localization verbisbox.DoorsOpened */ 'verbisbox.DoorsOpened')}
                    {:else}
                        {localize(/* @kp-localization verbisbox.DoorsClosed */ 'verbisbox.DoorsClosed')}
                    {/if}
                </span>
            </div>
        {/key}
    </div>

    <div class="box-body text-ellipsis">
        {#if hasItems}
            <span class="text-ellipsis" transition:fade={{duration: 250}}>
                <strong>{validItems.length}</strong>

                <span>
                    <KpPluralizeText count={validItems.length}
                                     oneCountText={localize(/* @kp-localization commons.item */ 'commons.item')}
                                     moreText={localize(/* @kp-localization commons.items */ 'commons.items')}/>
                    {localize(/* @kp-localization verbisbox.shipment.forUser */ 'verbisbox.shipment.forUser')}
                </span>
                <br>

                <Label labeled={boxState.user} explicitKind={Kind.KIND_USER}/>
            </span>
        {/if}
    </div>

    <div class="box-footer" class:warning={isAfterPickupDeadline(boxState.pickupDeadlineDate)}>
        <div class="transition-wrapper">
            {#key statusMessageWithDuration}
                <span class="box-status-with-duration" class:sr-only={!hasItems} transition:fade={{duration: 250}}>
                    {statusMessageWithDuration}
                </span>
            {/key}
        </div>
    </div>

    <!-- invisible button for keyboard control, click events are automatically forwarded to single-box-panel which has listener -->
    <button type="button" class="sr-only">{localize(/* @kp-localization commons.more */ 'commons.more')}</button>

    {#if (hasItems && exists(boxState.insertDate) && $isPickingSelectionEnabled$) || $isNonPickingSelectionEnabled$}
        <div class="select-badge" transition:fade|global={{duration: 250}}>
            <Checkbox id="checkbox-{boxState.id}" on:change={toggleSelect} size="lg"/>
        </div>
    {/if}
</div>

{#if isDetailViewOpened}
    <div class="custom-modal-window" transition:zoomFrom|global={{duration: 250, referenceElement: singleBoxPanel}}>
        <div class="box-detail-view" role="dialog" use:enforceFocusWithin use:clickOutside on:click-outside={close}>
            <div class="box-detail-view-header">
                <span class="box-label">
                    {localize(/* @kp-localization verbisbox.BoxNumber */ 'verbisbox.BoxNumber')}:
                    {label}
                </span>
                <button type="button" class="close" aria-label="close" on:click={close}>&times;</button>
            </div>

            <div class="box-status">
                {localize(/* @kp-localization verbisbox.BoxStatus */ 'verbisbox.BoxStatus')}:
                {statusMessage}
            </div>

            <div class="door-state">
                {localize(/* @kp-localization verbisbox.BoxDoors */ 'verbisbox.BoxDoors')}:
                {#if boxState.doorState === BoxDoorState.OPEN}
                    {localize(/* @kp-localization verbisbox.DoorsOpened */ 'verbisbox.DoorsOpened')}
                {:else}
                    {localize(/* @kp-localization verbisbox.DoorsClosed */ 'verbisbox.DoorsClosed')}
                {/if}
            </div>

            {#if hasItems}
                <div class="user">
                    {localize(/* @kp-localization verbisbox.shipment.ForUser */ 'verbisbox.shipment.ForUser')}:
                    <Label labeled={boxState.user} explicitKind={Kind.KIND_USER}/>
                </div>

                {#if exists(boxState.assignDate)}
                    <div class="assign-date">
                        {localize(/* @kp-localization verbisbox.shipment.DateAssigned */ 'verbisbox.shipment.DateAssigned')}:
                        {pipe(boxState.assignDate, dateFormatter('dd. MM. yyyy HH:mm'))}
                    </div>
                {/if}

                {#if exists(boxState.insertDate)}
                    <div class="insert-date">
                        {localize(/* @kp-localization verbisbox.shipment.DateInserted */ 'verbisbox.shipment.DateInserted')}:
                        {pipe(boxState.insertDate, dateFormatter('dd. MM. yyyy HH:mm'))}
                    </div>
                {/if}

                {#if exists(boxState.pickupDeadlineDate)}
                    <div class="pickup-deadline">
                        {localize(/* @kp-localization verbisbox.shipment.PickupUntil */ 'verbisbox.shipment.PickupUntil')}:
                        {pipe(boxState.pickupDeadlineDate, dateFormatter('dd. MM. yyyy HH:mm'))}
                    </div>
                {/if}

                <KpShipmentItems items={boxState.items}/>
            {/if}
        </div>
    </div>
{/if}

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";
    @import (reference) "bootstrap-less/bootstrap/mixins/vendor-prefixes";

    @dark: #5f5e6e;

    :global(.disable-scrolling) {
        overflow: hidden;
    }

    .single-box-panel {
        height: 100%;
        padding: 0.5em;
        display: grid;
        grid-template-rows: auto 1fr minmax(calc(1em * @line-height-base + 0.5em), auto);
        grid-template-columns: 1fr;
        place-items: stretch;
        grid-template-areas:
            "header"
            "body"
            "footer";

        color: @dark;
        transition: background 0.2s ease;

        &.clickable {
            cursor: pointer;
        }

        &:hover, &:focus-within {
            background-color: white;
        }

        .box-header {
            grid-area: header;
            display: flex;
            justify-content: space-between;
            gap: 1em;
        }

        .box-label {
            font-weight: bold;
            font-size: 1.4em
        }

        .door-state {
            margin: 0.3em 0.3em 0 0.3em;
        }

        .box-body {
            grid-area: body;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .box-footer {
            grid-area: footer;
            display: flex;
            justify-content: center;
            margin: 0 -0.5em -0.5em;
            padding: 0.5em;
        }

        .warning-icon {
            color: @brand-warning;
        }

        .warning {
            background-color: lighten(@brand-warning, 20%);
        }

        .select-badge {
            margin: 0 -0.5em -0.5em 0; // to move over panel padding right to the edge
            padding: 4em 0.75em 0.75em 4em;
            background: linear-gradient(to bottom right, transparent 50%, #ccc 52%, darken(@state-info-bg, 12%) 52%, darken(@state-info-bg, 10%) 53%);

            // to position at bottom right of the panel
            grid-area: body / body / footer / footer;
            justify-self: end;
            align-self: end;
        }
    }

    .box-detail-view {
        height: 100%;
        padding: 2em;
        overflow-x: auto;
        background-color: white;
        background-clip: padding-box;
        border: 1px solid rgba(0, 0, 0, 0.2);
        .box-shadow(0 3px 9px rgba(0, 0, 0, .5));
        @media (min-width: @screen-sm-min) {
            .box-shadow(0 5px 15px rgba(0, 0, 0, .5));
        }

        .box-detail-view-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            font-size: 2em;
            margin-bottom: 10px;
        }

        .box-status, .door-state, .user, .insert-date, .assign-date, .pickup-deadline {
            margin-bottom: 0.2em;
        }
    }

    .custom-modal-window {
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        height: 75%;


        z-index: @zindex-modal;
        width: 90%;
        @media (min-width: @screen-sm-min) {
            width: @modal-md;
        }
        @media (min-width: @screen-md-min) {
            width: @modal-lg;
        }
    }
</style>