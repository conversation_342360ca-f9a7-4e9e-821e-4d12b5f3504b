<script lang="ts">
    import {getLocalization} from 'core/svelte-context/context';
    import KpStationVoltagePanel from './grafana/KpStationVoltagePanel.svelte';
    import KpStationInternetSignalStrengthPanel from './grafana/KpStationInternetSignalStrengthPanel.svelte';

    export let stationIds: string[];

    const localize = getLocalization();
</script>

<div class="verbisbox-metrics-panels">
    <h2>
        {localize(/* @kp-localization verbisbox.Metrics */ 'verbisbox.Metrics')}
    </h2>

    <div class="panels-grid">
        <KpStationVoltagePanel {stationIds} height="300px"/>
        <KpStationInternetSignalStrengthPanel {stationIds} height="300px"/>
    </div>
</div>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";
    @import (reference) "styles/portaro.variables.less";

    .verbisbox-metrics-panels {
        display: flex;
        flex-direction: column;
        gap: @spacing-ml;
        width: 100%;

        h2 {
            margin: 0;
        }

        .panels-grid {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(1, minmax(0, 1fr));
            gap: 10px;
        }
    }
</style>