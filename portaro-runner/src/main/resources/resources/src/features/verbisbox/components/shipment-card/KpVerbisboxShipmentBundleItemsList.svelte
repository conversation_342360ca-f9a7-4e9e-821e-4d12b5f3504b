<script lang="ts">
    import type {ViewableItemInsight} from '../../types';
    import {isNullOrUndefined} from 'shared/utils/custom-utils';
    import {booleanComparator} from '../../../record-editation/field7-and-field8-editors/utils/utils';
    import KpVerbisboxShipmentBundleItem from './KpVerbisboxShipmentBundleItem.svelte';

    export let items: ViewableItemInsight[];

    let sortedItems: ViewableItemInsight[];
    $: sortedItems = items.sort((a, b) => booleanComparator(isNullOrUndefined(a.cancel), isNullOrUndefined(b.cancel)))
</script>

<ul class="verbisbox-shipment-bundle-items-list">
    {#each sortedItems as item}
        <li>
            <KpVerbisboxShipmentBundleItem on:reload-shipments item={item}/>
        </li>
    {/each}
</ul>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";
    @import (reference) "styles/portaro.variables.less";

    .verbisbox-shipment-bundle-items-list {
        display: flex;
        flex-direction: column;

        li {
            border-top: 1px solid @panel-default-border;
        }
    }
</style>