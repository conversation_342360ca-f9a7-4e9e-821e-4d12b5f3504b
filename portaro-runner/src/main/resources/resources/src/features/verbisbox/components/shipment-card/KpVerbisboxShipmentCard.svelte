<script lang="ts">
    import type {ViewableShipmentInsight} from '../../types';
    import {getDateFormatter, getLocalization} from 'core/svelte-context/context';
    import {exists} from 'shared/utils/custom-utils';
    import {Kind} from 'shared/constants/portaro.constants';
    import {pipe} from 'core/utils';
    import Label from 'shared/components/kp-label/Label.svelte';
    import KpGenericPanel from 'shared/ui-widgets/panel/KpGenericPanel.svelte';
    import KpVerbisboxShipmentEventLog from './KpVerbisboxShipmentEventLog.svelte';
    import KpVerbisboxShipmentBundleCard from './KpVerbisboxShipmentBundleCard.svelte';
    import KpVerbisboxShipmentIconedInfo from './KpVerbisboxShipmentIconedInfo.svelte';

    export let shipment: ViewableShipmentInsight;

    const dateFormatter = getDateFormatter();
    const localize = getLocalization();
</script>

<KpGenericPanel additionalClasses="verbisbox-shipment-card" hasBodyPadding="{false}">
    <h2 class="unset-style" slot="heading">
        {localize(/* @kp-localization verbisbox.shipment.ShipmentFor */ 'verbisbox.shipment.ShipmentFor')}:
        <span><Label labeled="{shipment.user}" explicitKind={Kind.KIND_USER}/></span>
    </h2>

    <div class="shipment-card-content">
        <div class="shipment-info-container">
            <KpVerbisboxShipmentIconedInfo icon="hastag">
                {shipment.id}
            </KpVerbisboxShipmentIconedInfo>

            <KpVerbisboxShipmentIconedInfo icon="clock" startAligned>
                <span>
                    {localize(/* @kp-localization verbisbox.shipment.DateCreated */ 'verbisbox.shipment.DateCreated')}:
                    {pipe(shipment.create.date, dateFormatter('dd. MM. yyyy HH:mm'))}
                </span>

                {#if exists(shipment.insert)}
                    <span>
                        {localize(/* @kp-localization verbisbox.shipment.DateInserted */ 'verbisbox.shipment.DateInserted')}:
                        {pipe(shipment.insert.date, dateFormatter('dd. MM. yyyy HH:mm'))}
                    </span>

                    {#if exists(shipment.insert.handleDate)}
                        <span>
                            {localize(/* @kp-localization verbisbox.shipment.HandleDateInserted */ 'verbisbox.shipment.HandleDateInserted')}:
                            {pipe(shipment.insert.handleDate, dateFormatter('dd. MM. yyyy HH:mm'))}
                        </span>
                    {/if}

                    {#if exists(shipment.insert.lockDate)}
                        <span>
                            {localize(/* @kp-localization verbisbox.shipment.LockDateInserted */ 'verbisbox.shipment.LockDateInserted')}:
                            {pipe(shipment.insert.lockDate, dateFormatter('dd. MM. yyyy HH:mm'))}
                        </span>
                    {/if}
                {/if}
            </KpVerbisboxShipmentIconedInfo>
        </div>

        <div class="shipment-bundles-container">
            <span id="shipment-bundles-heading">Obsahuje následující balíčky:</span>

            <div class="shipment-bundles" aria-labelledby="shipment-bundles-heading">
                {#each shipment.bundles as bundle, index}
                    <KpVerbisboxShipmentBundleCard bundleIndex="{index}"
                                                   on:reload-shipments bundle={bundle}/>
                {/each}
            </div>
        </div>

        <KpVerbisboxShipmentEventLog {shipment}/>
    </div>
</KpGenericPanel>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";
    @import (reference) "styles/portaro.variables.less";

    h2 span {
        font-weight: 500;
    }

    .shipment-card-content {
        display: flex;
        flex-direction: column;
        gap: @spacing-ml;
        padding: @spacing-ml @spacing-l;

        .shipment-info-container {
            display: flex;
            flex-direction: column;
            gap: @spacing-sm;
            margin-left: 1px;
        }

        .shipment-bundles-container {
            display: flex;
            flex-direction: column;
            gap: @spacing-m;

            #shipment-bundles-heading {
                font-weight: 500;
            }

            .shipment-bundles {
                display: grid;
                gap: @spacing-ml;

                @media (max-width: @screen-md-max) {
                    grid-template-columns: 1fr;
                }
                @media (min-width: @screen-lg-min) {
                    grid-template-columns: repeat(2, 1fr);
                }
            }
        }
    }

    :global {
        .verbisbox-shipment-card {
            & > .panel-heading {
                padding: @spacing-sm @spacing-l;
            }

            .red-highlighted {
                font-weight: 500;
                color: var(--danger-red);
            }
        }
    }
</style>