<script lang="ts">
    import type {BundleInsight} from '../../types';
    import {getDateFormatter, getInjector, getLocalization} from 'core/svelte-context/context';
    import {exists} from 'shared/utils/custom-utils';
    import {VerbisboxService} from '../../verbisbox.service';
    import {createEventDispatcher} from 'svelte';
    import {getVerbisboxContext} from '../../utils/verbisbox-context';
    import {pipe} from 'core/utils';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpVerbisboxShipmentIconedInfo from './KpVerbisboxShipmentIconedInfo.svelte';
    import KpShipmentItems from './KpVerbisboxShipmentBundleItemsList.svelte';

    export let bundle: BundleInsight;
    export let bundleIndex: number;

    const dateFormatter = getDateFormatter();
    const localize = getLocalization();
    const dispatch = createEventDispatcher();
    const presenter = getInjector().getByToken<VerbisboxService>(VerbisboxService.presenterName);
    const station = getVerbisboxContext().station;

    $: boxIsChangeable = isBoxChangeable(bundle);

    async function changeBox() {
        await presenter.changeBoxOfBundle(station, bundle.id, null);
        dispatch('reload-shipments', station);
    }

    function isBoxChangeable(changingBundle: BundleInsight) {
        const bundleIsNotCancelledOrPickup = !exists(changingBundle.cancel) && !exists(changingBundle.pickup);
        const boxIsAssigned = exists(changingBundle.box);
        const anyItemIsInserted = !changingBundle.items.some((value) => exists(value.insert))
        return boxIsAssigned && anyItemIsInserted && bundleIsNotCancelledOrPickup;
    }
</script>

<div class="verbisbox-shipment-bundle-card"
     class:red-highlighted-background={exists(bundle.cancel)}
     class:green-highlighted-background={exists(bundle.pickup)}>

    <div class="bundle-info-wrapper">
        <div class="bundle-info-container">
            <span class="heading">
                {localize(/* @kp-localization verbisbox.Bundle */ 'verbisbox.Bundle')} #{bundleIndex + 1}
            </span>

            <KpVerbisboxShipmentIconedInfo icon="hastag">
                {bundle.id}
            </KpVerbisboxShipmentIconedInfo>

            {#if exists(bundle.box)}
                <KpVerbisboxShipmentIconedInfo icon="cube">
                    {localize(/* @kp-localization verbisbox.Box */ 'verbisbox.Box')}:
                    {bundle.box.text}
                </KpVerbisboxShipmentIconedInfo>
            {:else}
                <KpVerbisboxShipmentIconedInfo icon="cube" redHighlighted>
                    {localize(/* @kp-localization verbisbox.bundle.BoxNotAssigned */ 'verbisbox.bundle.BoxNotAssigned')}
                </KpVerbisboxShipmentIconedInfo>
            {/if}

            <KpVerbisboxShipmentIconedInfo icon="clock" startAligned>
                <span>
                    {localize(/* @kp-localization verbisbox.shipment.DateCreated */ 'verbisbox.shipment.DateCreated')}:
                    {pipe(bundle.create.date, dateFormatter('dd. MM. yyyy HH:mm'))}
                </span>

                {#if exists(bundle.occupy)}
                    <span>
                        {localize(/* @kp-localization verbisbox.bundle.DateOccupy */ 'verbisbox.bundle.DateOccupy')}:
                        {pipe(bundle.occupy.date, dateFormatter('dd. MM. yyyy HH:mm'))}
                    </span>
                {/if}

                {#if exists(bundle.insert)}
                    <span>
                        {localize(/* @kp-localization verbisbox.shipment.DateInserted */ 'verbisbox.shipment.DateInserted')}:
                        {pipe(bundle.insert.date, dateFormatter('dd. MM. yyyy HH:mm'))}
                    </span>
                {/if}

                {#if exists(bundle.pickup)}
                    <span class="green-highlight">
                        {localize(/* @kp-localization verbisbox.shipment.DatePickup */ 'verbisbox.shipment.DatePickup')}:
                        {pipe(bundle.pickup.date, dateFormatter('dd. MM. yyyy HH:mm'))}
                    </span>
                {/if}

                {#if exists(bundle.cancel)}
                    <span class="red-highlighted">
                        {localize(/* @kp-localization verbisbox.shipment.DateCancelled */ 'verbisbox.shipment.DateCancelled')}:
                        {pipe(bundle.cancel.date, dateFormatter('dd. MM. yyyy HH:mm'))}
                    </span>
                {/if}
            </KpVerbisboxShipmentIconedInfo>
        </div>

        {#if boxIsChangeable}
            <KpButton buttonStyle="accent-blue-new" on:click={() => changeBox()}>
                <IconedContent icon="cube">
                    {localize(/* @kp-localization verbisbox.bundle.ChangeBundleBox */ 'verbisbox.bundle.ChangeBundleBox')}
                </IconedContent>
            </KpButton>
        {/if}
    </div>

    <KpShipmentItems on:reload-shipments items={bundle.items}/>
</div>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";
    @import (reference) "styles/portaro.variables.less";

    .verbisbox-shipment-bundle-card {
        background-color: @panel-default-heading-bg;
        border: 1px solid @panel-default-border;
        display: flex;
        flex-direction: column;
        border-radius: @border-radius-default;

        &.red-highlighted-background {
            background-color: #FCEAEC;
        }

        &.green-highlighted-background {
            background-color: #DFF8E9;
        }

        .bundle-info-wrapper {
            display: flex;
            gap: @spacing-l;
            align-items: start;
            padding: @spacing-m @spacing-ml;

            .bundle-info-container {
                display: flex;
                flex-direction: column;
                gap: @spacing-sm;
                flex: 1;

                .heading {
                    font-weight: 500;
                }
            }
        }
    }

    :global {
        .verbisbox-shipment-bundle-card .bundle-info-container .verbisbox-shipment-iconed-info {
            margin-left: 1px;
        }
    }
</style>