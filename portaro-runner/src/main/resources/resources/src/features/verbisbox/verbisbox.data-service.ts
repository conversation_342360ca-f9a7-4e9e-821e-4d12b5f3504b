import type {ActionResponse, UUID} from 'typings/portaro.be.types';
import {transferify} from 'shared/utils/data-service-utils';
import type {
    Box,
    BoxStation,
    ChangeBundleBoxFormRequest,
    ItemsMoveFormRequest,
    StationLayout,
    ViewableShipmentInsight,
    ViewableStationState
} from './types';
import type {AjaxService} from 'core/data-services/ajax.service';

export class VerbisboxDataService {
    public static serviceName = 'verbisboxDataService';

    private static BOX_STATIONS = 'box-stations';
    private static SERVICE_ACCESS_ROUTE = 'service-access';
    private static SHIPMENT_ACTIVE_ROUTE = 'shipment/active';
    private static ITEMS_MOVE_ROUTE = 'items/move';
    private static BUNDLES_CHANGE_BOX_ROUTE = 'bundles/change-box';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    public async getStations(): Promise<BoxStation[]> {
        return this.ajaxService
            .createRequest(VerbisboxDataService.BOX_STATIONS)
            .get();
    }

    public async getStation(stationId: UUID): Promise<BoxStation> {
        return this.ajaxService
            .createRequest(`${VerbisboxDataService.BOX_STATIONS}/${stationId}`)
            .get();
    }

    public async getLayout(station: BoxStation): Promise<StationLayout> {
        return this.ajaxService
            .createRequest(`${VerbisboxDataService.BOX_STATIONS}/${station.id}/layout`)
            .get();
    }


    public async getState(station: BoxStation): Promise<ViewableStationState> {
        return this.ajaxService
            .createRequest(`${VerbisboxDataService.BOX_STATIONS}/${station.id}/state`)
            .get();
    }

    public async getActiveShipments(station: BoxStation): Promise<ViewableShipmentInsight[]> {
        return this.ajaxService
            .createRequest(`${VerbisboxDataService.BOX_STATIONS}/${station.id}/${VerbisboxDataService.SHIPMENT_ACTIVE_ROUTE}`)
            .get();
    }

    public async moveToNewBundle(station: BoxStation, request: ItemsMoveFormRequest): Promise<void> {
        return this.ajaxService
            .createRequest(`${VerbisboxDataService.BOX_STATIONS}/${station.id}/${VerbisboxDataService.ITEMS_MOVE_ROUTE}`)
            .post(request);
    }

    public async changeBoxOfBundle(station: BoxStation, request: ChangeBundleBoxFormRequest): Promise<void> {
        return this.ajaxService
            .createRequest(`${VerbisboxDataService.BOX_STATIONS}/${station.id}/${VerbisboxDataService.BUNDLES_CHANGE_BOX_ROUTE}`)
            .post(request);
    }

    public async createServiceAccess(station: BoxStation, boxes: Box[], picking: boolean): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${VerbisboxDataService.BOX_STATIONS}/${station.id}/${VerbisboxDataService.SERVICE_ACCESS_ROUTE}`)
            .post(transferify({boxes, picking}));
    }
}