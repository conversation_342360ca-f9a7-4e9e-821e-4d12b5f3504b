<script lang="ts">
    import type {BoxStation} from '../types';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {VerbisboxService} from '../verbisbox.service';
    import KpVerbisboxStation from '../components/KpVerbisboxStation.svelte';
    import KpVerbisboxMetricsPanels from '../components/KpVerbisboxMetricsPanels.svelte';
    import KpPageContainer from 'shared/layouts/containers/KpPageContainer.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    const localize = getLocalization();
    const presenter = getInjector().getByToken<VerbisboxService>(VerbisboxService.presenterName);

    const stationsPromise = presenter.getStations();

    function isSingleStation(stations: BoxStation[]): boolean {
        return stations.length === 1;
    }
</script>

<KpPageContainer>
    {#await stationsPromise}
        <KpHeading type="h1">Verbisbox</KpHeading>
    {:then stations}
        {#if !isSingleStation(stations)}
            <KpHeading type="h1">Verbisbox</KpHeading>
        {/if}
    {/await}

    {#await stationsPromise then stations}
        {#if isSingleStation(stations)}
            {#await presenter.getLayout(stations[0]) then layout}
                <KpVerbisboxStation {layout}
                                    station={stations[0]}
                                    stationName={stations[0].text}/>
            {/await}
        {:else}
            <ul>
                {#each stations as station (station.id)}
                    <li>
                        <a href="#!/verbisbox/{station.id}">{station.text}</a>
                    </li>
                {:else}
                    <div>{localize(/* @kp-localization verbisbox.NoVerbisboxOrNotActivated */ 'verbisbox.NoVerbisboxOrNotActivated')}</div>
                {/each}
            </ul>
        {/if}
    {/await}

    {#await stationsPromise then stations}
        {#if !isSingleStation(stations)}
            <KpVerbisboxMetricsPanels stationIds={stations.map((value) => value.id)}/>
        {/if}
    {/await}
</KpPageContainer>