<script lang="ts">
    import type {AnyObject} from 'typings/portaro.fe.types';
    import {buildQueryStringWithoutFalsyValues} from 'shared/utils/url-utils';
    import {
        DEFAULT_PANEL_HEIGHT,
        DEFAULT_PANEL_REFRESH,
        DEFAULT_PANEL_THEME,
        DEFAULT_PANEL_WIDTH
    } from './grafana.constants';

    export let dashboardId: string;
    export let dashboardName: string;
    export let panelId: string;
    export let orgId: string;
    export let refresh = DEFAULT_PANEL_REFRESH;
    export let from: Date = null;
    export let to: Date = null;
    export let variables: Record<string, any> = {};
    export let theme: 'dark' | 'light' = DEFAULT_PANEL_THEME;
    export let width = DEFAULT_PANEL_WIDTH;
    export let height = DEFAULT_PANEL_HEIGHT;

    $: queryStrings = buildQueryStringWithoutFalsyValues({
        orgId,
        refresh: `${refresh}s`,
        from: +from,
        to: +to,
        theme,
        panelId,
        ...addVarPrefixToKeys(variables)
    });

    function addVarPrefixToKeys(object: AnyObject): AnyObject {
        return Object.fromEntries(
            Object.entries(object).map(([key, value]) =>
                [`var-${key}`, value]
            )
        )
    }
</script>

<iframe title="Grafana embedded panel"
        class="grafana-iframe"
        style:width={width || null}
        style:height={height || null}
        src="https://grafana.verbis.io/d-solo/{dashboardId}/{dashboardName}?{queryStrings}"
        frameborder="0"></iframe>