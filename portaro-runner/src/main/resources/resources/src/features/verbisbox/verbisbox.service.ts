import type {UUID} from 'typings/portaro.be.types';
import type {VerbisboxDataService} from './verbisbox.data-service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type {
    Box,
    BoxStation,
    StationLayout,
    ViewableItemInsight,
    ViewableShipmentInsight,
    ViewableStationState,
} from './types';

export class VerbisboxService {
    public static presenterName = 'verbisboxService';

    /*@ngInject*/
    constructor(private verbisboxDataService: VerbisboxDataService,
                private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    public async getStations(): Promise<BoxStation[]> {
        return this.verbisboxDataService.getStations();
    }

    public async getStation(id: UUID): Promise<BoxStation> {
        return this.verbisboxDataService.getStation(id);
    }

    public async getLayout(station: BoxStation): Promise<StationLayout> {
        return this.verbisboxDataService.getLayout(station);
    }

    public async getViewableState(station: BoxStation): Promise<ViewableStationState> {
        return this.getState(station);
    }

    public getActiveShipments(station: BoxStation): Promise<ViewableShipmentInsight[]> {
        return this.verbisboxDataService.getActiveShipments(station);
    }

    public moveToNewBundle(station: BoxStation, item: ViewableItemInsight, bundleId: UUID): Promise<void> {
        return this.verbisboxDataService.moveToNewBundle(station, {
            confirmed: false,
            stationId: station.id,
            bundleId,
            itemId: item.id
        });
    }

    public changeBoxOfBundle(station: BoxStation, bundleId: UUID, boxId: UUID): Promise<void> {
        return this.verbisboxDataService.changeBoxOfBundle(station, {
            confirmed: false,
            stationId: station.id,
            bundleId,
            boxId
        });
    }

    public async requestServiceAccess(station: BoxStation, boxes: Box[]): Promise<void> {
        try {
            const response = await this.verbisboxDataService.createServiceAccess(station, boxes, true);
            await this.finishedResponseInteractionService.showSuccessResponseInModalWindow(response);
        } catch (error) {
            await this.finishedResponseInteractionService.showFailedInModalWindow(error);
        }
    }

    public async requestNonPickingServiceAccess(station: BoxStation, boxes: Box[]): Promise<void> {
        try {
            const response = await this.verbisboxDataService.createServiceAccess(station, boxes, false);
            await this.finishedResponseInteractionService.showSuccessResponseInModalWindow(response);
        } catch (error) {
            await this.finishedResponseInteractionService.showFailedInModalWindow(error);
        }
    }

    private async getState(station: BoxStation): Promise<ViewableStationState> {
        return this.verbisboxDataService.getState(station);
    }
}