<script lang="ts">
    import KpGrafanaIframe from './KpGrafanaIframe.svelte';
    import {
        DEFAULT_PANEL_HEIGHT,
        DEFAULT_PANEL_REFRESH,
        DEFAULT_PANEL_THEME,
        DEFAULT_PANEL_WIDTH,
        STATION_METRICS_DASHBOARD_NAME,
        STATION_METRICS_DASHBOARD_ID,
        MAIN_ORG_ID
    } from './grafana.constants';

    export let panelId: string;
    export let refresh = DEFAULT_PANEL_REFRESH;
    export let from: Date = null;
    export let to: Date = null;

    export let variables: Record<string, any> = {};
    export let theme: 'dark' | 'light' = DEFAULT_PANEL_THEME;

    export let width = DEFAULT_PANEL_WIDTH;
    export let height = DEFAULT_PANEL_HEIGHT;
</script>

<KpGrafanaIframe dashboardId={STATION_METRICS_DASHBOARD_ID}
                 dashboardName={STATION_METRICS_DASHBOARD_NAME}
                 orgId={MAIN_ORG_ID}
                 {panelId}
                 {refresh}
                 {from}
                 {to}
                 {variables}
                 {theme}
                 {width}
                 {height}/>