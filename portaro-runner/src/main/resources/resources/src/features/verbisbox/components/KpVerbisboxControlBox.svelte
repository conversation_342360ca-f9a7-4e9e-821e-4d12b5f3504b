<script lang="ts">
    import type {DisplayMessage} from '../types';
    import {tooltip} from 'shared/ui-widgets/tooltip/use.tooltip';
    import {getLocalization} from 'core/svelte-context/context';
    import VerbisboxLogoOrangeBlack from './VerbisboxLogoOrangeBlack.svelte';

    export let display: DisplayMessage;

    const localize = getLocalization();
</script>

<div class="control-box">
    <VerbisboxLogoOrangeBlack style="width: 160px;"/>

    <pre use:tooltip={localize(/* @kp-localization verbisbox.LastKnownDisplayMessage */ 'verbisbox.LastKnownDisplayMessage')}>
        {#each display.content as line}
            <div>{line}</div>
        {/each}
    </pre>
</div>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    .control-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;

        @media (min-width: @screen-lg-min) {
            gap: 10px;
        }

        pre {
            margin: 0;
            padding-top: 0;
            padding-bottom: 0;
            background-color: white;
            color: @gray-light;
            font-weight: bold;
            border: 1px solid @gray-light;
            box-shadow: inset 0 0 3px @gray-light;
            font-size: 12px;

            @media (min-width: @screen-md-min) and (max-width: @screen-md-max) {
                font-size: 10px;
            }

            @media (max-width: @screen-md-min) {
                margin-bottom: 10px;
            }
        }
    }
</style>