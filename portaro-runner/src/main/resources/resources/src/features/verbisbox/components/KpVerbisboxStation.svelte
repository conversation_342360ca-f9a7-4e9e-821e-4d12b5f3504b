<script lang="ts">
    import type {Box, BoxStation, StationLayout} from '../types';
    import type {ViewableStationState} from '../types';
    import type {Writable} from 'svelte/store';
    import type {Subscription} from 'rxjs';
    import {VerbisboxService} from '../verbisbox.service';
    import {getInjector, getLocalization, getLogger} from 'core/svelte-context/context';
    import {onDestroy, onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {get, readonly, writable} from 'svelte/store';
    import {setVerbisboxContext} from '../utils/verbisbox-context';
    import {tooltip} from 'shared/ui-widgets/tooltip/use.tooltip';
    import {EMPTY, exhaustMap, from, interval, startWith, catchError} from 'rxjs';
    import KpVerbisboxStationLayout from './KpVerbisboxStationLayout.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import KpVerbisboxMetricsPanels from './KpVerbisboxMetricsPanels.svelte';
    import KpVerbisboxActiveShipments from './KpVerbisboxActiveShipments.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    export let station: BoxStation;
    export let layout: StationLayout;
    export let stationName: string;

    const localize = getLocalization();
    const logger = getLogger();
    const presenter = getInjector().getByToken<VerbisboxService>(VerbisboxService.presenterName);
    $: stationIds = [station.id];

    const DATA_REFRESH_INTERVAL_MS = 30_000; // 30 seconds
    let subscription: Subscription;

    let state: ViewableStationState;

    const isPickingSelectionEnabled$: Writable<boolean> = writable(false);
    const isNonPickingSelectionEnabled$: Writable<boolean> = writable(false);
    const selectedBoxes$: Writable<Box[]> = writable([]);

    setVerbisboxContext({
        isPickingSelectionEnabled$: readonly(isPickingSelectionEnabled$),
        isNonPickingSelectionEnabled$: readonly(isNonPickingSelectionEnabled$),
        selectedBoxes$,
        stationName,
        station
    });

    // setup data refreshing by using "cold" observable
    const boxState$ = interval(DATA_REFRESH_INTERVAL_MS).pipe(      // emit number every DATA_REFRESH_INTERVAL_MS ms
        startWith(-1),                                              // but emit first number (-1) immediately on subscribe
        exhaustMap(() => from(presenter.getViewableState(station))  // load new data for every emitted number and ignore subsequent emits if data is still loading
            .pipe(catchError((error) => {
                logger.error(error);
                return EMPTY;
            }))
        )
    );

    onMount(() => {
        subscription = boxState$.subscribe((boxState) => state = boxState);
    });

    onDestroy(() => {
        if (exists(subscription)) {
            subscription.unsubscribe();
        }
    });

    function startBoxSelection() {
        selectedBoxes$.set([]);
        isPickingSelectionEnabled$.set(true);
    }

    function startBoxSelectionForNonPickingAccess() {
        selectedBoxes$.set([]);
        isNonPickingSelectionEnabled$.set(true);
    }

    function cancelBoxSelection() {
        isPickingSelectionEnabled$.set(false);
        isNonPickingSelectionEnabled$.set(false);
        selectedBoxes$.set([]);
    }

    async function requestServiceAccess() {
        const selectedBoxes = get(selectedBoxes$);
        if (selectedBoxes.length === 0) {
            return;
        }

        await presenter.requestServiceAccess(station, selectedBoxes);
        cancelBoxSelection();
    }

    async function requestNonPickingServiceAccess() {
        const selectedBoxes = get(selectedBoxes$);
        if (selectedBoxes.length === 0) {
            return;
        }

        await presenter.requestNonPickingServiceAccess(station, selectedBoxes);
        cancelBoxSelection();
    }
</script>

<Flex class="verbisbox-station-container" direction="column" alignItems="center" gap="xxl" width="100%">
    {#if exists(state)}
        <div class="btn-group">
            {#if $isPickingSelectionEnabled$}
                <button class="btn btn-primary"
                        disabled={$selectedBoxes$.length === 0}
                        on:click={requestServiceAccess}>

                    {localize(/* @kp-localization verbisbox.serviceAccess.GeneratePin */ 'verbisbox.serviceAccess.GeneratePin')}
                </button>

                <button class="btn btn-default" on:click={cancelBoxSelection}>
                    {localize(/* @kp-localization commons.zrusit */ 'commons.zrusit')}
                </button>
            {:else}
                <button class="btn btn-default"
                        disabled={$isNonPickingSelectionEnabled$}
                        on:click={startBoxSelection}
                        use:tooltip={localize(/* @kp-localization verbisbox.serviceAccess.PickingAccessMsg */ 'verbisbox.serviceAccess.PickingAccessMsg')}>

                    {localize(/* @kp-localization verbisbox.serviceAccess.SelectBoxesForPickingAccess */ 'verbisbox.serviceAccess.SelectBoxesForPickingAccess')}
                </button>
            {/if}

            {#if $isNonPickingSelectionEnabled$}
                <button class="btn btn-primary"
                        disabled={$selectedBoxes$.length === 0}
                        on:click={requestNonPickingServiceAccess}>

                    {localize(/* @kp-localization verbisbox.serviceAccess.GeneratePin */ 'verbisbox.serviceAccess.GeneratePin')}
                </button>

                <button class="btn btn-default" on:click={cancelBoxSelection}>
                    {localize(/* @kp-localization commons.zrusit */ 'commons.zrusit')}
                </button>
            {:else}
                <button class="btn btn-default"
                        disabled={$isPickingSelectionEnabled$}
                        on:click={startBoxSelectionForNonPickingAccess}
                        use:tooltip={localize(/* @kp-localization verbisbox.serviceAccess.NonPickingAccessMsg */ 'verbisbox.serviceAccess.NonPickingAccessMsg')}>

                    {localize(/* @kp-localization verbisbox.serviceAccess.SelectBoxesForNonPickingAccess */ 'verbisbox.serviceAccess.SelectBoxesForNonPickingAccess')}
                </button>
            {/if}
        </div>

        <KpVerbisboxStationLayout {layout} {state}/>
        <KpVerbisboxMetricsPanels {stationIds}/>
        <KpVerbisboxActiveShipments {station}/>
    {:else}
        <KpLoadingBlock/>
    {/if}
</Flex>