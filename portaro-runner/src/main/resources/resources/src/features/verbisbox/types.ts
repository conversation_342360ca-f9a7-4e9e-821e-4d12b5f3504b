import type {BasicUser, Department, Identified, LabeledIdentified, Rec, UUID} from 'typings/portaro.be.types';

export interface BoxStation extends LabeledIdentified<UUID> {
    department: Department;
}

export interface StationLayout {
    boxes: PhysicalBox[];
}

export interface StationState {
    health: StationHealth;
    enablement: StationEnablement;
    boxes: Box[];
    display: DisplayMessage;
}

export interface DisplayMessage {
    type: DisplayType;
    content: string[];
}

export type DisplayType = 'MONOSPACE_4X20'

export interface PhysicalBox {
    id: UUID;
    label: string;
    capabilities: BoxCapability[];
    position: BoxPosition;
    size: BoxSize;
}

export enum BoxCapability {
    STORE = 'STORE',
    DISPLAY = 'DISPLAY',
    CONTROL = 'CONTROL'
}

export interface BoxPosition {
    row: number;
    column: number;
}

export interface BoxSize {
    width: number;
    height: number;
    depth: number;
}

export interface StationHealth {
    status: StationHealthStatus;
    details: Record<string, any>;
}

export enum StationHealthStatus {
    UP = 'UP',
    DOWN = 'DOWN',
    OFFLINE = 'OFFLINE'
}

export interface StationEnablement {
    status: StationEnablementType
}

export enum StationEnablementType {
    ENABLED = 'ENABLED',
    DISABLED = 'DISABLED'
}

export enum BoxDoorState {
    OPEN = 'OPEN',
    CLOSED = 'CLOSED'
}

export interface Box {
    id: UUID;
    insertDate?: string;
    assignDate?: string;
    pickupDeadlineDate?: string;
    items: ShipmentItem[];
    doorState: BoxDoorState
}

export interface ShipmentItem {
    id: UUID;
    create: InsightEventWithDate;
    insert: InsightEventWithDate;
    pickup: InsightEventWithDate;
    cancel: InsightEventWithDate;
}

export interface ShipmentItemInfo extends LabeledIdentified<UUID> {
    user: BasicUser;
    record?: Rec;
}

export interface ViewableItemInsight extends ShipmentItem {
    identifiers: Identifier[];
    text: string;
    record?: Rec;
}

export interface ViewableBox extends Omit<Box, 'items'> {
    items: ViewableItemInsight[];
    user?: ViewableUser;
}

export interface ViewableStationState extends Omit<StationState, 'boxes'> {
    boxes: ViewableBox[];
}

export interface ItemsMoveFormRequest {
    confirmed: boolean;
    stationId: UUID;
    itemId: UUID;
    bundleId: UUID;
}

export interface ChangeBundleBoxFormRequest {
    confirmed: boolean;
    stationId: UUID;
    bundleId: UUID;
    boxId: UUID;
}

export interface ViewableShipmentInsight extends Identified<UUID> {
    stationId: UUID;
    user?: ViewableUser;
    create: InsightEventWithDate;
    insert?: InsightEventWithOptionalHandleAndLock;
    pickup?: InsightEventWithOptionalHandleAndDeadline;
    cancel?: InsightEventWithOptionalHandle;
    bundles: BundleInsight[];
    events: EventInsight[];
}

export interface ViewableUser {
    id?: number;
    text: string;
}

export interface BundleInsight extends Identified<UUID> {
    box?: ViewableBoxInsight;
    create: InsightEventWithDate;
    occupy?: InsightEventWithDate;
    insert?: InsightEventWithDate;
    pickup?: InsightEventWithDate;
    cancel?: InsightEventWithDate;
    items: ViewableItemInsight[];
}

export interface ViewableBoxInsight extends Identified<UUID> {
    text: string;
}

export interface Identifier extends Identified<UUID> {
    itemId: UUID;
    stationId: UUID;
    type: string;
    value: string;
    deleteDate: Date;
}

export interface EventInsight extends Identified<UUID> {
    type: string;
    subjectId: UUID;
    initiatorId?: UUID;
    date: InsightEventWithDate;
}

export interface InsightEventWithDate {
    date: string;
}

export interface InsightEventWithOptionalHandleAndLock extends InsightEventWithDate {
    handleDate?: string;
    lockDate?: string;
}

export interface InsightEventWithOptionalHandleAndDeadline extends InsightEventWithDate {
    handleDate?: string;
    deadlineDate?: string;
}

export interface InsightEventWithOptionalHandle extends InsightEventWithDate {
    handleDate?: string;
}
