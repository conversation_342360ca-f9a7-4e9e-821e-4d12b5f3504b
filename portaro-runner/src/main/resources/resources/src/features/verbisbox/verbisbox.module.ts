import register from '@kpsys/angularjs-register';
import verbisboxRoutes from './verbisbox.routes';
import {VerbisboxDataService} from './verbisbox.data-service';
import {VerbisboxService} from './verbisbox.service';

export default register('portaro.features.verbisbox')
    .config(verbisboxRoutes)
    .service(VerbisboxDataService.serviceName, VerbisboxDataService)
    .service(VerbisboxService.presenterName, VerbisboxService)
    .name();