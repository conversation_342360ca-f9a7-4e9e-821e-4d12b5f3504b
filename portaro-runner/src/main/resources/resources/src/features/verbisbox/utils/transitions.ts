import type {TransitionConfig} from 'svelte/transition';
import {linear} from 'svelte/easing';

export interface ZoomFromElementTransitionParams {
    duration?: number;
    referenceElement: HTMLElement;
}

// mixed fly and scale svelte transitions (taken directly from svelte code)
export function zoomFrom(node: HTMLElement, {duration = 400, referenceElement}: ZoomFromElementTransitionParams): TransitionConfig {
    const {top, left, width, height} = referenceElement?.getBoundingClientRect();
    const deltaX = (left + width / 2) - (window.innerWidth / 2);
    const deltaY = (top + height / 2) - (window.innerHeight / 2);

    const start = 0.25;
    const x = deltaX;
    const y = deltaY;

    const style = getComputedStyle(node);
    const target_opacity = +style.opacity;
    const transform = style.transform === 'none' ? '' : style.transform;
    const sd = 1 - start;
    const od = target_opacity;
    return {
        duration,
        easing: linear,
        css: (t, u) =>
            // order is important, translate first then scale !
            `
            transform: ${transform} translate(${(1 - t) * x}px, ${(1 - t) * y}px) scale(${1 - (sd * u)});
            opacity: ${target_opacity - (od * u)};
            `
    };
}