<script lang="ts">
    import type {RecordEditation} from 'typings/portaro.be.types';
    import type {RecordEditationManagerFactory} from '../services/record-editation-manager.factory';
    import type WalkerService from 'shared/services/walker.service';
    import {getRecordPath} from 'shared/utils/data-service-utils';
    import {exists} from 'shared/utils/custom-utils';
    import KpRecordEditation from '../kp-record-editation/KpRecordEditation.svelte';

    // Injected from record-editation.routes.ts
    export let editationObject: RecordEditation;
    export let recordEditationManagerFactory: RecordEditationManagerFactory;
    export let walker: WalkerService;

    const editationManager = recordEditationManagerFactory.createManager(editationObject, undefined, {
        publishAndClose: publishDraftAndClose,
        saveAndClose,
        deleteDraftAndClose,
        discardAndClose,
        saveAndContinue
    });

    async function publishDraftAndClose(): Promise<void> {
        await editationManager.publish();
        await successExit();
    }

    async function deleteDraftAndClose(): Promise<void> {
        await editationManager.remove();
        nonSuccessExit();
    }

    async function saveAndClose(): Promise<void> {
        await editationManager.save();
        await successExit();
    }

    async function saveAndContinue(): Promise<void> {
        await editationManager.save();
    }

    async function discardAndClose(): Promise<void> {
        await successExit();
    }

    async function successExit(): Promise<void> {
        await walker.newPage(getRecordPath(editationManager.recordId));
    }

    function nonSuccessExit(): void {
        window.history.back();
    }
</script>

{#if exists(editationManager)}
    <KpRecordEditation {editationManager} viewType="page"/>
{/if}