<script lang="ts">
    import type {EditableField} from 'typings/portaro.be.types';
    import {createEventDispatcher} from 'svelte';
    import {getLocalization} from 'core/svelte-context/context';
    import KpDropdownMenuItem from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuItem.svelte';
    import KpDropdownMenuButton from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuButton.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let fieldRepetitions: EditableField[];

    const localize = getLocalization();
    const dispatch = createEventDispatcher<{ 'delete-field': EditableField }>();
</script>

<KpDropdownMenuButton buttonSize="sm"
                      additionalContainerClasses="record-editation-delete-repetitions-dropdown"
                      additionalButtonClasses="delete-repetitions-dropdown-button">

    <svelte:fragment slot="button">
        <UIcon icon="menu-dots"/>
    </svelte:fragment>

    <svelte:fragment slot="menu">
        {#each fieldRepetitions as field, index(field.id)}
            <KpDropdownMenuItem on:click={() => dispatch('delete-field', field)}>
                {localize(/* @kp-localization editace.RemoveRepetition */ 'editace.RemoveRepetition')} #{index + 1}
            </KpDropdownMenuItem>
        {/each}
    </svelte:fragment>
</KpDropdownMenuButton>

<style lang="less">
    :global {
        .record-editation-delete-repetitions-dropdown .delete-repetitions-dropdown-button {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
</style>