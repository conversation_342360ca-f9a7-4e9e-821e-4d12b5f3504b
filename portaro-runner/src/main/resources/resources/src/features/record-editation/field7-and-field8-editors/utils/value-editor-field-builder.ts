import type {
    ValueEditorBindings,
    ValueEditorOptions,
    ValueEditorValidations
} from 'shared/value-editors/kp-value-editor/types';
import type {SingleAcceptableValueEditorOptions} from 'shared/value-editors/internal/editors/single-acceptable/types';
import type {SvelteComponentConstructor} from 'core/types';
import type {ObjectValueEditorField} from 'shared/value-editors/internal/meta-editors/object/types';
import type {TextValueEditorValidations} from 'shared/value-editors/internal/editors/text/types';

export class ValueEditorFieldBuilder<FORM_MODEL> {

    private label: string;
    private fieldName: keyof FORM_MODEL;
    private editor: ValueEditorBindings;

    withLabel(label: string) {
        this.label = label;
        return this;
    }

    withFieldName(fieldName: keyof FORM_MODEL) {
        this.fieldName = fieldName;
        return this;
    }

    withSingleAcceptableEditor(optionLabelComponent: SvelteComponentConstructor<{option: any}>, validations?: ValueEditorValidations) {
        this.editor = {
            type: 'single-acceptable',
            options: {
                switchToInlineModeThreshold: 1,
                optionLabelComponent
            } as SingleAcceptableValueEditorOptions<any>,
            validations
        }
        return this;
    }

    withTextEditor(validations: TextValueEditorValidations) {
        this.editor = {
            type: 'text',
            validations
        }
        return this;
    }

    withHiddenEditor() {
        this.editor = {
            type: 'hidden'
        }
        return this;
    }

    withOptions(options: ValueEditorOptions) {
        this.editor.options = options;
        return this;
    }

    asInline(enableInlineMode: boolean) {
        (this.editor.options as SingleAcceptableValueEditorOptions<any>).switchToInlineModeThreshold = enableInlineMode ? 1 : 0;
        return this;
    }

    withAcceptableValues(acceptableValues: any[]) {
        (this.editor.options as SingleAcceptableValueEditorOptions<any>).acceptableValues = acceptableValues;
        return this;
    }

    withCustomIdResolver<T>(optionIdResolver: ({option}: {option: T}) => string | number) {
        (this.editor.options as SingleAcceptableValueEditorOptions<any>).optionIdResolver = optionIdResolver;
        return this;
    }

    build(): ObjectValueEditorField<any> {

        if (!this.editor) {
            throw new Error('No editor specified');
        }

        return {
            label: this.label,
            fieldName: this.fieldName,
            editor: this.editor,
        };
    }
}