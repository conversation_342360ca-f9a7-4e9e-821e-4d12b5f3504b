<script lang="ts">
    import {getLocalization} from 'core/svelte-context/context';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    const localize = getLocalization();
</script>

<button class="add-field-repetition-button field-repeat-button" on:click>
    <IconedContent icon="add">
        {localize(/* @kp-localization editace.RepeatField */ 'editace.RepeatField')}
    </IconedContent>
</button>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    @add-repetition-btn-height: 32px;

    .add-field-repetition-button {
        height: @add-repetition-btn-height;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 @spacing-xl;
        text-wrap: nowrap;
        outline: none;
        border: 1px solid @themed-border-default;
        border-top: none;
        background-color: @themed-body-bg;
        border-bottom-left-radius: @border-radius-default;
        border-bottom-right-radius: @border-radius-default;
        align-self: end;
        transition: opacity 0.3s ease-in-out, background-color 0.3s ease-in-out;

        &:hover {
            opacity: 0.75;
            background-color: #F5F5F5;
        }
    }
</style>