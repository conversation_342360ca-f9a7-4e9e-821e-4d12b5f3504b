import type {Field8Model, Field8SharedPartModel} from '../utils/types';
import type {LogService} from 'core/logging/log.service';
import {EMPTY_SYMBOLS, UNDEFINED_POSITION_CHARACTER} from '../utils/types';
import {ascendingOrderComparator, zip} from 'shared/utils/array-utils';
import {IncorrectSectionFormatError, MissingDocumentTypesError, UnknownSectionsCodesError} from './field8-errors';
import {isNullOrUndefined, isUndefined} from 'shared/utils/custom-utils';
import type {
    Field008Code,
    Field008Definitions,
    Field008DocumentType,
    Field008StaticCode,
    LabeledIdentified
} from 'typings/portaro.be.types';
import {
    booleanComparator,
    findAcceptableValueByCode,
    findAcceptableValueById,
    findCodeByCodeSymbolAndPosition,
    findLabelByOrder,
    getCodesByDocumentTypeCode,
    getCodesByPosition,
    getLabelsAndCodesByDocumentTypeCode,
    getLabelsByDocumentTypeCode,
    isMergedWithPreviousPositions,
    optionsComparator,
} from '../utils/utils';

export class Field8CodeConverter {
    constructor(private definitions: Field008Definitions, private logService: LogService) {
    }

    public serializeModel(model: Field8Model): string {

        const labels = getLabelsByDocumentTypeCode(this.definitions.labels, model.documentType.code);

        let variablePart = ''; // positions 18-34
        for (let positionIndex = 18; positionIndex <= 34; positionIndex++) {
            if (isMergedWithPreviousPositions(findLabelByOrder(labels, positionIndex))) {
                continue; // skip position if previous position is double characters long
            }

            if (model[`position${positionIndex}`]) {
                const position: Field008Code = model[`position${positionIndex}`];
                variablePart = variablePart.concat(position.code);
            } else {
                variablePart = variablePart.concat(UNDEFINED_POSITION_CHARACTER);
            }
        }

        if ([model.dateEnteredOnFile, model.date1, model.date2].some((value) => isNullOrUndefined(value))) {
            throw new Error('field008 serialization error, dateEnteredOnFile, date1 or date 2 is null/undefined');
        }

        let result = '';
        result = result
            .concat(model.dateEnteredOnFile) // 0-5
            .concat(model.publicationStatus.code) // 6
            .concat(model.date1) // 7-10
            .concat(model.date2) // 11-14
            .concat(model.placeOfPublication.id.padEnd(3)) // 15-17
            .concat(variablePart) // 18-34
            .concat(model.language.id) // 35-37
            .concat(model.modifiedRecord.code) // 38
            .concat(model.catalogingSource.code); // 39

        if (result.length !== 40) {
            throw new Error('field008 serialization error, length !== 40');
        }

        return result;
    }

    // returns parsed model for every possible documentType
    // if returned map is empty value could not be parsed for any documentType
    public parseCode(value: string): Map<string, Field8Model> {
        const models: Map<string, Field8Model> = new Map();

        if (!value) {
            return models;
        }

        const dateEnteredOnFile = value.substring(0, 6); // 0-5
        const publicationStatusCode = value.charAt(6); // 6
        const date1 = value.substring(7, 11); // 7-10
        const date2 = value.substring(11, 15); // 11-14
        const placeOfPublicationCode = value.substring(15, 18).trim(); // 15-17
        const variablePart = value.substring(18, 35); // 18-34
        const languageCode = value.substring(35, 38); // 35-37
        const modifiedRecordCode = value.charAt(38); // 38
        const catalogingSourceCode = value.charAt(39); // 39

        if (!Number.isInteger(Number(dateEnteredOnFile))) { // creation date code has non number characters
            throw new IncorrectSectionFormatError('dateEnteredOnFile', dateEnteredOnFile);
        }

        const sharedPart: Field8SharedPartModel = {
            catalogingSource: this.findValueByCodeOrGetDefaultValue(this.definitions.catalogingSource.codes, catalogingSourceCode),
            placeOfPublication: this.findValueByIdOrGetDefaultValue(this.definitions.placeOfPublication.values, placeOfPublicationCode, 'xr'),
            language: this.findValueByIdOrGetDefaultValue(this.definitions.language.values, languageCode, 'cze'),
            date1,
            date2,
            publicationStatus: this.findValueByCodeOrGetDefaultValue(this.definitions.publicationStatus.codes, publicationStatusCode),
            dateEnteredOnFile,
            modifiedRecord: this.findValueByCodeOrGetDefaultValue(this.definitions.modifiedRecord.codes, modifiedRecordCode)
        }

        if (Object.values(sharedPart).some((val) => isUndefined(val))) { // some values from shared part are undefined (probably language or place of publication)
            const sectionsWithUnknownCodes: (keyof Field8SharedPartModel)[] = Object.entries(sharedPart)
                .filter(([, val]) => isUndefined(val))
                .map(([key,]) => key as keyof Field8SharedPartModel);

            throw new UnknownSectionsCodesError(sectionsWithUnknownCodes)
        }

        if (this.definitions.documentTypes.length === 0) {
            throw new MissingDocumentTypesError();
        }

        this.definitions.documentTypes
            .map((documentType): [Field008DocumentType, boolean] => [documentType, this.doUndefinedPositionMatchForDocumentType(variablePart, documentType.code)]) // guess probable documentTypes from variable part
            .sort(([, isMatching1], [, isMatching2]) => booleanComparator(isMatching1, isMatching2)) // sort probable correct document types higher on the list
            .map(([documentType]) => this.parseVariablePartForDocumentType(sharedPart, documentType, variablePart)) // parse variable part
            .forEach((model) => models.set(model.documentType.code, model));

        return models;
    }

    private findValueByCodeOrGetDefaultValue(values: Field008StaticCode[], code: string): Field008StaticCode | undefined {
        let value = findAcceptableValueByCode(values, code);
        if (!value) { // if unknown symbol use default value (first value in list)
            value = values.at(0);
        }
        return value;
    }

    private findValueByIdOrGetDefaultValue(values: LabeledIdentified<string>[], code: string, defaultValueCode: string): LabeledIdentified<string> | undefined {
        let value = findAcceptableValueById(values, code);
        if (!value) {
            value = findAcceptableValueById(values, defaultValueCode);
        }
        return value;
    }

    private parseVariablePartForDocumentType(sharedPart: Field8SharedPartModel, documentType: Field008DocumentType, variablePart: string): Field8Model {

        const model: Field8Model = {...sharedPart, documentType};

        const [labels, codes] = getLabelsAndCodesByDocumentTypeCode(this.definitions, model.documentType.code);

        const parseSingleSymbol = (codeSymbol: string, position: number): Field008Code => {
            if (!findLabelByOrder(labels, position)?.isDefined) {
                return undefined; // skip undefined positions
            }

            const isDoubleCharacterSymbol = isMergedWithPreviousPositions(findLabelByOrder(labels, position + 1));

            let validCodeSymbol = codeSymbol;
            if (isDoubleCharacterSymbol) {
                validCodeSymbol += variablePart.charAt(position - 17); // next position in variable part (-18+1)
            }

            let result = findCodeByCodeSymbolAndPosition(codes, validCodeSymbol, position);
            if (!result) { // if unknown symbol use default value
                result = getCodesByPosition(codes, position)
                    .sort(optionsComparator) // in case of multiple default values (fill character has higher priority)
                    .find((code) => code.isDefaultValue);
                this.logService.warn(`Field8 parsing: unknown symbol [${validCodeSymbol}] for document type [${documentType.code}] at position ${position} - setting default value`)
            }
            return result;
        }

        [...variablePart] // string to array of chars
            .map((codeSymbol, index) => parseSingleSymbol(codeSymbol, index + 18)) // positions are numbered from 18
            .filter((code) => code !== undefined)
            .forEach((code) => model[`position${code.position}`] = code)

        return model;
    }

    private doUndefinedPositionMatchForDocumentType(variablePart: string, documentTypeCode: string): boolean {
        const labelsByDocumentType = getLabelsByDocumentTypeCode(this.definitions.labels, documentTypeCode);
        const codesByDocumentType = getCodesByDocumentTypeCode(this.definitions.codes, documentTypeCode);

        return zip([...variablePart], labelsByDocumentType.sort(ascendingOrderComparator)) // zip symbols (as char array) with corresponding labels into tuples
            .filter(([code, label]) => EMPTY_SYMBOLS.includes(code) || !label.isDefined) // left undefined positions or positions with empty symbols
            .filter(([, label]) => !isMergedWithPreviousPositions(label)) // filter out double character code positions
            .every(([code, label]) => (!label.isDefined && EMPTY_SYMBOLS.includes(code)) || findCodeByCodeSymbolAndPosition(codesByDocumentType, code, label.order));
        // either symbol is empty symbol in undefined positions or it is valid code for that position
    }
}
