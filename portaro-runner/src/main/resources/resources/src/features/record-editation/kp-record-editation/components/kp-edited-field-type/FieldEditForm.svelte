<script lang="ts">
    import type {Field as SvelteFormsField} from 'node_modules/svelte-forms/types';
    import type {FormController, FormControlOptions} from 'shared/value-editors/internal/forms/types';
    import type {ForceSetting} from 'shared/value-editors/kp-value-editor-force-settings/types';
    import type {EditableField, EditableFieldType, FieldValue, Fond} from 'typings/portaro.be.types';
    import type {Observable} from 'rxjs';
    import {ValueEditorSize} from 'shared/value-editors/types';
    import {createEventDispatcher, onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import KpValueEditorForceSettings from 'shared/value-editors/kp-value-editor-force-settings/KpValueEditorForceSettings.svelte';
    import {getRecordPath} from 'shared/utils/data-service-utils';

    export let fieldOrSubfieldType: EditableFieldType;
    export let field: EditableField;
    export let showRecordLink = false;
    export let fond: Fond | null = null;
    export let isSubfield = false;

    const dispatch = createEventDispatcher<{ 'field-value-submit': FieldValue }>();

    const editorSize = ValueEditorSize.SM;
    const formControlOptions: FormControlOptions = {
        updateTriggers: [{
            eventName: 'input',
            debounceValue: 1500
        }, {eventName: 'blur', debounceValue: 0}]
    }

    let editorController: KpValueEditor<any>;
    let formField$: Observable<SvelteFormsField<any>>;
    let isTouched$: Observable<boolean>;

    onMount(() => {
        const formControl = getFormController();
        formField$ = formControl.getFieldState$();
        isTouched$ = formControl.isTouched$();
    });

    function submitFieldValueIfValid(value: FieldValue, formFieldState: SvelteFormsField<any>) {
        if (formFieldState.valid) {
            dispatch('field-value-submit', value);
        }
    }

    function showValidationError(fieldState: SvelteFormsField<any>, show: boolean): boolean {
        if (fieldState) {
            return fieldState.invalid && show;
        }

        return false;
    }

    function createFieldForceSettings(usedFond: Fond): ForceSetting[] {
        return [
            {type: 'single-acceptable', options: {switchToInlineModeThreshold: 1}, strategy: 'merge'},
            {type: 'multiple-acceptable', options: {switchToInlineModeThreshold: 1}, strategy: 'merge'},
            {type: 'text', options: {switchToTextareaThreshold: 80}, strategy: 'merge'},
            {type: 'editable-record-search', options: {editParams: {focusedFieldTypeId: fieldOrSubfieldType.singleLinkedRecordSubfieldTypeId}}, strategy: 'merge'},
            {type: 'field7', options: {createParams: {fond: usedFond}, editParams: {fond: usedFond}}, strategy: 'merge'},
            {type: 'field8', options: {createParams: {fond: usedFond}, editParams: {fond: usedFond}}, strategy: 'merge'}
        ];
    }

    function createSubfieldForceSettings(): ForceSetting[] {
        return [
            {type: 'single-acceptable', options: {switchToInlineModeThreshold: 1}, strategy: 'merge'},
            {type: 'multiple-acceptable', options: {switchToInlineModeThreshold: 1}, strategy: 'merge'},
            {type: 'text', options: {switchToTextareaThreshold: 80}, strategy: 'merge'},
            {type: 'editable-record-search', options: {editParams: {focusedFieldTypeId: fieldOrSubfieldType.singleLinkedRecordSubfieldTypeId}}, strategy: 'merge'}
        ];
    }
    export function getFormController(): FormController<any> {
        return editorController.getFormController();
    }
</script>

<form class="field-value-form"
      on:submit|preventDefault
      class:has-error={showValidationError($formField$, $isTouched$)}
      class:has-not-error={!showValidationError($formField$, $isTouched$)}>

    <div class="value-editor-container">
        {#if showRecordLink && exists(field.recordLink)}
            <a class="text-ellipsis" href={getRecordPath(field.recordLink.id)} target="_blank" rel="noreferrer">{field.text}</a>
        {/if}

        <div class="value-editor">
            <KpValueEditorForceSettings forceSettings="{isSubfield || !exists(fond) ? createSubfieldForceSettings() : createFieldForceSettings(fond)}">
                <KpValueEditor type={fieldOrSubfieldType.editor.type}
                               options={fieldOrSubfieldType.editor.options}
                               validations={fieldOrSubfieldType.editor.validations}
                               isDisabled="{fieldOrSubfieldType.editor.isDisabled}"
                               isFocused="{fieldOrSubfieldType.editor.isFocused}"
                               editorId={field.id}
                               editorName={field.typeId}
                               size={editorSize}
                               {formControlOptions}
                               model={exists(field.recordLink) ? field.recordLink : field.value}
                               on:model-change={(event) => submitFieldValueIfValid(event.detail, $formField$)}
                               bind:this={editorController}/>
            </KpValueEditorForceSettings>
        </div>
    </div>
</form>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .field-value-form {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: @spacing-s;

        .value-editor-container {
            flex-grow: 1;
            display: flex;
            align-items: center;
            gap: @spacing-s;

            .value-editor {
                position: relative;
                flex-grow: 1;
            }
        }
    }
</style>