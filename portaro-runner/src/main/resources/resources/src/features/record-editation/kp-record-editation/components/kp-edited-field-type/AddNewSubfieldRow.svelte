<script lang="ts">
    import type {EditableFieldType} from 'typings/portaro.be.types';
    import type {SingleAcceptableValueEditorOptions} from 'shared/value-editors/internal/editors/single-acceptable/types';
    import {createEventDispatcher} from 'svelte';
    import {ValueEditorSize} from 'shared/value-editors/types';
    import {getLocalization} from 'core/svelte-context/context';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';

    export let addableSubfieldTypes: EditableFieldType[];

    const dispatch = createEventDispatcher<{ 'add-subfield': EditableFieldType }>();
    const localize = getLocalization();

    let editorOptions: SingleAcceptableValueEditorOptions<EditableFieldType>;
    $: editorOptions = {
        acceptableValues: addableSubfieldTypes,
        switchToInlineModeThreshold: 1
    }

    let typeToAdd = null;

    function addEmptyField(subfield: EditableFieldType) {
        dispatch('add-subfield', subfield);
        typeToAdd = null;
    }
</script>

<div class="add-new-subfield-row">
    <div class="heading-container col-md-4">
        <span class="text-muted">
            {localize(/* @kp-localization editace.PridejDalsiPodpole */ 'editace.PridejDalsiPodpole')}
        </span>
    </div>

    <div class="editor-container">
        <KpValueEditor size="{ValueEditorSize.SM}"
                       type={'single-acceptable'}
                       placeholder="{localize(/* @kp-localization editace.ChooseSubfieldType */ 'editace.ChooseSubfieldType')}"
                       options={editorOptions}
                       bind:model={typeToAdd}
                       on:model-change={(event) => addEmptyField(event.detail)}/>
    </div>

    <div class="col-md-2 empty-spacer"></div>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "bootstrap-less/bootstrap/variables";

    .add-new-subfield-row {
        display: flex;
        align-items: center;
        gap: @spacing-sm;

        .heading-container {
            padding: 0;
        }

        .editor-container {
            flex: 1;
        }

        @media (max-width: @screen-xs-max) {
            .empty-spacer {
                display: none;
            }
        }
    }
</style>