<script lang="ts">
    import type {EditableField, EditableFieldType, FieldValue, RecordEditation} from 'typings/portaro.be.types';
    import type {RecordEditationManager} from '../../record-editation.manager';
    import {popInAnim, popOutAnim} from 'shared/animations/pop-animations';
    import {exists} from 'shared/utils/custom-utils';
    import {onMount} from 'svelte';
    import FieldHeading from './FieldHeading.svelte';
    import PositionChangeArrowsButton from './PositionChangeArrowsButton.svelte';
    import FieldEditForm from './FieldEditForm.svelte';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';

    export let editationState: RecordEditation;
    export let editationManager: RecordEditationManager;
    export let parentField: EditableField;
    export let subfield: EditableField;
    export let subfieldType: EditableFieldType;
    export let subfieldIndex: number;
    export let previousSubfieldSameCode: boolean;

    let isFileDragging = false;
    let subfieldElement: HTMLDivElement;

    onMount(() => {
        if (editationManager.focusedFieldTypeId === subfield.typeId && exists(subfieldElement)) {
            subfieldElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'center'
            });

            const firstInput = subfieldElement.querySelector<HTMLElement>('.field-value-form .value-editor kp-value-editor input');
            if (exists(firstInput)) {
                firstInput.focus();
            }
        }
    });

    const handleSubfieldDragOver = (event: DragEvent) => {
        event.preventDefault();

        if (isDraggingFile(event)) {
            isFileDragging = true;
        }
    }

    const handleSubfieldDragLeave = (event: DragEvent) => {
        event.preventDefault();
        isFileDragging = false;
    }

    const handleSubfieldDrop = (event: DragEvent) => {
        event.preventDefault();
        isFileDragging = false;

        const fileList = event.dataTransfer?.files;
        if (!exists(fileList)) {
            return;
        }

        editationManager.handleSubfieldFilesDropped(subfield, fileList)
    }

    const handleMoveSubfieldUp = () => {
        editationManager.actions.moveFieldUp(subfield);
    }

    const handleMoveSubfieldDown = () => {
        editationManager.actions.moveFieldDown(subfield);
    }

    const handleClearSubfield = () => {
        editationManager.actions.clearFieldValue(subfield);
    }

    const handleSubmitSubfieldValue = (value: FieldValue) => {
        editationManager.actions.setFieldValue(subfield, value);
    }

    const handleDeleteSubfield = () => {
        editationManager.actions.deleteField(subfield);
    }

    function isDraggingFile(event: DragEvent): boolean {
        const types = Array.from(event.dataTransfer?.types || []);
        return types.includes('Files');
    }
</script>

<div class="subfield-row field_{subfield.code} subfield_{subfield.code}"
     role="application"
     class:file-dragging={isFileDragging}
     bind:this={subfieldElement}
     on:dragover={handleSubfieldDragOver}
     on:dragleave={handleSubfieldDragLeave}
     on:drop={handleSubfieldDrop}
     in:popInAnim={{key: subfield}}
     out:popOutAnim={{key: subfield}}>

    <FieldHeading fieldType="{subfieldType}"
                  showIdentifiers="{editationState.showFieldIdentifiers}"
                  isEntry="{subfieldType.entryElement}"
                  hintCharactersLimit="{50}"
                  showContents="{!previousSubfieldSameCode}"
                  isSubfield/>

    <div class="subfield-editor-container">
        {#if exists(subfieldType)}
            <FieldEditForm field="{subfield}"
                           fieldOrSubfieldType="{subfieldType}"
                           showRecordLink="{subfieldType.editor.type === 'editable-record-search'}"
                           on:field-value-submit={(event) => handleSubmitSubfieldValue(event.detail)}
                           isSubfield/>
        {/if}
    </div>

    <div class="subfields-buttons-container col-md-2">
        {#if subfield.value !== null && subfield.recordLink !== null}
            <KpIconButton icon="broom" on:click={handleClearSubfield}/>
        {/if}

        <KpIconButton icon="trash"
                      on:click={handleDeleteSubfield}/>

        {#if parentField.fields.length > 1}
            <PositionChangeArrowsButton allowUp="{subfieldIndex > 0}"
                                        allowDown="{subfieldIndex < parentField.fields.length - 1}"
                                        on:up={handleMoveSubfieldUp}
                                        on:down={handleMoveSubfieldDown}/>
        {/if}
    </div>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "bootstrap-less/bootstrap/variables";

    .subfield-row {
        display: flex;
        align-items: center;
        gap: @spacing-sm;
        outline-offset: 4px;
        border-radius: @border-radius-small;
        outline: 2px solid transparent;
        transition: outline-color 0.3s ease-in-out, outline 0.3s ease-in-out;

        &.file-dragging {
            outline: 2px dashed var(--accent-blue-new);
        }

        &:hover {
            outline: 1px solid @themed-border-default;
        }

        @media (max-width: @screen-xs-max) {
            flex-direction: column;
            gap: @spacing-s;
        }

        .subfield-editor-container {
            flex: 1;
        }

        .subfields-buttons-container {
            display: flex;
            gap: @spacing-s;
            justify-content: end;
            padding: 0;
        }
    }

    :global {
        .kp-edited-field-type .subfield-row {
            @media (max-width: @screen-xs-max) {
                .field-heading-container,
                .subfield-editor-container,
                .subfields-buttons-container {
                    width: 100%;
                }
            }
        }
    }
</style>