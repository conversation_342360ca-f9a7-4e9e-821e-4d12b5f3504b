import type {PromiseBasedRecordEditationDataService, RecordEditationAction} from '../types';
import type {Fond, RecordEditation} from 'typings/portaro.be.types';
import type {Observable} from 'rxjs';
import {GenericEditationAction} from './generic-editation-action';
import {from} from 'rxjs';

export class SetFondAction extends GenericEditationAction implements RecordEditationAction {
    constructor(private backend: PromiseBasedRecordEditationDataService, private fond: Fond) {
        super();
    }

    public executeAction(editationObject: RecordEditation): Observable<RecordEditation> {
        return from(this.backend.setFond(editationObject.id, this.fond.id));
    }
}