import type {Field007Code, Field007Definitions, Field007DocumentCategory} from 'typings/portaro.be.types';
import type {Field7Model} from '../utils/types';
import type {LogService} from 'core/logging/log.service';
import {UnknownDocumentCategoryCodeError} from './field7-errors';
import {UNDEFINED_POSITION_CHARACTER} from '../utils/types';
import {
    findCodeByCodeSymbolAndPosition,
    findLabelByOrder,
    getCodesByPosition,
    getLabelsAndCodesByDocumentCategoryCode
} from '../utils/utils';

export class Field7CodeConverter {
    constructor(private definitions: Field007Definitions, private logService: LogService) {
    }

    public serializeModel(model: Field7Model) {

        const maxCodeLength: number = this.definitions.codes // field007 code has variable length => find highest position to iterate
            .map((code) => code.position)
            .reduce((a, b) => Math.max(a, b));

        let value: string = model.documentCategory.code;

        for (let positionIndex = 1; positionIndex <= maxCodeLength; positionIndex++) {
            if (this.isDoubleCharacterSymbolPosition(model.documentCategory, positionIndex - 1)) {
                continue; // skip position if previous position is double characters long
            }

            if (model[`position${positionIndex}`]) {
                const position: Field007Code = model[`position${positionIndex}`];
                value = value.concat(position.code);
            } else {
                value = value.concat(UNDEFINED_POSITION_CHARACTER);
            }
        }
        return value.trimEnd();
    }

    public parseCode(value: string): Field7Model | null {

        if (!value) {
            return null;
        }

        const documentCategoryCode = value.charAt(0);
        const variablePart = value.slice(1);

        const model: Field7Model = {
            documentCategory: this.definitions.documentCategories.find((documentType) => documentType.code === documentCategoryCode)
        };

        if (!model.documentCategory) { // document category was not found (it is undefined)
            throw new UnknownDocumentCategoryCodeError(documentCategoryCode)
        }

        const [labels, codes] = getLabelsAndCodesByDocumentCategoryCode(this.definitions, documentCategoryCode);

        const parseSingleSymbol = (codeSymbol: string, position: number): Field007Code => {
            if (!findLabelByOrder(labels, position)) {
                return undefined; // skip undefined positions
            }

            let validCodeSymbol = codeSymbol;
            if (this.isDoubleCharacterSymbolPosition(model.documentCategory, position)) {
                validCodeSymbol += variablePart.charAt(position); // next position in variable part (-1+1)
            }

            let result = findCodeByCodeSymbolAndPosition(codes, validCodeSymbol, position);
            if (!result) { // if unknown symbol use default value
                result = getCodesByPosition(codes, position).find((code) => code.isDefaultValue);
                this.logService.warn(`Field7 parsing: unknown symbol [${validCodeSymbol}] at position ${position} - setting default value`)
            }
            return result;
        }

        [...variablePart]
            .map((codeSymbol, index) => parseSingleSymbol(codeSymbol, index + 1)) // positions are numbered from 1
            .filter((code) => code !== undefined)
            .forEach((code) => model[`position${code.position}`] = code)

        return model;
    }

    private isDoubleCharacterSymbolPosition(documentCategory: Field007DocumentCategory, position: number): boolean {
        return documentCategory.code === 'r' && position === 9; // only double character codes in field 0007
    }

}