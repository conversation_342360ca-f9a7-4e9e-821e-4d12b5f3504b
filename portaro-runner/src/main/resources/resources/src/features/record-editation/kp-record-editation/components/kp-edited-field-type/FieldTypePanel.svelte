<script lang="ts">
    import type {EditableFieldType} from 'typings/portaro.be.types';
    import {createEventDispatcher} from 'svelte';
    import AddFieldRepetitionButton from './AddFieldRepetitionButton.svelte';

    export let additionalClasses = '';
    export let fieldType: EditableFieldType;

    const dispatch = createEventDispatcher<{ 'add-repetition': void }>();
</script>

<div class="field-type-panel {additionalClasses}" class:is-repeatable={fieldType.repeatable}>
    <div class="field-panel-container field_{fieldType.code}">
        <slot/>
    </div>

    {#if fieldType.repeatable}
        <AddFieldRepetitionButton on:click={() => dispatch('add-repetition')}/>
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    @header-bg: #F5F5F5;
    @add-repetition-btn-height: 32px;

    .field-type-panel {
        display: flex;
        flex-direction: column;

        &.is-repeatable .field-panel-container {
            border-radius: @border-radius-default @border-radius-default 0 @border-radius-default;
        }

        .field-panel-container {
            width: 100%;
            border-radius: @border-radius-default;
            border: 1px solid @themed-border-default;
            display: flex;
            flex-direction: column;
        }
    }
</style>