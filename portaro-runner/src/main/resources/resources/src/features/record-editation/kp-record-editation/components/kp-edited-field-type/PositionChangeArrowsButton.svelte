<script lang="ts">
    import {createEventDispatcher} from 'svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let allowUp = true;
    export let allowDown = true;

    const dispatch = createEventDispatcher<{ 'up': void, 'down': void }>();
</script>

<div class="position-change-arrows-button">
    <button type="button" class="arrow-up" disabled="{!allowUp}" on:click={() => dispatch('up')}>
        <UIcon icon="arrow-small-up" lineHeight="1"/>
    </button>

    <div class="divider"></div>

    <button type="button" class="arrow-down" disabled="{!allowDown}" on:click={() => dispatch('down')}>
        <UIcon icon="arrow-small-down" lineHeight="1"/>
    </button>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    @size: 30px;
    @disabled-bg: #F5F5F5;

    .position-change-arrows-button {
        width: @size;
        height: @size;
        display: flex;
        flex-direction: column;
        border: 1px solid @themed-border-default;
        border-radius: @border-radius-default;
        background-color: @themed-body-bg;
        overflow: hidden;
        font-size: @font-size-small;

        .arrow-up,
        .arrow-down {
            width: 100%;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            outline: none;
            border: none;
            background-color: @themed-body-bg;

            &:disabled {
                background-color: @disabled-bg;
                cursor: not-allowed;
                opacity: 0.4;
            }

            &:not(:disabled) {
                cursor: pointer;
                transition: background-color 0.3s ease-in-out, opacity 0.3s ease-in-out;

                &:hover {
                    background-color: @disabled-bg;
                    opacity: 0.75;
                }
            }
        }

        .divider {
            width: 100%;
            background-color: @themed-border-default;
            height: 1px;
            flex-shrink: 0;
        }
    }
</style>