import type {EditationCallbacks, RecordEditationActionFactory} from '../types';
import type {FieldTypeId, RecordEditation} from 'typings/portaro.be.types';
import type {LogService} from 'core/logging/log.service';
import type {FileService} from '../../file/file.service';
import type {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
import type {FieldTypeDataService} from 'src/features/record/field-types/field-type-data.service';
import {RecordEditationManager} from '../kp-record-editation/record-editation.manager';

/*@ngInject*/
export function recordEditationManagerFactory(SERVER_URL: string,
                                              fileService: FileService,
                                              recordEditationActionFactory: RecordEditationActionFactory,
                                              toastMessageService: ToastMessageService,
                                              logService: LogService,
                                              fieldTypeDataService: FieldTypeDataService): RecordEditationManagerFactory {

    return {
        createManager: (recordEditation, focusedFieldTypeId, editationCallbacks) => {
            decorateRecordEditationWithFieldTypes(recordEditation);
            return new RecordEditationManager(recordEditation, fileService, SERVER_URL, fieldTypeDataService, editationCallbacks, logService, toastMessageService, focusedFieldTypeId, recordEditationActionFactory);
        }
    };

    function decorateRecordEditationWithFieldTypes(recordEditation: RecordEditation): void {
        recordEditation.addableFieldTypes = [];
        recordEditation.usedFieldTypes = [];
    }
}

recordEditationManagerFactory.factoryName = 'recordEditationManagerFactory';

export interface RecordEditationManagerFactory {
    createManager(recordEditation: RecordEditation, focusedFieldTypeId: FieldTypeId | undefined, editationCallbacks: EditationCallbacks | undefined): RecordEditationManager;
}