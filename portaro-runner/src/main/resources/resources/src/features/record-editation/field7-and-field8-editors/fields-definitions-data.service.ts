import {ngAsync} from 'shared/utils/ng-@decorators';
import type {AuthorityField008Definitions, Field007Definitions, Field008Definitions} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';

export class FieldsDefinitionsDataService {
    public static serviceName = 'fieldsDefinitionsDataService'

    private static readonly FIELDS_DEFINITIONS_007_ROUTE = 'fields-definitions/007';
    private static readonly FIELDS_DEFINITIONS_008_ROUTE = 'fields-definitions/008';
    private static readonly FIELDS_DEFINITIONS_AUTHORITY_008_ROUTE = 'fields-definitions/authority-008';


    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async getField007Definitions(): Promise<Field007Definitions> {
        return this.ajaxService
            .createRequest(`${FieldsDefinitionsDataService.FIELDS_DEFINITIONS_007_ROUTE}`)
            .get();
    }

    @ngAsync()
    public async getField008Definitions(): Promise<Field008Definitions> {
        return this.ajaxService
            .createRequest(`${FieldsDefinitionsDataService.FIELDS_DEFINITIONS_008_ROUTE}`)
            .get();
    }

    @ngAsync()
    public async getAuthorityField008Definitions(): Promise<AuthorityField008Definitions> {
        return this.ajaxService
            .createRequest(`${FieldsDefinitionsDataService.FIELDS_DEFINITIONS_AUTHORITY_008_ROUTE}`)
            .get();
    }
}