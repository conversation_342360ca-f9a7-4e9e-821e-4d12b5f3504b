import type {EditableField, FieldType, FieldValue, Fond, RecordEditation} from 'typings/portaro.be.types';
import type {RecordEditationAction, RecordEditationActionFactory} from '../types';
import type {RecordEditationManager} from './record-editation.manager';
import {BehaviorSubject, EMPTY, firstValueFrom, type Observable, Subject} from 'rxjs';
import {ActionAndResponse} from '../utils/action-and-response';
import {catchError, filter, first, map, mergeMap, switchMap, tap} from 'rxjs/operators';
import {SetFondAction} from '../utils/set-fond-action';
import {shareWithRefCount} from 'shared/utils/observables-utils';
import {SaveAction} from '../utils/save-action';
import {PublishAction} from '../utils/publish-action';
import {RemoveAction} from '../utils/remove-action';
import type {LogService} from 'core/logging/log.service';
import {transferify} from 'shared/utils/data-service-utils';
import {isDefined} from 'shared/utils/custom-utils';

export class RecordEditationActionsManager {

    readonly #isRequestPendingState: BehaviorSubject<boolean>;

    private savingFinishedNotifier: Subject<void>;
    private publishingFinishedNotifier: Subject<void>;
    private removingFinishedNotifier: Subject<void>;

    private isSaving = false;
    private isRemoving = false;
    private isPublishing = false;

    private readonly action$: Subject<RecordEditationAction> = new Subject();
    private readonly actionAndResponse$: Observable<ActionAndResponse>;

    // hooks for completed actions:
    private readonly saveActionsResponse$: Observable<RecordEditation>;
    private readonly publishActionsResponse$: Observable<RecordEditation>;
    private readonly removeActionResponse$: Observable<RecordEditation>;

    constructor(private recordEditationManager: RecordEditationManager,
                private recordEditationActionFactory: RecordEditationActionFactory,
                private logger: LogService,
                private editationObject: RecordEditation) {

        this.#isRequestPendingState = new BehaviorSubject(false);

        this.actionAndResponse$ = this.action$.pipe(
            tap(() => {
                this.#isRequestPendingState.next(true);
            }),
            switchMap((action) =>
                action.executeAction(editationObject)
                    .pipe( // request data
                        catchError(() => this.handleRequestError(action)),  // catch error caused by request
                        map((response) => new ActionAndResponse(action, response)) // pair response with corresponding action
                    )
            ),
            mergeMap(async (result) => {
                if (result.action instanceof SetFondAction) {
                    await this.recordEditationManager.refreshEditableFieldTypes();
                }
                return result;
            }),
            mergeMap(async (result) => {
                result.response = await this.processResponse(result.response);
                return result;
            }),
            shareWithRefCount()
        );

        this.saveActionsResponse$ = this.actionAndResponse$.pipe(
            filter((actionAndResponse) => actionAndResponse.action instanceof SaveAction),
            map((actionAndResponse) => actionAndResponse.response)
        );
        this.saveActionsResponse$.subscribe(() => this.onSaveComplete());

        this.publishActionsResponse$ = this.actionAndResponse$.pipe(
            filter((actionAndResponse) => actionAndResponse.action instanceof PublishAction),
            map((actionAndResponse) => actionAndResponse.response)
        );
        this.publishActionsResponse$.subscribe(() => this.onPublishComplete());

        this.removeActionResponse$ = this.actionAndResponse$.pipe(
            filter((actionAndResponse) => actionAndResponse.action instanceof RemoveAction),
            map((actionAndResponse) => actionAndResponse.response)
        );
        this.removeActionResponse$.subscribe(() => this.onRemoveComplete());
    }


    public addEmptyField(fieldType: FieldType): void {
        this.dispatchActionIfCanProceed(this.recordEditationActionFactory.createAddEmptyFieldAction(fieldType));
        this.log('addEmptyField', {fieldType});
    }

    public addEmptySubfield(parentField: EditableField, subfieldType: FieldType): void {
        this.dispatchActionIfCanProceed(this.recordEditationActionFactory.createAddEmptySubFieldAction(parentField, subfieldType));
        this.log('addEmptySubfield', {parentField: parentField.typeId, subfieldType});
    }

    public setFieldValue(field: EditableField, value: FieldValue): void {
        this.dispatchActionIfCanProceed(this.recordEditationActionFactory.createSetFieldValueAction(field, value));
        this.log('setFieldValue', {field: field.typeId, value});
    }

    public clearFieldValue(field: EditableField): void {
        this.dispatchActionIfCanProceed(this.recordEditationActionFactory.createClearFieldValueAction(field));
        this.log('clearFieldValue', {field: field.typeId});
    }

    public moveFieldUp(field: EditableField): void {
        this.dispatchActionIfCanProceed(this.recordEditationActionFactory.createMoveFieldUpAction(field));
        this.log('moveFieldUp', {field: field.typeId});
    }

    public moveFieldDown(field: EditableField): void {
        this.dispatchActionIfCanProceed(this.recordEditationActionFactory.createMoveFieldDownAction(field));
        this.log('moveFieldDown', {field: field.typeId});
    }

    public deleteField(field: EditableField): void {
        this.dispatchActionIfCanProceed(this.recordEditationActionFactory.createDeleteFieldAction(field));
        this.log('deleteField', {field: field.typeId});
    }

    public setFond(fond: Fond): void {
        this.dispatchActionIfCanProceed(this.recordEditationActionFactory.createSetFondAction(fond));
        this.log('setFond', {fond});
    }

    public saveEditation(): Promise<void> {
        this.isSaving = true;

        // Wait for pending request to finish and then dispatch action
        if (this.#isRequestPendingState.getValue()) {
            this.actionAndResponse$.pipe(first()).subscribe(() => this.action$.next(this.recordEditationActionFactory.createSaveAction()));
        } else {
            this.action$.next(this.recordEditationActionFactory.createSaveAction());
        }

        this.savingFinishedNotifier = new Subject();
        this.log('saveEditation');
        return firstValueFrom(this.savingFinishedNotifier);
    }

    public publishEditation(): Promise<void> {
        this.isPublishing = true;

        // Wait for pending request to finish and then dispatch action
        if (this.#isRequestPendingState.getValue()) {
            this.actionAndResponse$.pipe(first()).subscribe(() => this.action$.next(this.recordEditationActionFactory.createPublishAction()));
        } else {
            this.action$.next(this.recordEditationActionFactory.createPublishAction());
        }

        this.publishingFinishedNotifier = new Subject();
        this.log('publishEditation');
        return firstValueFrom(this.publishingFinishedNotifier);
    }

    public removeEditation(): Promise<void> {
        this.isRemoving = true;

        // Wait for pending request to finish and then dispatch action
        if (this.#isRequestPendingState.getValue()) {
            this.actionAndResponse$.pipe(first()).subscribe(() => this.action$.next(this.recordEditationActionFactory.createRemoveAction()));
        } else {
            this.action$.next(this.recordEditationActionFactory.createRemoveAction());
        }

        this.removingFinishedNotifier = new Subject();
        this.log('removeEditation');
        return firstValueFrom(this.removingFinishedNotifier);
    }

    public get isRequestPendingState$(): BehaviorSubject<boolean> {
        return this.#isRequestPendingState;
    }


    private processResponse(editationObjectResponse: RecordEditation): Promise<RecordEditation> {
        this.#isRequestPendingState.next(false);

        const decoratedEditationObject = this.recordEditationManager.decorateEditationObject(editationObjectResponse);
        return Promise.resolve(decoratedEditationObject);
    }

    private handleRequestError(action: RecordEditationAction): Observable<never> {
        if (action instanceof PublishAction) {
            this.isPublishing = false;
        }

        if (action instanceof RemoveAction) {
            this.isRemoving = false;
        }

        if (action instanceof SaveAction) {
            this.isSaving = false;
        }

        this.#isRequestPendingState.next(false);
        return EMPTY;
    }

    private onSaveComplete(): void {
        this.isSaving = false;
        this.savingFinishedNotifier.next();
    }

    private onPublishComplete(): void {
        this.isPublishing = false;
        this.publishingFinishedNotifier.next();
    }

    private onRemoveComplete(): void {
        this.isRemoving = false;
        this.removingFinishedNotifier.next();
    }

    private dispatchActionIfCanProceed(action: RecordEditationAction): void {
        if (this.isPublishing || this.isRemoving || this.isSaving) {
            return;
        }

        this.action$.next(action);
    }

    private log(action: string, params?: Record<string, any>) {
        const {id, text} = this.editationObject;
        this.logger.debug(`Record editation of ${id} (${text})`, `action: ${action}`, isDefined(params) ? `params: ${transferify(params)}` : undefined);
    }
}