<script lang="ts">
    import type {ForceSetting} from 'shared/value-editors/kp-value-editor-force-settings/types';
    import type {Fond, RecordEditation} from 'typings/portaro.be.types';
    import type {RecordEditationManager} from '../record-editation.manager';
    import {exists} from 'shared/utils/custom-utils';
    import {onMount} from 'svelte';
    import {getLocalization} from 'core/svelte-context/context';
    import {ValueEditorSize} from 'shared/value-editors/types';
    import {isEqual} from 'lodash-es';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpValueEditorForceSettings from 'shared/value-editors/kp-value-editor-force-settings/KpValueEditorForceSettings.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import RecordEditationHeading from './RecordEditationHeading.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import KpSearchBar from 'shared/ui-widgets/search/KpSearchBar.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    export let editationState: RecordEditation;
    export let editationManager: RecordEditationManager;
    export let viewType: 'modal' | 'page';
    export let isRequestPending: boolean;
    export let forceSearchResetButton: boolean;

    const localize = getLocalization();

    $: anyChanges = exists(editationState) && !isEqual(editationState, editationManager.editationObjectBeforeCurrentEditation);
    let mainMenuHeight = 0;

    onMount(() => {
        const mainMenuElement = document.querySelector('nav.kp-main-menu');

        if (exists(mainMenuElement)) {
            mainMenuHeight = mainMenuElement.getBoundingClientRect().height;
        }
    });

    const handleFondChange = (event: CustomEvent<Fond>) => {
        editationManager.actions.setFond(event.detail);
    };

    function createFondSelectorForceSettings(): ForceSetting[] {
        return [
            {type: 'single-acceptable', options: {switchToInlineModeThreshold: 1}},
            {type: 'multiple-acceptable', options: {switchToInlineModeThreshold: 1}}
        ];
    }
</script>

<div class="record-editation-toolbar in-{viewType}" style:--main-menu-height="{mainMenuHeight}px">
    {#if viewType === 'page'}
        <div class="container">
            <KpHeading type="h1">
                <RecordEditationHeading {editationState}/>
            </KpHeading>
        </div>
    {/if}

    <div class="toolbar-content-wrapper" class:container={viewType === 'page'}>
        <div class="toolbar-content-container">
            <KpSearchBar width="auto"
                         height="34px"
                         forceResetButton="{forceSearchResetButton}"
                         placeholder="{localize(/* @kp-localization localization.EnterPartOfTextOrCode */ 'localization.EnterPartOfTextOrCode')}"
                         on:search
                         on:reset/>

            <div class="buttons-container">
                {#if editationState.draft}
                    <KpButton buttonStyle="success-new"
                              isDisabled="{!anyChanges || isRequestPending}"
                              id="record-editation-publish-and-close-{editationManager.recordId}"
                              dataQa="record-editation-publish-and-close"
                              on:click={() => editationManager.callbacks.publishAndClose()}>

                        <IconedContent icon="check">
                            {localize(/* @kp-localization editace.PublishAndClose */ 'editace.PublishAndClose')}
                        </IconedContent>
                    </KpButton>

                    <KpButton buttonStyle="danger-new"
                              isDisabled="{isRequestPending}"
                              id="record-editation-discard-concept-{editationManager.recordId}"
                              dataQa="record-editation-discard-concept"
                              on:click={() => editationManager.callbacks.deleteDraftAndClose()}>

                        <IconedContent icon="trash">
                            {localize(/* @kp-localization editace.DiscardConcept */ 'editace.DiscardConcept')}
                        </IconedContent>
                    </KpButton>
                {/if}

                {#if !editationState.draft}
                    <KpButton buttonStyle="success-new"
                              isDisabled="{isRequestPending}"
                              id="record-editation-save-and-close-{editationManager.recordId}"
                              dataQa="record-editation-save-and-close"
                              on:click={() => editationManager.callbacks.saveAndClose()}>

                        <IconedContent icon="disk">
                            {localize(/* @kp-localization editace.SaveAndClose */ 'editace.SaveAndClose')}
                        </IconedContent>
                    </KpButton>
                {/if}

                {#if exists(editationManager.callbacks.saveAndContinue)}
                    <KpButton isDisabled="{isRequestPending}"
                              id="record-editation-save-and-continue-{editationManager.recordId}"
                              dataQa="record-editation-save-and-continue"
                              on:click={() => editationManager.callbacks.saveAndContinue()}>

                        <IconedContent icon="disk">
                            {localize(/* @kp-localization editace.SaveChangesAndContinue */ 'editace.SaveChangesAndContinue')}
                        </IconedContent>
                    </KpButton>
                {/if}

                {#if !editationState.draft}
                    <KpButton isDisabled="{isRequestPending}"
                              id="record-editation-close-{editationManager.recordId}"
                              dataQa="record-editation-close"
                              on:click={() => editationManager.callbacks.discardAndClose()}>

                        <IconedContent icon="cross-circle">
                            {localize(/* @kp-localization commons.zavrit */ 'commons.zavrit')}
                        </IconedContent>
                    </KpButton>
                {/if}

                {#if !anyChanges && editationState.revisionSaved}
                    <div class="changes-saved-container">
                        <IconedContent icon="check-circle" gap="3px">
                            {localize(/* @kp-localization editace.AllChangesSaved */ 'editace.AllChangesSaved')}
                        </IconedContent>
                    </div>
                {/if}

                <div class="loading-container" class:is-visible={isRequestPending}>
                    <KpLoadingBlock size="xs" data-qa="record-editation-processing-indicator"/>
                </div>
            </div>

            {#if exists(editationState.fondSelectionEditor)}
                <div class="fond-select-container">
                    <label class="text-muted" for="record-editation-fond-select">
                        {localize(/* @kp-localization editace.vyberFond */ 'editace.vyberFond')}:
                    </label>

                    <KpValueEditorForceSettings forceSettings="{createFondSelectorForceSettings()}">
                        <KpValueEditor {...editationState.fondSelectionEditor}
                                       size="{ValueEditorSize.SM}"
                                       model="{editationState.fond}"
                                       editorId="record-editation-fond-select"
                                       on:model-change={handleFondChange}/>
                    </KpValueEditorForceSettings>
                </div>
            {/if}
        </div>
    </div>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "bootstrap-less/bootstrap/variables";

    .record-editation-toolbar {
        width: 100%;
        display: flex;
        flex-direction: column;
        background-color: @themed-body-bg;
        gap: @spacing-sm;

        &.in-modal {
            padding: @spacing-ml;
            border-bottom: 1px solid @themed-border-default;

            @media (min-width: @screen-sm-min) {
                position: sticky;
                z-index: 90;
                top: 0;
            }
        }

        &.in-page {
            padding: @spacing-xl 0 @spacing-l;
            border-bottom: 1px solid @themed-border-default;

            @media (min-width: @screen-sm-min) {
                position: sticky;
                z-index: 90;
                top: var(--main-menu-height);
            }
        }

        .toolbar-content-wrapper .toolbar-content-container {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: @spacing-m;
            width: 100%;

            .buttons-container {
                display: flex;
                align-items: center;
                gap: @spacing-sm;

                .changes-saved-container {
                    color: var(--success-green);
                }

                .loading-container {
                    opacity: 0;
                    visibility: hidden;
                    transition: visibility 0.2s ease-in-out, opacity 0.2s ease-in-out;

                    &.is-visible {
                        opacity: 1;
                        visibility: visible;
                    }
                }
            }

            .fond-select-container {
                display: flex;
                gap: @spacing-xs;
                margin-left: auto;
                flex-direction: column;

                @media (max-width: @screen-xs-max) {
                    margin-left: initial;
                }

                label {
                    padding: 0;
                    margin: 0;
                }
            }
        }
    }
</style>