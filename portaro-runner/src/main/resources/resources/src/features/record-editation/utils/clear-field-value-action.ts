import type {PromiseBasedRecordEditationDataService, RecordEditationAction,} from '../types';
import type {EditableField, RecordEditation} from 'typings/portaro.be.types';
import type {Observable} from 'rxjs';
import {from} from 'rxjs';
import {GenericEditationAction} from './generic-editation-action';

export class ClearFieldValueAction extends GenericEditationAction implements RecordEditationAction {
    constructor(private backend: PromiseBasedRecordEditationDataService, private field: EditableField) {
        super();
    }

    public executeAction(editationObject: RecordEditation): Observable<RecordEditation> {
        return from(this.backend.clearFieldValue(editationObject.id, this.field.idPath));
    }
}