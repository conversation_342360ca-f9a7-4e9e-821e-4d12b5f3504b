<script lang="ts">
    import type {EditableFieldType} from 'typings/portaro.be.types';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import KpStringCollapser from 'shared/components/kp-string-collapser/KpStringCollapser.svelte';

    export let fieldType: EditableFieldType;
    export let showIdentifiers: boolean;
    export let hintCharactersLimit: number;
    export let isSubfield = false;
    export let isEntry = false;
    export let showContents = true;
</script>

<div class="field-heading-container col-sm-4">
    {#if showContents}
        <span class="identifier-and-name" class:is-entry={isEntry}>
            {#if showIdentifiers}
                <span class="field-identifier {isSubfield ? 'subfield-type-code' : 'field-type-code'}">{fieldType.code}</span>
            {/if}

            <span class="field-name">{pipe(fieldType, loc())}</span>

            {#if isEntry}
                <strong>*</strong>
            {/if}
        </span>

        {#if fieldType.editNote}
            <div class="text-muted small">
                <KpStringCollapser value="{fieldType.editNote}" charactersLimit="{hintCharactersLimit}"/>
            </div>
        {/if}
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    @identifier-height: 18px;
    @identifier-padding: 8px;

    .field-heading-container {
        padding: 0;
        display: flex;
        flex-direction: column;
        gap: @spacing-xs;

        .identifier-and-name {
            display: flex;
            align-items: center;
            gap: @spacing-s;

            &.is-entry {
                color: var(--danger-red);

                .subfield-type-code {
                    font-weight: 500;
                    color: var(--danger-red);
                }
            }

            .field-type-code {
                display: flex;
                align-items: center;
                background-color: var(--accent-blue-new);
                color: white;
                height: @identifier-height;
                border-radius: calc(@identifier-height / 2);
                padding: 0 @identifier-padding;
                font-weight: 500;
                font-size: @font-size-small;
            }

            .subfield-type-code {
                color: #777;
            }

            .field-name {
                font-weight: 500;
            }
        }
    }
</style>