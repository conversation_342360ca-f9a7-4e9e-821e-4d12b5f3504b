import type {RecordEditationAction} from '../types';
import type {RecordEditation} from 'typings/portaro.be.types';
import type {Observable} from 'rxjs';

/**
 * @name GenericEditationAction
 * @class
 * @abstract
 * @description abstract class implementing shared parts of {@link RecordEditationAction}
 */
export abstract class GenericEditationAction implements RecordEditationAction {
    public abstract executeAction(editationObject: RecordEditation): Observable<RecordEditation>;

    public equals(otherAction: RecordEditationAction): boolean {
        return this === otherAction;
    }
}