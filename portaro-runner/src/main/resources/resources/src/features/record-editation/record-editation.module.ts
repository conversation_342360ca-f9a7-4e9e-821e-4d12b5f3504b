import register from '@kpsys/angularjs-register';
import {recordEditationManagerFactory} from './services/record-editation-manager.factory';
import {RecordEditationDataService} from './services/record-editation.data-service';
import {BasicRecordEditationActionFactory} from './utils/basic-record-editation-factory';
import {FieldsDefinitionsDataService} from './field7-and-field8-editors/fields-definitions-data.service';
import {Field7EditorServiceFactory} from './field7-and-field8-editors/field7-editor-modal/field7-editor-service.factory';
import {Field8EditorServiceFactory} from './field7-and-field8-editors/field8-editor-modal/field8-editor-service.factory';
import {AuthorityField8EditorServiceFactory} from './field7-and-field8-editors/authority-field8-editor-modal/authority-field8-editor-service.factory';
import {recordEditationRoutes} from './kp-record-editation-page/record-editation.routes';

/**
 * @ngdoc module
 * @name portaro.features.record-editation
 * @module portaro.features.record-editation
 */
export default register('portaro.features.record-editation')
    .config(recordEditationRoutes)
    .service(RecordEditationDataService.serviceName, RecordEditationDataService)
    .factory(recordEditationManagerFactory.factoryName, recordEditationManagerFactory)
    .service(BasicRecordEditationActionFactory.serviceName, BasicRecordEditationActionFactory)
    .service(FieldsDefinitionsDataService.serviceName, FieldsDefinitionsDataService)
    .service(Field7EditorServiceFactory.serviceName, Field7EditorServiceFactory)
    .service(Field8EditorServiceFactory.serviceName, Field8EditorServiceFactory)
    .service(AuthorityField8EditorServiceFactory.serviceName, AuthorityField8EditorServiceFactory)
    .name();