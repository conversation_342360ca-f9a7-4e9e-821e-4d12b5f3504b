import type {AuthorityField8Model} from '../utils/types';

export class UnknownSectionsCodesError extends Error {
    constructor(public readonly sectionsWithUnknownCodes: (keyof AuthorityField8Model)[]) {
        super(`Authority field8 parse error - codes from following sections could not be mapped to definitions: ${sectionsWithUnknownCodes.join(', ')}`);
        this.name = UnknownSectionsCodesError.name;
    }
}

export class IncorrectSectionFormatError extends Error {
    constructor(public readonly section: keyof AuthorityField8Model, public readonly sectionValue: string) {
        super(`Authority field8 parse error - value [${sectionValue}] of section [${section}] has incorrect format`);
        this.name = IncorrectSectionFormatError.name;
    }
}
