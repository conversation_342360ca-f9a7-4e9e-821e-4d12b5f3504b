<script lang="ts">
    import type {SingleAcceptableValueEditorOptions} from 'shared/value-editors/internal/editors/single-acceptable/types';
    import type {EditableFieldType} from 'typings/portaro.be.types';
    import {createEventDispatcher} from 'svelte';
    import {ValueEditorSize} from 'shared/value-editors/types';
    import {getLocalization} from 'core/svelte-context/context';
    import {exists} from 'shared/utils/custom-utils';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    export let addableFieldTypes: EditableFieldType[] = [];
    export let size = ValueEditorSize.MD;

    const dispatch = createEventDispatcher<{ 'add-field': EditableFieldType }>();
    const localize = getLocalization();

    let editorOptions: SingleAcceptableValueEditorOptions<EditableFieldType>;
    $: editorOptions = {
        acceptableValues: addableFieldTypes,
        switchToInlineModeThreshold: 1
    }

    let typeToAdd = null;

    function addEmptyField(field: EditableFieldType) {
        dispatch('add-field', field);
        typeToAdd = null;

        setTimeout(() => scrollToField(field.code), 1000);
    }

    function scrollToField(code: string) {
        const fields = document.querySelectorAll(`.field_${code}`);
        const lastOccurrence = fields[fields.length - 1];

        if (exists(lastOccurrence)) {
            lastOccurrence.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'center'
            });
        }
    }
</script>

<div class="record-editation-add-new-field-type">
    <KpValueEditor {size}
                   type={'single-acceptable'}
                   placeholder="{localize(/* @kp-localization editace.ChooseFieldType */ 'editace.ChooseFieldType')}"
                   options={editorOptions}
                   bind:model={typeToAdd}
                   on:model-change={(event) => addEmptyField(event.detail)}/>
</div>

<style lang="less">
    .record-editation-add-new-field-type {
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>