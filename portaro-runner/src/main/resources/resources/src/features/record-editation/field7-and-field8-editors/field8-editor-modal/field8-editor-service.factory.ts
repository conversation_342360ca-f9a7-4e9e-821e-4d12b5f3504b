import type {Field008Definitions} from 'typings/portaro.be.types';
import type {LogService} from 'core/logging/log.service';
import {Field8EditorService} from './field8-editor.service';


export class Field8EditorServiceFactory {
    public static serviceName = 'field8EditorServiceFactory';

    /*@ngInject*/
    constructor(private logService: LogService) {
    }

    createService(definitions: Field008Definitions): Field8EditorService {
        return new Field8EditorService(definitions, this.logService);
    }
}