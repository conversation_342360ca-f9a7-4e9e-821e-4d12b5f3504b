import type {RecordEditation} from 'typings/portaro.be.types';
import type {PromiseBasedRecordEditationDataService, RecordEditationAction} from '../types';
import type {Observable} from 'rxjs';
import {GenericEditationAction} from './generic-editation-action';
import {from} from 'rxjs';

export class PublishAction extends GenericEditationAction implements RecordEditationAction {
    constructor(private backend: PromiseBasedRecordEditationDataService) {
        super();
    }

    public executeAction(editationObject: RecordEditation): Observable<RecordEditation> {
        return from(this.backend.publish(editationObject.id));
    }
}