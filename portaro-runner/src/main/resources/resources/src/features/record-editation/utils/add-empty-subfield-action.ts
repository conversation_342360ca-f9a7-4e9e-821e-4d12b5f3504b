import type {PromiseBasedRecordEditationDataService, RecordEditationAction,} from '../types';
import type {EditableField, FieldType, RecordEditation} from 'typings/portaro.be.types';
import type {Observable} from 'rxjs';
import {from} from 'rxjs';
import {GenericEditationAction} from './generic-editation-action';

export class AddEmptySubfieldAction extends GenericEditationAction implements RecordEditationAction {
    constructor(private backend: PromiseBasedRecordEditationDataService, private parentField: EditableField, private subfieldType: FieldType) {
        super();
    }

    public executeAction(editationObject: RecordEditation): Observable<RecordEditation> {
        return from(this.backend.createSubfield(editationObject.id, this.parentField.id, this.subfieldType.code));
    }
}