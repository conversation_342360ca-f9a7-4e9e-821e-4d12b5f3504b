import type {AuthorityField008Definitions, AuthorityField008StaticCode} from 'typings/portaro.be.types';
import type {AuthorityField8Model} from '../utils/types';
import {findAcceptableValueByCode} from '../utils/utils';
import {IncorrectSectionFormatError, UnknownSectionsCodesError} from './authority-field8-errors';
import {isNullOrUndefined, isUndefined} from 'shared/utils/custom-utils';

const UNDEFINED_POSITION_CHARACTER = '-';

export class AuthorityField8CodeConverter {
    constructor(private definitions: AuthorityField008Definitions) {
    }

    public serializeModel(model: AuthorityField8Model): string {

        if (isNullOrUndefined(model.dateEnteredOnFile)) {
            throw new Error('Authority field008 serialization error, dateEnteredOnFile is undefined');
        }

        let result = '';
        result = result
            .concat(model.dateEnteredOnFile)
            .concat(model.directOrIndirectGeographicSubdivision.code) // 6
            .concat(model.romanizationScheme.code) // 7
            .concat(model.languageOfCatalog.code) // 8
            .concat(model.kindOfRecord.code) // 9
            .concat(model.descriptiveCatalogingRules.code) // 10
            .concat(model.subjectHeadingSystemThesaurus.code) // 11
            .concat(model.typeOfSeries.code) // 12
            .concat(model.numberedOrUnnumberedSeries.code) // 13
            .concat(model.headingUseMainOrAddedEntry.code) // 14
            .concat(model.headingUseSubjectAddedEntry.code) // 15
            .concat(model.headingUseSeriesAddedEntry.code) // 16
            .concat(model.typeOfSubjectSubdivision.code) // 17
            .concat(UNDEFINED_POSITION_CHARACTER.repeat(10)) // 18-27
            .concat(model.typeOfGovernmentAgency.code) // 28
            .concat(model.referenceEvaluation.code) // 29
            .concat(UNDEFINED_POSITION_CHARACTER) // 30
            .concat(model.recordUpdateInProcess.code) // 31
            .concat(model.undifferentiatedPersonalName.code) // 32
            .concat(model.levelOfEstablishment.code) // 33
            .concat(UNDEFINED_POSITION_CHARACTER.repeat(4)) // 34 -37
            .concat(model.modifiedRecord.code) // 38
            .concat(model.catalogingSource.code); // 39

        if (result.length !== 40) {
            throw new Error('authority field008 serialization error, length !== 40');
        }

        return result;
    }

    public parseCode(value: string): AuthorityField8Model | null {

        if (!value) {
            return null;
        }

        const dateEnteredOnFile = value.substring(0, 6); // 0-5
        const directOrIndirectGeographicSubdivision = value.charAt(6); // 6
        const romanizationScheme = value.charAt(7); // 7
        const languageOfCatalog = value.charAt(8); // 8
        const kindOfRecord = value.charAt(9); // 9
        const descriptiveCatalogingRules = value.charAt(10); // 10
        const subjectHeadingSystemThesaurus = value.charAt(11); // 11
        const typeOfSeries = value.charAt(12); // 12
        const numberedOrUnnumberedSeries = value.charAt(13); // 13
        const headingUseMainOrAddedEntry = value.charAt(14); // 14
        const headingUseSubjectAddedEntry = value.charAt(15); // 15
        const headingUseSeriesAddedEntry = value.charAt(16); // 16
        const typeOfSubjectSubdivision = value.charAt(17); // 17
        const typeOfGovernmentAgency = value.charAt(28); // 28
        const referenceEvaluation = value.charAt(29); // 29
        const recordUpdateInProcess = value.charAt(31); // 31
        const undifferentiatedPersonalName = value.charAt(32); // 32
        const levelOfEstablishment = value.charAt(33); // 33
        const modifiedRecord = value.charAt(38); // 38
        const catalogingSource = value.charAt(39); // 39

        if (!Number.isInteger(Number(dateEnteredOnFile))) { // creation date code has non number characters
            throw new IncorrectSectionFormatError('dateEnteredOnFile', dateEnteredOnFile);
        }

        const model: AuthorityField8Model = {
            dateEnteredOnFile,
            directOrIndirectGeographicSubdivision: this.findValueByCodeOrGetDefaultValue(this.definitions.directOrIndirectGeographicSubdivision.codes, directOrIndirectGeographicSubdivision),
            romanizationScheme: this.findValueByCodeOrGetDefaultValue(this.definitions.romanizationScheme.codes, romanizationScheme),
            languageOfCatalog: this.findValueByCodeOrGetDefaultValue(this.definitions.languageOfCatalog.codes, languageOfCatalog),
            kindOfRecord: this.findValueByCodeOrGetDefaultValue(this.definitions.kindOfRecord.codes, kindOfRecord),
            descriptiveCatalogingRules: this.findValueByCodeOrGetDefaultValue(this.definitions.descriptiveCatalogingRules.codes, descriptiveCatalogingRules),
            subjectHeadingSystemThesaurus: this.findValueByCodeOrGetDefaultValue(this.definitions.subjectHeadingSystemThesaurus.codes, subjectHeadingSystemThesaurus),
            typeOfSeries: this.findValueByCodeOrGetDefaultValue(this.definitions.typeOfSeries.codes, typeOfSeries),
            numberedOrUnnumberedSeries: this.findValueByCodeOrGetDefaultValue(this.definitions.numberedOrUnnumberedSeries.codes, numberedOrUnnumberedSeries),
            headingUseMainOrAddedEntry: this.findValueByCodeOrGetDefaultValue(this.definitions.headingUseMainOrAddedEntry.codes, headingUseMainOrAddedEntry),
            headingUseSubjectAddedEntry: this.findValueByCodeOrGetDefaultValue(this.definitions.headingUseSubjectAddedEntry.codes, headingUseSubjectAddedEntry),
            headingUseSeriesAddedEntry: this.findValueByCodeOrGetDefaultValue(this.definitions.headingUseSeriesAddedEntry.codes, headingUseSeriesAddedEntry),
            typeOfSubjectSubdivision: this.findValueByCodeOrGetDefaultValue(this.definitions.typeOfSubjectSubdivision.codes, typeOfSubjectSubdivision),
            typeOfGovernmentAgency: this.findValueByCodeOrGetDefaultValue(this.definitions.typeOfGovernmentAgency.codes, typeOfGovernmentAgency),
            referenceEvaluation: this.findValueByCodeOrGetDefaultValue(this.definitions.referenceEvaluation.codes, referenceEvaluation),
            recordUpdateInProcess: this.findValueByCodeOrGetDefaultValue(this.definitions.recordUpdateInProcess.codes, recordUpdateInProcess),
            undifferentiatedPersonalName: this.findValueByCodeOrGetDefaultValue(this.definitions.undifferentiatedPersonalName.codes, undifferentiatedPersonalName),
            levelOfEstablishment: this.findValueByCodeOrGetDefaultValue(this.definitions.levelOfEstablishment.codes, levelOfEstablishment),
            modifiedRecord: this.findValueByCodeOrGetDefaultValue(this.definitions.modifiedRecord.codes, modifiedRecord),
            catalogingSource: this.findValueByCodeOrGetDefaultValue(this.definitions.catalogingSource.codes, catalogingSource),
        }

        if (Object.values(model).some((val) => isUndefined(val))) { // some values from shared part are undefined (probably language or place of publication)
            const sectionsWithUnknownCodes: (keyof AuthorityField8Model)[] = Object.entries(model)
                .filter(([, val]) => isUndefined(val))
                .map(([key,]) => key as keyof AuthorityField8Model);

            throw new UnknownSectionsCodesError(sectionsWithUnknownCodes)
        }

        return model;
    }

    private findValueByCodeOrGetDefaultValue(values: AuthorityField008StaticCode[], code: string): AuthorityField008StaticCode | undefined {
        let value = findAcceptableValueByCode(values, code);
        if (!value) { // if unknown symbol use default value (first value in list)
            value = values.at(0);
        }
        return value;
    }
}
