import type {Field7Model} from '../utils/types';
import type {LogService} from 'core/logging/log.service';
import type {KpUniversalFormSettings} from 'shared/value-editors/kp-universal-form/types';
import type {ObjectValueEditorField} from 'shared/value-editors/internal/meta-editors/object/types';
import type {SvelteComponentConstructor} from 'core/types';
import {ValueEditorFieldBuilder} from '../utils/value-editor-field-builder';
import {Field7CodeConverter} from './field7-code-converter';
import {getCodesByPosition, optionsComparator} from '../utils/utils';
import {ascendingOrderComparator} from 'shared/utils/array-utils';
import CodeAndDescriptionAcceptableEditorLabel from '../utils/CodeAndDescriptionAcceptableEditorLabel.svelte';
import type {
    Field007Code,
    Field007Definitions,
    Field007DocumentCategory,
    Field007Label
} from 'typings/portaro.be.types';

export class Field7EditorService {
    private readonly codeConverter: Field7CodeConverter;
    private readonly fieldBuilder: ValueEditorFieldBuilder<Field7Model>;

    constructor(definitions: Field007Definitions, logService: LogService) {
        this.codeConverter = new Field7CodeConverter(definitions, logService);
        this.fieldBuilder = new ValueEditorFieldBuilder();
    }

    public serializeModel(model: Field7Model) {
        return this.codeConverter.serializeModel(model);
    }

    public parseCode(value: string): Field7Model | null {
        return this.codeConverter.parseCode(value);
    }

    public initializeFormsFirstSegment(filteredDocumentCategoriesOptions: Field007DocumentCategory[]): KpUniversalFormSettings<Field7Model, ObjectValueEditorField<Field7Model>[]> {
        this.fieldBuilder
            .withLabel('00 Kategorie dokumentu')
            .withFieldName('documentCategory')
            .withSingleAcceptableEditor(CodeAndDescriptionAcceptableEditorLabel as SvelteComponentConstructor<{ option: any }>, {required: true})
            .asInline(false)
            .withAcceptableValues(filteredDocumentCategoriesOptions);

        return {fields: [this.fieldBuilder.build()], id: FORM_ID, text: FORM_TEXT};
    }

    // impure function - mutates formModel
    public setPositionsDefaultValueToModel(formModel: Field7Model, positionOrder: number, codes: Field007Code[]) {
        formModel[`position${positionOrder}`] = codes.find((code) => code.isDefaultValue);
    }

    public initializeFormsSecondSegment(formSettings: KpUniversalFormSettings<Field7Model, ObjectValueEditorField<Field7Model>[]>, labels: Field007Label[], codes: Field007Code[]): KpUniversalFormSettings<Field7Model, ObjectValueEditorField<Field7Model>[]> {

        const otherFields = labels
            .sort(ascendingOrderComparator)
            .map((label) => this.createEditorField(label, getCodesByPosition(codes, label.order)));

        return {fields: [formSettings.fields.at(0), ...otherFields], id: FORM_ID, text: FORM_TEXT};
    }

    private createEditorField(label: Field007Label, options: Field007Code[]): ObjectValueEditorField<Field7Model> {
        return this.fieldBuilder
            .withLabel(label.description)
            .withFieldName(`position${label.order}`)
            .withSingleAcceptableEditor(CodeAndDescriptionAcceptableEditorLabel as SvelteComponentConstructor<{ option: any }>, {required: true})
            .asInline(true)
            .withAcceptableValues(options.sort(optionsComparator))
            .build();
    }
}

const FORM_ID = 'field7-form';
const FORM_TEXT = 'Kódy fyzického popisu';