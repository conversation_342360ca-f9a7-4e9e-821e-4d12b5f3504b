<script lang="ts">
    import type {EditableFieldType, RecordEditation} from 'typings/portaro.be.types';
    import type {RecordEditationManager} from './record-editation.manager';
    import type {Subscription} from 'rxjs';
    import {onDestroy, onMount} from 'svelte';
    import {flip} from 'svelte/animate';
    import {fly} from 'svelte/transition';
    import {popInAnim, popOutAnim} from 'shared/animations/pop-animations';
    import {ofCode} from '../utils';
    import {cleanup, exists} from 'shared/utils/custom-utils';
    import RecordEditationToolbar from './components/RecordEditationToolbar.svelte';
    import KpEditedFieldType from './components/kp-edited-field-type/KpEditedFieldType.svelte';
    import RecordEditationAddNewFieldType from './components/RecordEditationAddNewFieldType.svelte';

    export let editationManager: RecordEditationManager;
    export let viewType: 'modal' | 'page' | 'sutor-reports';

    let initialized = false;
    let editationState: RecordEditation;
    let isRequestPending: boolean;
    let searchQuery = '';
    let editationStateSubscription: Subscription;
    let isRequestPendingStateSubscription: Subscription;

    const flyInAnimParams = {y: 10, duration: 250};

    onMount(async () => {
        await editationManager.initializeEditation();

        editationStateSubscription = editationManager.editationState$.subscribe((currentState) => editationState = currentState);
        isRequestPendingStateSubscription = editationManager.actions.isRequestPendingState$.subscribe((currentIsRequestPending) => isRequestPending = currentIsRequestPending);

        initialized = true;
    });

    onDestroy(() => {
        cleanup(editationStateSubscription, isRequestPendingStateSubscription);
    });

    const handleAddField = (event: CustomEvent<EditableFieldType>) => {
        editationManager.actions.addEmptyField(event.detail);
    }

    const handleSearch = (event: CustomEvent<string>) => {
        searchQuery = event.detail;
    }

    const handleSearchReset = () => {
        searchQuery = '';
    }

    function filterUsedFieldTypes(recordEditation: RecordEditation, query: string): EditableFieldType[] {
        if (!searchQuery) {
            return recordEditation.usedFieldTypes;
        }

        return recordEditation.usedFieldTypes.filter((fieldType) => {
            const fieldRepetitions = recordEditation.fields.filter(ofCode(fieldType.code));

            const anyFoundSubfields = fieldRepetitions.filter((repetition) => {
                return repetition.fields.filter((subfield) => {
                    return isAtLeastOneMatching(query, 'fieldTypeText' in subfield ? subfield.fieldTypeText : null, subfield.code, subfield.text, subfield.typeId);
                }).length > 0;
            });

            return anyFoundSubfields.length > 0 || isAtLeastOneMatching(query, fieldType.text, fieldType.code, fieldType.id);
        });
    }

    function isAtLeastOneMatching(query: string, ...strings: string[]): boolean {
        let anyEqual = false;

        strings.forEach((str) => {
            if (exists(str) && str.toLowerCase().includes(query.toLowerCase())) {
                anyEqual = true;
            }
        });

        return anyEqual;
    }
</script>

{#if initialized}
    <div class="kp-record-editation" in:fly={flyInAnimParams}>
        {#if viewType !== 'sutor-reports'}
            <RecordEditationToolbar {editationState}
                                    {editationManager}
                                    {viewType}
                                    {isRequestPending}
                                    forceSearchResetButton="{searchQuery !== ''}"
                                    on:search={handleSearch}
                                    on:reset={handleSearchReset}/>
        {/if}

        <div class="edited-fields-container"
             in:fly={flyInAnimParams}
             class:container={viewType === 'page'}
             class:in-modal={viewType === 'modal'}>

            {#each filterUsedFieldTypes(editationState, searchQuery) as fieldType (fieldType.id)}
                <div animate:flip={{duration: 250}}
                     in:popInAnim={{key: fieldType}}
                     out:popOutAnim={{key: fieldType}}>

                    <KpEditedFieldType {editationState} {editationManager} {fieldType}/>
                </div>
            {/each}

            {#if editationState.addableFieldTypes.length > 0}
                <RecordEditationAddNewFieldType addableFieldTypes="{editationState.addableFieldTypes}"
                                                on:add-field={handleAddField}/>
            {/if}
        </div>
    </div>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .kp-record-editation {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: @spacing-l;

        .edited-fields-container {
            display: flex;
            flex-direction: column;
            gap: @spacing-xl;

            &.in-modal {
                width: 100%;
                padding: 0 @spacing-ml @spacing-m;
            }
        }
    }
</style>