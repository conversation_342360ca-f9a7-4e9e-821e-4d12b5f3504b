import type {
    Field007DocumentCategory,
    Field008DocumentType,
    LabeledIdentified,
    AuthorityField008StaticCode,
    Field008StaticCode,
    Field007Code,
    Field008Code
} from 'typings/portaro.be.types';

type DynamicPositions<CODE> = Record<`position${number}`, CODE>;

export interface Field7Model extends DynamicPositions<Field007Code> {
    documentCategory: Field007DocumentCategory
}

export interface Field8Model extends Field8SharedPartModel, DynamicPositions<Field008Code> {
    documentType: Field008DocumentType;
}

export interface Field8SharedPartModel {
    dateEnteredOnFile: string;
    publicationStatus: Field008StaticCode;
    date1: string;
    date2: string;
    placeOfPublication: LabeledIdentified<string>;
    language: LabeledIdentified<string>;
    modifiedRecord: Field008StaticCode;
    catalogingSource: Field008StaticCode;
}

export interface AuthorityField8Model {
    dateEnteredOnFile: string;
    directOrIndirectGeographicSubdivision: AuthorityField008StaticCode;
    romanizationScheme: AuthorityField008StaticCode;
    languageOfCatalog: AuthorityField008StaticCode;
    kindOfRecord: AuthorityField008StaticCode;
    descriptiveCatalogingRules: AuthorityField008StaticCode;
    subjectHeadingSystemThesaurus: AuthorityField008StaticCode;
    typeOfSeries: AuthorityField008StaticCode;
    numberedOrUnnumberedSeries: AuthorityField008StaticCode;
    headingUseMainOrAddedEntry: AuthorityField008StaticCode;
    headingUseSubjectAddedEntry: AuthorityField008StaticCode;
    headingUseSeriesAddedEntry: AuthorityField008StaticCode;
    typeOfSubjectSubdivision: AuthorityField008StaticCode;
    typeOfGovernmentAgency: AuthorityField008StaticCode;
    referenceEvaluation: AuthorityField008StaticCode;
    recordUpdateInProcess: AuthorityField008StaticCode;
    undifferentiatedPersonalName: AuthorityField008StaticCode;
    levelOfEstablishment: AuthorityField008StaticCode;
    modifiedRecord: AuthorityField008StaticCode;
    catalogingSource: AuthorityField008StaticCode;
}

export const DEFAULT_FILL_CHARACTER = '|'
export const EMPTY_SYMBOLS = [DEFAULT_FILL_CHARACTER, ' ', '-', '#'];
export const UNDEFINED_POSITION_CHARACTER = ' ';