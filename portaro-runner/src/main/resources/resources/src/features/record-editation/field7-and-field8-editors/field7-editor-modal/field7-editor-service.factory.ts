import type {Field007Definitions} from 'typings/portaro.be.types';
import type {LogService} from 'core/logging/log.service';
import {Field7EditorService} from './field7-editor.service';

export class Field7EditorServiceFactory {
    public static serviceName = 'field7EditorServiceFactory';

    /*@ngInject*/
    constructor(private logService: LogService) {}

    createService(definitions: Field007Definitions): Field7EditorService {
        return new Field7EditorService(definitions, this.logService);
    }
}