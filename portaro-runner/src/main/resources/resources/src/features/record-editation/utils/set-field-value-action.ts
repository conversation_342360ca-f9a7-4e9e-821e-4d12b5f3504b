import type {PromiseBasedRecordEditationDataService, RecordEditationAction} from '../types';
import type {EditableField, FieldValue, RecordEditation} from 'typings/portaro.be.types';
import type {Observable} from 'rxjs';
import {from} from 'rxjs';
import {GenericEditationAction} from './generic-editation-action';
import {createSetFieldValueRequest} from 'shared/utils/data-service-utils';

export class SetFieldValueAction extends GenericEditationAction implements RecordEditationAction {
    constructor(private backend: PromiseBasedRecordEditationDataService, private field: EditableField, private value: FieldValue) {
        super();
    }

    public executeAction(editationObject: RecordEditation): Observable<RecordEditation> {
        return from(this.backend.setFieldValue(editationObject.id, this.field.idPath, createSetFieldValueRequest(this.value)));
    }

    public equals(otherAction: RecordEditationAction) {
        if (SetFieldValueAction.isSetFieldValueAction(otherAction)) {
            return this.value === otherAction.value && this.field.id === otherAction.field.id;
        } else {
            return false;
        }
    }

    /**
     * User-defined type guard (to check if action is of type 'SetFieldValueAction').
     *
     * @param {RecordEditationAction} action - A RecordEditationAction object
     * @return {action is SetFieldValueAction}
     *
     */
    public static isSetFieldValueAction(action: RecordEditationAction): action is SetFieldValueAction {
        return action instanceof SetFieldValueAction;
    }
}