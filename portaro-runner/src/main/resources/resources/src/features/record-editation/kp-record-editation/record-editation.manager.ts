import type {EditationCallbacks, RecordEditationActionFactory} from '../types';
import type {LogService} from 'core/logging/log.service';
import type {FileService} from '../../file/file.service';
import type {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
import type {FieldTypeDataService} from 'src/features/record/field-types/field-type-data.service';
import {BehaviorSubject} from 'rxjs';
import {ofCode} from '../utils';
import {RecordEditationActionsManager} from './record-editation-actions-manager';
import {findFirst, byId} from 'shared/utils/array-utils';
import {exists, isNullOrUndefined} from 'shared/utils/custom-utils';
import {IS_FIELD_INDICATOR} from 'typings/portaro.be.types';
import {cloneDeep} from 'lodash-es';
import type {
    EditableField,
    EditableFieldList,
    EditableFieldType,
    FieldTypeId,
    RecordEditation,
    UUID
} from 'typings/portaro.be.types';

export class RecordEditationManager {

    readonly #actionsManager: RecordEditationActionsManager;
    readonly #editationState$: BehaviorSubject<RecordEditation>;
    readonly #recordId: UUID;

    private allEditableFieldTypes: EditableFieldType[] = [];
    private oldRecordEditation: RecordEditation;

    constructor(private editationObject: RecordEditation,
                private fileService: FileService,
                private SERVER_URL: string,
                private fieldTypeDataService: FieldTypeDataService,
                private editationCallbacks: EditationCallbacks | undefined,
                private logger: LogService,
                private toastMessageService: ToastMessageService,
                readonly focusedFieldTypeId: FieldTypeId | undefined,
                recordEditationActionFactory: RecordEditationActionFactory) {

        this.#actionsManager = new RecordEditationActionsManager(this, recordEditationActionFactory, logger, editationObject);
        this.#editationState$ = new BehaviorSubject(editationObject);
        this.#recordId = editationObject.recordId;
    }

    public async initializeEditation(): Promise<void> {
        await this.refreshEditableFieldTypes();
        const decoratedInitialEditationObject = this.decorateEditationObject(this.editationObject);

        this.oldRecordEditation = cloneDeep(decoratedInitialEditationObject);
    }

    public async save(): Promise<void> {
        await this.actions.saveEditation();
    }

    public async publish(): Promise<void> {
        if (!this.editationObject.draft) {
            throw new Error('Record is not a draft, cannot publish');
        }

        await this.actions.publishEditation();
    }

    public async remove(): Promise<void> {
        await this.actions.removeEditation();
    }

    public decorateEditationObject(recordEditation: RecordEditation): RecordEditation {
        recordEditation.usedFieldTypes = this.allEditableFieldTypes.filter((fieldType) => recordEditation.fields.some(ofCode(fieldType.code)));
        recordEditation.addableFieldTypes = this.filterAddableFieldTypes(this.allEditableFieldTypes, recordEditation, false);

        this.allEditableFieldTypes.forEach((fieldType) => {
            const datafieldsOfThisNumber = recordEditation.fields.filter(ofCode(fieldType.code));
            for (const field of datafieldsOfThisNumber) {
                if (field.fields) {
                    field.addableFieldTypes = this.filterAddableFieldTypes(fieldType.subfieldTypes, field, true);
                }
            }
        });

        this.updateState(recordEditation);
        return recordEditation;
    }

    public filterExistingEditableIndicators(subfields: EditableField[]): EditableField[] {
        return this.filterExistingEditableSubfields(subfields)
            .filter(IS_FIELD_INDICATOR);
    }

    public filterExistingEditableStandardFields(subfields: EditableField[]): EditableField[] {
        return this.filterExistingEditableSubfields(subfields)
            .filter((subfield) => !IS_FIELD_INDICATOR(subfield));
    }

    private filterExistingEditableSubfields(subfields: EditableField[]): EditableField[] {
        const allEditableSubfieldsTypes = this.allEditableFieldTypes.flatMap((editableFieldType) => editableFieldType.subfieldTypes || []);
        const filteredSubfields: EditableField[] = [];

        for (const subfield of subfields) {
            const editableSubfieldType = findFirst(allEditableSubfieldsTypes, byId(subfield.typeId));

            if (isNullOrUndefined(editableSubfieldType)) {
                const errMessage = `Subfield type ${subfield.typeId} exists in record but does not exist in editation (probably not defined in styles)`;

                this.toastMessageService.showWarning(errMessage);
                this.logger.warn(errMessage);
            }

            if (exists(editableSubfieldType)) {
                filteredSubfields.push(subfield);
            }
        }

        return filteredSubfields;
    }

    public getEditableSubfieldTypeById(subfieldTypeId: FieldTypeId): EditableFieldType {
        const allEditableSubfieldsTypes = this.allEditableFieldTypes.flatMap((editableFieldType) => editableFieldType.subfieldTypes || []);
        const editableSubfieldType = findFirst(allEditableSubfieldsTypes, byId(subfieldTypeId));

        if (isNullOrUndefined(editableSubfieldType)) {
            throw new Error(`Subfield type ${subfieldTypeId} exists in record but does not exist in editation (probably not defined in styles)`);
        }

        return editableSubfieldType;
    }

    public handleSubfieldFilesDropped(subfield: EditableField, fileList: FileList): void {
        if (fileList.length > 1) {
            throw new Error('Cannot set more than 1 file to 1 subfield');
        }

        this.fileService.uploadFile(this.editationObject.directoryId, fileList[0])
            .then((file) => {
                const newValue = `${this.SERVER_URL}/media-viewer?rootDirectory=${file.directory.id}#!?file=${file.id}`;
                this.actions.setFieldValue(subfield, newValue);
            });
    }

    public async refreshEditableFieldTypes(): Promise<void> {
        this.allEditableFieldTypes = await this.fieldTypeDataService.queryByRecordEditation(this.editationObject.id);
    }

    public get editationObjectBeforeCurrentEditation(): RecordEditation {
        return this.oldRecordEditation;
    }

    public get actions(): RecordEditationActionsManager {
        return this.#actionsManager;
    }

    public get editationState$(): BehaviorSubject<RecordEditation> {
        return this.#editationState$;
    }

    public get callbacks(): EditationCallbacks {
        return this.editationCallbacks;
    }

    public get recordId(): UUID {
        return this.#recordId;
    }

    private updateState(editationObject: RecordEditation) {
        this.#editationState$.next(editationObject);
    }

    private filterAddableFieldTypes(editableFieldTypes: EditableFieldType[], fieldContainer: EditableFieldList, withRepeatable: boolean): EditableFieldType[] {
        return editableFieldTypes.filter((fieldType) => (fieldType.repeatable && withRepeatable) || !fieldContainer.fields.some(ofCode(fieldType.code)));
    }
}