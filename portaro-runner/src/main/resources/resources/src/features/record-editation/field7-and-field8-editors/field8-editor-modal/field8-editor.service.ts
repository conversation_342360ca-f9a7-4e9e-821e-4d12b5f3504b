import type {Field8Model} from '../utils/types';
import type {LogService} from 'core/logging/log.service';
import type {KpUniversalFormSettings} from 'shared/value-editors/kp-universal-form/types';
import type {ObjectValueEditorField} from 'shared/value-editors/internal/meta-editors/object/types';
import type {SvelteComponentConstructor} from 'core/types';
import {getCodesByPosition, optionsComparator} from '../utils/utils';
import {Field8CodeConverter} from './field8-code-converter';
import {formatDateAsYYMMDD, formatDateAsYYYY} from 'shared/utils/date-utils';
import {ValueEditorFieldBuilder} from '../utils/value-editor-field-builder';
import {ascendingOrderComparator} from 'shared/utils/array-utils';
import {exists} from 'shared/utils/custom-utils';
import CodeAndDescriptionAcceptableEditorLabel from '../utils/CodeAndDescriptionAcceptableEditorLabel.svelte';
import IdAndTextAcceptableEditorLabel from '../utils/IdAndTextAcceptableEditorLabel.svelte';
import type {
    Field008Code,
    Field008Definitions,
    Field008DocumentType,
    Field008Label,
    Field008Position,
    Field008PositionWithDictionaryValue,
    Field008StaticCode,
} from 'typings/portaro.be.types';

export class Field8EditorService {
    private readonly codeConverter: Field8CodeConverter;
    private readonly fieldBuilder: ValueEditorFieldBuilder<Field8Model>;

    constructor(private definitions: Field008Definitions, logService: LogService) {
        this.codeConverter = new Field8CodeConverter(definitions, logService);
        this.fieldBuilder = new ValueEditorFieldBuilder();
    }

    public serializeModel(model: Field8Model): string {
        return this.codeConverter.serializeModel(model);
    }

    public parseCode(value: string): Map<string, Field8Model> {
        return this.codeConverter.parseCode(value);
    }

    public createDefaultSharedValuesModel(documentType: Field008DocumentType): Field8Model {

        const currentDate = new Date()
        const date1 = formatDateAsYYYY(currentDate);
        const dateEnteredOnFile = formatDateAsYYMMDD(currentDate);
        let publicationStatus: Field008StaticCode;
        let date2: string;
        if (documentType.code === 'CR') {
            publicationStatus =
                this.definitions.publicationStatus.codes.find((status) => status.code === 'c');
            date2 = '9999';
        } else {
            publicationStatus =
                this.definitions.publicationStatus.codes.find((status) => status.code === 's');
            date2 = '----';
        }
        const language = this.definitions.language.values.filter((languageCode) => !!languageCode.text).at(0); // filter out null option from db
        const placeOfPublication = this.definitions.placeOfPublication.values.filter((countryCode) => !!countryCode.text).at(0); // filter out null option from db
        const modifiedRecord = this.definitions.modifiedRecord.codes.at(0);
        const catalogingSource = this.definitions.catalogingSource.codes.at(0);
        return {
            documentType,
            dateEnteredOnFile,
            publicationStatus,
            date2,
            date1,
            language,
            placeOfPublication,
            modifiedRecord,
            catalogingSource
        };
    }

    // impure function - mutates formModel
    public setDefaultDatesForPublicationStatusToModel(formModel: Field8Model) {
        if (formModel.publicationStatus.code === 'u') {
            formModel.date2 = 'uuuu';
        }
        if (formModel.publicationStatus.code === 's') {
            formModel.date2 = '----';
        }
        if (formModel.publicationStatus.code === 'b') {
            formModel.date1 = '----';
            formModel.date2 = '----';
        }
        if (formModel.publicationStatus.code === 'c') {
            formModel.date2 = '9999';
        }
    }

    public updateFormsDate2Label(formSettings: KpUniversalFormSettings<Field8Model, ObjectValueEditorField<Field8Model>[]>, publicationStatusCode: string): KpUniversalFormSettings<Field8Model, ObjectValueEditorField<Field8Model>[]> {

        let newLabel: string;
        if (publicationStatusCode === 'e') {
            newLabel = 'Měsíc a den';
        } else {
            newLabel = 'Rok ukončení vydávání';
        }

        const field = formSettings.fields.at(3);
        if (field.label !== newLabel) {
            formSettings.fields[3] = {...field, label: newLabel};
            formSettings = {fields: [...formSettings.fields], id: FORM_ID, text: FORM_TEXT};
        }
        return formSettings;
    }

    public initializeFormsFirstSegment(filteredDocumentTypesOptions: Field008DocumentType[]): KpUniversalFormSettings<Field8Model, ObjectValueEditorField<Field8Model>[]> {
        const fields = [];

        this.fieldBuilder
            .withLabel('Typ dokumentu')
            .withFieldName('documentType')
            .withSingleAcceptableEditor(CodeAndDescriptionAcceptableEditorLabel as SvelteComponentConstructor<{
                option: any
            }>, {required: true})
            .asInline(false)
            .withAcceptableValues(filteredDocumentTypesOptions);
        fields.push(this.fieldBuilder.build());

        fields.push(this.createEditorFieldForStaticPosition('publicationStatus', this.definitions.publicationStatus));

        this.fieldBuilder
            .withLabel('Rok počátku vydávaní')
            .withFieldName('date1')
            .withTextEditor({required: true, maxlength: 4, minlength: 4, pattern: this.createRegexForDateFields()});
        fields.push(this.fieldBuilder.build());

        this.fieldBuilder
            .withLabel('Rok ukončení vydávání')
            .withFieldName('date2')
            .withTextEditor({required: true, maxlength: 4, minlength: 4, pattern: this.createRegexForDateFields()});
        fields.push(this.fieldBuilder.build());

        fields.push(this.createEditorFieldForStaticPositionWithDictionaryValues('placeOfPublication', this.definitions.placeOfPublication));

        fields.push(this.createEditorFieldForStaticPositionWithDictionaryValues('language', this.definitions.language));

        fields.push(this.createEditorFieldForStaticPosition('modifiedRecord', this.definitions.modifiedRecord));

        fields.push(this.createEditorFieldForStaticPosition('catalogingSource', this.definitions.catalogingSource));

        return {fields, id: FORM_ID, text: FORM_TEXT}
    }

    // impure function - mutates formModel
    public setPositionsDefaultValueToModel(formModel: Field8Model, positionOrder: number, codes: Field008Code[]) {
        formModel[`position${positionOrder}`] = codes
            .sort(optionsComparator) // in case of multiple default values (fill character has higher priority)
            .find((code) => code.isDefaultValue);
    }

    public initializeFormsSecondSegment(formSettings: KpUniversalFormSettings<Field8Model, ObjectValueEditorField<Field8Model>[]>, labels: Field008Label[], codes: Field008Code[]): KpUniversalFormSettings<Field8Model, ObjectValueEditorField<Field8Model>[]> {

        const otherFields = labels
            .filter((label) => label.isDefined)
            .sort(ascendingOrderComparator)
            .map((label) => this.createEditorField(label, getCodesByPosition(codes, label.order)));

        return {fields: [...formSettings.fields.slice(0, 8), ...otherFields], id: FORM_ID, text: FORM_TEXT};
    }

    private createEditorField(label: Field008Label, options: Field008Code[]): ObjectValueEditorField<Field8Model> {
        const builder = this.fieldBuilder
            .withLabel(label.description)
            .withFieldName(`position${label.order}`);

        if (!label.isDefined) {
            builder.withHiddenEditor().withLabel(label.description);
        } else {
            builder
                .withSingleAcceptableEditor(CodeAndDescriptionAcceptableEditorLabel as SvelteComponentConstructor<{
                    option: any
                }>, {required: true})
                .asInline(true)
                .withAcceptableValues(options.sort(optionsComparator));
        }

        return builder.build();
    }

    private createEditorFieldForStaticPosition(fieldName: keyof Field8Model, {
        label,
        codes
    }: Field008Position): ObjectValueEditorField<Field8Model> {
        return this.fieldBuilder
            .withLabel(label)
            .withFieldName(fieldName)
            .withSingleAcceptableEditor(CodeAndDescriptionAcceptableEditorLabel as SvelteComponentConstructor<{
                option: any
            }>, {required: true})
            .withCustomIdResolver<Field008StaticCode>(({option}) => option.code)
            .asInline(true)
            .withAcceptableValues(codes)
            .build();
    }

    private createEditorFieldForStaticPositionWithDictionaryValues(fieldName: keyof Field8Model, {
        label,
        values
    }: Field008PositionWithDictionaryValue): ObjectValueEditorField<Field8Model> {
        return this.fieldBuilder
            .withLabel(label)
            .withFieldName(fieldName)
            .withSingleAcceptableEditor(IdAndTextAcceptableEditorLabel as SvelteComponentConstructor<{
                option: any
            }>, {required: true})
            .asInline(true)
            .withAcceptableValues(values.filter((value) => exists(value.text))) // remove null options
            .build();
    }

    private createRegexForDateFields(): RegExp {
        const dateFillCharacterPattern = '([u\\-|# ])';
        // (\<number>) is to match same character from dateFillCharacterPattern group that was already matched previously (it matches n-th matching group)
        // it prevents mixing of fill characters like 2|u#, which is not allowed
        const datePattern0 = '([0-9]{4})'                                    // e.g. 1999
        const datePattern1 = `([0-9]{3}${dateFillCharacterPattern}{1})`      // e.g. 199u
        const datePattern2 = `([0-9]{2}${dateFillCharacterPattern}(\\5){1})` // e.g. 19uu
        const datePattern3 = `([0-9]{1}${dateFillCharacterPattern}(\\8){2})` // e.g. 1uuu
        const datePattern4 = `(${dateFillCharacterPattern}(\\11){3})`         // e.g. uuuu
        return new RegExp(`${datePattern0}|${datePattern1}|${datePattern2}|${datePattern3}|${datePattern4}`) // OR
    }
}

const FORM_ID = 'field8-form';
const FORM_TEXT = 'Údaje pevné délky';