import type {
    AuthorityField008StaticCode,
    Field007Code,
    Field007Definitions,
    Field007Label,
    Field008Code,
    Field008Definitions,
    Field008Label,
    Field008StaticCode,
    LabeledIdentified,
} from 'typings/portaro.be.types';
import {DEFAULT_FILL_CHARACTER} from './types';

export function optionsComparator<T extends Field007Code | Field008Code>(item1: T, item2: T) {

    // compare by default value flag
    if (item1.isDefaultValue && !item2.isDefaultValue) return -1;
    if (!item1.isDefaultValue && item2.isDefaultValue) return 1;

    // fill character has priority over other default values ('#' vs '|') because there can be multiple default values
    if (item1.code === DEFAULT_FILL_CHARACTER) return -1;
    if (item2.code === DEFAULT_FILL_CHARACTER) return 1;

    return item1.code.localeCompare(item2.code);
}

// sort TRUE values first
export function booleanComparator(item1: boolean, item2: boolean): number {
    if (item1 === item2) {
        return 0;
    }
    if (item1) {
        return -1;
    }
    return 1;
}

export function isMergedWithPreviousPositions(label: Field008Label | undefined): boolean {
    if (!label) {
        return false;
    }
    return !label.isDefined && label.description === '';
}

type AnyCode = Field007Code | Field008Code | Field008StaticCode | AuthorityField008StaticCode;

export function findAcceptableValueByCode<T extends AnyCode>(values: T[], code: string): T | undefined {
    return values.find((value) => value.code === code);
}

export function findAcceptableValueById(values: LabeledIdentified<string>[], id: string): LabeledIdentified<string> | undefined {
    return values.find((value) => value.id === id);
}

export function findLabelByOrder<T extends Field008Label | Field007Label>(labels: T[], order: number): T | undefined {
    return labels.find((label) => label.order === order);
}

export function findCodeByCodeSymbolAndPosition<T extends Field007Code | Field008Code>(codes: T[], codeSymbol: string, position: number): T | undefined {
    return codes.find((code) => code.code === codeSymbol && code.position === position);
}

export function getLabelsByDocumentTypeCode(labels: Field008Label[], documentTypeCode: string): Field008Label[] {
    return labels.filter((label) => label.documentTypeCode === documentTypeCode);
}

export function getCodesByDocumentTypeCode(codes: Field008Code[], documentTypeCode: string): Field008Code[] {
    return codes.filter((code) => code.documentTypeCode === documentTypeCode);
}

export function getCodesByPosition<T extends Field007Code | Field008Code>(codes: T[], position: number): T[] {
    return codes.filter((code) => code.position === position);
}

export function getLabelsAndCodesByDocumentTypeCode(definitions: Field008Definitions, documentTypeCode: string): [Field008Label[], Field008Code[]] {
    const labels = getLabelsByDocumentTypeCode(definitions.labels, documentTypeCode);
    const codes = getCodesByDocumentTypeCode(definitions.codes, documentTypeCode);
    return [labels, codes];
}

export function getLabelsByDocumentCategoryCode(labels: Field007Label[], documentCategoryCode: string): Field007Label[] {
    return labels.filter((label) => label.documentCategoryCode === documentCategoryCode);
}

export function getCodesByDocumentCategoryCode(codes: Field007Code[], documentCategoryCode: string): Field007Code[] {
    return codes.filter((code) => code.documentCategoryCode === documentCategoryCode);
}

export function getLabelsAndCodesByDocumentCategoryCode(definitions: Field007Definitions, documentCategoryCode: string): [Field007Label[], Field007Code[]] {
    const labels = getLabelsByDocumentCategoryCode(definitions.labels, documentCategoryCode);
    const codes = getCodesByDocumentCategoryCode(definitions.codes, documentCategoryCode);
    return [labels, codes];
}