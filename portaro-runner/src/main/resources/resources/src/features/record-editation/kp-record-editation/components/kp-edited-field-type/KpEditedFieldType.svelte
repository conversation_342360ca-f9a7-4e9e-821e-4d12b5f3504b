<script lang="ts">
    import type {EditableField, EditableFieldType, FieldValue, RecordEditation} from 'typings/portaro.be.types';
    import type {RecordEditationManager} from '../../record-editation.manager';
    import {exists} from 'shared/utils/custom-utils';
    import {ofCode} from '../../../utils';
    import {flip} from 'svelte/animate';
    import {popInAnim, popOutAnim} from 'shared/animations/pop-animations';
    import FieldEditForm from './FieldEditForm.svelte';
    import FieldTypePanel from './FieldTypePanel.svelte';
    import FieldHeading from './FieldHeading.svelte';
    import AddNewSubfieldRow from './AddNewSubfieldRow.svelte';
    import DeleteRepetitionsDropdown from './DeleteRepetitionsDropdown.svelte';
    import SubfieldRow from './SubfieldRow.svelte';
    import PositionChangeArrowsButton from './PositionChangeArrowsButton.svelte';

    export let editationState: RecordEditation;
    export let editationManager: RecordEditationManager;
    export let fieldType: EditableFieldType;

    $: fieldRepetitions = editationState.fields.filter(ofCode(fieldType.code));
    $: hasAnySubfieldsOrAddableSubfields = fieldRepetitions.flatMap((fieldRepetition) => fieldRepetition.fields).length > 0
        || fieldRepetitions.flatMap((fieldRepetition) => fieldRepetition.addableFieldTypes).length > 0;

    const handleAddFieldRepetitionClick = () => {
        editationManager.actions.addEmptyField(fieldType)
    }

    const handleAddSubfield = (parentField: EditableField, subfieldType: EditableFieldType) => {
        editationManager.actions.addEmptySubfield(parentField, subfieldType);
    }

    const handleSubmitFieldValue = (field: EditableField, value: FieldValue) => {
        editationManager.actions.setFieldValue(field, value);
    }

    const handleDeleteField = (field: EditableField) => {
        editationManager.actions.deleteField(field);
    }

    const handleMoveFieldUp = (field: EditableField) => {
        editationManager.actions.moveFieldUp(field);
    }

    const handleMoveFieldDown = (field: EditableField) => {
        editationManager.actions.moveFieldDown(field);
    }

    function isPreviousSubfieldSameCode(parentField: EditableField, subfield: EditableField): boolean {
        const subfieldIndex = parentField.fields.indexOf(subfield);
        if (subfieldIndex === 0) {
            return false;
        }

        return parentField.fields[subfieldIndex - 1].code === subfield.code;
    }
</script>

<FieldTypePanel {fieldType}
                additionalClasses="kp-edited-field-type"
                on:add-repetition={handleAddFieldRepetitionClick}>

    <!-- Heading row (containing field repetitions and their editors) -->
    <div class="field-heading-row" class:has-subfields={hasAnySubfieldsOrAddableSubfields}>
        <FieldHeading {fieldType}
                      showIdentifiers="{editationState.showFieldIdentifiers}"
                      hintCharactersLimit="{160}"
                      isEntry="{fieldType.entryElement}"/>

        <!-- Editors in header in case they exist (shows editors of all repetitions) -->
        <div class="field-heading-editor">
            {#if exists(fieldType.editor)}
                {#each fieldRepetitions as fieldRepetition, fieldRepetitionIndex(fieldRepetition.id)}
                    <div id="{fieldRepetition.code}#{fieldRepetitionIndex}"
                         class="field-editor-container field_{fieldType.code}"
                         animate:flip={{duration: 250}}
                         in:popInAnim={{key: fieldRepetition}}
                         out:popOutAnim={{key: fieldRepetition}}>

                        <FieldEditForm fieldOrSubfieldType="{fieldType}"
                                       fond="{editationState.fond}"
                                       field="{fieldRepetition}"
                                       on:field-value-submit={(event) => handleSubmitFieldValue(fieldRepetition, event.detail)}/>
                    </div>
                {/each}
            {/if}
        </div>

        <div class="field-menu-container col-md-2">
            <DeleteRepetitionsDropdown {fieldRepetitions}
                                       on:delete-field={(event) => handleDeleteField(event.detail)}/>
        </div>
    </div>

    <!-- Body of field (containing subfields and subfields of field repetitions and their editors) -->
    <div class="datafield-body" class:no-subfields={!hasAnySubfieldsOrAddableSubfields}>
        {#each fieldRepetitions as fieldRepetition, fieldRepetitionIndex(fieldRepetition.id)}
            <div id="{fieldRepetition.code}#{fieldRepetitionIndex}"
                 class="field-repetition field_{fieldType.code}"
                 animate:flip={{duration: 250}}
                 class:bottom-change-position-padding={fieldRepetitionIndex < fieldRepetitions.length - 2}
                 class:top-change-position-padding={fieldRepetitionIndex > 0 && fieldRepetitionIndex < fieldRepetitions.length - 2}
                 in:popInAnim={{key: fieldRepetition}}
                 out:popOutAnim={{key: fieldRepetition}}>

                <!-- indicators -->
                {#if editationState.showIndicators}
                    {#each editationManager.filterExistingEditableIndicators(fieldRepetition.fields) as subfield, subfieldIndex (subfield.id)}
                        {@const subfieldType = editationManager.getEditableSubfieldTypeById(subfield.typeId)}
                        {@const previousSubfieldSameCode = isPreviousSubfieldSameCode(fieldRepetition, subfield)}

                        <div animate:flip={{duration: 250}}>
                            <SubfieldRow parentField="{fieldRepetition}"
                                         {editationManager}
                                         {editationState}
                                         {subfield}
                                         {subfieldType}
                                         {subfieldIndex}
                                         {previousSubfieldSameCode}/>
                        </div>
                    {/each}
                {/if}

                <!-- standard fields (all except indicators) -->
                {#each editationManager.filterExistingEditableStandardFields(fieldRepetition.fields) as subfield, subfieldIndex (subfield.id)}
                    {@const subfieldType = editationManager.getEditableSubfieldTypeById(subfield.typeId)}
                    {@const previousSubfieldSameCode = isPreviousSubfieldSameCode(fieldRepetition, subfield)}

                    <div animate:flip={{duration: 250}}>
                        <SubfieldRow parentField="{fieldRepetition}"
                                     {editationManager}
                                     {editationState}
                                     {subfield}
                                     {subfieldType}
                                     {subfieldIndex}
                                     {previousSubfieldSameCode}/>
                    </div>
                {/each}

                {#if fieldRepetition.addableFieldTypes.length > 0}
                    <AddNewSubfieldRow on:add-subfield={(event) => handleAddSubfield(fieldRepetition, event.detail)}
                                       addableSubfieldTypes="{fieldRepetition.addableFieldTypes}"/>
                {/if}

                {#if fieldRepetitions.length > 1}
                    <div class="change-field-position-container">
                        <PositionChangeArrowsButton allowUp="{fieldRepetitionIndex > 0}"
                                                    allowDown="{fieldRepetitionIndex < fieldRepetitions.length - 1}"
                                                    on:up={() => handleMoveFieldUp(fieldRepetition)}
                                                    on:down={() => handleMoveFieldDown(fieldRepetition)}/>
                    </div>
                {/if}
            </div>
        {/each}
    </div>
</FieldTypePanel>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    @header-bg: #F5F5F5;

    .field-heading-row {
        background-color: @header-bg;
        padding: @spacing-s @spacing-ml;
        border-top-left-radius: @border-radius-default;
        border-top-right-radius: @border-radius-default;
        display: flex;
        align-items: center;
        gap: @spacing-sm;
        width: 100%;

        &.has-subfields {
            border-bottom: 1px solid @themed-border-default;
        }

        .field-heading-editor {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: @spacing-sm;

            .field-editor-container {
                flex: 1;
            }
        }

        .field-menu-container {
            display: flex;
            gap: @spacing-s;
            justify-content: end;
            padding: 0;
        }
    }

    .datafield-body {
        display: flex;
        flex-direction: column;
        width: 100%;

        &.no-subfields {
            display: none;
        }

        .field-repetition {
            position: relative;
            padding: @spacing-m @spacing-ml;
            border-bottom: 1px solid @themed-border-default;
            display: flex;
            flex-direction: column;
            gap: @spacing-sm;

            &:last-child {
                border-bottom: none;
            }

            &.bottom-change-position-padding {
                padding-bottom: @spacing-l;
            }

            &.top-change-position-padding {
                padding-top: @spacing-l;
            }

            .change-field-position-container {
                position: absolute;
                bottom: 0;
                z-index: 1;
                transform: translateY(50%);
                left: @spacing-ml;
            }
        }
    }
</style>