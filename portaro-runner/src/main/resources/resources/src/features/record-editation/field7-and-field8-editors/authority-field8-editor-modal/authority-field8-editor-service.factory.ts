import type {AuthorityField008Definitions} from 'typings/portaro.be.types';
import {AuthorityField8EditorService} from './authority-field8-editor.service';

export class AuthorityField8EditorServiceFactory {
    public static serviceName = 'authorityField8EditorServiceFactory';

    createService(definitions: AuthorityField008Definitions): AuthorityField8EditorService {
        return new AuthorityField8EditorService(definitions);
    }
}