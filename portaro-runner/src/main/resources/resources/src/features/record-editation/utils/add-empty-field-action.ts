import type {PromiseBasedRecordEditationDataService, RecordEditationAction} from '../types';
import type {FieldType, RecordEditation} from 'typings/portaro.be.types';
import type {Observable} from 'rxjs';
import {from} from 'rxjs';
import {GenericEditationAction} from './generic-editation-action';

export class AddEmptyFieldAction extends GenericEditationAction implements RecordEditationAction {
    constructor(private backend: PromiseBasedRecordEditationDataService, private fieldType: FieldType) {
        super();
    }

    public executeAction(editationObject: RecordEditation): Observable<RecordEditation> {
        return from(this.backend.createField(editationObject.id, this.fieldType.code));
    }

    /**
     * User-defined type guard (to check if action is of type 'AddEmptyFieldAction').
     *
     * @param {RecordEditationAction} action - A RecordEditationAction object
     * @return {action is AddEmptyFieldAction}
     *
     */
    public static isAddEmptyFieldAction(action: RecordEditationAction): action is AddEmptyFieldAction {
        return action instanceof AddEmptyFieldAction;
    }

    public getFieldType() {
        return this.fieldType;
    }

}