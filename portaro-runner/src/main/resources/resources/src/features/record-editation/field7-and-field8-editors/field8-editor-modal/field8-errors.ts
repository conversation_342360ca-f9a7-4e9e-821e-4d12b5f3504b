// eslint-disable-next-line max-classes-per-file
import type {Field8SharedPartModel} from '../utils/types';

export class UnknownSectionsCodesError extends Error {
    constructor(public readonly sectionsWithUnknownCodes: (keyof Field8SharedPartModel)[]) {
        super(`Field8 parse error - codes from following sections could not be mapped to definitions: ${sectionsWithUnknownCodes.join(', ')}`);
        this.name = UnknownSectionsCodesError.name;
    }
}

export class IncorrectSectionFormatError extends Error {
    constructor(public readonly section: keyof Field8SharedPartModel, public readonly sectionValue: string) {
        super(`Field8 parse error - value [${sectionValue}] of section [${section}] has incorrect format`);
        this.name = IncorrectSectionFormatError.name;
    }
}

export class MissingDocumentTypesError extends Error {
    constructor() {
        super('Field8 parse error - document type definitions are missing');
        this.name = MissingDocumentTypesError.name;
    }
}