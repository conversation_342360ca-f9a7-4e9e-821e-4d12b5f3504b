<script lang="ts">
    import type {RecordEditation} from 'typings/portaro.be.types';
    import {getLocalization} from 'core/svelte-context/context';
    import {exists} from 'shared/utils/custom-utils';

    export let editationState: RecordEditation;

    const localize = getLocalization();
</script>

{#if exists(editationState)}
    {#if editationState.text}
        <span data-qa="record-editation-record-name">{editationState.text}</span>
    {:else}
        <span>{localize(/* @kp-localization editace.Untitled */ 'editace.Untitled')}</span>
    {/if}

    {#if editationState.draft}
        <span class="text-muted">({localize(/* @kp-localization editace.draft */ 'editace.draft')})</span>
    {/if}
{/if}