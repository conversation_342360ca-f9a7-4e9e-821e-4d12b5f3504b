import type {PromiseBasedRecordEditationDataService} from '../types';
import type {RecordEditationManagerFactory} from '../services/record-editation-manager.factory';
import type {StateProvider} from '@uirouter/angularjs';
import type WalkerService from 'shared/services/walker.service';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export function recordEditationRoutes($stateProvider: StateProvider) {
    let recordEditationPageModule: { default: any; };
    $stateProvider
        .state({
            name: 'record-editations',
            url: '/record-editations/:id',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => recordEditationPageModule.default,

                /*@ngInject*/
                props: async (
                    recordEditationDataService: PromiseBasedRecordEditationDataService,
                    recordEditationManagerFactory: RecordEditationManagerFactory,
                    walker: WalkerService,
                    $stateParams: { id: string } // Injects URL parameters
                ) => {
                    const editationObject = await recordEditationDataService.getEditation($stateParams.id);
                    return {editationObject, recordEditationManagerFactory, walker};
                }
            },
            lazyLoad: async () => {
                recordEditationPageModule = await import(/* webpackChunkName: "recordEditationPage" */ './KpRecordEditationPage.svelte');
                return null;
            }
        });
}