import type {NewRecordEditationParams} from './services/record-editation.data-service';
import type {Observable} from 'rxjs';
import type {EditableField, FieldType, FieldValue, Fond, RecordEditation, SetFieldValueRequest, UUID} from 'typings/portaro.be.types';

/**
 * @ngdoc type
 * @name RecordEditationAction
 * @module portaro.types
 *
 * @description editation action is command describing details of requested change of record, (command design pattern)
 */
export interface RecordEditationAction {
    /**
     * @ngdoc method
     * @name RecordEditationAction#executeAction
     *
     * @param {RecordEditation} editationObject Current record editation
     *
     * @return {Observable<RecordEditation>} Observable/stream made of response from http request
     *
     * @description
     * Action get executed - it gets new data from backend based on the type of the action and its parameters
     */
    executeAction(editationObject: RecordEditation): Observable<RecordEditation>;

    /**
     * @ngdoc method
     * @name RecordEditationAction#equals
     *
     * @param {RecordEditationAction} otherAction Other action to compare to
     *
     * @return {boolean} True if actions are equal
     *
     * @description
     * Method to compare actions
     */
    equals(otherAction: RecordEditationAction): boolean;
}

/**
 * @ngdoc type
 * @name PromiseBasedRecordEditationDataService
 * @module portaro.types
 *
 * @description RecordEditationDataService API based on Promises
 */
export interface PromiseBasedRecordEditationDataService {
    createNewEditation(params: NewRecordEditationParams): Promise<RecordEditation>;

    getEditation(editationId: string): Promise<RecordEditation>;

    createField(editationId: string, code: string): Promise<RecordEditation>;

    createSubfield(editationId: string, parentId: UUID, code: string): Promise<RecordEditation>;

    deleteField(editationId: string, fieldIdPath: string): Promise<RecordEditation>;

    moveField(editationId: string, fieldIdPath: string, isDown: boolean): Promise<RecordEditation>;

    setFieldValue(editationId: string, fieldIdPath: string, setFieldValueRequest: SetFieldValueRequest): Promise<RecordEditation>;

    clearFieldValue(editationId: string, fieldIdPath: string): Promise<RecordEditation>;

    setFond(editationId: string, fondId: number): Promise<RecordEditation>;

    save(editationId: string): Promise<RecordEditation>;

    publish(editationId: string): Promise<RecordEditation>;

    remove(editationId: string): Promise<RecordEditation>;
}

/**
 * @ngdoc type
 * @name RecordEditationActionFactory
 * @module portaro.types
 *
 * @description RecordEditationActionFactory is abstract factory for creating RecordEditationActions
 */
export interface RecordEditationActionFactory {
    createAddEmptyFieldAction(fieldType: FieldType): RecordEditationAction;

    createAddEmptySubFieldAction(parentField: EditableField, subfieldType: FieldType): RecordEditationAction;

    createClearFieldValueAction(field: EditableField): RecordEditationAction;

    createDeleteFieldAction(field: EditableField): RecordEditationAction;

    createMoveFieldDownAction(field: EditableField): RecordEditationAction;

    createMoveFieldUpAction(field: EditableField): RecordEditationAction;

    createPublishAction(): RecordEditationAction;

    createRemoveAction(): RecordEditationAction;

    createSaveAction(): RecordEditationAction;

    createSetFieldValueAction(field: EditableField, value: FieldValue): RecordEditationAction;

    createSetFondAction(fond: Fond): RecordEditationAction;
}

/**
 * @ngdoc type
 * @name EditationCallbacks
 * @module portaro.types
 *
 * @description Editation callbacks
 */
export interface EditationCallbacks {
    publishAndClose: () => Promise<void>;
    saveAndClose: () => Promise<void>;
    deleteDraftAndClose: () => Promise<void>;
    discardAndClose: () => Promise<void>;
    saveAndContinue?: () => Promise<void>;
    modalClose?: () => Promise<void>;
}