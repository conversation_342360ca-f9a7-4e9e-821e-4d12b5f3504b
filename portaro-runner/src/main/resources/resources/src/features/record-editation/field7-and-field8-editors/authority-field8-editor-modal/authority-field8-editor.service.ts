import type {AuthorityField8Model} from '../utils/types';
import type {KpUniversalFormSettings} from 'shared/value-editors/kp-universal-form/types';
import type {ObjectValueEditorField} from 'shared/value-editors/internal/meta-editors/object/types';
import type {SvelteComponentConstructor} from 'core/types';
import {formatDateAsYYMMDD} from 'shared/utils/date-utils';
import {ValueEditorFieldBuilder} from '../utils/value-editor-field-builder';
import {AuthorityField8CodeConverter} from './authority-field8-code-converter';
import CodeAndDescriptionAcceptableEditorLabel from '../utils/CodeAndDescriptionAcceptableEditorLabel.svelte';
import type {
    AuthorityField008Definitions,
    AuthorityField008Position,
    AuthorityField008StaticCode
} from 'typings/portaro.be.types';

export class AuthorityField8EditorService {
    private readonly codeConverter: AuthorityField8CodeConverter;
    private readonly fieldBuilder: ValueEditorFieldBuilder<AuthorityField8Model>;

    constructor(private definitions: AuthorityField008Definitions) {
        this.codeConverter = new AuthorityField8CodeConverter(definitions);
        this.fieldBuilder = new ValueEditorFieldBuilder();
    }

    public serializeModel(model: AuthorityField8Model): string {
        return this.codeConverter.serializeModel(model);
    }

    public parseCode(value: string): AuthorityField8Model | null {
        return this.codeConverter.parseCode(value);
    }

    public createDefaultModel(): AuthorityField8Model {
        return this.codeConverter.parseCode(`${formatDateAsYYMMDD(new Date())}|n|aznnnaabn-----------n-a|a------`);
    }

    public initializeForm(): KpUniversalFormSettings<AuthorityField8Model, ObjectValueEditorField<AuthorityField8Model>[]> {
        const fields = Object.entries(this.definitions)
            .map(([fieldName, {label, codes}]: [keyof AuthorityField8Model, AuthorityField008Position]) => this.createEditorField(fieldName, {label, codes}))

        return {fields, id: 'authority-field8-form', text: 'Údaje pevné délky (autoritní)'}
    }

    private createEditorField(fieldName: keyof AuthorityField8Model, {label, codes}: AuthorityField008Position): ObjectValueEditorField<AuthorityField8Model> {
        return this.fieldBuilder
            .withLabel(label)
            .withFieldName(fieldName)
            .withSingleAcceptableEditor(CodeAndDescriptionAcceptableEditorLabel as SvelteComponentConstructor<{ option: any }>, {required: true})
            .withCustomIdResolver<AuthorityField008StaticCode>(({option}) => option.code)
            .asInline(true)
            .withAcceptableValues(codes)
            .build();
    }
}