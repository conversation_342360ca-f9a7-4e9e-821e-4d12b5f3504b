import type {PromiseBasedRecordEditationDataService, RecordEditationActionFactory} from '../types';
import type {EditableField, FieldType, FieldValue, Fond} from 'typings/portaro.be.types';
import {AddEmptyFieldAction} from './add-empty-field-action';
import {AddEmptySubfieldAction} from './add-empty-subfield-action';
import {ClearFieldValueAction} from './clear-field-value-action';
import {DeleteFieldAction} from './delete-field-action';
import {MoveFieldDownAction} from './move-field-down-action';
import {MoveFieldUpAction} from './move-field-up-action';
import {PublishAction} from './publish-action';
import {RemoveAction} from './remove-action';
import {SaveAction} from './save-action';
import {SetFieldValueAction} from './set-field-value-action';
import {SetFondAction} from './set-fond-action';

export class BasicRecordEditationActionFactory implements RecordEditationActionFactory {

    public static readonly serviceName = 'recordEditationActionFactory';

    /*@ngInject*/
    constructor(private recordEditationDataService: PromiseBasedRecordEditationDataService) {
    }

    public createAddEmptyFieldAction(fieldType: FieldType) {
        return new AddEmptyFieldAction(this.recordEditationDataService, fieldType);
    }

    public createAddEmptySubFieldAction(parentField: EditableField, subfieldType: FieldType) {
        return new AddEmptySubfieldAction(this.recordEditationDataService, parentField, subfieldType);
    }

    public createClearFieldValueAction(field: EditableField) {
        return new ClearFieldValueAction(this.recordEditationDataService, field);
    }

    public createDeleteFieldAction(field: EditableField) {
        return new DeleteFieldAction(this.recordEditationDataService, field);
    }

    public createMoveFieldDownAction(field: EditableField) {
        return new MoveFieldDownAction(this.recordEditationDataService, field);
    }

    public createMoveFieldUpAction(field: EditableField) {
        return new MoveFieldUpAction(this.recordEditationDataService, field);
    }

    public createPublishAction() {
        return new PublishAction(this.recordEditationDataService);
    }

    public createRemoveAction() {
        return new RemoveAction(this.recordEditationDataService);
    }

    public createSaveAction() {
        return new SaveAction(this.recordEditationDataService);
    }

    public createSetFieldValueAction(field: EditableField, value: FieldValue) {
        return new SetFieldValueAction(this.recordEditationDataService, field, value);
    }

    public createSetFondAction(fond: Fond) {
        return new SetFondAction(this.recordEditationDataService, fond);
    }

}
