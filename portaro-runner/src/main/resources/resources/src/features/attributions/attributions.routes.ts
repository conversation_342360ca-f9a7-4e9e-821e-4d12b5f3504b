import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function attributionsRoutes($stateProvider: StateProvider) {
    let attributionModule: { default: any; };
    $stateProvider
        .state({
            name: 'attributions',
            url: '/attributions',
            component:  KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => attributionModule.default,
            },
            lazyLoad: async () => {
                attributionModule = await import(/* webpackChunkName: "attributions" */ './KpAttributions.svelte');
                return null;
            }
        });
}