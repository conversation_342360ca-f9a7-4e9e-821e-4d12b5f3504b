<script lang="ts">
    import {getLocalization} from 'core/svelte-context/context';
    import {attributionLinks} from './attributions';
    import KpPageContainer from 'shared/layouts/containers/KpPageContainer.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';
    import KpBarebonesTable from 'shared/ui-widgets/table/barebones/KpBarebonesTable.svelte';

    const localize = getLocalization();
</script>

<KpPageContainer id="attributions" additionalClasses="kp-attributions-page">
    <KpHeading type="h1">
        {localize(/* @kp-localization attributions.PageTitle */ 'attributions.PageTitle')}
    </KpHeading>

    <KpBarebonesTable rowsTopBordered headerFooterDivided colorAccented responsive>
        <tr slot="header">
            <th>{localize(/* @kp-localization attributions.UsedResource */ 'attributions.UsedResource')}</th>
            <th>{localize(/* @kp-localization attributions.AttributionLink */ 'attributions.AttributionLink')}</th>
        </tr>

        <svelte:fragment slot="body">
            {#each attributionLinks as attribution}
                <tr>
                    <td>{attribution.text}</td>
                    <td>
                        <a href="{attribution.url}">{attribution.url}</a>
                    </td>
                </tr>
            {/each}
        </svelte:fragment>
    </KpBarebonesTable>
</KpPageContainer>