<script lang="ts">
    import {KpLoanCategoriesPresenter} from './kp-loan-categories.presenter';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import KpPageContainer from 'shared/layouts/containers/KpPageContainer.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';
    import KpBarebonesTable from 'shared/ui-widgets/table/barebones/KpBarebonesTable.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    const localize = getLocalization();
    const presenter = getInjector().getByToken<KpLoanCategoriesPresenter>(KpLoanCategoriesPresenter.presenterName);
</script>

<KpPageContainer id="loan-categories" additionalClasses="kp-loan-categories-page">
    <KpHeading type="h1">
        {localize(/* @kp-localization commons.LoanCategories */ 'commons.LoanCategories')}
    </KpHeading>

    <KpBarebonesTable responsive colorAccented rowsTopBordered headerFooterDivided>
        <tr slot="header">
            <th>#</th>
            <th>{localize( /* @kp-localization commons.nazev */ 'commons.nazev')}</th>
            <th>Půjčování</th>
            <th>Rezervace</th>
            <th>Prezenční</th>
            <th>Prolongace</th>
            <th>Upomínky</th>
            <th>K cirkulaci</th>
            <th>K dislokaci</th>
            <th>Titulová</th>
            <th>Okamžité vracení</th>
        </tr>

        <svelte:fragment slot="body">
            {#await presenter.getAll()}
                <KpLoadingBlock/>
            {:then loanCategories}
                {#each loanCategories as lc (lc.id)}
                    <tr>
                        <td>{lc.id}</td>

                        <td>
                            <span>{lc.name}</span>
                            <br/>
                            <span class="text-muted">({lc.text})</span>
                        </td>

                        <td>
                            {#if lc.lendable}
                                <UIcon icon="check" color="var(--success-green)"/>
                            {/if}
                        </td>

                        <td>
                            {#if lc.reservable}
                                <UIcon icon="check" color="var(--success-green)"/>
                            {/if}
                        </td>

                        <td>
                            {#if lc.onSite}
                                <UIcon icon="check" color="var(--success-green)"/>
                            {/if}
                        </td>

                        <td>
                            {#if lc.renewable}
                                <UIcon icon="check" color="var(--success-green)"/>
                            {/if}
                        </td>

                        <td>
                            {#if lc.reminderable}
                                <UIcon icon="check" color="var(--success-green)"/>
                            {/if}
                        </td>

                        <td>
                            {#if lc.circular}
                                <UIcon icon="check" color="var(--success-green)"/>
                            {/if}
                        </td>

                        <td>
                            {#if lc.forDislocation}
                                <UIcon icon="check" color="var(--success-green)"/>
                            {/if}
                        </td>

                        <td>
                            {#if lc.chunkable}
                                <UIcon icon="check" color="var(--success-green)"/>
                            {/if}
                        </td>

                        <td>
                            {#if lc.returningImmediately}
                                <UIcon icon="check" color="var(--success-green)"/>
                            {/if}
                        </td>
                    </tr>
                {/each}
            {/await}
        </svelte:fragment>
    </KpBarebonesTable>
</KpPageContainer>