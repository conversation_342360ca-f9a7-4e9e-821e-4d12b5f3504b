import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function loanCategoriesRoutes($stateProvider: StateProvider) {
    let loanCategoriesModule: { default: any; };
    $stateProvider
        .state({
            name: 'loan-categories',
            url: '/loan-categories',
            component:  KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => loanCategoriesModule.default,
            },
            lazyLoad: async () => {
                loanCategoriesModule = await import(/* webpackChunkName: "loan-categories" */ './KpLoanCategories.svelte');
                return null;
            }
        });
}