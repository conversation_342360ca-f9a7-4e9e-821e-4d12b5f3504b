import {ngAsync} from 'shared/utils/ng-@decorators';
import type {LoanCategory} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';

export class LoanCategoryDataService {
    public static serviceName = 'loanCategoryDataService';

    public static readonly ROUTE = 'loan-categories';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async getAll(): Promise<LoanCategory[]> {
        return this.ajaxService
            .createRequest(`${LoanCategoryDataService.ROUTE}`)
            .get();
    }
}