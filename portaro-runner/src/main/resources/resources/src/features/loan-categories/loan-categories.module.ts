import register from '@kpsys/angularjs-register';
import loanCategoriesRoutes from './loan-categories.routes';
import {KpLoanCategoriesPresenter} from './kp-loan-categories.presenter';
import {LoanCategoryDataService} from './loan-category.data-service';

export default register('portaro.features.loan-categories')
    .config(loanCategoriesRoutes)
    .service(LoanCategoryDataService.serviceName, LoanCategoryDataService)
    .service(KpLoanCategoriesPresenter.presenterName, KpLoanCategoriesPresenter)
    .name();