import type {LoanCategoryDataService} from './loan-category.data-service';
import type {LoanCategory} from 'typings/portaro.be.types';


export class KpLoanCategoriesPresenter {
    public static presenterName = 'kpLoanCategoriesPresenter';

    /*@ngInject*/
    constructor(private loanCategoryDataService: LoanCategoryDataService) {
    }

    public async getAll(): Promise<LoanCategory[]> {
        return this.loanCategoryDataService.getAll();
    }


}