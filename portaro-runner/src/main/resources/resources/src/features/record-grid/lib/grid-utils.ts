import {exists} from 'shared/utils/custom-utils';
import type {
    Columnable,
    FieldTypeDefinitionAttribute,
    FieldTypeDefinition
} from 'src/features/record/field-types/types';


export function findHierarchicalColumns(columnable: Columnable): FieldTypeDefinition[] {
    const expandableColumns = [];
    // find only single expandable field per each topfield
    for (const fieldType of columnable.fieldTypes) {
        const expandableColumn = findHierarchicalColumnRecursive(fieldType);
        if (exists(expandableColumn)) {
            expandableColumns.push(expandableColumn);
        }
    }
    return expandableColumns;
}

function findHierarchicalColumnRecursive(fieldType: FieldTypeDefinition): FieldTypeDefinition | undefined {
    if (fieldType.hierarchicalRecordLink.defaultValue) {
        return fieldType;
    }

    for (const type of fieldType.fieldTypes) {
        const res = findHierarchicalColumnRecursive(type);
        if (exists(res)) {
            return res;
        }
    }
    return undefined;
}

export function resolveGridFieldAttributeValue<T>(flag: FieldTypeDefinitionAttribute<T>, fondId: number): T {
    return flag.fondedValues[fondId] ?? flag.defaultValue;
}