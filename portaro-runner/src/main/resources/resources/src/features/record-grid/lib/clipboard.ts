import type {SimpleGridFieldValue} from './types';
import {exists} from 'shared/utils/custom-utils';
import {DateTime} from 'luxon';
import {fromLocaleStringNumberParser} from 'shared/utils/number-utils';
import type {ClipboardCellData} from 'shared/ui-widgets/grid/clipboard/clipboard';
import {assertIsArray, join} from 'shared/utils/array-utils';
import {isString} from 'shared/utils/string-utils';
import {assertSimpleGridFieldValue} from './types-utils';
import type {Datatype} from 'typings/portaro.be.types';
import type {LocalizeFunction} from 'core/types';
import type {SupportedEditors} from './grid-value-editors';


export interface PlainTextParsingContext {
    datatype: Datatype;
    editorType: SupportedEditors;
    localize: LocalizeFunction;
    locale: string;
}

export function marshallCellValue(value: SimpleGridFieldValue[]): ClipboardCellData {
    const text = join(value.map((field) => field.text), ', ');
    return {value, text};
}

export function unmarshallCellValue(data: ClipboardCellData['value'] | ClipboardCellData['text'], parsingContext: PlainTextParsingContext): SimpleGridFieldValue[] {
    if (isString(data)) {
        return data.split(', ')
            .map((val) => parseStringIntoSimpleGridFieldValue(val, parsingContext))
            .filter(exists);
    } else {
        assertIsArray(data);
        data.forEach(assertSimpleGridFieldValue);
        return data;
    }
}

function parseStringIntoSimpleGridFieldValue(text: string, parsingContext: PlainTextParsingContext): SimpleGridFieldValue | null {
    const {editorType, datatype, localize, locale} = parsingContext;

    if (!exists(text)) {
        return null;
    }

    if (editorType === 'text' || editorType === 'autocomplete') {
        return {
            value: text,
            text,
            recordReference: null,
        };
    }

    if (editorType === 'date') {
        const dateTime = DateTime.fromFormat(text, 'd.L.y');
        if (datatype.name === 'DATE') {
            return {
                value: dateTime.toISODate(),
                text,
                recordReference: null,
            };
        }
        return {
            value: dateTime.toISO(),
            text,
            recordReference: null,
        };
    }

    if (editorType === 'number') {
        if (datatype.name === 'NUMBER_DECIMAL_2') {
            return {
                value: fromLocaleStringNumberParser(locale)(text),
                text,
                recordReference: null,
            };
        }
        return {
            value: Number.parseInt(text, 10),
            text,
            recordReference: null,
        };
    }

    if (editorType === 'boolean') {
        return {
            value: text === localize(/* @kp-localization commons.ANO */ 'commons.ANO'),
            text,
            recordReference: null,
        };
    }

    return null;
}