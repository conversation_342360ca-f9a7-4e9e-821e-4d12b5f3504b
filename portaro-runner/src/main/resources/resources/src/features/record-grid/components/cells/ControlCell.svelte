<script lang="ts">
    import type {Row} from '@tanstack/svelte-table';
    import type {HierarchicalRecordRow} from '../../lib/types';
    import type {RecordStatusTransition, UUID} from 'typings/portaro.be.types';
    import type {Readable} from 'svelte/store';
    import {readable} from 'svelte/store';
    import type {RecordGridDataManager} from 'src/features/record-grid/lib/record-grid-data-manager';
    import {sequence} from 'shared/utils/array-utils';
    import {exists} from 'shared/utils/custom-utils';
    import {getAncestorRowAtDepth, isFirstSubRow, isLastSubRow} from 'shared/ui-widgets/grid/utils';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpButtonStyleAnchor from 'shared/ui-widgets/button/KpButtonStyleAnchor.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import HierarchyHorizontalArrowPart from '../misc/HierarchyHorizontalArrowPart.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import type {RecordStatusTransitionButtonDefinition} from 'src/features/record/types';
    import {getInjector} from 'core/svelte-context/context';
    import RecordStatusService from 'src/features/record/record-status.service';

    export let recordGridDataManager: RecordGridDataManager;
    export let row: Row<HierarchicalRecordRow>;
    export let rowExpansionEnabled = false;
    export let expandButtonHidden = false;
    export let rowsWithSubrowsLoading$: Readable<UUID[]> = readable([]);
    export let forceShowDetailButton = false;

    const recordStatusService = getInjector().getByToken<RecordStatusService>(RecordStatusService.serviceName);

    $: isExpanded = row.getIsExpanded();
    $: canExpand = row.getCanExpand();
    $: depth = row.depth;
    $: subrowsAreLoading = $rowsWithSubrowsLoading$.includes(row.original.id);

    function shouldDisplayVerticalConnector(currentRow: Row<HierarchicalRecordRow>, depthLevel: number): boolean {
        const ancestorRowAtDepth = getAncestorRowAtDepth(currentRow, depthLevel);
        return !isLastSubRow(ancestorRowAtDepth);
    }

    function getStatusTransitionButton(statusTransition: RecordStatusTransition): RecordStatusTransitionButtonDefinition {
        return recordStatusService.getStatusTransitionButton(row.original.status, statusTransition);
    }
</script>

<div class="kp-grid-control-cell control-cell-{row.id}">
    {#each row.original.recordStatusTransitions as statusTransition}
        {@const statusTransitionButton = getStatusTransitionButton(statusTransition)}

        <KpButton additionalClasses="add-new-row-btn"
                  buttonStyle="{statusTransitionButton.buttonStyle}"
                  buttonSize="xs"
                  isDisabled="{!statusTransition.permission.allowed}"
                  tooltipLabel="{statusTransitionButton.title}"
                  on:click={() => recordGridDataManager.changeStatus(row, statusTransition.targetStatus.id)}>

            <IconedContent icon="{statusTransitionButton.icon}">
                {#if statusTransitionButton.textAlwaysShown}
                    {statusTransitionButton.shortText}
                {/if}
            </IconedContent>
        </KpButton>
    {/each}

    {#if row.original.draft}
        <KpButton additionalClasses="add-new-row-btn"
                  buttonStyle="danger-new"
                  buttonSize="xs"
                  title="Smazat záznam"
                  on:click={() => recordGridDataManager.deleteRow(row)}>

            <IconedContent icon="trash">
                Smazat
            </IconedContent>
        </KpButton>
    {:else}
        {#if !exists($$slots.default) || forceShowDetailButton}
            <KpButtonStyleAnchor href="/#!/records/{row.original.id}" title="Zobrazit záznam" buttonSize="xs" buttonStyle="link">
                <IconedContent icon="eye"/>
            </KpButtonStyleAnchor>
        {/if}

        {#if exists($$slots.default)}
            <slot/>
        {/if}

        {#if rowExpansionEnabled}
            {#if depth > 0}
                {#each sequence(1, depth - 1) as depthLevel}
                    <div class="offset-container">
                        {#if shouldDisplayVerticalConnector(row, depthLevel)}
                            <div class="vertical-connector"/>
                        {/if}
                    </div>
                {/each}

                <div class="hierarchy-arrow-container">
                    <div class="horizontal-arrow-part">
                        <HierarchyHorizontalArrowPart/>
                    </div>

                    <div class="vertical-arrow-part"
                         class:last-expanded={isLastSubRow(row)}
                         class:first-expanded={isFirstSubRow(row)}></div>
                </div>
            {/if}

            {#if canExpand && !expandButtonHidden}
                <div class="expand-button-container">
                    <KpButton additionalClasses="toggle-expand-btn"
                              buttonSize="xs"
                              isDisabled="{!canExpand}"
                              on:click={row.getToggleExpandedHandler()}>

                        <div class="arrow-container" class:opened={isExpanded}>
                            <UIcon icon="angle-small-down"/>
                        </div>
                    </KpButton>
                </div>
            {/if}

            {#if subrowsAreLoading}
                <KpLoadingBlock size="xs"/>
            {/if}
        {/if}
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    @expand-offset-width: 32px;
    @expand-btn-width: 32px;
    @hierarchy-arrow-color: #ADACB2;

    @vertical-padding: 3px;
    @horizontal-padding: 5px;

    .kp-grid-control-cell {
        padding: @vertical-padding @horizontal-padding;
        display: flex;
        overflow: hidden;
        height: 100%;
        gap: @spacing-xs;

        --hierarchy-arrow-color: @hierarchy-arrow-color;

        :global(.expand-button-container .toggle-expand-btn) {
            width: calc(@expand-btn-width + 2px); // Button width + borders
        }

        :global(.kp-button),
        :global(.dropdown-toggle),
        :global(.kp-button-style-link) {
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .offset-container {
        position: relative;
        width: @expand-offset-width;

        display: flex;
        align-items: center;
        justify-content: center;

        .vertical-connector {
            display: flex;
            width: 2px;
            border-radius: 1px;
            position: absolute;
            left: 50%;
            background-color: var(--hierarchy-arrow-color);
            transform: translateX(-50%);
            top: calc(@vertical-padding * -1);
            bottom: calc(@vertical-padding * -1);
        }
    }

    .hierarchy-arrow-container {
        flex-shrink: 0;
        position: relative;
        width: @expand-btn-width;

        .vertical-arrow-part {
            display: flex;
            width: 2px;
            border-radius: 1px;
            position: absolute;
            left: 50%;
            background-color: var(--hierarchy-arrow-color);
            transform: translateX(-50%);
            top: calc(@vertical-padding * -1);
            bottom: calc(@vertical-padding * -1);

            &.first-expanded {
                top: 0;
            }

            &.last-expanded {
                bottom: 47.5%;
            }
        }

        .horizontal-arrow-part {
            display: flex;
            position: absolute;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
        }
    }

    .expand-button-container .arrow-container {
        transition: rotate 0.2s ease-in-out;

        &.opened {
            rotate: 180deg;
        }
    }
</style>