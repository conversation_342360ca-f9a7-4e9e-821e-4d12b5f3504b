<script lang="ts">
    import type {FieldTypeId, Fond, UUID} from 'typings/portaro.be.types';
    import type {Readable} from 'svelte/store';
    import type {Table} from '@tanstack/svelte-table';
    import type {HierarchicalRecordRow} from 'src/features/record-grid/lib/types';
    import type {SvelteComponentConstructor} from 'core/types';
    import type {FondFieldTypeDefinitions} from 'src/features/record/field-types/types';
    import KpSearchToolbar from 'src/features/search/kp-search-toolbar/KpSearchToolbar.svelte';
    import KpRecordGridDisplay from 'src/features/record-grid/KpRecordGridDisplay.svelte';

    export let fond: Fond;
    export let fieldTypeDefinitions: FondFieldTypeDefinitions;
    export let relatedFonds: Fond[] | null;
    export let referenceRecordId: UUID | null;
    export let referenceFieldTypeId: FieldTypeId | null;

    export let includeAddNewRow: boolean;
    export let includeToolbar: boolean;
    export let expandedFirstLevel: boolean;
    export let stickyHeader: boolean;
    export let searchType: 'record-name' | 'query';
    export let insertableFonds: Fond[];
    export let controlCellComponent: SvelteComponentConstructor;

    let table$: Readable<Table<HierarchicalRecordRow>> | undefined;

    let showGridProcessing = false;
    function handleGridProcessing(event: CustomEvent<boolean>) {
        showGridProcessing = event.detail;
    }

</script>

<div class="kp-record-grid-container">
    {#if includeToolbar}
        <div class="kp-record-grid-toolbar">
            <KpSearchToolbar {relatedFonds}
                             {fond}
                             {table$}
                             {searchType}
                             showLoadingIndicator="{showGridProcessing}"
                             on:fond-change/>
        </div>
    {/if}

    <KpRecordGridDisplay {referenceRecordId}
                         {referenceFieldTypeId}
                         {fond}
                         {fieldTypeDefinitions}
                         {insertableFonds}
                         {includeAddNewRow}
                         {expandedFirstLevel}
                         {controlCellComponent}
                         {stickyHeader}
                         bind:table$
                         on:grid-processing={handleGridProcessing}
                         on:row-published
                         on:rows-updated
                         on:row-deleted>
    </KpRecordGridDisplay>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .kp-record-grid-container {
        display: flex;
        flex-direction: column;
        gap: @spacing-ml;
    }

    .kp-record-grid-toolbar {
        padding-inline: @spacing-xxl;
        padding-top: @spacing-ml;
    }
</style>