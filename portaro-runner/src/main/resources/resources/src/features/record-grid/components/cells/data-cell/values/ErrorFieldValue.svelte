<script lang="ts">
    import type {ErrorGridFieldValue} from 'src/features/record-grid/lib/types';
    import {tooltip} from 'shared/ui-widgets/tooltip/use.tooltip';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let value: ErrorGridFieldValue;
</script>

<span class="error-icon-container" use:tooltip={{enabled: true, content: value.error.text, role: 'tooltip'}}>
    <UIcon icon="exclamation" color="var(--danger-red)"/>
</span>

<span>{value.text}</span>