import {RecordGridDataService} from './services/record-grid.data-service';
import {RecordGridService} from './services/record-grid.service';
import register from '@kpsys/angularjs-register';
import routes from './page/routes.config';
import coreModule from 'core/core.module';

export default register('portaro.features.record-grid', [coreModule])
    .config(routes)
    .service(RecordGridDataService.serviceName, RecordGridDataService)
    .service(RecordGridService.serviceName, RecordGridService)
    .name()