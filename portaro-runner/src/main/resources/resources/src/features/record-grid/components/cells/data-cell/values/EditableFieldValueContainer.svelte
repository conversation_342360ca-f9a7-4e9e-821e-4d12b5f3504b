<script lang="ts">
    import type {
        GridField,
        HierarchicalRecordRow,
        SimpleGridFieldValue
    } from '../../../../lib/types';
    import type {Row} from '@tanstack/svelte-table';
    import type {ForceSetting} from 'shared/value-editors/kp-value-editor-force-settings/types';
    import type {ModelInputChangeEvent} from 'shared/value-editors/events';
    import type {SvelteComponentConstructor} from 'core/types';
    import type {Rec} from 'typings/portaro.be.types';
    import type {EditorValueToGridFieldValueConversionParams, FieldIdAndFieldValue} from 'src/features/record-grid/components/cells/data-cell/types';
    import type {FieldTypeDefinition} from 'src/features/record/field-types/types';
    import type {Subscription} from 'rxjs';
    import type {CellCoordinates, GridCommand, GridState} from 'shared/ui-widgets/grid/types';
    import {cleanup, exists, isNullOrUndefined} from 'shared/utils/custom-utils';
    import {ValueEditorSize} from 'shared/value-editors/types';
    import {createEventDispatcher, onMount, onDestroy} from 'svelte';
    import {getLocalization} from 'core/svelte-context/context';
    import {getGridContext} from 'shared/ui-widgets/grid/grid.context';
    import {getGridCellContext} from 'shared/ui-widgets/grid/grid-cell/grid-cell.context';
    import {isCellInEditMode} from 'shared/ui-widgets/grid/state/state-query';
    import {hasRecordReference, isErrorGridFieldValue, isOptionGridFieldValue} from 'src/features/record-grid/lib/types-utils';
    import {convertEditorModelToGridFieldValue, convertGridFieldValueToEditorModel} from 'src/features/record-grid/components/cells/data-cell/conversion';
    import {isDateOrNumericDatatype} from 'src/features/record-grid/components/cells/data-cell/utils';
    import {createRecordIdFieldTypeId} from 'shared/utils/record-field-utils';
    import {asSupported} from 'src/features/record-grid/lib/grid-value-editors';
    import {fade} from 'svelte/transition';
    import {isEqual} from 'lodash-es';
    import OptionFieldValue from './OptionFieldValue.svelte';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import ScalarFieldValue from './ScalarFieldValue.svelte';
    import ReferenceFieldValue from './ReferenceFieldValue.svelte';
    import KpValueEditorForceSettings from 'shared/value-editors/kp-value-editor-force-settings/KpValueEditorForceSettings.svelte';
    import RecordAsChipTagModel from 'shared/value-editors/internal/editors/search-or-edit/templates/RecordAsChipTagModel.svelte';
    import ErrorFieldValue from './ErrorFieldValue.svelte';

    export let field: FieldIdAndFieldValue<GridField<SimpleGridFieldValue>>;
    export let fieldType: FieldTypeDefinition;
    export let row: Row<HierarchicalRecordRow>;
    export let editable = false;

    const localize = getLocalization();
    const dispatch = createEventDispatcher<{'cell-edit': SimpleGridFieldValue | null}>();
    const fadeInAnimParams = {duration: 250};
    const savableEditors = ['date', 'editable-record-search', 'single-acceptable', 'inline-record-search'];
    const forcedEditorSettings: ForceSetting[] = [
        {
            type: 'editable-record-search',
            strategy: 'merge',
            options: {
                modelComponent: RecordAsChipTagModel as SvelteComponentConstructor<{value: Rec}>,
                searchParams: {constraintsRecordFieldType: createRecordIdFieldTypeId(row.original.id, fieldType.id)}
            }
        },
        {
            type: 'inline-record-search',
            strategy: 'merge',
            options: {
                searchParams: {constraintsRecordFieldType: createRecordIdFieldTypeId(row.original.id, fieldType.id)}
            }
        }
    ];

    const {stateManager, stateManager: {state$}, command$} = getGridContext();
    const {coordinates$} = getGridCellContext();

    let editValue: SimpleGridFieldValue;
    let isInEditMode = false;
    let valueChanged = false;
    let subscription: Subscription;

    onMount(() => {
        subscription = command$.subscribe(handleCellEditRollbackEvent);
    });

    onDestroy(() => cleanup(subscription));

    $: handleStateUpdate($state$, $coordinates$);

    const handleCellEditRollbackEvent = (command: GridCommand) => {
        if (command.type === 'rollback-cell-change' && command.target.isEqualTo($coordinates$)) {
            rollbackValue();
        }
    };

    const handleModelChange = (event: ModelInputChangeEvent<any>) => {
        if (isNullOrUndefined(event.detail)) {
            saveNewCellValue(null);
            return;
        }

        // assume that values and editor types correlates, possible TODO: type check
        const params = {
            editorType: asSupported(fieldType.editor.defaultValue.type),
            currentFieldValue: editValue,
            editorModelValue: event.detail
        } as EditorValueToGridFieldValueConversionParams;
        const newValue = convertEditorModelToGridFieldValue(params, localize);

        if (isEqual(editValue, newValue)) {
            return;
        }

        editValue = newValue;
        valueChanged = true;

        if (savableEditors.includes(fieldType.editor.defaultValue.type)) {
            stateManager.forceExitEditMode();
        }
    };

    function handleStateUpdate(state: GridState, coordinates: CellCoordinates) {
        if (isCellInEditMode(state, coordinates) && !isInEditMode) {
            handleEditModeEnter();
        }

        if (!isCellInEditMode(state, coordinates) && isInEditMode) {
            handleEditModeExit();
        }
    }

    function handleEditModeEnter() {
        if (!editable) {
            return;
        }
        editValue = field.value;
        isInEditMode = true;
        valueChanged = false;
    }

    function handleEditModeExit() {
        if (valueChanged) {
            field = {
                ...field,
                value: {
                    ...field.value,
                    ...editValue
                }
            };

            saveNewCellValue(field.value);
        }
        isInEditMode = false;
        editValue = null;
    }

    function rollbackValue() {
        if (isInEditMode && valueChanged) {
            editValue = field.value;
            valueChanged = false;
        }
    }

    function saveNewCellValue(newValue: SimpleGridFieldValue) {
        dispatch('cell-edit', newValue);
    }
</script>

{#if (exists(field?.value))}
    <div class="value-container"
         class:editable-value={editable}
         class:end-align={isDateOrNumericDatatype(fieldType.datatype)}>

        {#if hasRecordReference(field.value)}
            <ReferenceFieldValue {field}/>
        {:else if isErrorGridFieldValue(field.value)}
            <ErrorFieldValue value="{field.value}"/>
        {:else if isOptionGridFieldValue(field.value)}
            <OptionFieldValue value="{field.value}"/>
        {:else}
            <ScalarFieldValue value="{field.value}"/>
        {/if}
    </div>
{/if}

{#if exists(fieldType.editor.defaultValue) && isInEditMode}
    <div class="value-editor-container" in:fade={fadeInAnimParams}>
        <KpValueEditorForceSettings forceSettings="{forcedEditorSettings}">
            <KpValueEditor {...fieldType.editor.defaultValue}
                           on:model-change={handleModelChange}
                           model="{convertGridFieldValueToEditorModel(editValue)}"
                           type="{fieldType.editor.defaultValue.type}"
                           size="{ValueEditorSize.XS}"
                           isFocused="{true}"/>
        </KpValueEditorForceSettings>
    </div>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .value-container {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        overflow: hidden;
        position: relative;

        &.end-align {
            justify-content: end;
            text-align: end;
            transition: padding-left 0.2s ease-in-out;
        }
    }

    .value-editor-container {
        display: flex;
        align-items: center;
        top: 0;
        left: 0;
        bottom: 0;
        position: absolute;
        background-color: #F4E1DC;
        outline: 2px solid var(--brand-orange-new);
        box-shadow: 0 0 3px 3px rgba(255, 87, 18, 0.2);
        min-width: 100%;
        z-index: 1;

        :global {
            kp-value-editor {
                width: 100%;

                input[type="text"],
                input[type="number"] {
                    all: unset;
                    width: 100%;
                }

                .input-group {
                    display: inline-flex;
                    width: 100%;
                }

                .input-group-btn {
                    display: contents;
                }
            }
        }
    }
</style>
