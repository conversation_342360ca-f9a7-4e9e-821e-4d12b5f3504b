<script lang="ts">
    import type {FieldTypeId} from 'typings/portaro.be.types';
    import {exists} from 'shared/utils/custom-utils';
    import KpSearchFieldTypeFilter from 'src/features/search/kp-search-field-type-filter/KpSearchFieldTypeFilter.svelte';

    export let text: string;
    export let label: string | undefined;
    export let fieldTypeId: FieldTypeId | null = null;
</script>

{#if exists(fieldTypeId)}
    <KpSearchFieldTypeFilter {fieldTypeId}>
        <span title={label}>{text}</span>
    </KpSearchFieldTypeFilter>
{:else}
    <span title={label}>{text}</span>
{/if}