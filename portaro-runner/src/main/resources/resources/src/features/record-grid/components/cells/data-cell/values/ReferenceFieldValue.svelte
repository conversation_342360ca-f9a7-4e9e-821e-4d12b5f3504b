<script lang="ts">
    import type {<PERSON>rid<PERSON>ield, SimpleGridFieldValue} from '../../../../lib/types';
    import type {FieldIdAndFieldValue} from 'src/features/record-grid/components/cells/data-cell/types';
    import GridFieldValue from 'shared/ui-widgets/grid/GridFieldValue.svelte';

    export let field: FieldIdAndFieldValue<GridField<SimpleGridFieldValue>>;
</script>

{#if field?.value?.text}
    <GridFieldValue field="{field.value}" chipSize="xs" linkButtonPosition="left"/>
{/if}