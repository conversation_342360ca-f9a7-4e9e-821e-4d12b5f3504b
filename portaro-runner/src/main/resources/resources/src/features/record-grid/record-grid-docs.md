
# Record grid docs & notes


## Data model

```
┌─────────────────────────────────────────────────┐                             
│                   GridField                     │◄──────────────┐             
├─────────────────────┬───────────────────────────┤               │             
│                     │                           │               │             
│ GridFieldMetadata   │ GridFieldValue            │               │             
│  - fieldId          │  - text                   │               │             
│                     │  - recordReference?       │               │             
│                     │                           │               │             
└─────────────────────┴───────────────────────────┘               │             
                                  ▲                               │             
                         inherits │                               │             
            ┌─────────────────────┴─────────┐                     │             
            │                               │                     │             
┌───────────┴────────────┐     ┌────────────┴───────────┐         │             
│                        │     │                        │         │             
│ SimpleGridFieldValue   │     │ CompoundGridFieldValue │ contains│             
│                        │     │  - fields[] ───────────┼─────────┘             
│                        │     │                        │                       
└────────────────────────┘     └────────────────────────┘                       
                 ▲                                                              
        inherits │                                                              
           ┌─────┴─────────────────────┬──────────────────────────┐             
           │                           │                          │             
┌──────────┴────────────┐  ┌───────────┴───────────┐  ┌───────────┴───────────┐ 
│                       │  │                       │  │                       │ 
│ OptionGridFieldValue  │  │ ScalarGridFieldValue  │  │ ErrorGridFieldValue   │ 
│  - id                 │  │  - value              │  │  - error              │ 
│                       │  │                       │  │                       │ 
└───────────────────────┘  └───────────────────────┘  └───────────────────────┘ 
```


## Entity diagram (dependencies)

```
                                                                                         ┌────────────────┐                     
                                                                                         │                │                     
                                                                                         │     table$     │                     
                                           ┌────────────────────────────────────────────►│ (SVELTE TABLE) │◄────────┐           
                                           │                                             │                │         │           
                                           │                                             └────────────────┘         │           
                                           │                                                 ▲        ▲             │           
 svelte table                              │                                                 │        │             │           
───────────────────────────────────────────┼─────────────────────────────────────────────────┼────────┼─────────────┼───────────
 generic grid                              │                                                 │        │             │           
                                           │                                                 │        │             │           
                                 ┌─────────┴────────┐                                        │        │             │           
                                 │                  │                                        │        │             │           
                                 │ InputsController │                                        │        │             │           
                                 │                  │                                        │        │             │           
                                 └─┬─────────────┬──┘                                        │        │             │           
                                   │             │                                           │        │             │           
                                   │             │                                           │        │             │           
                                   ▼             ▼                                           │        │             │           
                      ┌─────────────┐      ┌──────────────────┐      ┌───────────────────┐   │        │             │           
                      │             │      │                  │      │                   │   │        │             │           
                      │  command$   │◄─────┤ GridStateManager │◄─────┤ <GenericGridCell> │   │        │             │           
                 ┌───►│ (EVENT BUS) │      │                  │      │                   │   │        │             │           
                 │    │             │      └──────────────────┘      └───────────────────┘   │        │             │           
                 │    └─────────────┘         ▲                                              │        │             │           
                 │           ▲                │                                              │        │             │           
                 │           │                │            ┌─────────────────────────────────┘        │             │           
                 │           │                │            │                                          │             │           
                 │           │                │            │                                          │             │           
                 │           │                │            │                                          │             │           
 generic grid    │           │                │            │                                          │             │           
─────────────────┼───────────┼────────────────┼────────────┼──────────────────────────────────────────┼─────────────┼───────────
 record grid     │           │                │            │                                          │             │           
                 │      ┌────┴────────────────┴┐           │                           ┌──────────────┴────────┐    │           
                 │      │                      ├───────────┘                           │                       │    │           
                 │      │ RecordGridDataManger │◄──────────────────────────────────────┤ <KpRecordGridDisplay> │    │           
                 │      │                      ├─────────────────────────────┐         │                       │    │           
                 │      └──────────────────────┘                             │         └─┬─────────────────────┘    │           
                 │                ▲                                          │           │                          │           
                 │                │                                          │           │                          │           
                 │                │                                          ▼           ▼                          │           
                 │                │                                       ┌────────────────┐                        │           
             ┌───┴────────────────┴──────────┐                            │                │                        │           
             │                               │                            │ flatTableData$ │                        │           
             │ <DataCell>                    │                            │ (GRID DATASET) │                        │           
             │ <ControlCell>                 │                            │                │                        │           
             │ <FooterControlCell>           │                            └────────────────┘                        │           
             │ <EditableFieldValueContainer> ├──────────────────────────────────────────────────────────────────────┘           
             │                               │                                                                                  
             └───────────────────────────────┘                                                                                  
```

## Data flow diagram

```
                              ┌──────────────────┐                      ┌───────────────────┐                                        
        create commands       │                  │  render cell state   │                   │                                        
    ┌─────────────────────────┼ GridStateManager ├─────────────────────►│ <GenericGridCell> │                                        
    │                         │                  │                      │                   │                                        
    │                         └──────────────────┘                      └───────────────────┘                                        
    │                               ▲                                                                                                
    │                               │                                                                                                
    │                               │updates state                                                                                   
    │                               │                                                                                                
    │                               │                                                                                                
    │                         ┌─────┴────────────┐                                                                                   
    │      create commands    │                  │                                                                                   
    │     ┌───────────────────┤ InputsController │                                                                                   
    │     │                   │                  │                                                                                   
    │     │                   └──────────────────┘                                                                                   
    ▼     ▼                             ▲                                                                                            
┌─────────────┐                         │                                                                                            
│             │                         │ notify of data update                                                                      
│  command$   │                         │                                                                                            
│ (EVENT BUS) │                         │                                                                                            
│             │                         │                                                                                            
└──┬──────────┘                         │          ┌────────────────┐ create table and                                               
   │                                    │          │                │ update table data      ┌───────────────────────┐               
   │                                    └──────────┤     table$     │◄───────────────────────┤                       │               
   │                                               │ (SVELTE TABLE) │                        │ <KpRecordGridDisplay> │◄─────────────┐
   │ emmits commands                               │                ├───────────────────────►│                       │              │
   │                                               └────┬───┬───────┘   render table and     └──┬────┬───────────────┘              │
   │                                                    │   │         notify of data update     │    │ ▲ push new data set and      │
   │                                                    │   │                                   │    │ │ create hierarchical rows   │
   │     ┌───────────────────────────────┐              │   │                                   │    │ │                            │
   │     │                               │◄─────────────┘   │                                   │    │ │                            │
   │     │ <DataCell>                    │  render data     │                                   │    │ │                            │
   │     │ <ControlCell>                 │                  │                                   │    │ │                            │
   ├────►│ <FooterControlCell>           │                  │                                   │    │ │                            │
   │     │ <EditableFieldValueContainer> │                  │                                   │    │ │                            │
   │     │                               │                  │                                   │    │ │                            │
   │     └──────┬────────────────────────┘                  │                                   │    │ │                            │
   │            │                                           │                                   │    │ │update data set             │
   │            │ request data update                       │                                   │    ▼ │with new search results     │
   │            │                                           │                                   │   ┌──┴─────────────┐              │
   │            │                                           │                                   │   │                │              │
   │            │                                           │                                   │   │ flatTableData$ │              │
   │            ▼                                           │                                   │   │ (GRID DATASET) │              │
   │      ┌──────────────────────┐ notify of data update    │                                   │   │                │              │
   │      │                      │◄─────────────────────────┘                                   │   └────────────────┘              │
   └─────►│ RecordGridDataManger │                                                              │            ▲                      │
          │                      │◄─────────────────────────────────────────────────────────────┘            │                      │
          └───────┬──────┬───────┘ request data update                                                       │                      │
                  │      │                                                                                   │                      │
                  │      │                                                                update data set    │                      │
                  │      └───────────────────────────────────────────────────────────────────────────────────┘                      │
                  │                                                                                                                 │
                  │                                     ┌───────────────┐                                                           │
                  │ refresh page/reload search          │               │       push current search results                         │
                  └────────────────────────────────────►│ SearchManager ├───────────────────────────────────────────────────────────┘
                                                        │               │                                                            
                                                        └───────────────┘                                                            
```