<script lang="ts">
    import type {Fond} from 'typings/portaro.be.types';
    import type {TabButton, TabPageDefinitionsMap} from 'shared/ui-widgets/tabset/types';
    import {getInjector} from 'core/svelte-context/context';
    import {RecordGridService} from '../services/record-grid.service';
    import {ErpSidebarNavService} from '../../erp/components/erp-sidebar-nav/erp-sidebar-nav.service';
    import {onMount} from 'svelte';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import ErpPageLayout from 'src/features/erp/components/ErpPageLayout.svelte';
    import KpRecordGrid from 'src/features/record-grid/KpRecordGrid.svelte';
    import ErpHeadingBar from 'src/features/erp/components/ErpHeadingBar.svelte';
    import TabbedSubpagesRouter from 'src/features/erp/components/erp-tabbed-subpages/TabbedSubpagesRouter.svelte';
    import ErpTabbedSubpagesContainer from 'src/features/erp/components/erp-tabbed-subpages/ErpTabbedSubpagesContainer.svelte';

    export let fond: Fond;

    const recordGridService = getInjector().getByClass(RecordGridService);
    const erpSidebarNavService = getInjector().getByClass(ErpSidebarNavService);

    let loading = true;
    let fonds: Fond[] = [];

    const tabs: TabPageDefinitionsMap = {};
    const tabButtons: TabButton[] = [];

    onMount(async () => {
        // We want to collapse Sutor sidebar on "record grid" page
        erpSidebarNavService.toggleCollapse(true);
        fonds = await recordGridService.getCompatibleFonds(fond.id);

        fonds.forEach((loadedFonds) => {
            tabs[`tab-${loadedFonds.id}`] = {
                component: KpRecordGrid,
                props: {
                    fondOrFondId: loadedFonds.id,
                    useGlobalSearch: true
                }
            };

            tabButtons.push({
                id: `tab-${loadedFonds.id}`,
                label: pipe(loadedFonds, loc()),
                icon: 'grid-alt',
                tabPageWithoutPadding: true,
                tabPageContainerClass: 'record-grid-tab-page'
            });
        });

        loading = false;
    });
</script>

<ErpPageLayout pageClass="erp-record-grid-page"
               withoutContentPadding
               {loading}>

    <ErpHeadingBar slot="heading" title="{pipe(fond, loc())}"/>

    <ErpTabbedSubpagesContainer {tabButtons}
                                let:activeTab
                                additionalClasses="record-grid-tabbed-subpages">

        <TabbedSubpagesRouter {tabs} {activeTab}/>
    </ErpTabbedSubpagesContainer>
</ErpPageLayout>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro-erp.less";

    :global {
        .erp-record-grid-page {
            position: relative;

            .kp-record-grid-container {
                .flex-grow();
                gap: @spacing-ml;
            }
        }
    }
</style>