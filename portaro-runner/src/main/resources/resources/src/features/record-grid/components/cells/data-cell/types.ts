import type {GridFieldMetadata, GridFieldValue, GridFieldValueWithReference, OptionGridFieldValue, ScalarGridValue, SimpleGridFieldValue} from 'src/features/record-grid/lib/types';
import type {FondedRecordLabeledReference, LabeledIdentified} from 'typings/portaro.be.types';
import type {SupportedEditors} from 'src/features/record-grid/lib/grid-value-editors';

export interface FieldIdAndFieldValue<T extends SimpleGridFieldValue> extends GridFieldMetadata {
    value: T | null;
}

export interface EditorTypeToGridFieldValueTypeMap extends Record<SupportedEditors, GridFieldValue> {
    'text': ScalarGridValue<string>;
    'autocomplete': ScalarGridValue<string>;
    'date': ScalarGridValue<string>;
    'number': ScalarGridValue<number>;
    'boolean': ScalarGridValue<boolean>;
    'single-acceptable': OptionGridFieldValue;
    'editable-record-search': GridFieldValueWithReference<ScalarGridValue<any>>;
    'inline-record-search': GridFieldValueWithReference<ScalarGridValue<any>>;
}

export interface EditorTypeToEditorModelTypeMap extends Record<SupportedEditors, string | number | boolean | LabeledIdentified<any> | GridFieldValueWithReference> {
    'text': string;
    'autocomplete': string;
    'date': string;
    'number': number;
    'boolean': boolean;
    'single-acceptable': LabeledIdentified<any>;
    'editable-record-search': FondedRecordLabeledReference;
    'inline-record-search': FondedRecordLabeledReference;
}

export type EditorValueToGridFieldValueConversionParams = {
    [EDITOR_TYPE in SupportedEditors]: {
        editorType: EDITOR_TYPE;
        currentFieldValue: EditorTypeToGridFieldValueTypeMap[EDITOR_TYPE];
        editorModelValue: EditorTypeToEditorModelTypeMap[EDITOR_TYPE]
    }
}[SupportedEditors];