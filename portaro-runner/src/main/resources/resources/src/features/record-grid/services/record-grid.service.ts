import type {GridDeleteRequest, GridEditRequest, GridFieldValue, RecordRow} from '../lib/types';
import type {FondFieldTypeDefinitions} from 'src/features/record/field-types/types';
import type {RecordGridDataService} from './record-grid.data-service';
import type {RecordEditationDataService} from '../../record-editation/services/record-editation.data-service';
import type {LogService} from 'core/logging/log.service';
import type {UrlStateManagerService} from 'shared/state-manager/url-state-manager.service';
import type {FondsDataService} from '../../fonds/services/fonds.data-service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type RecordDataService from 'src/features/record/record.data-service';
import type {FieldTypeDataService} from 'src/features/record/field-types/field-type-data.service';
import {Subkind} from 'shared/constants/portaro.constants';
import {createSetFieldValueRequest} from 'shared/utils/data-service-utils';
import {stringifyArgument} from 'shared/utils/error-utils';
import {hasRecordReference} from 'src/features/record-grid/lib/types-utils';
import type {ActionResponse, FieldId, FieldTypeId, Fond, Rec, RecordEditation, RecordStatusId, SetFieldValueRequest, UUID} from 'typings/portaro.be.types';
import type {TypeOrIdentifiedStub} from 'typings/portaro.fe.types';

export class RecordGridService {
    public static serviceName = 'recordGridService';

    /*@ngInject*/
    constructor(private recordGridDataService: RecordGridDataService,
                private fieldTypeDataService: FieldTypeDataService,
                private fondsDataService: FondsDataService,
                private recordEditationDataService: RecordEditationDataService,
                private recordDataService: RecordDataService,
                private logService: LogService,
                private urlStateManagerService: UrlStateManagerService<any>,
                private finishedResponseInteractionService: FinishedResponseInteractionService) {
    }

    public async getFond(fondId: number): Promise<Fond> {
        try {
            return await this.fondsDataService.getById(fondId);
        } catch (error) {
            this.logService.error(`Can not load fond by id: ${fondId}`, error);
            this.finishedResponseInteractionService.showFailedResponseInToast(error);
            throw error;
        }
    }

    public async getCompatibleFonds(fondId: number): Promise<Fond[]> {
        return this.fondsDataService.getCompatibleFonds(fondId);
    }

    public async loadInsertableFonds(fond: Fond) {
        try {
            return await this.recordGridDataService.loadInsertableFonds(fond.id);
        } catch (error) {
            this.logService.error(`Error when loading insertable fonds for fond: ${fond?.id}`, error);
            this.finishedResponseInteractionService.showFailedResponseInToast(error);
            throw error;
        }
    }

    public async loadFondFieldTypeDefinitions(fond: Fond): Promise<FondFieldTypeDefinitions> {
        try {
            return await this.fieldTypeDataService.loadFondFieldTypeDefinitions(fond.id);
        } catch (error) {
            this.logService.error(`Error when loading table layout for fond: ${fond?.id}`, error);
            this.finishedResponseInteractionService.showFailedResponseInToast(error);
            throw error;
        }
    }

    public async loadSingleRow(recordId: UUID): Promise<RecordRow> {
        try {
            return await this.recordGridDataService.loadSingleRow(recordId);
        } catch (error) {
            this.logService.error(`Error when loading record: ${recordId}`, error);
            this.finishedResponseInteractionService.showFailedResponseInToast(error);
            throw error;
        }
    }

    public async loadRows(records: TypeOrIdentifiedStub<RecordRow>[]): Promise<RecordRow[]> {
        try {
            return await this.recordGridDataService.loadRows(records.map((record) => record.id));
        } catch (error) {
            this.logService.error(`Error when loading records: ${stringifyArgument(records)}`, error);
            this.finishedResponseInteractionService.showFailedResponseInToast(error);
            throw error;
        }
    }

    public async createNewRow(fond: Fond, initialValues?: Record<FieldTypeId, SetFieldValueRequest>): Promise<RecordRow> {
        let recordEditation: RecordEditation;
        try {
            recordEditation = await this.recordEditationDataService.createNewEditation({
                subkind: fond.ofAuthority ? Subkind.SUBKIND_AUTHORITY : Subkind.SUBKIND_DOCUMENT,
                fond,
                rootFond: fond,
                initialValues
            });
        } catch (error) {
            this.logService.error('Error when creating new record', error);
            this.finishedResponseInteractionService.showFailedResponseInToast(error);
            throw error;
        }

        return this.loadSingleRow(recordEditation.recordId);
    }

    public async editCell(recordId: UUID, fieldId: FieldId, value: GridFieldValue): Promise<ActionResponse> {
        return this.editCells(recordId, [{fieldId, value}]);
    }

    public async editCells(recordId: UUID, cells: {
        fieldId: FieldId,
        value: GridFieldValue
    }[]): Promise<ActionResponse> {
        try {
            const request: GridEditRequest = {
                cellEditRequests: cells.map(({fieldId, value}) => {
                    return {record: recordId, fieldId, setFieldValueRequest: this.createSetFieldValueRequest(value)};
                })
            };
            return await this.recordGridDataService.editCells(request);
        } catch (error) {
            this.logService.error(`Error when editing cells ${stringifyArgument(cells)} of record ${recordId}`, error);
            this.finishedResponseInteractionService.showFailedResponseInToast(error);
            throw error;
        }
    }


    public async updateCells(updates: {row: RecordRow, fieldId: FieldId, value: GridFieldValue}[]): Promise<ActionResponse> {
        try {
            const request: GridEditRequest = {
                cellEditRequests: updates.map(({row, fieldId, value}) => ({record: row.id, fieldId, setFieldValueRequest: this.createSetFieldValueRequest(value)}))
            };
            return await this.recordGridDataService.editCells(request);
        } catch (error) {
            this.logService.error(`Error when making updates ${stringifyArgument(updates)}`, error);
            this.finishedResponseInteractionService.showFailedResponseInToast(error);
            throw error;
        }
    }

    public async deleteCell(recordId: UUID, fieldId: FieldId): Promise<ActionResponse> {
        try {
            const request: GridDeleteRequest = {
                cellDeleteRequests: [{record: recordId, fieldId}]
            };
            return await this.recordGridDataService.deleteCells(request);
        } catch (error) {
            this.logService.error(`Error when deleting cell ${fieldId} of record ${recordId}`, error);
            this.finishedResponseInteractionService.showFailedResponseInToast(error);
            throw error;
        }
    }

    public async deleteCells(updates: {row: RecordRow, fieldId: FieldId}[]): Promise<ActionResponse> {
        try {
            const request: GridDeleteRequest = {
                cellDeleteRequests: updates.map(({row, fieldId}) => ({record: row.id, fieldId}))
            };
            return await this.recordGridDataService.deleteCells(request);
        } catch (error) {
            this.logService.error(`Error when deleting cells ${stringifyArgument(updates)}`, error);
            this.finishedResponseInteractionService.showFailedResponseInToast(error);
            throw error;
        }
    }

    public async duplicateRow(recordRow: RecordRow): Promise<RecordRow> {
        let record: Rec;
        try {
            const copyOptions = {
                targetFond: recordRow.fond,
                copyFiles: true,
                copyMetadata: true,
                publishCopiedRecord: false
            };
            const response = await this.recordDataService.copyRecord(recordRow, copyOptions);
            record = response.savedObject;
        } catch (error) {
            this.logService.error('Error when creating duplicating record', error);
            this.finishedResponseInteractionService.showFailedResponseInToast(error);
            throw error;
        }

        return this.loadSingleRow(record.id);
    }

    public async deleteRow(recordRow: RecordRow) {
        try {
            await this.recordDataService.deleteSubtreeHoldings(recordRow);
        } catch (error) {
            this.logService.error('Error when deleting record', error);
            this.finishedResponseInteractionService.showFailedResponseInToast(error);
            throw error;
        }
    }

    public async changeStatus(recordRow: RecordRow, recordStatusId: RecordStatusId) {
        try {
            await this.recordDataService.changeStatus(recordRow, recordStatusId);
        } catch (error) {
            this.logService.error('Error when changing record status', error);
            this.finishedResponseInteractionService.showFailedResponseInToast(error);
            throw error;
        }
        return this.loadSingleRow(recordRow.id);
    }

    public resetUrlState() {
        this.urlStateManagerService.requestChangeState({});
    }


    // because we set record link instead of actual value
    private createSetFieldValueRequest(value: GridFieldValue): SetFieldValueRequest | null {
        return createSetFieldValueRequest(hasRecordReference(value) ? value.recordReference : value);
    }
}
