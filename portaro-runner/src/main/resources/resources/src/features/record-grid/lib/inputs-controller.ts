import {assertIsElement} from 'shared/utils/types-utils';
import {extractCoordinates, tryToFindGridCell} from 'shared/ui-widgets/grid/utils';
import {assertTrue, exists, isNull, isNullOrUndefined} from 'shared/utils/custom-utils';
import {isInEditMode, isInNormalMode} from 'shared/ui-widgets/grid/state/state-query';
import type {Unsubscriber} from 'svelte/store';
import {get, type Readable} from 'svelte/store';
import {MOUSE_BUTTONS} from 'shared/ui-widgets/grid/constants';
import type {GridStateManager} from 'shared/ui-widgets/grid/state/state-management';
import type {Table} from '@tanstack/svelte-table';
import type {ActionReturn} from 'svelte/action';
import type {Subject} from 'rxjs';
import type { GridCommand } from 'src/shared/ui-widgets/grid/types';


export function bindInputs(element: HTMLElement, inputsController: InputsController): ActionReturn<InputsController> {
    inputsController.connect();
    const abortController = new AbortController();
    element.addEventListener('mouseover', (event) => inputsController.handleMouseOverEvent(event), {signal: abortController.signal});
    element.addEventListener('focus', () => inputsController.handleFocusEvent(), {signal: abortController.signal});
    element.addEventListener('mouseleave', () => inputsController.handleMouseLeaveEvent(), {signal: abortController.signal});
    element.addEventListener('keydown', (event) => inputsController.handleKeyDownEvent(event), {signal: abortController.signal});
    element.addEventListener('mousedown', (event) => inputsController.handleMouseDownEvent(event), {signal: abortController.signal});
    element.addEventListener('mouseup', (event) => inputsController.handleMouseUpEvent(event), {signal: abortController.signal});
    element.addEventListener('dblclick', (event) => inputsController.handleDoubleClickEvent(event), {signal: abortController.signal});
    element.addEventListener('click-outside', () => inputsController.handleClickOutsideEvent(), {signal: abortController.signal});
    element.addEventListener('copy', (event) => inputsController.handleCopyEvent(event), {signal: abortController.signal});
    element.addEventListener('paste', (event) => inputsController.handlePasteEvent(event), {signal: abortController.signal});
    element.addEventListener('cut', (event) => inputsController.handleCutEvent(event), {signal: abortController.signal});

    return {
        destroy() {
            abortController.abort();
            inputsController.disconnect();
        }
    }
}

export class InputsController {
    private isDragging = false;
    private table: Table<any> = null;
    private unsubscriber: Unsubscriber = null;

    constructor(private stateManager: GridStateManager,
                private command$: Subject<GridCommand>,
                private table$: Readable<Table<any>>) {
    }

    public connect() {
        assertTrue(isNull(this.unsubscriber));
        this.unsubscriber = this.table$.subscribe((table) => this.table = table);
    }

    public disconnect() {
        assertTrue(exists(this.unsubscriber));
        this.unsubscriber();
        this.table = null;
        this.unsubscriber = null;
    }

    public handleMouseOverEvent(event: MouseEvent) {
        assertIsElement(event.target)
        const cellElement = tryToFindGridCell(event.target);
        if (isNullOrUndefined(cellElement)) {
            this.stateManager.handleMouseOver(null, this.table, {dragging: this.isDragging, shiftKey: false, ctrlKey: false});
            return;
        }
        this.stateManager.handleMouseOver(extractCoordinates(cellElement), this.table, {
            dragging: this.isDragging,
            shiftKey: false,
            ctrlKey: false
        });
    }

    public handleMouseLeaveEvent() {
        this.stateManager.handleMouseLeave();
    }

    // TODO prevent default pri editmodu i pro jine eventy
    public handleKeyDownEvent(event: KeyboardEvent) {
        if (isInNormalMode(get(this.stateManager.state$)) && ['Backspace', 'Delete', 'Escape', 'Enter', 'ArrowUp', 'ArrowRight', 'ArrowDown', 'ArrowLeft'].includes(event.key)) {
            event.preventDefault(); // prevent default only for keys that grid is listening for
        }
        this.stateManager.handleKeyDown(event.key, this.table, {shiftKey: event.shiftKey, ctrlKey: event.ctrlKey});
    }

    public handleClickOutsideEvent() {
        this.stateManager.handleClickOutside();
    }

    public handleMouseDownEvent(event: MouseEvent) {
        if (event.button !== MOUSE_BUTTONS.LEFT && event.button !== MOUSE_BUTTONS.RIGHT) {
            return;
        }
        this.handleMouseEventPreventDefault(event);
        this.isDragging = true;
        assertIsElement(event.target)
        const cellElement = tryToFindGridCell(event.target);
        if (isNullOrUndefined(cellElement)) {
            this.stateManager.handleMouseDown(null, {shiftKey: event.shiftKey, ctrlKey: event.ctrlKey, dragging: false});
        } else {
            this.stateManager.handleMouseDown(extractCoordinates(cellElement), {
                shiftKey: event.shiftKey,
                ctrlKey: event.ctrlKey,
                dragging: false
            });
        }
    }

    public handleMouseUpEvent(event: MouseEvent) {
        this.handleMouseEventPreventDefault(event);
        this.isDragging = false;
    }

    public handleDoubleClickEvent(event: MouseEvent) {
        this.handleMouseEventPreventDefault(event);
        assertIsElement(event.target)
        const cellElement = tryToFindGridCell(event.target);
        if (exists(cellElement)) {
            this.stateManager.handleDoubleClick(extractCoordinates(cellElement));
        }
    }

    public handleFocusEvent() {
        this.stateManager.handleGridFocus(this.table);
    }

    public handleMouseEventPreventDefault(event: MouseEvent | ClipboardEvent) {
        const state = get(this.stateManager.state$);
        if (isInNormalMode(state)) {
            event.preventDefault();
            return;
        }

        if (isInEditMode(state)) {
            assertIsElement(event.target);
            const cellElement = tryToFindGridCell(event.target);
            if (isNullOrUndefined(cellElement)) {
                event.preventDefault();
                return;
            }
            const coordinates = extractCoordinates(cellElement);
            if (!coordinates.isEqualTo(state.edit.editedCell)) {
                event.preventDefault();
                return;
            }
        }
    }

    public hasCustomBehavior(event: Event): boolean {
        const state = get(this.stateManager.state$);
        if (isInNormalMode(state)) {
            return true;
        }

        if (isInEditMode(state)) {
            assertIsElement(event.target);
            const cellElement = tryToFindGridCell(event.target);
            if (isNullOrUndefined(cellElement)) {
                return true;
            }
            const coordinates = extractCoordinates(cellElement);
            if (!coordinates.isEqualTo(state.edit.editedCell)) {
                return true;
            }
        }
        return false;
    }

    public handleCopyEvent(event: Event) {
        if (!this.hasCustomBehavior(event)) {
            return;
        }
        event.preventDefault();
        assertIsElement(event.target);
        const cellElement = tryToFindGridCell(event.target);
        if (isNullOrUndefined(cellElement)) {
            return;
        }
        this.command$.next({type: 'copy', target: extractCoordinates(cellElement)});
    }

    public handlePasteEvent(event: Event) {
        if (!this.hasCustomBehavior(event)) {
            return;
        }
        event.preventDefault();
        assertIsElement(event.target);
        const cellElement = tryToFindGridCell(event.target);
        if (isNullOrUndefined(cellElement)) {
            return;
        }
        this.command$.next({type: 'paste', target: extractCoordinates(cellElement)});
    }

    public handleCutEvent(event: Event) {
        if (!this.hasCustomBehavior(event)) {
            return;
        }
        event.preventDefault();
        assertIsElement(event.target);
        const cellElement = tryToFindGridCell(event.target);
        if (isNullOrUndefined(cellElement)) {
            return;
        }
        this.command$.next({type: 'cut', target: extractCoordinates(cellElement)});
    }
}