import type {StateProvider, Transition} from '@uirouter/angularjs';
import type {RecordGridService} from '../services/record-grid.service';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';
import {isNullOrUndefined} from 'shared/utils/custom-utils';

/*@ngInject*/
export default function recordGridRoutes($stateProvider: StateProvider) {
    let componentModule: {default: any;};

    $stateProvider
        .state({
            name: 'record-grid',
            url: '/record-grid/fond/:fond/view',
            component: KpSvelteComponentWrapperComponent.componentName,
            params: {
                view: {squash: true, value: null}
            },
            resolve: {
                component: () => componentModule.default,
                /*@ngInject*/
                props: async (recordGridService: RecordGridService, $transition$: Transition) => {
                    const fondId = parseInt($transition$.params().fond);
                    const fond = await recordGridService.getFond(fondId);
                    return {fond};
                }
            },
            lazyLoad: async () => {
                if (window.portaroConfiguration.isSutorSutinLayout) {
                    componentModule = await import(/* webpackChunkName: "erp-record-grid" */ './ErpRecordGridPage.svelte');
                } else {
                    componentModule = await import(/* webpackChunkName: "kp-record-grid" */ './KpRecordGridPage.svelte');
                }

                return null;
            }
        });
}

function parseInt(value: string | null): number | null {
    if (isNullOrUndefined(value)) {
        return null;
    }

    return Number.parseInt(value, 10);
}
