import type {SimpleGridFieldValue} from 'src/features/record-grid/lib/types';
import {exists, isNullOrUndefined} from 'shared/utils/custom-utils';
import {hasRecordReference, isErrorGridFieldValue, isOptionGridFieldValue} from 'src/features/record-grid/lib/types-utils';
import type {LocalizeFunction} from 'core/types';
import {DateTime} from 'luxon';
import {datetimeEquals} from 'shared/utils/date-utils';
import {Kind} from 'shared/constants/portaro.constants';
import type {EditorValueToGridFieldValueConversionParams} from 'src/features/record-grid/components/cells/data-cell/types';

export function convertGridFieldValueToEditorModel(fieldValue: SimpleGridFieldValue): any {
    if (isNullOrUndefined(fieldValue)) {
        return null;
    }

    if (isErrorGridFieldValue(fieldValue)) {
        return null;
    }

    if (hasRecordReference(fieldValue)) {
        return fieldValue.recordReference;
    }

    if (isOptionGridFieldValue(fieldValue)) {
        return fieldValue;
    }

    return fieldValue.value;
}

export function convertEditorModelToGridFieldValue({
                                                       editorType,
                                                       currentFieldValue,
                                                       editorModelValue
                                                   }: EditorValueToGridFieldValueConversionParams, localize: LocalizeFunction): SimpleGridFieldValue {
    if (!exists(editorModelValue)) {
        return null;
    }

    const recordReference = currentFieldValue?.recordReference ?? null;
    if (editorType === 'text' || editorType === 'autocomplete') {
        return {
            value: editorModelValue,
            text: editorModelValue,
            recordReference,
        };
    }

    if (editorType === 'date') {
        const updatedDateTime = DateTime.fromISO(editorModelValue);

        // custom equality check because different ISO string can represent equal datetime
        if (isNullOrUndefined(currentFieldValue) || !datetimeEquals(updatedDateTime, currentFieldValue.value)) {
            return {
                value: editorModelValue,
                text: updatedDateTime.toFormat('d.L.y'),
                recordReference,
            };
        }

        return currentFieldValue;
    }

    if (editorType === 'number') {
        return {
            value: editorModelValue,
            text: editorModelValue.toString(10),
            recordReference,
        };
    }

    if (editorType === 'boolean') {
        return {
            value: editorModelValue,
            text: editorModelValue
                ? localize(/* @kp-localization commons.ANO */ 'commons.ANO')
                : localize(/* @kp-localization commons.NE */ 'commons.NE'),
            recordReference,
        };
    }

    if (editorType === 'single-acceptable') {
        return {
            text: editorModelValue.text,
            id: editorModelValue.id,
            recordReference,
        }
    }

    if (editorType === 'editable-record-search' || editorType === 'inline-record-search') {
        return {
            text: editorModelValue.text,
            value: editorModelValue.text,
            recordReference: {
                id: editorModelValue.id,
                kind: Kind.KIND_RECORD,
                fond: editorModelValue.fond,
                text: editorModelValue.text
            }
        }
    }

    throw new Error(`Unsupported editor: ${editorType}`);
}