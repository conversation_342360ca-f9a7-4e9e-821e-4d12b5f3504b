import type {
    CompoundGridFieldValue,
    ErrorGridFieldValue,
    Fieldable,
    GridFieldValue,
    GridFieldValueWithReference,
    OptionGridFieldValue,
    RecordRow,
    ScalarGridValue,
    SimpleGridFieldValue
} from './types';
import {isLabeled, isLabeledIdentified, isLabeledValue, isObject} from 'shared/utils/types-utils';
import {exists, isNull} from 'shared/utils/custom-utils';
import type {Labeled} from 'typings/portaro.be.types';


export function isErrorGridFieldValue(value: unknown): value is ErrorGridFieldValue {
    return isLabeled(value) && 'error' in value && exists(value.error);
}

export function isOptionGridFieldValue(value: unknown): value is OptionGridFieldValue {
    return isLabeled(value) && isLabeledIdentified(value);
}

export function isScalarGridValue(value: unknown): value is ScalarGridValue {
    return isLabeled(value) && isLabeledValue(value);
}

export function isSimpleGridFieldValue(value: unknown): value is SimpleGridFieldValue {
    return isNull(value) || isScalarGridValue(value) || isOptionGridFieldValue(value) || isErrorGridFieldValue(value);
}

export function hasRecordReference<GRID_FIELD_SUBTYPE extends GridFieldValue = GridFieldValue>(value: GRID_FIELD_SUBTYPE): value is GridFieldValueWithReference<GRID_FIELD_SUBTYPE> {
    return exists(value) && 'recordReference' in value && exists(value.recordReference);
}

export function assertHasRecordReference<T extends GridFieldValue = GridFieldValue>(value: T): asserts value is GridFieldValueWithReference<T> {
    if (!hasRecordReference(value)) {
        throw new TypeError(`${JSON.stringify(value)} is not of type GridFieldValueWithReference`);
    }
}

export function assertIsScalarGridValue(value: Labeled): asserts value is ScalarGridValue {
    if (!isScalarGridValue(value)) {
        throw new TypeError(`${JSON.stringify(value)} is not of type ScalarGridValue`);
    }
}

export function assertSimpleGridFieldValue(value: unknown): asserts value is SimpleGridFieldValue {
    if (!isSimpleGridFieldValue(value)) {
        throw new TypeError(`${JSON.stringify(value)} is not of type SimpleGridFieldValue`);
    }
}

export function asSimpleGridFieldValue(value: unknown): SimpleGridFieldValue {
    assertSimpleGridFieldValue(value);
    return value;
}

export function isFieldable(value: unknown): value is Fieldable {
    return isObject(value) && 'fields' in value && exists(value.fields) && isObject(value.fields);
}

export function assertFieldable(value: unknown): asserts value is Fieldable {
    if (!isFieldable(value)) {
        throw new TypeError(`${JSON.stringify(value)} is not of type Fieldable`);
    }
}

export function isCompoundGridFieldValue(value: GridFieldValue): value is CompoundGridFieldValue {
    return isFieldable(value);
}

export function assertCompoundGridFieldValue(value: GridFieldValue): asserts value is CompoundGridFieldValue {
    if (!isCompoundGridFieldValue(value)) {
        throw new TypeError(`${JSON.stringify(value)} is not of type CompoundGridFieldValue`);
    }
}

export function isRecordRow(item: any): item is RecordRow {
    return 'id' in item && 'fields' in item;
}

export function asRecordRow(item: any): RecordRow {
    if (!isRecordRow(item)) {
        throw new TypeError(`${JSON.stringify(item)} is not a RecordRow`);
    }

    return item;
}