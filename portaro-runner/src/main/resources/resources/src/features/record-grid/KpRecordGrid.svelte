<script lang="ts">
    import type {FieldTypeId, Fond, RecordSearchParams, UUID} from 'typings/portaro.be.types';
    import type {FondFieldTypeDefinitions} from 'src/features/record/field-types/types';
    import type {SvelteComponentConstructor} from 'core/types';
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import type {SearchManager} from 'src/features/search/search-manager/search-manager';
    import {getInjector} from 'core/svelte-context/context';
    import {exists, isFunction, isNullOrUndefined} from 'shared/utils/custom-utils';
    import {RecordGridService} from './services/record-grid.service';
    import {Kind, SearchType} from 'shared/constants/portaro.constants';
    import {onMount} from 'svelte';
    import {findHierarchicalColumns} from 'src/features/record-grid/lib/grid-utils';
    import {isNotEmpty} from 'shared/utils/array-utils';
    import {mapFieldTypeIdToDynamicFieldSearchParamName} from 'shared/utils/record-field-utils';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import KpSearchContext from '../search/kp-search-context/KpSearchContext.svelte';
    import KpRecordGridInner from 'src/features/record-grid/KpRecordGridInner.svelte';
    import ControlCell from 'src/features/record-grid/components/cells/ControlCell.svelte';

    export let fondOrFondId: Fond | number;
    export let referenceRecordId: UUID | null = null;
    export let referenceFieldTypeId: FieldTypeId | null = null;

    export let relatedFonds: Fond[] | null = null;
    export let includeAddNewRow = true;
    export let includeToolbar = true;
    export let useGlobalSearch = false;
    export let expandedFirstLevel = false;
    export let stickyHeader = true;
    export let controlCellComponent: SvelteComponentConstructor = ControlCell;
    export let searchType: 'record-name' | 'query' = 'query';

    const injector = getInjector();
    const recordGridService = injector.getByClass(RecordGridService);

    let selectedFond: Fond;
    let getSearchManagerInternal: (() => SearchManager<RecordRow>) | null = null;

    onMount(async () => {
        selectedFond = await getFond(fondOrFondId);
    });

    async function loadStaticData(fond: Fond) {
        return Promise.all([
            Promise.resolve(fond),
            recordGridService.loadFondFieldTypeDefinitions(fond),
            recordGridService.loadInsertableFonds(fond)
        ]);
    }

    async function getFond(fondOrId: Fond | number): Promise<Fond> {
        if (typeof fondOrId === 'number') {
            return recordGridService.getFond(fondOrId);
        }

        return fondOrId;
    }

    function createStaticParams(rootFond: Fond, fieldTypeDefinitions: FondFieldTypeDefinitions): RecordSearchParams {
        return {
            kind: [Kind.KIND_RECORD],
            type: SearchType.TYPE_TABLE,
            rootFond: [rootFond],
            pageSize: 50,
            ...resolveDynamicFieldSearchParam(fieldTypeDefinitions)
        };
    }

    function resolveDynamicFieldSearchParam(fieldTypeDefinitions: FondFieldTypeDefinitions) {
        // REFERENCE MODE (used in related records tables in record detail)
        if (exists(referenceRecordId) && exists(referenceFieldTypeId)) {
            return {[mapFieldTypeIdToDynamicFieldSearchParamName(referenceFieldTypeId, true)]: referenceRecordId};
        }

        // WHOLE DATA SET MODE (used in record grid for fond pages)
        const hierarchicalColumns = findHierarchicalColumns(fieldTypeDefinitions).map((fieldType) => fieldType.id);
        if (isNullOrUndefined(referenceRecordId) && isNotEmpty(hierarchicalColumns)) {
            const chosenHierarchicalColumn = hierarchicalColumns.at(0);
            return {[mapFieldTypeIdToDynamicFieldSearchParamName(chosenHierarchicalColumn, true)]: referenceRecordId};
        }

        return {};
    }

    const handleFondChange = (event: CustomEvent<Fond>) => {
        if (event.detail.id === selectedFond.id) {
            return;
        }

        recordGridService.resetUrlState();
        selectedFond = event.detail;
    };

    export function getSearchManager(): SearchManager<RecordRow> | null {
        if(!isFunction(getSearchManagerInternal)) {
            return null;
        }

        return getSearchManagerInternal();
    }
</script>

{#if !exists(selectedFond)}
    <KpLoadingBlock fillAvailableSpace/>
{/if}

{#if exists(selectedFond)}
    {#key selectedFond}
        {#await loadStaticData(selectedFond)}
            <KpLoadingBlock fillAvailableSpace/>
        {:then [fond, fieldTypeDefinitions, insertableFonds]}
            <KpSearchContext staticParams="{createStaticParams(fond, fieldTypeDefinitions)}" localSearch="{!useGlobalSearch}" bind:getSearchManager={getSearchManagerInternal}>
                <KpRecordGridInner {referenceRecordId}
                                   {referenceFieldTypeId}
                                   {relatedFonds}
                                   {includeAddNewRow}
                                   {includeToolbar}
                                   {searchType}
                                   {expandedFirstLevel}
                                   {controlCellComponent}
                                   {stickyHeader}
                                   {fond}
                                   {fieldTypeDefinitions}
                                   {insertableFonds}
                                   on:row-published
                                   on:rows-updated
                                   on:row-deleted
                                   on:fond-change="{handleFondChange}"/>
            </KpSearchContext>
        {:catch error}
            <div class="text-danger">{JSON.stringify(error, null, 2)}</div>
        {/await}
    {/key}
{/if}