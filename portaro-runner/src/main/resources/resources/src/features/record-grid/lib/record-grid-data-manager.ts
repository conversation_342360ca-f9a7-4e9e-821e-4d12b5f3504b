import type {GridStateManager} from 'shared/ui-widgets/grid/state/state-management';
import type {Row, Table} from '@tanstack/svelte-table';
import type {Logger} from 'src/core/logging/types';
import type {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
import type {GridCommand} from 'shared/ui-widgets/grid/types';
import {CellCoordinates} from 'shared/ui-widgets/grid/types';
import type {ClipboardCellData} from 'shared/ui-widgets/grid/clipboard/clipboard';
import {createClipBoardItem, resolveClipboardData} from 'shared/ui-widgets/grid/clipboard/clipboard';
import {getCellValueByCoordinates, getColumnByIndex, getRowByIndex} from 'shared/ui-widgets/grid/utils';
import {assertUnreachable, cleanup, exists, getId} from 'shared/utils/custom-utils';
import type {<PERSON>rid<PERSON><PERSON>, GridFieldMetadata, HierarchicalRecordRow, KpRecordGridDisplayEvents, RecordRow, SimpleGridFieldValue} from 'src/features/record-grid/lib/types';
import type {Observable, Subscription} from 'rxjs';
import {BehaviorSubject, combineLatest, EMPTY, firstValueFrom, from, of, Subject} from 'rxjs';
import {catchError, concatMap, filter, map, take, tap} from 'rxjs/operators';
import {byIdOf, distinct, distinctBy, isNotEmpty, removeAll, replaceAll, uuidComparator, zip} from 'shared/utils/array-utils';
import type {RecordGridService} from 'src/features/record-grid/services/record-grid.service';
import type {Readable, Unsubscriber} from 'svelte/store';
import {COLUMN_TYPES} from 'src/features/record-grid/lib/constants';
import {resolveGridFieldAttributeValue} from 'src/features/record-grid/lib/grid-utils';
import type {FieldId, FieldTypeId, Fond, RecordStatusId, SetFieldValueRequest, UUID} from 'typings/portaro.be.types';
import type {EventDispatcher} from 'svelte';
import {tick} from 'svelte';
import {deleteFieldByFieldIdRecursive, setFieldByFieldIdRecursive} from 'src/features/record-grid/lib/grid-fields';
import type {LocalizeFunction} from 'core/types';
import type {PlainTextParsingContext} from 'src/features/record-grid/lib/clipboard';
import {marshallCellValue, unmarshallCellValue} from 'src/features/record-grid/lib/clipboard';
import {generateIdOfInitialField} from 'src/features/record-grid/components/cells/data-cell/utils';
import {asFieldTypeId} from 'shared/utils/record-field-utils';
import {bufferWithDebounceTimeAndSignalFlushing, updateValue} from 'shared/utils/observables-utils';
import {downgradeToRecordRow, isSubrow} from 'src/features/record-grid/lib/record-row';
import {isNotEmptyCollection} from 'shared/utils/collection-utils';


export interface FieldUpdateRequest<T extends SimpleGridFieldValue> extends GridFieldMetadata {
    value: T | null;
    row: Row<HierarchicalRecordRow>;
}

interface ReloadedRecordRowWithMetadata {
    row: RecordRow;
    rowWasSubrow: boolean;
}


export class RecordGridDataManager {

    private table: Table<HierarchicalRecordRow>;
    private readonly updateQueue$ = new Subject<FieldUpdateRequest<SimpleGridFieldValue>>();
    private readonly updatesSubscription: Subscription;
    private readonly bufferFlushSignal$ = new Subject<void>();
    private readonly dirtyRowsIds$ = new BehaviorSubject<Set<UUID>>(new Set());
    private readonly pendingSyncRowsIdsWithRequestsCount$ = new BehaviorSubject<Map<UUID, number>>(new Map());
    private readonly isProcessingEdits$: Observable<boolean>;
    private readonly commandsSubscription: Subscription;
    private readonly tableUnsubscriber: Unsubscriber;

    constructor(private table$: Readable<Table<HierarchicalRecordRow>>,
                private stateManager: GridStateManager,
                private flatRootRows$: BehaviorSubject<RecordRow[]>,
                private flatDraftRootRows$: BehaviorSubject<RecordRow[]>,
                private flatSubrows$: BehaviorSubject<RecordRow[]>,
                private command$: Subject<GridCommand>,
                private recordGridService: RecordGridService,
                private initialValuesEntries: [FieldTypeId, SetFieldValueRequest][],
                private localize: LocalizeFunction,
                private locale: string,
                private logger: Logger,
                private toastMessageService: ToastMessageService,
                private eventDispatcher: EventDispatcher<KpRecordGridDisplayEvents>) {

        this.tableUnsubscriber = this.table$.subscribe((table) => this.table = table);

        this.isProcessingEdits$ = combineLatest([this.dirtyRowsIds$, this.pendingSyncRowsIdsWithRequestsCount$])
            .pipe(map(([dirtyRowsIds, pendingSyncRowsIdsWithRequestsCount]) => isNotEmptyCollection(dirtyRowsIds) || isNotEmptyCollection(pendingSyncRowsIdsWithRequestsCount)));


        const localUpdates$ = this.updateQueue$.pipe(
            tap(({fieldId, value, row}) => this.updateTableDataCellState(row.original, fieldId, value)),
            tap((update) => this.addRowToDirtyRowsTracking(update)),
            bufferWithDebounceTimeAndSignalFlushing(1000, this.bufferFlushSignal$),
            map((updates) => this.keepOnlyLastUpdateForEachRecordRowAndFieldPair(updates)),
            tap((updates) => this.removeRowsFromDirtyRowsTracking(updates))
        );

        const serverUpdates$ = localUpdates$.pipe(
            tap((updates) => this.resolveStartOfPendingSyncRowsTracking(updates)),
            concatMap((updates) => from(this.updateCellValueOnServer(updates)).pipe(catchError(() => of(updates)))),
            map((updates) => this.collectAllAffectedRowsForReload(updates)),
            map((rows) => distinctBy(rows,  getId())),
            concatMap((rows) => from(this.reloadRows(rows)).pipe(catchError(() => EMPTY))),
            tap((rows) => this.resolveCompletionOfPendingSyncRowsTracking(rows)),
            map((rows) => this.filterDirtyOrSyncPendingRows(rows)),
            filter(isNotEmpty),
            tap((rows) => this.refreshRowsInTableData(rows))
        );

        this.updatesSubscription = serverUpdates$.subscribe();

        this.commandsSubscription = this.command$.subscribe((command) => this.processCommand(command));
    }

    public disconnect() {
        cleanup(this.commandsSubscription, this.updatesSubscription, this.tableUnsubscriber);
    }

    public processCommand(command: GridCommand) {
        switch (command.type) {
            case 'delete':
                this.deleteCell(command.target);
                break;
            case 'copy':
                this.copyCell(command.target);
                break;
            case 'paste':
                this.pasteCell(command.target);
                break;
            case 'cut':
                this.cutCell(command.target);
                break;
            case 'rollback-cell-change':
                break;
            default:
                assertUnreachable(command.type);
        }
    }

    public queueUpdate(update: FieldUpdateRequest<SimpleGridFieldValue>) {
        this.updateQueue$.next(update);
    }

    public getIsProcessingEdits$(): Observable<boolean> {
        return this.isProcessingEdits$;
    }

    public async copyCell(sourceCell: CellCoordinates) {
        try {
            const cellData = this.extractCellData(sourceCell);
            const clipboardItem = createClipBoardItem(cellData);
            await navigator.clipboard.write([clipboardItem]);
        } catch (error) {
            this.logger.error('Error during copying from grid', error);
            this.toastMessageService.showError('Error during copying from grid');
        }
    }

    public async pasteCell(targetCell: CellCoordinates) {
        try {
            const clipboardItems = await navigator.clipboard.read();
            const clipboardData = await resolveClipboardData(clipboardItems);
            if (exists(clipboardData)) {
                const targetColumn = getColumnByIndex(this.table, targetCell.columnIndex);
                const ctx: PlainTextParsingContext = {
                    datatype: targetColumn.columnDef.meta.datatype,
                    editorType: targetColumn.columnDef.meta.editorType,
                    localize: this.localize,
                    locale: this.locale
                };
                const fieldValues = unmarshallCellValue(clipboardData, ctx);
                const row = getRowByIndex(this.table, targetCell.rowIndex);
                const fieldId = generateIdOfInitialField(asFieldTypeId(targetColumn.id));

                this.updateQueue$.next({row, fieldId, value: fieldValues.at(0)});
                this.flushEditsBuffer();
            }
        } catch (error) {
            this.logger.error('Error during pasting values to grid', error);
            this.toastMessageService.showError('Error during pasting values to grid');
        }
    }

    public async cutCell(sourceCell: CellCoordinates) {
        await this.copyCell(sourceCell);
        this.deleteCell(sourceCell);
    }

    public deleteCell(targetCell: CellCoordinates) {
        const row = getRowByIndex(this.table, targetCell.rowIndex);
        const fields = getCellValueByCoordinates<HierarchicalRecordRow, GridField<SimpleGridFieldValue>[]>(this.table, targetCell);

        fields.forEach((field) => this.updateQueue$.next({row, fieldId: field.fieldId, value: null}));
        this.flushEditsBuffer();
    }

    public async createRow(selectedSubFond: Fond) {
        const initialValues = this.createInitialValues();
        const newRecordRow = await this.recordGridService.createNewRow(selectedSubFond, initialValues);
        await this.addDraftRow(newRecordRow);
    }

    public async changeStatus(row: Row<HierarchicalRecordRow>, recordStatusId: RecordStatusId) {
        this.flushEditsBuffer();
        await this.waitUntilEditsProcessingFinishes();
        const newRow = await this.recordGridService.changeStatus(row.original, recordStatusId);
        if (isSubrow(row.original)) {
            this.replaceRowIn(newRow, this.flatSubrows$);
        } else if (row.original.draft) {
            this.removeRowFrom(row.original, this.flatDraftRootRows$);
            this.appendRowTo(newRow, this.flatRootRows$);
        } else {
            this.replaceRowIn(newRow, this.flatRootRows$);
        }

        this.eventDispatcher('row-published', newRow);
    }

    public async deleteRow(row: Row<HierarchicalRecordRow>) {
        this.flushEditsBuffer();
        await this.waitUntilEditsProcessingFinishes();
        this.removeRowFrom(row.original, this.getOriginalRecordRowSource(row.original));
        await this.recordGridService.deleteRow(row.original);
        this.eventDispatcher('row-deleted', row.original);
    }

    public async duplicateRow(row: Row<HierarchicalRecordRow>) {
        this.flushEditsBuffer();
        await this.waitUntilEditsProcessingFinishes();
        const newRecordRow = await this.recordGridService.duplicateRow(row.original);
        await this.addDraftRow(newRecordRow);
    }

    private updateTableDataCellState(row: HierarchicalRecordRow, fieldId: FieldId, value: SimpleGridFieldValue) {
        if (exists(value)) {
            this.editCellValueInTableData(row, fieldId, {...value, fieldId});
        } else {
            this.deleteCellValueFromTableData(row, fieldId);
        }
    }

    private async addDraftRow(newRecordRow: RecordRow) {
        this.appendRowTo(newRecordRow, this.flatDraftRootRows$);
        await tick();
        const requiredColumn = this.findFirstRequiredColumn(this.table, newRecordRow.fond.id);
        if (exists(requiredColumn)) {
            const targetCoordinates = new CellCoordinates({
                rowIndex: this.table.getRowModel().rows.length - 1,
                columnIndex: requiredColumn.getIndex(requiredColumn.getIsPinned() || undefined)
            });
            this.stateManager.forceEditModeIn(targetCoordinates);
        }
    }

    private editCellValueInTableData(row: HierarchicalRecordRow, fieldId: FieldId, value: GridField) {
        const updatedRow = structuredClone(downgradeToRecordRow(row));
        setFieldByFieldIdRecursive(updatedRow, fieldId, value);
        this.replaceRowIn(updatedRow, this.getOriginalRecordRowSource(row));
    }

    private deleteCellValueFromTableData(row: HierarchicalRecordRow, fieldId: FieldId) {
        const updatedRow = structuredClone(downgradeToRecordRow(row));
        deleteFieldByFieldIdRecursive(updatedRow, fieldId);
        this.replaceRowIn(updatedRow, this.getOriginalRecordRowSource(row));
    }

    private async updateCellValueOnServer(updates: FieldUpdateRequest<SimpleGridFieldValue>[]): Promise<FieldUpdateRequest<SimpleGridFieldValue>[]> {
        const editUpdates = updates.filter((update) => exists(update.value));
        const deleteUpdates = updates.filter((update) => !exists(update.value));

        if (isNotEmpty(editUpdates)) {
            await this.recordGridService.updateCells(editUpdates.map((update) => ({...update, row: update.row.original})));
        }
        if (isNotEmpty(deleteUpdates)) {
            await this.recordGridService.deleteCells(deleteUpdates.map((update) => ({...update, row: update.row.original})));
        }
        return updates;
    }

    private extractCellData(coordinates: CellCoordinates): ClipboardCellData {
        const data = getCellValueByCoordinates<HierarchicalRecordRow, GridField<SimpleGridFieldValue>[]>(this.table, coordinates);
        return marshallCellValue(data);
    }

    private addRowToDirtyRowsTracking(update: FieldUpdateRequest<SimpleGridFieldValue>) {
        updateValue(this.dirtyRowsIds$, (dirtyRowsIds) => dirtyRowsIds.add(update.row.original.id));
    }

    private removeRowsFromDirtyRowsTracking(updates: FieldUpdateRequest<SimpleGridFieldValue>[]) {
        const rowsIds = updates.map((update) => update.row.original.id);
        const distinctRecordRowsIds = distinct(rowsIds);

        distinctRecordRowsIds.forEach((recordRowId) => {
            updateValue(this.dirtyRowsIds$, (dirtyRowsIds) => {
                dirtyRowsIds.delete(recordRowId);
                return dirtyRowsIds;
            });
        });
    }

    private resolveStartOfPendingSyncRowsTracking(updates: FieldUpdateRequest<SimpleGridFieldValue>[]) {
        const rowsIds = updates.map((update) => update.row.original.id);
        const distinctRecordRowsIds = distinct(rowsIds);

        distinctRecordRowsIds.forEach((recordRowId) => {
            updateValue(this.pendingSyncRowsIdsWithRequestsCount$, (pendingSyncRowsIdsToRequestCnt) => {
                if (pendingSyncRowsIdsToRequestCnt.has(recordRowId)) {
                    pendingSyncRowsIdsToRequestCnt.set(recordRowId, pendingSyncRowsIdsToRequestCnt.get(recordRowId) + 1);
                } else {
                    pendingSyncRowsIdsToRequestCnt.set(recordRowId, 1);
                }
                return pendingSyncRowsIdsToRequestCnt;
            });
        });
    }

    private resolveCompletionOfPendingSyncRowsTracking(reloadedRowsWithMetadata: ReloadedRecordRowWithMetadata[]) {
        reloadedRowsWithMetadata.forEach(({row}) => {
            const recordRowId = row.id;

            updateValue(this.pendingSyncRowsIdsWithRequestsCount$, (pendingSyncRowsIdsToRequestCnt) => {
                const pendingRequests = pendingSyncRowsIdsToRequestCnt.get(recordRowId);
                if (pendingRequests > 1) {
                    pendingSyncRowsIdsToRequestCnt.set(recordRowId, pendingRequests - 1);
                } else {
                    pendingSyncRowsIdsToRequestCnt.delete(recordRowId);
                }
                return pendingSyncRowsIdsToRequestCnt;
            });
        });
    }

    private keepOnlyLastUpdateForEachRecordRowAndFieldPair(updates: FieldUpdateRequest<SimpleGridFieldValue>[]) {
        return distinctBy(updates, ({row, fieldId}) => `${row.original.id}:${fieldId}`)
    }

    private filterDirtyOrSyncPendingRows(reloadedRowsWithMetadata: ReloadedRecordRowWithMetadata[]) {
        return reloadedRowsWithMetadata.filter(({row}) => {
            return !this.dirtyRowsIds$.getValue().has(row.id) && !this.pendingSyncRowsIdsWithRequestsCount$.getValue().has(row.id)
        });
    }

    private collectAllAffectedRowsForReload(updates: FieldUpdateRequest<SimpleGridFieldValue>[]): HierarchicalRecordRow[] {
        const rows = updates.map((update) => update.row);
        const parentChainsRows = rows.flatMap((row) => row.getParentRows());
        const subtreesRows = rows.flatMap((row) => row.getLeafRows());
        return [...rows, ...parentChainsRows, ...subtreesRows].map((row) => row.original);
    }

    private async reloadRows(originalRows: HierarchicalRecordRow[]): Promise<ReloadedRecordRowWithMetadata[]> {
        const reloadedRows: RecordRow[] = await this.recordGridService.loadRows(originalRows);
        return zip(originalRows.toSorted(uuidComparator), reloadedRows.toSorted(uuidComparator))
            .map(([originalRow, reloadedRow]) => ({row: reloadedRow, rowWasSubrow: isSubrow(originalRow)}));
    }

    private refreshRowsInTableData(reloadedRowsWithMetadata: ReloadedRecordRowWithMetadata[]) {
        const refreshedSubrows = reloadedRowsWithMetadata
            .filter(({rowWasSubrow}) => rowWasSubrow)
            .map(({row}) => row);

        const refreshedDraftRootRows = reloadedRowsWithMetadata
            .filter(({row, rowWasSubrow}) => !rowWasSubrow && row.draft)
            .map(({row}) => row);

        const refreshedRootRows = reloadedRowsWithMetadata
            .filter(({row, rowWasSubrow}) => !rowWasSubrow && !row.draft)
            .map(({row}) => row);

        this.replaceRowsIn(refreshedSubrows, this.flatSubrows$);
        this.replaceRowsIn(refreshedDraftRootRows, this.flatDraftRootRows$);
        this.replaceRowsIn(refreshedRootRows, this.flatRootRows$);

        this.eventDispatcher('rows-updated', [...refreshedRootRows, ...refreshedDraftRootRows, ...refreshedSubrows]);
    }

    private findFirstRequiredColumn(table: Table<HierarchicalRecordRow>, fondId: number) {
        return table.getAllFlatColumns()
            .filter((column) => column.columnDef.meta.columnType === COLUMN_TYPES.DATA_COLUMN)
            .find((column) => resolveGridFieldAttributeValue(column.columnDef.meta.required, fondId));
    }

    private createInitialValues(): Record<FieldTypeId, SetFieldValueRequest> | undefined {
        return isNotEmpty(this.initialValuesEntries) ? Object.fromEntries(this.initialValuesEntries) : undefined;
    }

    private getOriginalRecordRowSource(recordRow: HierarchicalRecordRow): BehaviorSubject<RecordRow[]> {
        if (isSubrow(recordRow)) {
            return this.flatSubrows$;
        } else if (recordRow.draft) {
            return this.flatDraftRootRows$;
        } else {
            return this.flatRootRows$;
        }
    }

    private appendRowTo(recordRow: RecordRow, recordRows$: BehaviorSubject<RecordRow[]>) {
        updateValue(recordRows$, (recordRows) => [...recordRows, recordRow]);
    }

    private removeRowFrom(recordRow: RecordRow, recordRows$: BehaviorSubject<RecordRow[]>) {
        updateValue(recordRows$, (recordRows) => removeAll(recordRows, byIdOf(recordRow)));
    }

    private replaceRowIn(recordRow: RecordRow, recordRows$: BehaviorSubject<RecordRow[]>) {
        updateValue(recordRows$, (recordRows) => replaceAll(recordRows, byIdOf(recordRow), recordRow));
    }

    private replaceRowsIn(replacementRows: RecordRow[], recordRows$: BehaviorSubject<RecordRow[]>) {
        updateValue(recordRows$, (recordRows) => {
            replacementRows.forEach((replacementRow) => {
                recordRows = replaceAll(recordRows, byIdOf(replacementRow), replacementRow);
            });
            return recordRows;
        });
    }

    private flushEditsBuffer() {
        this.bufferFlushSignal$.next();
    }

    private async waitUntilEditsProcessingFinishes() {
        await firstValueFrom(this.isProcessingEdits$.pipe(filter((isProcessingEdits) => !isProcessingEdits), take(1)));
    }
}