import type {DefinedValueEditorTypeAliases, ValueEditorType} from 'shared/value-editors/types';
import {stringifyArgument} from 'shared/utils/error-utils';


export const SUPPORTED_EDITORS = [
    'text',
    'autocomplete',
    'date',
    'number',
    'boolean',
    'single-acceptable',
    'editable-record-search',
    'inline-record-search'
] as const;

export type SupportedEditors = Extract<ValueEditorType | DefinedValueEditorTypeAliases, typeof SUPPORTED_EDITORS[number]>;

export function isSupported(type: ValueEditorType | DefinedValueEditorTypeAliases): type is SupportedEditors {
    return (SUPPORTED_EDITORS as readonly string[]).includes(type);
}

export function assertIsisSupported(type: ValueEditorType | DefinedValueEditorTypeAliases): asserts type is SupportedEditors {
    if (!isSupported(type)) {
        throw new TypeError(`${stringifyArgument(type)} is not supported editor type!`)
    }
}

export function asSupported(type: ValueEditorType | DefinedValueEditorTypeAliases): SupportedEditors {
    assertIsisSupported(type);
    return type;
}