<script lang="ts">
    import type {Fond} from 'typings/portaro.be.types';
    import {getInjector} from 'core/svelte-context/context';
    import {RecordGridService} from '../services/record-grid.service';
    import {onMount} from 'svelte';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import KpRecordGrid from 'src/features/record-grid/KpRecordGrid.svelte';
    import KpLoadablePageContainer from 'shared/layouts/containers/KpLoadablePageContainer.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    export let fond: Fond;

    const recordGridService = getInjector().getByClass(RecordGridService);

    let loading = true;
    let fonds: Fond[] = [];

    onMount(async () => {
        fonds = await recordGridService.getCompatibleFonds(fond.id);
        loading = false;
    });
</script>

<KpLoadablePageContainer isBootstrapContainer="{false}" pageClass="kp-record-grid-page" gap="10px" containerClass="kp-record-grid-page-container" {loading}>
    <div class="heading-container">
        <KpHeading type="h1">{pipe(fond, loc())}</KpHeading>
    </div>

    <KpRecordGrid fondOrFondId="{fond.id}" relatedFonds="{fonds}" useGlobalSearch/>
</KpLoadablePageContainer>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro-erp.less";

    .heading-container {
        padding: 0 @spacing-xl;
    }

    :global {
        .kp-record-grid-page-container .record-grid-container {
            border-radius: 0 !important;
            border-left: none !important;
            border-right: none !important;
            border-bottom: none !important;
        }
    }
</style>