<script lang="ts" generics="ROW">
    import type {Column} from '@tanstack/svelte-table';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let column: Column<ROW>;

    const handleToggleColumnPin = () => {
        if (column.getIsPinned() !== false) {
            column.pin(false);
            return;
        }

        column.pin('left');
    }
</script>

<button class="kp-column-pinner icon-button"
        class:pinned={column.getIsPinned()}
        on:click={handleToggleColumnPin}>

    <UIcon icon="thumbtack" lineHeight="1"/>
</button>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "bootstrap-less/bootstrap/variables";

    .kp-column-pinner {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        right: @spacing-sm;
        top: 50%;
        transform: translate(@spacing-xs, -50%);
        border: none;
        opacity: 0;
        z-index: 1;
        visibility: hidden;
        cursor: pointer;
        padding: @spacing-xs;
        border-radius: @border-radius-default;
        outline: 1px solid var(--accent-blue-new);
        color: var(--accent-blue-new);
        background-color: @body-bg;
        transition: transform 0.2s ease-in-out, opacity 0.2s ease-in-out, visibility 0.2s ease-in-out, outline-color 0.2s ease-in-out;

        &.pinned {
            outline-width: 2px;
            outline-color: var(--brand-orange-new);
            background-color: #F4E1DC;
            transform: translate(0, -50%);
            color: var(--brand-orange-new);
            opacity: 1;
            visibility: visible;
        }
    }

    :global {
        .kp-generic-grid-header-cell:hover .kp-column-pinner {
            transform: translate(0, -50%);
            opacity: 1;
            visibility: visible;
        }

        .kp-column-pinner {
            &.pinned .uicon {
                rotate: -45deg;
            }

            .uicon {
                transition: rotate 0.2s ease-in-out, color 0.2s ease-in-out;
            }
        }
    }
</style>