<script lang="ts">
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpDropdownMenuButton from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuButton.svelte';
    import type {Fond} from 'typings/portaro.be.types';
    import KpDropdownMenuItem from 'shared/ui-widgets/dropdown-menu/KpDropdownMenuItem.svelte';
    import type {RecordGridDataManager} from 'src/features/record-grid/lib/record-grid-data-manager';

    export let recordGridDataManager: RecordGridDataManager;
    export let insertableFonds: Fond[];
    export let rowCreationDisabled = false;
</script>

<div class="kp-grid-footer-control-cell">
    {#if (insertableFonds.length === 1)}
        {@const fond = insertableFonds.at(0)}
        <KpButton additionalClasses="add-new-row-btn"
                  buttonStyle="success-new"
                  buttonSize="sm"
                  isDisabled="{rowCreationDisabled}"
                  on:click={() => recordGridDataManager.createRow(fond)}>
            <IconedContent icon="plus">
                Add {fond.text} (#{fond.id})
            </IconedContent>
        </KpButton>
    {:else}
        <KpDropdownMenuButton additionalButtonClasses="add-new-row-btn"
                              buttonStyle="success-new"
                              buttonSize="sm"
                              defaultPlacement="top-end"
                              isDisabled="{rowCreationDisabled}">

            <svelte:fragment slot="button">
                <IconedContent icon="add" trailingIcon="angle-small-down"/>
            </svelte:fragment>

            <svelte:fragment slot="menu">
                {#each insertableFonds as fond (fond.id)}
                    <KpDropdownMenuItem on:click={() => recordGridDataManager.createRow(fond)}>
                        {fond.text} (#{fond.id})
                    </KpDropdownMenuItem>
                {/each}
            </svelte:fragment>
        </KpDropdownMenuButton>
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    @vertical-padding: 3px;
    @horizontal-padding: 5px;

    .kp-grid-footer-control-cell {
        padding: @vertical-padding @horizontal-padding;
        display: flex;
        overflow: hidden;
        gap: @spacing-xs;

        :global(.add-new-row-btn) {
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
</style>