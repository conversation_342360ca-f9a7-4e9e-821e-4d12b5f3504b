import type {ColumnPinningState, ColumnSizingState, VisibilityState} from '@tanstack/svelte-table';
import type {Fond} from 'typings/portaro.be.types';
import {exists} from 'shared/utils/custom-utils';

const tableSettingsLocalStorageKey = 'recordGridTableSettings';

interface FondTableSettings {
    columnSizing: ColumnSizingState;
    columnPinning: ColumnPinningState;
    columnVisibility: VisibilityState;
}

interface RecordGridTableSettingsHolder {
    fondSettings: Record<number, FondTableSettings>;
}

class TableSettingsStorage {

    public saveFondSettings(fond: Fond, tableSettings: FondTableSettings) {
        const settingsHolder = this.getLocalStorageSettingsHolder();
        settingsHolder.fondSettings[fond.id] = tableSettings;

        this.saveLocalStorageSettingsHolder(settingsHolder);
    }

    public loadFondSettings(fond: Fond): FondTableSettings {
        const settingsHolder = this.getLocalStorageSettingsHolder();
        const fondTableSettings = settingsHolder.fondSettings[fond.id];
        if (!exists(fondTableSettings)) {
            return {
                columnPinning: {},
                columnSizing: {},
                columnVisibility: {}
            }
        }

        return fondTableSettings;
    }

    private getLocalStorageSettingsHolder(): RecordGridTableSettingsHolder {
        const jsonString = localStorage.getItem(tableSettingsLocalStorageKey);

        if (!exists(jsonString)) {
            return {fondSettings: {}};
        }

        try {
            return JSON.parse(jsonString) as RecordGridTableSettingsHolder;
        } catch {
            return {fondSettings: {}};
        }
    }

    private saveLocalStorageSettingsHolder(settingsHolder: RecordGridTableSettingsHolder) {
        localStorage.setItem(tableSettingsLocalStorageKey, JSON.stringify(settingsHolder));
    }
}

export const recordGridTableSettingsStorage = new TableSettingsStorage();