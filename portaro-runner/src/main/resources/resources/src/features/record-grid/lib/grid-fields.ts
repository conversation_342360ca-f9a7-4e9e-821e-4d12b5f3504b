import type {CompoundGridFieldValue, Fieldable, GridField, GridFieldValue, SimpleGridFieldValue} from './types';
import type {FieldId, FieldTypeId} from 'typings/portaro.be.types';
import {exists, isNull, isNullOrUndefined} from 'shared/utils/custom-utils';
import {anyMatches, byProperty, findFirst, removeAll, replaceAll, zip} from 'shared/utils/array-utils';
import {isEqual} from 'lodash-es';
import {
    assertFieldable,
    assertSimpleGridFieldValue,
    isCompoundGridFieldValue, isErrorGridFieldValue,
    isFieldable, isOptionGridFieldValue, isScalarGridValue,
    isSimpleGridFieldValue
} from './types-utils';
import {
    convertFieldIdToFieldTypeId,
    getParentFieldId,
    parseFieldIdToPath,
    parseFieldTypeIdToPath
} from 'shared/utils/record-field-utils';

export function getFieldByFieldTypeIdRecursive(fieldable: Fieldable, fieldTypeId: FieldTypeId): GridField[] {
    const pathElements = parseFieldTypeIdToPath(fieldTypeId); // a.b.c -> [a, a.b, a.b.c]
    const [firstPathElement, ...restPathElements] = pathElements;  // [a, a.b, a.b.c] -> a, [a.b, a.b.c]

    let fields: GridField[]; // accumulator to store found fields during traversing fieldable hierarchy
    fields = getFieldByFieldTypeId(fieldable, firstPathElement); // -> F[a]

    for (const pathElement of restPathElements) {
        fields = fields.flatMap((field) => {
            assertFieldable(field);
            return getFieldByFieldTypeId(field, pathElement); // F[a] -> F[a][a.b], F[a][a.b] -> F[a][a.b][a.b.c]
        });
    }

    return fields;
}

export function getFirstFieldByFieldTypeIdRecursive(fieldable: Fieldable, fieldTypeId: FieldTypeId): GridField | null {
    return getFieldByFieldTypeIdRecursive(fieldable, fieldTypeId).at(0) || null;
}

function getFieldByFieldTypeId(fieldable: Fieldable, fieldTypeId: FieldTypeId): GridField[] | [] {
    const field = fieldable.fields[fieldTypeId];
    if (isNullOrUndefined(field)) {
        return [];
    }
    return field;
}

function getFieldByFieldId(fieldable: Fieldable, fieldId: FieldId): GridField | undefined {
    const fieldTypeId = convertFieldIdToFieldTypeId(fieldId);
    const field = fieldable.fields[fieldTypeId];
    if (isNullOrUndefined(field)) {
        return undefined;
    }
    return findFirst<GridField>(field, byProperty('fieldId', fieldId));
}

export function extractAllSimpleGridFields(gridFields: GridField[]): GridField<SimpleGridFieldValue>[] {
    return gridFields.flatMap((gridField) => {
        if (isCompoundGridFieldValue(gridField)) {
            return extractAllSimpleGridFields(Object.values(gridField.fields).flat());
        } else {
            assertSimpleGridFieldValue(gridField);
            return gridField;
        }
    });
}

export function getFieldByFieldIdRecursive(fieldable: Fieldable, fieldId: FieldId): GridField {
    const [firstFieldIdPathElement, ...restFieldIdPathElements] = parseFieldIdToPath(fieldId);

    let field: GridField;
    field = getFieldByFieldId(fieldable, firstFieldIdPathElement);

    for (const pathElement of restFieldIdPathElements) {
        assertFieldable(field);
        field = getFieldByFieldId(field, pathElement);
    }

    return field;
}

function getFieldByFieldIdRecursiveCreating(fieldable: Fieldable, fieldId: FieldId): GridField {
    const [firstPathElement, ...restPathElements] = parseFieldIdToPath(fieldId);

    let field: GridField;
    field = getFieldByFieldId(fieldable, firstPathElement);

    if (isNullOrUndefined(field)) {
        field = createAndInsertCompoundGridField(fieldable, firstPathElement);
    }

    for (const pathElement of restPathElements) {
        assertFieldable(field);
        const nextField = getFieldByFieldId(field, pathElement);
        if (isNullOrUndefined(nextField)) {
            field = createAndInsertCompoundGridField(field, pathElement);
        } else {
            field = nextField;
        }
    }
    return field;
}

function createAndInsertCompoundGridField(fieldable: Fieldable, fieldId: FieldId): GridField<CompoundGridFieldValue> {
    const fieldTypeId = convertFieldIdToFieldTypeId(fieldId);
    if (isNullOrUndefined(fieldable.fields[fieldTypeId])) {
        fieldable.fields[fieldTypeId] = [];
    }
    const field = {
        fields: {},
        text: '',
        recordReference: null,
        fieldId
    } satisfies GridField<CompoundGridFieldValue>;

    (fieldable.fields[fieldTypeId] as GridField<CompoundGridFieldValue>[]).push(field);
    return field;
}

export function setFieldByFieldIdRecursive(fieldable: Fieldable, fieldId: FieldId, value: GridField) {
    const parentFieldId = getParentFieldId(fieldId);
    const parentField = getFieldByFieldIdRecursiveCreating(fieldable, parentFieldId);
    const fieldTypeId = convertFieldIdToFieldTypeId(fieldId);

    assertFieldable(parentField);

    // create missing cell field if not present
    if (isNullOrUndefined(parentField.fields[fieldTypeId])) {
        parentField.fields[fieldTypeId] = [];
    }

    if (anyMatches<GridField>(parentField.fields[fieldTypeId], byProperty('fieldId', fieldId))) {
        parentField.fields[fieldTypeId] = replaceAll(parentField.fields[fieldTypeId], byProperty('fieldId', fieldId), value) as GridField<SimpleGridFieldValue>[] | GridField<CompoundGridFieldValue>[];
    } else {
        parentField.fields[fieldTypeId] = [...parentField.fields[fieldTypeId], value] as GridField<SimpleGridFieldValue>[] | GridField<CompoundGridFieldValue>[];
    }
}

export function deleteFieldByFieldIdRecursive(fieldable: Fieldable, fieldId: FieldId) {
    const parentFieldId = getParentFieldId(fieldId);
    const parentField = getFieldByFieldIdRecursive(fieldable, parentFieldId);
    const fieldTypeId = convertFieldIdToFieldTypeId(fieldId);

    if (isFieldable(parentField) && exists(parentField.fields[fieldTypeId])) {
        parentField.fields[fieldTypeId] = removeAll<GridField>(parentField.fields[fieldTypeId], byProperty('fieldId', fieldId)) as GridField<SimpleGridFieldValue>[] | GridField<CompoundGridFieldValue>[];
    }
}

export function isFieldValueEqual(fieldValue1: GridFieldValue, fieldValue2: GridFieldValue): boolean {
    if (isCompoundGridFieldValue(fieldValue1) && isCompoundGridFieldValue(fieldValue2)) {
        return compoundFieldsEquals(fieldValue1, fieldValue2);
    }
    if (isSimpleGridFieldValue(fieldValue1) && isSimpleGridFieldValue(fieldValue2)) {
        return simpleFieldsEquals(fieldValue1, fieldValue2);
    }
    return false;
}

function compoundFieldsEquals(fieldValue1: CompoundGridFieldValue, fieldValue2: CompoundGridFieldValue): boolean {
    if (fieldValue1.text !== fieldValue2.text) {
        return false;
    }
    if (!isEqual(fieldValue1.recordReference, fieldValue2.recordReference)) {
        return false;
    }
    if (!isEqual(Object.keys(fieldValue1.fields), Object.keys(fieldValue2.fields))) {
        return false;
    }
    return zip(Object.values(fieldValue1.fields), Object.values(fieldValue2.fields))
        .every(([subfields1, subfields2]) => subfieldsEquals(subfields1, subfields2));
}

function subfieldsEquals(subfields1: GridFieldValue[], subfields2: GridFieldValue[]): boolean {
    if (subfields1.length !== subfields2.length) {
        return false;
    }
    return zip(subfields1, subfields2)
        .every(([subfield1, subfield2]) => isFieldValueEqual(subfield1, subfield2));
}

function simpleFieldsEquals(fieldValue1: SimpleGridFieldValue, fieldValue2: SimpleGridFieldValue): boolean {
    // both are null
    if (isNull(fieldValue1) && isNull(fieldValue2)) {
        return true;
    }

    // one is null the other is not null
    if (isNull(fieldValue1) || isNull(fieldValue2)) {
        return false;
    }

    if (fieldValue1.text !== fieldValue2.text) {
        return false;
    }
    if (!isEqual(fieldValue1.recordReference, fieldValue2.recordReference)) {
        return false;
    }
    if (isOptionGridFieldValue(fieldValue1) && isOptionGridFieldValue(fieldValue2)) {
        return isEqual(fieldValue1.id, fieldValue2.id);
    }
    if (isScalarGridValue(fieldValue1) && isScalarGridValue(fieldValue2)) {
        return isEqual(fieldValue1.value, fieldValue2.value);
    }
    if (isErrorGridFieldValue(fieldValue1) && isErrorGridFieldValue(fieldValue2)) {
        return isEqual(fieldValue1.error, fieldValue2.error);
    }
    return false;
}