import type {GridDeleteRequest, GridEditRequest, RecordRow} from '../lib/types';
import type {ActionResponse, Fond, UUID} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';

export class RecordGridDataService {
    public static serviceName = 'recordGridDataService';

    private readonly GRID_RECORD_ROUTE = 'grid/record';
    private readonly GRID_RECORDS_ROUTE = 'grid/records';
    private readonly GRID_EDIT2_ROUTE = 'grid/edit';
    private readonly GRID_DELETE_ROUTE = 'grid/delete';
    private readonly GRID_INSERTABLE_FONDS = 'grid/insertable-fonds';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    public async loadInsertableFonds(rootFondId: number): Promise<Fond[]> {
        return this.ajaxService
            .createRequest(`${this.GRID_INSERTABLE_FONDS}/${rootFondId}`)
            .get();
    }

    public async loadSingleRow(recordId: UUID): Promise<RecordRow> {
        return this.ajaxService
            .createRequest(`${this.GRID_RECORD_ROUTE}/${recordId}`)
            .get();
    }

    public async loadRows(recordIds: UUID[]): Promise<RecordRow[]> {
        return this.ajaxService
            .createRequest(`${this.GRID_RECORDS_ROUTE}`)
            .get({recordIds});
    }

    public async editCells(request: GridEditRequest): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${this.GRID_EDIT2_ROUTE}`)
            .post(request);
    }

    public async deleteCells(request: GridDeleteRequest): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${this.GRID_DELETE_ROUTE}`)
            .post(request);
    }
}