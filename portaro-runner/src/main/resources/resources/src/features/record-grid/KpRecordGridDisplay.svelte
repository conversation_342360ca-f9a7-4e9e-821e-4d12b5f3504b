<script lang="ts">
    import type {FieldTypeId, Fond, RecordSearchParams, SetFieldValueRequest, UUID} from 'typings/portaro.be.types';
    import type {ColumnDef, ColumnPinningState, ColumnSizingState, DisplayColumnDef, TableOptions, VisibilityState, ExpandedState} from '@tanstack/svelte-table';
    import type {Updater} from '@tanstack/table-core';
    import type {
        GridField,
        HierarchicalRecordRow,
        KpRecordGridDisplayEvents,
        RecordRow,
        SimpleGridFieldValue
    } from './lib/types';
    import type {SvelteComponentConstructor} from 'core/types';
    import type {GridContext} from 'shared/ui-widgets/grid/grid.context';
    import type {FieldTypeDefinition, FondFieldTypeDefinitions} from 'src/features/record/field-types/types';
    import {createSvelteTable, getCoreRowModel, getExpandedRowModel} from '@tanstack/svelte-table';
    import {derived, writable} from 'svelte/store';
    import {getCurrentLanguage, getInjector, getLocalization, getLogger} from 'core/svelte-context/context';
    import {defaultCellContent, defaultRowId, extractCoordinates, getStateOf, replaceTableData, tryToFindGridCell, tryToFindRowByElement, updateTableState} from 'shared/ui-widgets/grid/utils';
    import {createRender} from 'svelte-render';
    import {assertTrue, cleanup, exists, isNull, isNullOrUndefined, getId} from 'shared/utils/custom-utils';
    import {RecordGridService} from './services/record-grid.service';
    import {
        arrayShallowEqual,
        ascendingOrderComparator,
        byIdOf,
        isNotEmpty, distinctBy,
        replaceAll
    } from 'shared/utils/array-utils';
    import {onDestroy, createEventDispatcher} from 'svelte';
    import {getSearchContext} from '../search/kp-search-context/search-context';
    import {combineLatestWith, distinctUntilChanged, filter, map, tap} from 'rxjs/operators';
    import {findHierarchicalColumns} from './lib/grid-utils';
    import {extractAllSimpleGridFields, getFieldByFieldTypeIdRecursive} from 'src/features/record-grid/lib/grid-fields';
    import {
        createHierarchicalRows,
        createRootOnlyHierarchicalRows,
        parseRecordIdFromRowId
    } from 'src/features/record-grid/lib/record-row';
    import {asSupported} from 'src/features/record-grid/lib/grid-value-editors';
    import {RecordGridDataManager} from 'src/features/record-grid/lib/record-grid-data-manager';
    import {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
    import {createGridSettings} from 'shared/ui-widgets/grid/grid-settings';
    import {getGridContext, setGridContext} from 'shared/ui-widgets/grid/grid.context';
    import {BehaviorSubject} from 'rxjs';
    import {recordGridTableSettingsStorage} from './lib/settings-storage';
    import {isEqual} from 'lodash-es';
    import {COLUMN_TYPES} from 'src/features/record-grid/lib/constants';
    import KpGenericGrid from 'shared/ui-widgets/grid/KpGenericGrid.svelte';
    import KpPageableSearchResults from '../search/kp-search-results/KpPageableSearchResults.svelte';
    import HeaderCell from './components/cells/HeaderCell.svelte';
    import ControlCell from './components/cells/ControlCell.svelte';
    import DataCell from './components/cells/data-cell/DataCell.svelte';
    import ControlFooterCell from './components/cells/ControlFooterCell.svelte';
    import KpContextMenuButton from 'shared/components/kp-context-menu/KpContextMenuButton.svelte';
    import KpHorizontalSeparator from 'shared/ui-widgets/separator/KpHorizontalSeparator.svelte';
    import KpSearchFieldTypeFiltersContainer from 'src/features/search/kp-search-field-type-filter/KpSearchFieldTypeFiltersContainer.svelte';
    import {
        SearchManagerBuilderFactoryService
    } from 'src/features/search/search-manager/search-manager-factory.service';
    import {mapFieldTypeIdToDynamicFieldSearchParamName} from 'shared/utils/record-field-utils';
    import {asUuid} from 'shared/utils/types-utils';


    export let fond: Fond;
    export let fieldTypeDefinitions: FondFieldTypeDefinitions;
    export let insertableFonds: Fond[];

    export let referenceRecordId: UUID | null;
    export let referenceFieldTypeId: FieldTypeId | null;

    export let stickyHeader = false;
    export let pinControlColumn = false;
    export let expandedFirstLevel = false;
    export let includeAddNewRow = true;
    export let gridContext: GridContext<HierarchicalRecordRow> | null = null;
    export let controlCellComponent: SvelteComponentConstructor;

    const logger = getLogger();
    const toastMessageService = getInjector().getByClass(ToastMessageService);
    const searchManagerBuilderFactoryService = getInjector().getByClass(SearchManagerBuilderFactoryService);
    const localize = getLocalization();
    const locale = getCurrentLanguage();
    const recordGridService = getInjector().getByClass(RecordGridService);
    const searchManager = getSearchContext<RecordRow, RecordSearchParams>();
    const dispatch = createEventDispatcher<KpRecordGridDisplayEvents>();

    const expandableColumnsIds = findHierarchicalColumns(fieldTypeDefinitions).map((fieldType) => fieldType.id);
    const isExpandable = isNotEmpty(expandableColumnsIds);

    let {columnPinning, columnSizing, columnVisibility} = recordGridTableSettingsStorage.loadFondSettings(fond);
    $: recordGridTableSettingsStorage.saveFondSettings(fond, {
        columnPinning,
        columnSizing,
        columnVisibility
    });

    const options = createTableOptions(fieldTypeDefinitions, insertableFonds);
    export let table$ = createSvelteTable(options);

    const settings = createGridSettings<HierarchicalRecordRow>({
        columnPinningEnabled: true,
        columnResizingEnabled: true,
        condensed: true,
        mainGroupsDivided: true,
        stickyHeader,
        colorAccented: true,
        selectionEnabled: true,
        editable: true,
        showOnlyTopAndBottomHeaders: true
    });

    setGridContext<HierarchicalRecordRow>(table$, settings);
    gridContext = getGridContext<HierarchicalRecordRow>();
    const {stateManager, command$} = gridContext;


    const flatRootRows$ = new BehaviorSubject<RecordRow[]>([]);
    const flatDraftRootRows$ = new BehaviorSubject<RecordRow[]>([]);
    const flatSubrows$ = new BehaviorSubject<RecordRow[]>([]);
    const expandedRecordRowsIds = new Set<UUID>();
    const rowExpansionRequestsPerRowCounter = new Map<UUID, number>();
    const rowExpansionRequestsPerRowCounter$ = writable(rowExpansionRequestsPerRowCounter);
    const rowsWithSubrowsLoading$ = derived(rowExpansionRequestsPerRowCounter$, (perRowCounter) => Array.from(perRowCounter.keys()));

    const dataSubscription = flatRootRows$.pipe(
        combineLatestWith(flatSubrows$),
        map(([flat, subrows]) => [...flat, ...subrows]),
        map((rows) => isExpandable ? createHierarchicalRows(distinctBy(rows, getId()), expandableColumnsIds, referenceRecordId) : createRootOnlyHierarchicalRows(rows)),
        filter((rows) => exists(rows)),
        distinctUntilChanged(isEqual),
        combineLatestWith(flatDraftRootRows$.pipe(map((rows) => createRootOnlyHierarchicalRows(rows)))),
        map(([flat, drafts]) => [...flat, ...drafts]),
        tap((rows) => replaceTableData(options, rows))
    ).subscribe();

    const lastSearch = searchManager.getLastSearch$();
    const searchSubscription = searchManager.getPagination()
        .getPaginationData$()
        .pipe(map((data) => data.items), distinctUntilChanged(arrayShallowEqual))
        .subscribe(flatRootRows$);

    const draftOnlySearchParams = {includeDraft: true, includeActive: false, includeDeleted: false, includeExcluded: false};
    const draftSearchSubscription = searchManager.createDerivedSearch((params) => ({...params, ...draftOnlySearchParams, pageNumber: 1})) // assume there is not more than 1 page of drafts
        .getPagination()
        .getPaginationData$()
        .pipe(map((data) => data.items), distinctUntilChanged(arrayShallowEqual))
        .subscribe(flatDraftRootRows$);

    const subrowSearchManager = searchManagerBuilderFactoryService.createBuilder<RecordSearchParams, RecordRow>()
        .withStaticParams({...searchManager.getStaticParams(), includeDraft: true})
        .withAllowedSimultaneousRequests()
        .createLocalSearch();
    const subrowSearchSubscription = subrowSearchManager
        .getSearchResult$()
        .pipe(
            tap((result) => decreaseRowExpansionRequestsCounter(result.params)),
            map((result) => result.content), distinctUntilChanged(arrayShallowEqual))
        .subscribe(replaceOrAppendSubRowRecords);

    const recordGridDataManager = new RecordGridDataManager(
        table$,
        stateManager,
        flatRootRows$,
        flatDraftRootRows$,
        flatSubrows$,
        command$,
        recordGridService,
        createRowInitialValuesEntries(),
        localize,
        locale,
        logger,
        toastMessageService,
        dispatch
    );

    const isProcessingEdits$ = recordGridDataManager.getIsProcessingEdits$();

    $: dispatch('grid-processing', $isProcessingEdits$);

    onDestroy(() => {
        cleanup(dataSubscription, searchSubscription, draftSearchSubscription, subrowSearchSubscription);
        recordGridDataManager.disconnect();
    });

    function handleBeforeUnload(event: BeforeUnloadEvent) {
        if ($isProcessingEdits$) {
            event.preventDefault();
        }
    }

    function decreaseRowExpansionRequestsCounter(params: RecordSearchParams) {
        const dynamicFieldSearchParamName = mapFieldTypeIdToDynamicFieldSearchParamName(expandableColumnsIds.at(0), true);
        const recordRowId = asUuid(params[dynamicFieldSearchParamName]);
        const currentRequestCount = rowExpansionRequestsPerRowCounter.get(recordRowId);
        if (currentRequestCount === 1) {
            rowExpansionRequestsPerRowCounter.delete(recordRowId);
        } else {
            rowExpansionRequestsPerRowCounter.set(recordRowId, currentRequestCount - 1);
        }
        rowExpansionRequestsPerRowCounter$.set(rowExpansionRequestsPerRowCounter);
    }

    function createRowInitialValuesEntries(): [FieldTypeId, SetFieldValueRequest][] {
        if (exists(referenceRecordId) && exists(referenceFieldTypeId)) {
            return [[referenceFieldTypeId, {recordId: referenceRecordId}]];
        } else {
            return [];
        }
    }

    function replaceOrAppendSubRowRecords(subRowRecords: RecordRow[]) {
        let loadedRecords = [...flatSubrows$.getValue()];
        const loadedRecordsIds = new Set(loadedRecords.map((row) => row.id));
        subRowRecords.forEach((subrow) => {
            if (loadedRecordsIds.has(subrow.id)) {
                loadedRecords = replaceAll(loadedRecords, byIdOf(subrow), subrow);
            } else {
                loadedRecords.push(subrow);
                loadedRecordsIds.add(subrow.id);
            }
        });
        flatSubrows$.next(loadedRecords);
    }

    function createDataColumns(definitions: FondFieldTypeDefinitions): ColumnDef<HierarchicalRecordRow>[] {
        return definitions.fieldTypes.sort(ascendingOrderComparator).map((column) => createDataColumn(column));
    }

    function createDataColumn(definition: FieldTypeDefinition): ColumnDef<HierarchicalRecordRow> {
        if (isNullOrUndefined(definition.fieldTypes) || definition.fieldTypes.length === 0) {
            return {
                id: definition.id,
                meta: {
                    text: definition.text,
                    columnType: COLUMN_TYPES.DATA_COLUMN,
                    editorType: asSupported(definition.editor.defaultValue.type),
                    datatype: definition.datatype,
                    enabled: definition.enabled,
                    editable: definition.editable,
                    required: definition.required,
                    staticDependentFieldTypeIds: definition.staticDependentFieldTypeIds
                },
                accessorFn: (row) => extractAllSimpleGridFields(getFieldByFieldTypeIdRecursive(row, definition.id)),
                header: () => createRender(HeaderCell, {text: definition.text, label: definition.id, fieldTypeId: definition.id}),
                cell: (ctx) => {
                    return createRender(DataCell, {
                        values: ctx.cell.getValue<GridField<SimpleGridFieldValue>[]>(),
                        column: ctx.column,
                        fieldType: definition,
                        row: ctx.row,
                        recordGridDataManager
                    });
                }
            };
        }
        return {
            id: definition.id,
            meta: {text: definition.text, columnType: COLUMN_TYPES.TOP_COLUMN},
            header: () => createRender(HeaderCell, {text: definition.text, label: definition.id, fieldTypeId: definition.id}),
            columns: definition.fieldTypes.map((subColumn) => createDataColumn(subColumn))
        };
    }

    function createControlColumn(fonds: Fond[]): DisplayColumnDef<HierarchicalRecordRow> {
        const shouldRenderFooter = includeAddNewRow && isNotEmpty(fonds);
        return {
            id: 'EDIT',
            meta: {text: '', columnType: COLUMN_TYPES.TOP_COLUMN},
            enableHiding: false,
            header: () => createRender(HeaderCell, {text: '', label: ''}),
            cell: ({row}) => createRender(controlCellComponent ?? ControlCell, {
                recordGridDataManager,
                row,
                rowExpansionEnabled: isExpandable,
                rowsWithSubrowsLoading$
            }),
            ...shouldRenderFooter ? {
                footer: () => createRender(ControlFooterCell, {
                    recordGridDataManager,
                    insertableFonds: fonds
                })
            } : {}
        };
    }

    function createTableOptions(definitions: FondFieldTypeDefinitions, fonds: Fond[]) {
        const optionsData: TableOptions<HierarchicalRecordRow> = {
            data: [],
            columns: [createControlColumn(fonds), ...createDataColumns(definitions)],
            getSubRows: (row) => row.subrows ?? [],
            getCoreRowModel: getCoreRowModel(),
            getExpandedRowModel: getExpandedRowModel(),
            getRowCanExpand: (row) => !expandedRecordRowsIds.has(row.original.id) || (row.original.subrows?.length ?? 0) > 0,
            getRowId: defaultRowId(),
            onExpandedChange: handleOnExpandedChange,
            onColumnPinningChange: handleOnColumnPinningChange,
            onColumnSizingChange: handleOnColumnSizingChange,
            columnResizeDirection: 'ltr',
            onColumnVisibilityChange: handleOnColumnVisibilityChange,
            state: {
                columnPinning,
                columnSizing,
                columnVisibility
            },
            defaultColumn: {
                cell: defaultCellContent(),
                enableResizing: true,
                enablePinning: true,
                enableHiding: true
            },
            manualPagination: true
        };

        if (pinControlColumn && (!exists(columnPinning.left) || !columnPinning.left.includes('EDIT'))) {
            columnPinning.left = [...(columnPinning.left ?? []), 'EDIT'];
        }

        return writable<TableOptions<HierarchicalRecordRow>>(optionsData);
    }

    let expandedTableState: ExpandedState = {};

    function handleOnExpandedChange(updater: Updater<ExpandedState>) {
        const currentExpandedRows = new Set(Object.keys(expandedTableState));
        expandedTableState = getStateOf(expandedTableState, updater);
        const updatedExpandedRows = new Set(Object.keys(expandedTableState));
        const rowsToExpand = Array.from(updatedExpandedRows.difference(currentExpandedRows));
        loadSubrowsForRows(rowsToExpand);
        updateTableState(options, {expanded: expandedTableState});
    }

    function loadSubrowsForRows(rowsIdsToExpand: string[]) {
        if (rowsIdsToExpand.length === 0) {
            return;
        }
        assertTrue(isExpandable, 'Grid should be expandable, if there are expanded rows');

        rowsIdsToExpand.forEach((rowId) => {
            const recordRowId = parseRecordIdFromRowId(rowId);
            if (expandedRecordRowsIds.has(recordRowId)) {
                return;
            }
            const dynamicFieldParamName = mapFieldTypeIdToDynamicFieldSearchParamName(expandableColumnsIds.at(0), true);
            subrowSearchManager.newSearchWithPartialParams({[dynamicFieldParamName]: recordRowId});
            expandedRecordRowsIds.add(recordRowId);
            const currentRequestCount = rowExpansionRequestsPerRowCounter.get(recordRowId) ?? 0;
            rowExpansionRequestsPerRowCounter.set(recordRowId, currentRequestCount + 1);
            rowExpansionRequestsPerRowCounter$.set(rowExpansionRequestsPerRowCounter);
        });
    }

    function handleOnColumnPinningChange(updater: Updater<ColumnPinningState>) {
        columnPinning = getStateOf(columnPinning, updater);
        updateTableState(options, {columnPinning});
    }

    function handleOnColumnSizingChange(updater: Updater<ColumnSizingState>) {
        columnSizing = getStateOf(columnSizing, updater);
        updateTableState(options, {columnSizing});
    }

    function handleOnColumnVisibilityChange(updater: Updater<VisibilityState>) {
        columnVisibility = getStateOf(columnVisibility, updater);
        updateTableState(options, {columnVisibility});
    }

    function handleDuplicateRow(contextElementRef: Element) {
        const row = tryToFindRowByElement(contextElementRef, $table$);
        if (isNull(row)) {
            return;
        }
        recordGridDataManager.duplicateRow(row);
    }

    function handleDeleteRow(contextElementRef: Element) {
        const row = tryToFindRowByElement(contextElementRef, $table$);
        if (isNull(row)) {
            return;
        }
        recordGridDataManager.deleteRow(row);
    }

    function handleCopyCell(contextElementRef: Element) {
        const cellElement = tryToFindGridCell(contextElementRef);
        if (isNullOrUndefined(cellElement)) {
            return;
        }
        recordGridDataManager.copyCell(extractCoordinates(cellElement));
    }

    function handlePasteCell(contextElementRef: Element) {
        const cellElement = tryToFindGridCell(contextElementRef);
        if (isNullOrUndefined(cellElement)) {
            return;
        }
        recordGridDataManager.pasteCell(extractCoordinates(cellElement));
    }

    function handleCutCell(contextElementRef: Element) {
        const cellElement = tryToFindGridCell(contextElementRef);
        if (isNullOrUndefined(cellElement)) {
            return;
        }
        recordGridDataManager.cutCell(extractCoordinates(cellElement));
    }

    function handleDeleteCell(contextElementRef: Element) {
        const cellElement = tryToFindGridCell(contextElementRef);
        if (isNullOrUndefined(cellElement)) {
            return;
        }
        recordGridDataManager.deleteCell(extractCoordinates(cellElement));
    }
</script>

<svelte:window on:beforeunload={handleBeforeUnload}/>

<div class="kp-record-grid-display">
    <KpPageableSearchResults pagination="{searchManager.getPagination()}"
                             showPageButtons="{exists($lastSearch) && exists($lastSearch.features) && $lastSearch.features.includes('randomPageAccessing')}"
                             loadNextButtonAdditionalClasses="load-next-results-btn"
                             loadPreviousButtonAdditionalClasses="load-previous-results-btn"
                             showEmptyResultsMessage="{false}"
                             noRefreshLoading>

        <KpSearchFieldTypeFiltersContainer fondId="{fond.id}" {fieldTypeDefinitions}>
            <KpGenericGrid {table$} {expandedFirstLevel} additionalClasses="record-grid" additionalContainerClasses="record-grid-container">
                <svelte:fragment slot="grid-context-menu" let:contextElementRef>
                    <KpContextMenuButton icon="copy" on:click={() => handleCopyCell(contextElementRef)}>
                        {localize(/* @kp-localization commons.Zkopirovat */ 'commons.Zkopirovat')}
                        buňku
                    </KpContextMenuButton>

                    <KpContextMenuButton icon="paste" on:click={() => handlePasteCell(contextElementRef)}>
                        {localize(/* @kp-localization commons.Paste */ 'commons.Paste')}
                        buňku
                    </KpContextMenuButton>

                    <KpContextMenuButton icon="scissors" on:click={() => handleCutCell(contextElementRef)}>
                        {localize(/* @kp-localization commons.Cut */ 'commons.Cut')}
                        buňku
                    </KpContextMenuButton>

                    <KpContextMenuButton icon="trash" on:click={() => handleDeleteCell(contextElementRef)}>
                        {localize(/* @kp-localization commons.Smazat */ 'commons.Smazat')}
                        buňku
                    </KpContextMenuButton>

                    <KpHorizontalSeparator width="100%"/>

                    <KpContextMenuButton icon="duplicate" on:click={() => handleDuplicateRow(contextElementRef)}>
                        Duplikovat řádek
                    </KpContextMenuButton>

                    <KpContextMenuButton icon="trash" on:click={() => handleDeleteRow(contextElementRef)}>
                        Smazat řádek
                    </KpContextMenuButton>
                </svelte:fragment>
            </KpGenericGrid>
        </KpSearchFieldTypeFiltersContainer>
    </KpPageableSearchResults>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .kp-record-grid-display {
        display: flex;
        flex-direction: column;
    }

    :global {
        .kp-record-grid-display {
            .add-new-record-btn {
                height: 40px;
            }

            .record-grid-container {
                border: 1px solid @themed-border-muted;
            }

            .load-next-results-btn,
            .load-previous-results-btn {
                width: 25%;
                min-width: 200px;
                align-self: center;
            }
        }
    }
</style>
