import type {Auth, SdiRequest} from 'typings/portaro.be.types';
import type SdiRequestsDataService from './sdi-requests.data-service';
import type CurrentAuthService from 'shared/services/current-auth.service';

export class KpSdiRequestsPresenter {
    public static presenterName = 'kpSdiRequestsPresenter';

    public readonly showAllOption: boolean;

    /*@ngInject*/
    constructor(private sdiRequestsDataService: SdiRequestsDataService, private currentAuth: Auth, currentAuthService: CurrentAuthService) {
        this.showAllOption = currentAuthService.hasAnyRole('ROLE_ADMIN', 'ROLE_LIBRARIAN');
    }

    public async loadSdiRequests(showAll: boolean): Promise<SdiRequest[]> {
        return this.sdiRequestsDataService.query(showAll ? null : this.currentAuth.activeUser.id);
    }
}