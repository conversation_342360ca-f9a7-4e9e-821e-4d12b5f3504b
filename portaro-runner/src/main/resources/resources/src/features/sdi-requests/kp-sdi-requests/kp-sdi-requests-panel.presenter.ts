import type SdiRequestsDataService from '../sdi-requests.data-service';
import type {SdiRequest, User} from 'typings/portaro.be.types';

export class KpSdiRequestsPanelPresenter {
    public static presenterName = 'kpSdiRequestsPanelPresenter';

    /*@ngInject*/
    constructor(private sdiRequestsDataService: SdiRequestsDataService) {
    }

    public async loadRequests(user: User): Promise<SdiRequest[]> {
        return this.sdiRequestsDataService.query(user.id);
    }
}