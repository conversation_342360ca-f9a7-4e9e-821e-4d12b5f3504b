<script lang="ts">
    import type {SdiRequest} from 'typings/portaro.be.types';
    import {KpSdiRequestsPresenter} from './kp-sdi-requests.presenter';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {onMount} from 'svelte';
    import {fade} from 'svelte/transition';
    import {flip} from 'svelte/animate';
    import {popInAnim, popOutAnim} from 'shared/animations/pop-animations';
    import {removeAll, byReference} from 'shared/utils/array-utils';
    import KpSdiRequestListItem from './kp-sdi-requests/KpSdiRequestListItem.svelte';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import KpLoadingInline from 'shared/components/kp-loading/KpLoadingInline.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    const localize = getLocalization();
    const presenter = getInjector().getByToken<KpSdiRequestsPresenter>(KpSdiRequestsPresenter.presenterName);
    const requestsListFlipAnimationParams = {duration: 250};

    let loading = true;
    let showAll = presenter.showAllOption;
    let requests: SdiRequest[] = [];

    onMount(async () => {
        requests = await loadSdiRequests();
    });

    const handleShowAllUsersClick = async () => {
        showAll = !showAll;
        requests = await loadSdiRequests();
    };

    const handleModelChange = async () => {
        requests = await loadSdiRequests();
    };

    const handleRequestRemove = (event: CustomEvent<SdiRequest>) => {
        requests = removeAll(requests, byReference(event.detail));
    };

    async function loadSdiRequests(): Promise<SdiRequest[]> {
        loading = true;
        const loadedRequests = await presenter.loadSdiRequests(showAll);
        loading = false;
        return loadedRequests;
    }
</script>

<div class="container uc-sdi-requests">
    <div class="title-row">
        <div class="title-loader-container">
            <KpHeading type="h1">{localize(/* @kp-localization sdi.SdiRequests */ 'sdi.SdiRequests')}</KpHeading>

            {#if loading}
                <div transition:fade={{duration: 250}}>
                    <KpLoadingInline size="sm"/>
                </div>
            {/if}
        </div>

        {#if presenter.showAllOption}
            <div class="show-all-switch">
                <KpValueEditor type="boolean"
                               editorId="show-for-all-users-switch"
                               bind:model={showAll}
                               on:model-change={() => handleModelChange()}/>

                <label for="show-for-all-users-switch">
                    <button class="unset-style" on:click={handleShowAllUsersClick}>
                        {localize(/* @kp-localization commons.ShowForAllUsers */ 'commons.ShowForAllUsers')}
                    </button>
                </label>
            </div>
        {/if}
    </div>

    {#if !loading || requests.length > 0}
        {#each requests as request(request.id)}
            <div class="panel panel-default"
                 animate:flip={requestsListFlipAnimationParams}
                 in:popInAnim={{key: request.id}}
                 out:popOutAnim={{key: request.id}}>

                <div class="panel-body">
                    <KpSdiRequestListItem {request}
                                          showUser="{true}"
                                          on:request-remove={handleRequestRemove}/>
                </div>
            </div>
        {/each}

        {#if requests.length === 0}
            <div class="text-muted text-center">
                {localize(/* @kp-localization commons.ZadnePolozky */ 'commons.ZadnePolozky')}
            </div>
        {/if}
    {/if}
</div>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    .uc-sdi-requests {
        display: flex;
        flex-direction: column;

        .title-row {
            display: flex;
            align-items: center;
            margin: 50px 0 35px;

            .title-loader-container {
                display: flex;
                align-items: center;
                gap: @padding-large-horizontal;
            }

            .show-all-switch {
                flex: 1;
                display: flex;
                align-items: baseline;
                justify-content: end;
                gap: @padding-small-horizontal;
                margin-top: 0;

                label {
                    cursor: pointer;
                    margin-bottom: 0;
                }
            }

            @media (max-width: @screen-xs-max) {
                align-items: start;
                flex-direction: column;
                gap: @padding-base-vertical;

                .show-all-switch {
                    justify-content: start;
                }

                .title-loader-container {
                    width: 100%;
                }
            }
        }
    }
</style>