import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function sdiRequestsRoutes($stateProvider: StateProvider) {
    let sdiRequestsModule: { default: any; };
    $stateProvider
        .state({
            name: 'sdi-requests',
            url: '/sdi-requests',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => sdiRequestsModule.default,
            },
            lazyLoad: async () => {
                sdiRequestsModule = await import(/* webpackChunkName: "sdiRequests" */ './KpSdiRequests.svelte');
                return null;
            }
        });
}