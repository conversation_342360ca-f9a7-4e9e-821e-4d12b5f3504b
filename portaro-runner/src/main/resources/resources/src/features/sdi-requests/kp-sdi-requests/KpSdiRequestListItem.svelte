<script lang="ts">
    import {getDateFormatter, getInjector, getLocalization} from 'core/svelte-context/context';
    import {KpSdiRequestListItemPresenter} from './kp-sdi-request-list-item.presenter';
    import type {SdiRequest, SdiSending} from 'typings/portaro.be.types';
    import {pipe} from 'core/utils';
    import {encodeUri, loc, strParams} from 'shared/utils/pipes';
    import Label from 'shared/components/kp-label/Label.svelte';
    import {Kind} from 'shared/constants/portaro.constants';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpActionRequestButton from 'shared/components/kp-action-request-button/KpActionRequestButton.svelte';
    import {fade, slide} from 'svelte/transition';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import KpButtonStyleAnchor from 'shared/ui-widgets/button/KpButtonStyleAnchor.svelte';
    import {heightResizeAnimation} from 'shared/svelte-actions/use.height-resize-animation';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import {createEventDispatcher} from 'svelte';

    export let request: SdiRequest;
    export let showUser: boolean;

    const localize = getLocalization();
    const dateFormatter = getDateFormatter();
    const dispatch = createEventDispatcher<{ 'request-remove': SdiRequest }>();
    const presenter = getInjector().getByToken<KpSdiRequestListItemPresenter>(KpSdiRequestListItemPresenter.presenterName);
    const seekingsOutSlideAnimParams = {duration: 250};

    let showSendings = false;
    let sendingsLoading = false;
    let sendings: SdiSending[] = [];

    async function loadSdiSendings(): Promise<SdiSending[]> {
        sendingsLoading = true;
        const loadedSendings = await presenter.loadSendings(request);
        sendingsLoading = false;
        return loadedSendings;
    }

    const handleToggleRequestActiveClick = async () => {
        request = {...request, active: !request.active};
        await presenter.editSdiRequest(request);
    }

    const handleRemoveRequestClick = async () => {
        await presenter.removeSdiRequest(request);
        dispatch('request-remove', request);
    }

    const handleToggleSendingsVisibilityClick = async () => {
        showSendings = !showSendings;
        sendings = await loadSdiSendings();
    }
</script>

<div class="sdi-request-item"
     class:text-success={request.active}
     class:text-muted={!request.active}>

    <div class="top-row">
        <p class:overwritten-value={!request.active}>
            <span>{@html pipe(localize(/* @kp-localization sdi.ZasilaniX */ 'sdi.ZasilaniX'), strParams(request.text))}</span>

            {#if showUser}
                <span>(<Label explicitKind="{Kind.KIND_USER}" labeled="{request.user}"/>)</span>
            {/if}

            <br/>

            <span>- {pipe(request.periodicity, loc())}</span>

            {#if request.terminationDate}
            <span>
                {localize(/* @kp-localization commons.to */ 'commons.to')}
                {pipe(request.terminationDate, dateFormatter('d.M.yyyy'))}
            </span>
            {/if}
        </p>

        {#if !request.active}
            {localize(/* @kp-localization commons.Vypnuto */ 'commons.Vypnuto')}
        {/if}
    </div>

    <p class="text-muted">
        {localize(/* @kp-localization commons.Vytvoreno */ 'commons.Vytvoreno')}
        {pipe(request.createDate, dateFormatter('d.M.yyyy'))}
    </p>

    <div class="action-buttons-row">
        <KpButton buttonSize="xs" on:click={handleToggleRequestActiveClick}>
            <IconedContent icon="{request.active ? 'cross-circle' : 'checkbox'}">
                {#if request.active}
                    {localize(/* @kp-localization commons.Vypnout */ 'commons.Vypnout')}
                {:else}
                    {localize(/* @kp-localization commons.Zapnout */ 'commons.Zapnout')}
                {/if}
            </IconedContent>
        </KpButton>

        <KpButtonStyleAnchor href="/search?finalRawQuery={pipe(request.query, encodeUri())}" buttonSize="xs">
            <IconedContent icon="search-alt">
                {localize(/* @kp-localization sdi.ZobrazitVyhledaneZaznamy */ 'sdi.ZobrazitVyhledaneZaznamy')}
            </IconedContent>
        </KpButtonStyleAnchor>

        {#if presenter.isAdministrator}
            <KpActionRequestButton buttonSize="xs" path="/api/sdi/send?request={request.id}&force=true">
                <IconedContent icon="paper-plane">Send</IconedContent>
            </KpActionRequestButton>
        {/if}

        <KpButton buttonSize="xs" on:click={handleToggleSendingsVisibilityClick}>
            <UIcon icon="{showSendings ? 'zoom-out' : 'zoom-in'}"
                   label="{showSendings ? 'Skrýt zaslané' : 'Zobrazit zaslané'}"/>
        </KpButton>

        <KpButton buttonSize="xs" buttonStyle="danger" on:click={handleRemoveRequestClick}>
            <IconedContent icon="trash">
                {localize(/* @kp-localization commons.Smazat */ 'commons.Smazat')}
            </IconedContent>
        </KpButton>
    </div>

    {#if showSendings}
        <div class="sendings-container"
             use:heightResizeAnimation
             out:slide={seekingsOutSlideAnimParams}>
            {#if sendingsLoading}
                <div class="loading-container">
                    <KpLoadingBlock size="xs"/>
                </div>
            {/if}

            {#if !sendingsLoading && sendings.length === 0}
                <div class="no-items-container text-muted text-center">
                    {localize(/* @kp-localization commons.ZadnePolozky */ 'commons.ZadnePolozky')}
                </div>
            {/if}

            {#if !sendingsLoading && sendings.length > 0}
                <ul class="sendings-list" in:fade>
                    {#each sendings as sending(sending.id)}
                        <li class="sending-list-item">
                            <span class:text-muted={sending.recordsCount === 0}>
                                {localize(/* @kp-localization commons.Vyhledano */ 'commons.Vyhledano')}
                                {pipe(sending.date, dateFormatter('d.M.yyyy HH:mm'))},
                                {pipe(localize(/* @kp-localization sdi.NalezenoXNovinek */ 'sdi.NalezenoXNovinek'), strParams(sending.recordsCount.toString()))}
                            </span>

                            {#if sending.error}
                                <span class="text-danger">Chyba</span>
                            {/if}

                            {#if presenter.isAdministrator}
                                <KpActionRequestButton buttonSize="xs" path="/api/sdi/resend?sending={sending.id}">
                                    <IconedContent icon="refresh">Resend</IconedContent>
                                </KpActionRequestButton>
                            {/if}
                        </li>
                    {/each}
                </ul>
            {/if}
        </div>
    {/if}
</div>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    .sdi-request-item {
        display: flex;
        flex-direction: column;

        .top-row {
            display: flex;
            justify-content: space-between;
        }

        .action-buttons-row {
            display: flex;
            align-items: center;
            gap: @padding-xs-horizontal;
            flex-wrap: wrap;
        }

        .sendings-container {
            margin-top: @padding-large-vertical;

            .no-items-container,
            .loading-container {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: @panel-heading-padding;
            }

            .sendings-list {
                display: flex;
                flex-direction: column;
                gap: @padding-base-vertical;
            }
        }
    }
</style>