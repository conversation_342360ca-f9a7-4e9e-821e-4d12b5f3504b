import register from '@kpsys/angularjs-register';

import dataServiceModule from 'shared/data-services/data-services.module';
import sdiRequestsRoutes from './sdi-requests.routes';
import SdiRequestsDataService from './sdi-requests.data-service';
import {KpSdiRequestsPresenter} from './kp-sdi-requests.presenter';
import {KpSdiRequestsPanelPresenter} from './kp-sdi-requests/kp-sdi-requests-panel.presenter';
import {KpSdiRequestListItemPresenter} from './kp-sdi-requests/kp-sdi-request-list-item.presenter';

/**
 * @ngdoc module
 * @name portaro.features.sdi-requests
 * @module portaro.features.sdi-requests
 *
 * @description
 */
export default register('portaro.features.sdi-requests', [dataServiceModule])
    .config(sdiRequestsRoutes)
    .service(SdiRequestsDataService.serviceName, SdiRequestsDataService)
    .service(KpSdiRequestsPresenter.presenterName, KpSdiRequestsPresenter)
    .service(KpSdiRequestsPanelPresenter.presenterName, KpSdiRequestsPanelPresenter)
    .service(KpSdiRequestListItemPresenter.presenterName, KpSdiRequestListItemPresenter)
    .name();