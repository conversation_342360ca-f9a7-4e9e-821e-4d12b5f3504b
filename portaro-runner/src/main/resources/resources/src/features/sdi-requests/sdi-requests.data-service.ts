import {ngAsync} from 'shared/utils/ng-@decorators';
import {transferify} from 'shared/utils/data-service-utils';
import type {
    ActionResponse,
    SdiRequest,
    SdiRequestCreationRequest,
    SdiRequestEditRequest,
    SdiSending
} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';

export default class SdiRequestsDataService {
    public static serviceName = 'sdiRequestsDataService';

    private readonly SDI_REQUESTS_ROUTE = 'sdi-requests';
    private readonly SDI_REQUESTS_CREATE_ROUTE = 'sdi-requests/create';
    private readonly SDI_REQUESTS_EDIT_ROUTE = 'sdi-requests/edit';
    private readonly SDI_SENDINGS_ROUTE = 'sdi-sendings';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async query(userId?: number): Promise<SdiRequest[]> {
        const params = userId ? {user: userId} : undefined;

        return this.ajaxService
            .createRequest(this.SDI_REQUESTS_ROUTE)
            .get(params);
    }

    @ngAsync()
    public async createSdiRequest(sdiRequestCreationRequest: SdiRequestCreationRequest): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${this.SDI_REQUESTS_CREATE_ROUTE}`)
            .post(transferify(sdiRequestCreationRequest));
    }

    @ngAsync()
    public async editSdiRequest(sdiRequestEditRequest: SdiRequestEditRequest): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${this.SDI_REQUESTS_EDIT_ROUTE}`)
            .post(transferify(sdiRequestEditRequest));
    }

    @ngAsync()
    public async removeSdiRequest(sdiRequestId: number): Promise<ActionResponse> {
        return this.ajaxService
            .createRequest(`${this.SDI_REQUESTS_ROUTE}/${sdiRequestId}`)
            .delete();
    }

    @ngAsync()
    public async querySendings(sdiRequestId: number): Promise<SdiSending[]> {
        return this.ajaxService
            .createRequest(this.SDI_SENDINGS_ROUTE)
            .get({request: sdiRequestId});
    }
}