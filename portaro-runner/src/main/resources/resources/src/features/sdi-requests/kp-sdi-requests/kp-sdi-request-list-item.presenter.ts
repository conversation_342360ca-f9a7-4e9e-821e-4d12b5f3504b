import type SdiRequestsDataService from '../sdi-requests.data-service';
import type {SdiRequest, SdiSending} from 'typings/portaro.be.types';
import type CurrentAuthService from 'shared/services/current-auth.service';

export class KpSdiRequestListItemPresenter {
    public static presenterName = 'kpSdiRequestListItemPresenter';

    /*@ngInject*/
    constructor(private sdiRequestsDataService: SdiRequestsDataService, private currentAuthService: CurrentAuthService) {
    }

    public async editSdiRequest(request: SdiRequest): Promise<void> {
        await this.sdiRequestsDataService.editSdiRequest(request);
    }

    public async removeSdiRequest(request: SdiRequest): Promise<void> {
        await this.sdiRequestsDataService.removeSdiRequest(request.id);
    }

    public async loadSendings(request: SdiRequest): Promise<SdiSending[]> {
        return this.sdiRequestsDataService.querySendings(request.id);
    }

    public get isAdministrator(): boolean {
        return this.currentAuthService.isLoggedIn() && this.currentAuthService.hasRole('ROLE_ADMIN');
    }
}