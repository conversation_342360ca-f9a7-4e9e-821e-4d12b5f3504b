<script lang="ts">
    import type {UUID} from 'typings/portaro.be.types';
    import {MENTION_CLASS, MENTION_ID_ATTRIBUTE, MENTION_USER_CLASS} from 'src/features/threads/thread-content/composer/constants';

    export let uuid: UUID;
    export let name: string;
    export let userMention = false;
</script>

<a class="{MENTION_CLASS} {userMention ? MENTION_USER_CLASS : ''}"
   href="/#!/records/{uuid}"
   contenteditable="false"
   {...{[MENTION_ID_ATTRIBUTE]: uuid}}>

    {name}
</a>