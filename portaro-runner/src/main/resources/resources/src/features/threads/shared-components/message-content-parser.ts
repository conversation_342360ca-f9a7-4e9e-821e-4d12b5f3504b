import type {LabeledRecordRef} from 'typings/portaro.be.types';
import type {MarkdownPattern, ParsedContentPart} from '../thread-content/composer/types';
import {exists} from 'shared/utils/custom-utils';
import {MARKDOWN_PATTERNS} from 'src/features/threads/thread-content/composer/markdown';
import {
    MENTION_OTHER_PREFIX,
    MENTION_USER_FOND_ID,
    MENTION_USER_PREFIX
} from 'src/features/threads/thread-content/composer/constants';

export function parseMessageContentWithMentions(messageContent: string, mentions: LabeledRecordRef[]): ParsedContentPart[] {
    if (!messageContent.trim()) {
        return [{content: messageContent, isMention: false}];
    }

    return applyMarkdownFormattingToParts(replaceMentionsWithData(messageContent, mentions));
}

function replaceMentionsWithData(content: string, mentions: LabeledRecordRef[]): ParsedContentPart[] {
    if (!mentions.length) {
        return [{content, isMention: false}];
    }

    const parts: ParsedContentPart[] = [];
    const mentionRegex = /@([a-f0-9-]{36})/g;
    let lastIndex = 0;
    let match: RegExpExecArray | null;

    while ((match = mentionRegex.exec(content)) !== null) {
        // Add text before mention
        if (match.index > lastIndex) {
            const beforeText = content.substring(lastIndex, match.index);

            if (beforeText) {
                parts.push({
                    content: beforeText,
                    isMention: false
                });
            }
        }

        // Add mention
        const uuid = match[1];
        const mention = mentions.find((m: LabeledRecordRef): boolean => m.id === uuid);

        if (exists(mention)) {
            const isUser = mention.fond.id === MENTION_USER_FOND_ID;
            const prefix = isUser ? MENTION_USER_PREFIX : MENTION_OTHER_PREFIX;

            parts.push({
                content: `${prefix}${mention.text}`,
                isMention: true,
                mentionData: {
                    uuid: mention.id,
                    name: `${prefix}${mention.text}`,
                    userMention: isUser
                }
            });
        } else {
            // If mention not found, keep original text
            parts.push({
                content: match[0],
                isMention: false
            });
        }

        lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < content.length) {
        const remainingText = content.substring(lastIndex);
        if (remainingText) {
            parts.push({
                content: remainingText,
                isMention: false
            });
        }
    }

    if (parts.length === 0) {
        parts.push({
            content,
            isMention: false
        });
    }

    return parts;
}

function applyMarkdownFormattingToParts(parts: ParsedContentPart[]): ParsedContentPart[] {
    return parts.map((part: ParsedContentPart): ParsedContentPart => {
        if (part.isMention) {
            return part;
        } else {
            return {
                ...part,
                content: applyMarkdownToText(part.content)
            };
        }
    });
}

function applyMarkdownToText(text: string): string {
    if (!text.trim()) {
        return text;
    }

    let html = text;

    const sortedPatterns = [...MARKDOWN_PATTERNS].sort((a: MarkdownPattern, b: MarkdownPattern): number => b.priority - a.priority);

    for (const pattern of sortedPatterns) {
        pattern.regex.lastIndex = 0;

        html = html.replace(pattern.regex, (_match: string, _symbols: string, content: string): string => {
            return `<${pattern.tagName} class="${pattern.className}">${content}</${pattern.tagName}>`;
        });
    }

    return html;
}