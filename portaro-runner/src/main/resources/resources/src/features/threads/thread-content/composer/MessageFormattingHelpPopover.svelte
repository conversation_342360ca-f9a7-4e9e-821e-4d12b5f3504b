<script lang="ts">
    import KpPopover from 'shared/ui-widgets/popover/KpPopover.svelte';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';
</script>

<KpPopover placement="top-start">
    <svelte:fragment slot="whole-button" let:buttonId let:panelId let:floatingRef>
        <KpIconButton icon="question"
                      id="{buttonId}"
                      popovertarget="{panelId}"
                      use={[floatingRef]}/>
    </svelte:fragment>

    <svelte:fragment slot="popover-title">
        <span class="heading-title">Nápověda k formátování</span>
    </svelte:fragment>

    <Flex slot="popover-content" direction="column" gap="m" class="message-formatting-help">
        <Flex direction="column" as="section" gap="s" class="help-item">
            <span class="help-item-title">Označen<PERSON> už<PERSON>ů a záznamů</span>

            <span class="help-item-description">
                Uživatele lze označit pomocí znaku <code>@</code><br/>
                a ostatní záznamy pomocí znaku <code>#</code>.
            </span>
        </Flex>

        <Flex direction="column" as="section" gap="s" class="help-item">
            <span class="help-item-title">Formátování textu</span>

            <span class="help-item-description">
                <em>Kurzíva</em>: <code>*text*</code> nebo <code>_text_</code><br/>
                <strong>Tučně</strong>: <code>**text**</code> nebo <code>__text__</code><br/>
                <strong><em>Tučně a kurzíva</em></strong>: <code>***text***</code> nebo <code>___text___</code><br/>
                <del>Přeškrtnutý text</del>: <code>~~text~~</code><br/>
                <code class="markdown-code">Jednořádnový kód</code>: <code>`text`</code>
            </span>
        </Flex>
    </Flex>
</KpPopover>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .heading-title,
    .help-item-title {
        font-weight: 500;
    }

    .markdown-code {
        background-color: @themed-panel-bg;
        border-radius: @border-radius-sm;
        color: @themed-text-default;
        padding: 1px 3px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.9em;
    }
</style>