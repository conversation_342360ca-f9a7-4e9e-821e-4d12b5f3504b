import type {Message} from 'typings/portaro.be.types';
import type {Thread, ThreadMessagesSearchParams, ThreadUser} from 'src/features/threads/threads.types';
import type {ThreadsDataService} from './threads.data-service';
import type {SearchManagerBuilderFactoryService} from 'src/features/search/search-manager/search-manager-factory.service';
import type {SearchManager} from 'src/features/search/search-manager/search-manager';
import type {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
import type {ThreadsContextService} from 'src/features/threads/services/threads-context.service';
import type FinishedResponseInteractionService from 'src/shared/services/finished-response-interaction.service';
import type CurrentAuthService from 'src/shared/services/current-auth.service';
import {Kind, SearchType} from 'shared/constants/portaro.constants';

export class ThreadContentService {
    public static serviceName = 'threadContentService';

    /*@ngInject*/
    constructor(private threadsDataService: ThreadsDataService,
                private finishedResponseInteractionService: FinishedResponseInteractionService,
                private currentAuthService: CurrentAuthService,
                private searchManagerBuilderFactoryService: SearchManagerBuilderFactoryService,
                private toastMessageService: ToastMessageService,
                private threadsContextService: ThreadsContextService) {
    }

    public setupThreadSearchManager(thread: Thread): SearchManager<Message, ThreadMessagesSearchParams> {
        const messagesSearchManagerBuilder = this.searchManagerBuilderFactoryService.createBuilder<ThreadMessagesSearchParams, Message>()
            .withStaticParams(this.createStaticSearchParams(thread));

        return messagesSearchManagerBuilder.createLocalSearch(true);
    }

    private createStaticSearchParams(thread: Thread): Record<string, any> {
        return {
            kind: [Kind.KIND_MESSAGE],
            type: SearchType.TYPE_MESSAGE_SEARCH,
            pageSize: 50,
            thread,
            sorting: '-id'
        } as Record<string, any>;
    }

    public async createThreadMessage(thread: Thread, content: string): Promise<Message | null> {
        const currentUser = this.currentAuthService.getCurrentAuthValue().activeUser;

        try {
            return await this.threadsDataService.createNewThreadMessage({
                thread,
                content,
                sender: currentUser
            });
        } catch (errorException) {
            this.finishedResponseInteractionService.showFailedResponseInToast(errorException);
            return null;
        }
    }

    public async addThreadParticipants(thread: Thread, newParticipants: ThreadUser[]): Promise<void> {
        try {
            await this.threadsDataService.addThreadParticipants({
                thread,
                participants: newParticipants
            });

            this.toastMessageService.showSuccess('Uživatelé byli úspěšně přidáni do konverzace');
        } catch (errorException) {
            this.finishedResponseInteractionService.showFailedResponseInToast(errorException);
        }
    }

    public async removeThreadParticipants(thread: Thread, removedParticipants: ThreadUser[]): Promise<void> {
        try {
            await this.threadsDataService.removeThreadParticipants({
                thread,
                participants: removedParticipants
            });

            this.toastMessageService.showSuccess('Uživatelé byli úspěšně odebráni z konverzace');
        } catch (errorException) {
            this.finishedResponseInteractionService.showFailedResponseInToast(errorException);
        }
    }

    public async editThread(thread: Thread, newName: string): Promise<void> {
        try {
            await this.threadsDataService.updateThread({
                thread,
                name: newName
            });

            this.toastMessageService.showSuccess('Název konverzace byl úspěšně změněn');
        } catch (errorException) {
            this.finishedResponseInteractionService.showFailedResponseInToast(errorException);
        }
    }

    public async updateThread(thread: Thread): Promise<void> {
        await this.threadsContextService.updateThreadInContext(thread);
    }
}