import type {MentionState, DOMPosition, MentionSearchType} from './types';
import type {Rec} from 'typings/portaro.be.types';
import {exists} from 'shared/utils/custom-utils';
import {
    MENTION_CLASS,
    MENTION_ID_ATTRIBUTE,
    MENTION_OTHER_PREFIX,
    MENTION_USER_CLASS,
    MENTION_USER_PREFIX
} from './constants';

export function getCurrentSelectionRange(): Range | null {
    const selection = window.getSelection();
    return selection && selection.rangeCount > 0 ? selection.getRangeAt(0).cloneRange() : null;
}

export function isInsideMention(range: Range): boolean {
    const node = range.startContainer;
    const parent = node instanceof Element ? node : node.parentElement;
    return parent?.closest(`.${MENTION_CLASS}`) !== null;
}

export function extractRawText(container: HTMLElement): string {
    let result = '';

    const iterator = document.createNodeIterator(
        container,
        NodeFilter.SHOW_ALL,
        {
            acceptNode(node: Node): number {
                if (
                    node.nodeType === Node.TEXT_NODE &&
                    node.parentElement?.classList.contains(MENTION_CLASS)
                ) {
                    return NodeFilter.FILTER_REJECT;
                }
                return NodeFilter.FILTER_ACCEPT;
            }
        }
    );

    let currentNode: Node | null;
    while ((currentNode = iterator.nextNode())) {
        if (currentNode.nodeType === Node.ELEMENT_NODE) {
            const el = currentNode as HTMLElement;
            if (el.classList.contains(MENTION_CLASS)) {
                const id = el.getAttribute(MENTION_ID_ATTRIBUTE);
                if (id) {
                    result += `@${id}`;
                }
            }
        } else if (currentNode.nodeType === Node.TEXT_NODE) {
            result += currentNode.textContent ?? '';
        }
    }

    return result.trim();
}

export function insertMentionIntoEditor(
    contentEditableDiv: HTMLDivElement,
    mentionQuery: string,
    mentionedRecord: Rec,
    searchType: MentionSearchType,
    currentRange: Range
): boolean {
    const selection = window.getSelection();
    if (!exists(selection)) {
        return false;
    }

    const mentionPrefix = searchType === 'user' ? MENTION_USER_PREFIX : MENTION_OTHER_PREFIX;
    const fullMentionText = mentionPrefix + mentionQuery;

    const textBeforeCaret = extractTextBeforeCaret(contentEditableDiv, currentRange);
    const mentionStartIndex = textBeforeCaret.lastIndexOf(fullMentionText);

    if (mentionStartIndex === -1) {
        return false;
    }

    const targetPosition = findDOMPositionForTextIndex(contentEditableDiv, mentionStartIndex);
    if (!exists(targetPosition)) {
        return false;
    }

    const deleteRange = document.createRange();
    deleteRange.setStart(targetPosition.node, targetPosition.offset);
    deleteRange.setEnd(currentRange.endContainer, currentRange.endOffset);
    deleteRange.deleteContents();

    const mentionSpan = createMentionSpan(mentionedRecord, searchType);
    deleteRange.insertNode(mentionSpan);

    const space = document.createTextNode(' ');
    mentionSpan.after(space);

    const newRange = document.createRange();
    newRange.setStartAfter(space);
    newRange.setEndAfter(space);

    selection.removeAllRanges();
    selection.addRange(newRange);

    return true;
}

export function detectMentionState(contentEditableDiv: HTMLDivElement, range: Range): MentionState {
    const textBeforeCaret = extractTextBeforeCaret(contentEditableDiv, range);
    const otherRecordsMatch = buildMentionRegex(MENTION_OTHER_PREFIX).exec(textBeforeCaret);
    const userRecordsMatch = buildMentionRegex(MENTION_USER_PREFIX).exec(textBeforeCaret);

    if (exists(userRecordsMatch)) {
        return {
            query: userRecordsMatch[1],
            type: 'user'
        };
    }

    if (exists(otherRecordsMatch)) {
        return {
            query: otherRecordsMatch[1],
            type: 'other'
        };
    }

    return {
        query: '',
        type: null
    };
}

function findDOMPositionForTextIndex(container: HTMLElement, textIndex: number): DOMPosition | null {
    let currentIndex = 0;
    const walker = document.createTreeWalker(container, NodeFilter.SHOW_TEXT, null);
    let currentNode: Node | null;

    while ((currentNode = walker.nextNode())) {
        const nodeLength = (currentNode.textContent || '').length;

        if (currentIndex + nodeLength > textIndex) {
            return {
                node: currentNode,
                offset: textIndex - currentIndex
            };
        }

        currentIndex += nodeLength;
    }

    return null;
}

function createMentionSpan(mentionedRecord: Rec, searchType: MentionSearchType): HTMLSpanElement {
    const mentionSpan = document.createElement('span');
    const prefix = searchType === 'user' ? MENTION_USER_PREFIX : MENTION_OTHER_PREFIX;
    mentionSpan.textContent = `${prefix}${mentionedRecord.name}`;
    mentionSpan.contentEditable = 'false';
    mentionSpan.className = MENTION_CLASS;

    if (searchType === 'user') {
        mentionSpan.classList.add(MENTION_USER_CLASS);
    }

    mentionSpan.setAttribute(MENTION_ID_ATTRIBUTE, mentionedRecord.id);

    return mentionSpan;
}

function extractTextBeforeCaret(contentEditableDiv: HTMLDivElement, range: Range): string {
    const preCaretRange = range.cloneRange();
    preCaretRange.selectNodeContents(contentEditableDiv);
    preCaretRange.setEnd(range.endContainer, range.endOffset);
    return preCaretRange.toString();
}

function buildMentionRegex(prefix: string): RegExp {
    return new RegExp(`(?:^|\\s)${prefix}(\\S*)$`);
}