import type {Rec, UUID} from 'src/typings/portaro.be.types';
import type {Thread, ThreadUser} from 'src/features/threads/threads.types';
import type {ThreadsDataService} from './threads.data-service';
import type {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
import type FinishedResponseInteractionService from 'src/shared/services/finished-response-interaction.service';
import type CurrentAuthService from 'src/shared/services/current-auth.service';

export class ThreadsService {
    public static readonly serviceName = 'threadsService';

    /*@ngInject*/
    constructor(private threadsDataService: ThreadsDataService,
                private finishedResponseInteractionService: FinishedResponseInteractionService,
                private toastMessageService: ToastMessageService,
                private currentAuthService: CurrentAuthService) {
    }

    public async getThreadById(id: UUID): Promise<Thread | null> {
        try {
            return await this.threadsDataService.getThreadById(id);
        } catch (errorException) {
            this.finishedResponseInteractionService.showFailedResponseInToast(errorException);
            return null;
        }
    }

    public async getThreadByRecord(record: Rec): Promise<Thread | null> {
        try {
            return await this.threadsDataService.getThreadByRecordId(record.id);
        } catch (errorException) {
            this.finishedResponseInteractionService.showFailedResponseInToast(errorException);
            return null;
        }
    }

    // Record thread = thread with a linked record
    public async createRecordThread(record: Rec): Promise<Thread | null> {
        try {
            const thead = await this.threadsDataService.createNewThread({
                linkedRecord: record.id,
                participants: []
            });

            this.toastMessageService.showSuccess('Konverzace byla vytvořena');
            return thead;
        } catch (errorException) {
            this.finishedResponseInteractionService.showFailedResponseInToast(errorException);
            return null;
        }
    }

    // Group thread = thread with no linked record and with an n-sized list of participants (user records)
    public async createGroupThread(name: string, participants: ThreadUser[]): Promise<Thread | null> {
        const currentUser = this.currentAuthService.getCurrentAuthValue().activeUser;

        try {
            const thead = await this.threadsDataService.createNewThread({
                linkedRecord: null,
                participants: [...participants, currentUser],
                name
            });

            this.toastMessageService.showSuccess('Skupinová konverzace byla vytvořena');
            return thead;
        } catch (errorException) {
            this.finishedResponseInteractionService.showFailedResponseInToast(errorException);
            return null;
        }
    }

    // Private thread = thread between user <-> user
    public async createPrivateThread(name: string, otherUser: ThreadUser): Promise<Thread | null> {
        try {
            const thead = await this.threadsDataService.createNewThread({
                linkedRecord: null,
                participants: [otherUser],
                name
            });

            this.toastMessageService.showSuccess('Soukromá konverzace byla vytvořena');
            return thead;
        } catch (errorException) {
            this.finishedResponseInteractionService.showFailedResponseInToast(errorException);
            return null;
        }
    }
}