import type {Message} from 'typings/portaro.be.types';
import type {Thread, ThreadMessagesSearchParams} from 'src/features/threads/threads.types';
import type {Readable} from 'svelte/store';
import type {ThreadContentService} from 'src/features/threads/services/thread-content.service';
import type {SearchManager} from 'src/features/search/search-manager/search-manager';
import {getContext, hasContext, setContext} from 'svelte';
import {writable} from 'svelte/store';

const THREAD_CONTENT_CONTEXT_KEY = 'thread-content-context-key';

interface ThreadContentContext {
    service: ThreadContentService;
    thread: Thread;
    settingsOpened$: Readable<boolean>;
    searchManager: SearchManager<Message, ThreadMessagesSearchParams>;
}

export function createThreadContentContext(service: ThreadContentService, thread: Thread): ThreadContentContext {
    const settingsOpened = writable(false);

    return setContext<ThreadContentContext>(THREAD_CONTENT_CONTEXT_KEY, {
        service,
        thread,
        settingsOpened$: settingsOpened,
        searchManager: service.setupThreadSearchManager(thread)
    });
}

export function getThreadContentContext(): ThreadContentContext {
    if (!hasContext(THREAD_CONTENT_CONTEXT_KEY)) {
        throw new Error('Thread content context does not exist! Use `createThreadContentContext` function to create it in a parent component.');
    }

    return getContext<ThreadContentContext>(THREAD_CONTENT_CONTEXT_KEY);
}
