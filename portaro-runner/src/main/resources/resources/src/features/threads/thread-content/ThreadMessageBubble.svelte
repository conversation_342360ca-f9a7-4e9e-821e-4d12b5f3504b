<script lang="ts">
    import type {Auth, Message} from 'typings/portaro.be.types';
    import {cleanup, exists} from 'shared/utils/custom-utils';
    import {pipe} from 'core/utils';
    import {getDateFormatter, getInjector} from 'core/svelte-context/context';
    import {onDestroy} from 'svelte';
    import CurrentAuthService from 'shared/services/current-auth.service';
    import ParsedMessageContent from 'src/features/threads/shared-components/ParsedMessageContent.svelte';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let message: Message;
    export let withoutHeader = false;

    const currentAuthService = getInjector().getByClass(CurrentAuthService);
    const dateFormatter = getDateFormatter();

    let currentAuth: Auth;
    const currentAuthSubscription = currentAuthService.currentAuth$().subscribe((auth) => currentAuth = auth);

    $: selfMessage = message.senderUser.id === currentAuth?.activeUser?.id;

    onDestroy(() => {
        cleanup(currentAuthSubscription);
    });
</script>

<div class="thread-message-bubble" class:with-header="{!withoutHeader}">
    {#if !selfMessage}
        <div class="user-avatar-container" class:without-header="{withoutHeader}">
            <KpUserAvatar userRecordId="{message.senderUser.rid}" sizePx="{42}"/>
        </div>
    {/if}

    {#if selfMessage}
        <span class="sent-at-date">
            {pipe(message.creationDate, dateFormatter('dd.MM.yyyy HH:mm'))}
        </span>
    {/if}

    <div class="user-name-and-message-container">
        {#if !selfMessage && !withoutHeader}
            <span class="user-name">{message.senderUser.text}</span>
        {/if}

        <div class="message" class:self-message="{selfMessage}">
            {#if exists(message.content)}
                <ParsedMessageContent messageContent={message.content} mentions={message.mentions}/>
            {:else}
                <UIcon icon="question"/>
            {/if}
        </div>
    </div>

    {#if !selfMessage}
        <span class="sent-at-date">
            {pipe(message.creationDate, dateFormatter('dd.MM.yyyy HH:mm'))}
        </span>
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "../shared-components/parsed-message-styles.less";

    .thread-message-bubble {
        max-width: 460px;
        display: flex;
        align-items: flex-start;
        gap: @spacing-ml;

        &.with-header {
            margin-top: @spacing-m;
        }

        .user-avatar-container.without-header {
            opacity: 0;
        }

        .user-name-and-message-container {
            display: flex;
            flex-direction: column;
            gap: @spacing-s;

            .user-name {
                font-weight: 500;
                white-space: nowrap;
            }

            .message {
                padding: @spacing-sm;
                white-space: pre-wrap;
                background-color: @themed-panel-bg;
                color: @themed-text-default;
                border-radius: 0 @border-radius-xl @border-radius-xl @border-radius-xl;

                :global {
                    .parsed-message-styles();
                }
            }
        }

        .sent-at-date {
            align-self: flex-end;
            font-size: 10px;
            color: @themed-text-muted;
            white-space: nowrap;
        }
    }
</style>