import type {UUID} from 'typings/portaro.be.types';
import type {StateProvider, Transition} from '@uirouter/angularjs';
import type {ThreadsService} from 'src/features/threads/services/threads.service';
import type WalkerService from 'shared/services/walker.service';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';
import {exists} from 'shared/utils/custom-utils';

/*@ngInject*/
export function threadRoutes($stateProvider: StateProvider) {
    let threadsModule: {default: any;};

    $stateProvider
        .state({
            name: 'thread',
            url: '/thread/:id',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => threadsModule.default,

                /*@ngInject*/
                props: async (
                    threadsService: ThreadsService,
                    $transition$: Transition,
                    walker: WalkerService
                ) => {
                    const thread = await threadsService.getThreadById($transition$.params().id as UUID);

                    if (!exists(thread)) {
                        await walker.newSpaPage('');
                        return null;
                    }

                    return {thread};
                }
            },
            lazyLoad: async () => {
                threadsModule = await import(/* webpackChunkName: "threads" */ './KpThreadPage.svelte');
                return null;
            }
        });
}