import type {Rec, UUID} from 'typings/portaro.be.types';

export type MentionSearchType = 'user' | 'other';

export interface MarkdownPattern {
    regex: RegExp;
    className: string;
    tagName: string;
    priority: number;
}

export interface CursorPosition {
    containerOffset: number;
    isAtEnd: boolean;
}

export interface MentionState {
    query: string;
    type: 'user' | 'other' | null;
}

export interface MarkdownMatch {
    index: number;
    length: number;
    symbols: string;
    content: string;
    pattern: MarkdownPattern;
}

export interface DOMPosition {
    node: Node;
    offset: number;
}

export interface MentionData {
    uuid: UUID;
    name: string;
    userMention: boolean;
}

export interface ParsedContentPart {
    content: string;
    isMention: boolean;
    mentionData?: MentionData;
}

export interface MentionSelectEvent {
    detail: Rec;
}