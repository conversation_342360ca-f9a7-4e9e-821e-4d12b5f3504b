import type {<PERSON><PERSON><PERSON><PERSON>osition, MarkdownPattern, MarkdownMatch} from './types';
import {
    MENTION_CLASS,
    MARKDOWN_SYMBOL_CLASS,
    MARKDOWN_BOLD_CLASS,
    MARKDOWN_ITALIC_CLASS,
    MARKDOWN_BOLD_ITALIC_CLASS,
    MARKDOWN_STRIKETHROUGH_CLASS,
    MARKDOWN_CODE_CLASS
} from './constants';
import {exists} from 'shared/utils/custom-utils';

export const MARKDOWN_PATTERNS: MarkdownPattern[] = [
    {regex: /(\*\*\*)([^*\n]+?)\1/g, className: MARKDOWN_BOLD_ITALIC_CLASS, tagName: 'strong', priority: 5},
    {regex: /(___)([^_\n]+?)\1/g, className: MARKDOWN_BOLD_ITALIC_CLASS, tagName: 'strong', priority: 5},
    {regex: /(\*\*)([^*\n]+?)\1/g, className: MARKDOWN_BOLD_CLASS, tagName: 'strong', priority: 4},
    {regex: /(__)([^_\n]+?)\1/g, className: MARKDOWN_BOLD_CLASS, tagName: 'strong', priority: 4},
    {regex: /(\*)([^*\n]+?)\1/g, className: MARKDOWN_ITALIC_CLASS, tagName: 'em', priority: 3},
    {regex: /(_)([^_\n]+?)\1/g, className: MARKDOWN_ITALIC_CLASS, tagName: 'em', priority: 3},
    {regex: /(~~)([^~\n]+?)\1/g, className: MARKDOWN_STRIKETHROUGH_CLASS, tagName: 'del', priority: 2},
    {regex: /(`)([^`\n]+?)\1/g, className: MARKDOWN_CODE_CLASS, tagName: 'code', priority: 1}
];

let lastProcessedContent = '';

export function processMarkdown(contentEditableDiv: HTMLDivElement): void {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
        return;
    }

    const currentContent = contentEditableDiv.textContent || '';
    if (currentContent === lastProcessedContent) {
        return;
    }
    lastProcessedContent = currentContent;

    const range = selection.getRangeAt(0);
    const cursorPosition = saveCursorPosition(contentEditableDiv, range);

    clearMarkdownFormatting(contentEditableDiv);
    applyMarkdownFormatting(contentEditableDiv);

    restoreCursorPosition(contentEditableDiv, cursorPosition, selection);
}

function saveCursorPosition(container: HTMLDivElement, range: Range): CursorPosition {
    const containerText = container.textContent || '';
    let offset = 0;

    const walker = document.createTreeWalker(container, NodeFilter.SHOW_TEXT, null);
    let currentNode: Node | null;

    while ((currentNode = walker.nextNode())) {
        if (currentNode === range.startContainer) {
            offset += range.startOffset;
            break;
        } else {
            offset += (currentNode.textContent || '').length;
        }
    }

    return {
        containerOffset: offset,
        isAtEnd: offset >= containerText.length
    };
}

function clearMarkdownFormatting(container: HTMLDivElement): void {
    const markdownElements = container.querySelectorAll(`
        .${MARKDOWN_BOLD_CLASS},
        .${MARKDOWN_ITALIC_CLASS},
        .${MARKDOWN_BOLD_ITALIC_CLASS},
        .${MARKDOWN_STRIKETHROUGH_CLASS},
        .${MARKDOWN_CODE_CLASS}
    `);

    markdownElements.forEach((element) => {
        const textContent = element.textContent || '';
        const textNode = document.createTextNode(textContent);
        element.parentNode?.replaceChild(textNode, element);
    });

    container.normalize();
}

function applyMarkdownFormatting(container: HTMLDivElement): void {
    const containerText = container.textContent || '';
    const hasCompletePatterns = MARKDOWN_PATTERNS.some((pattern: MarkdownPattern): boolean => {
        pattern.regex.lastIndex = 0;
        return pattern.regex.test(containerText);
    });

    if (!hasCompletePatterns) {
        return;
    }

    const walker = document.createTreeWalker(
        container,
        NodeFilter.SHOW_TEXT,
        {
            acceptNode(node: Node): number {
                const parent = node.parentElement;
                return parent?.closest(`.${MENTION_CLASS}`)
                    ? NodeFilter.FILTER_REJECT
                    : NodeFilter.FILTER_ACCEPT;
            }
        }
    );

    const textNodes: Text[] = [];
    let currentNode: Node | null;

    while ((currentNode = walker.nextNode())) {
        textNodes.push(currentNode as Text);
    }

    textNodes.forEach(processTextNodeForMarkdown);
}

function processTextNodeForMarkdown(textNode: Text): void {
    const text = textNode.textContent || '';
    if (!text.trim()) {
        return;
    }

    const matches = findMarkdownMatches(text);
    if (matches.length === 0) {
        return;
    }

    const fragment = document.createDocumentFragment();
    let lastIndex = 0;

    for (const match of matches) {
        if (match.index > lastIndex) {
            const beforeText = text.substring(lastIndex, match.index);
            fragment.appendChild(document.createTextNode(beforeText));
        }

        const formattedElement = createFormattedElement(match.symbols, match.content, match.pattern);
        fragment.appendChild(formattedElement);

        lastIndex = match.index + match.length;
    }

    if (lastIndex < text.length) {
        fragment.appendChild(document.createTextNode(text.substring(lastIndex)));
    }

    if (exists(textNode.parentNode)) {
        textNode.parentNode.replaceChild(fragment, textNode);
    }
}

function findMarkdownMatches(text: string): MarkdownMatch[] {
    const matches: MarkdownMatch[] = [];

    const sortedPatterns = [...MARKDOWN_PATTERNS].sort((a: MarkdownPattern, b: MarkdownPattern): number => b.priority - a.priority);

    for (const pattern of sortedPatterns) {
        pattern.regex.lastIndex = 0;
        let match: RegExpExecArray | null;

        while ((match = pattern.regex.exec(text)) !== null) {
            const overlaps = matches.some((existingMatch: MarkdownMatch): boolean =>
                match.index < existingMatch.index + existingMatch.length &&
                match.index + match[0].length > existingMatch.index
            );

            if (!overlaps) {
                matches.push({
                    index: match.index,
                    length: match[0].length,
                    symbols: match[1],
                    content: match[2],
                    pattern
                });
            }
        }
    }

    return matches.sort((a: MarkdownMatch, b: MarkdownMatch): number => a.index - b.index);
}

function createFormattedElement(symbols: string, content: string, pattern: MarkdownPattern): HTMLElement {
    const element = document.createElement(pattern.tagName);
    element.className = pattern.className;

    const startSymbol = document.createElement('span');
    startSymbol.className = MARKDOWN_SYMBOL_CLASS;
    startSymbol.textContent = symbols;

    const endSymbol = document.createElement('span');
    endSymbol.className = MARKDOWN_SYMBOL_CLASS;
    endSymbol.textContent = symbols;

    element.appendChild(startSymbol);
    element.appendChild(document.createTextNode(content));
    element.appendChild(endSymbol);

    return element;
}

function restoreCursorPosition(container: HTMLDivElement, cursorPosition: CursorPosition, selection: Selection): void {
    const targetOffset = cursorPosition.containerOffset;

    if (cursorPosition.isAtEnd || targetOffset >= (container.textContent?.length || 0)) {
        const range = document.createRange();
        range.selectNodeContents(container);
        range.collapse(false);
        selection.removeAllRanges();
        selection.addRange(range);
        return;
    }

    try {
        let currentOffset = 0;
        const walker = document.createTreeWalker(container, NodeFilter.SHOW_TEXT, null);
        let currentNode: Node | null;

        while ((currentNode = walker.nextNode())) {
            const nodeLength = (currentNode.textContent || '').length;

            if (currentOffset + nodeLength >= targetOffset) {
                const localOffset = Math.min(targetOffset - currentOffset, nodeLength);
                const range = document.createRange();
                range.setStart(currentNode, localOffset);
                range.setEnd(currentNode, localOffset);

                selection.removeAllRanges();
                selection.addRange(range);
                return;
            }

            currentOffset += nodeLength;
        }
    } catch {
        // ignored
    }

    const fallbackRange = document.createRange();
    fallbackRange.selectNodeContents(container);
    fallbackRange.collapse(false);
    selection.removeAllRanges();
    selection.addRange(fallbackRange);
}
