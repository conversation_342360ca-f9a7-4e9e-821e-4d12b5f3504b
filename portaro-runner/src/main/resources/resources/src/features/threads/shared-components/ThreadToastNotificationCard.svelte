<script lang="ts">
    import type {ToastMessage} from 'shared/components/kp-toast-messages/types';
    import type {MessageReceivedEventData} from 'src/features/threads/threads.types';
    import {getInjector} from 'core/svelte-context/context';
    import {ThreadsContextService} from 'src/features/threads/services/threads-context.service';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {OPEN_THREAD_POPOVER_EVENT} from 'src/features/threads/threads.constants';
    import ToastMessageCardContainer from 'shared/components/kp-toast-messages/ToastMessageCardContainer.svelte';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';
    import ParsedMessageContent from 'src/features/threads/shared-components/ParsedMessageContent.svelte';

    export let toast: ToastMessage;
    export let messageData: MessageReceivedEventData;

    const threadsContextService = getInjector().getByClass(ThreadsContextService);

    const handleToastClick = () => {
        threadsContextService.eventBus.dispatchEvent(new CustomEvent(OPEN_THREAD_POPOVER_EVENT, {detail: messageData.threadId}));
    };
</script>

<ToastMessageCardContainer {toast} on:toast-click={handleToastClick}>
    <KpUserAvatar userRecordId="{messageData.message.senderUser.rid}" sizePx="{24}"/>

    <Flex direction="column" alignItems="flex-start">
        <span class="sender-name">{pipe(messageData.message.senderUser, loc())}</span>
        <small class="thread-name">Posláno v {messageData.threadName}</small>

        <Spacer direction="vertical" size="xs"/>

        <span class="message-content">
            <ParsedMessageContent messageContent="{messageData.message.content}" mentions="{messageData.message.mentions}"/>
        </span>
    </Flex>
</ToastMessageCardContainer>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "./parsed-message-styles.less";

    span,
    small {
        text-align: start;
    }

    .message-content {
        white-space: pre-wrap;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;

        :global {
            .parsed-message-styles();
        }
    }

    .sender-name {
        font-weight: 500;
    }

    .thread-name {
        color: @themed-text-muted;
        font-size: @font-size-xs;
    }
</style>