<script lang="ts">
    import type {MentionSearchType, MentionSelectEvent} from './types';
    import type {ThreadPlacement} from 'src/features/threads/threads.types';
    import type {Rec} from 'typings/portaro.be.types';
    import {createEventDispatcher} from 'svelte';
    import {processMarkdown} from './markdown';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import MentionsPopoverWrapper from 'src/features/threads/thread-content/composer/MentionsPopoverWrapper.svelte';
    import MessageFormattingHelpPopover from 'src/features/threads/thread-content/composer/MessageFormattingHelpPopover.svelte';
    import {
        detectMentionState,
        extractRawText,
        getCurrentSelectionRange,
        insertMentionIntoEditor,
        isInsideMention
    } from './mentions';

    export let threadContentPlacement: ThreadPlacement;

    const dispatch = createEventDispatcher<{'create-message': string}>();

    let contentEditableDiv: HTMLDivElement;
    let showPopup = false;
    let mentionQuery = '';
    let searchType: MentionSearchType | null = null;
    let selectionRange: Range | null = null;
    let usedMentions: Rec[] = [];
    let rawText = '';
    let isFocusedIn = false;

    const handleInput = (): void => {
        const range = getCurrentSelectionRange();

        if (!range || isInsideMention(range)) {
            resetMentionState();
            return;
        }

        selectionRange = range;
        updateMentionState(range);
        processMarkdown(contentEditableDiv);
        rawText = extractRawText(contentEditableDiv);
    };

    const handleMentionSelect = (event: MentionSelectEvent): void => {
        if (!selectionRange || !searchType) {
            return;
        }

        const mention = event.detail;
        const selection = window.getSelection();

        if (!selection) {
            return;
        }

        const success = insertMentionIntoEditor(contentEditableDiv, mentionQuery, mention, searchType, selectionRange);

        if (success) {
            usedMentions = [...usedMentions, mention];
            rawText = extractRawText(contentEditableDiv);
        }

        resetMentionState();
    };

    // Enter = submit, but Enter + Shift = new line
    const handleKeyDown = (event: KeyboardEvent): void => {
        if (event.key !== 'Enter') {
            return;
        }

        if (!event.shiftKey) {
            event.preventDefault();
            handleSubmit();
        }
    };

    const handleFocus = (): void => {
        isFocusedIn = true;
        resetMentionState();
    };

    const handleBlur = (): void => {
        isFocusedIn = false;
    };

    const handleSubmit = (): void => {
        if (!rawText) {
            return;
        }

        dispatch('create-message', rawText);

        rawText = '';
        usedMentions = [];
        contentEditableDiv.innerHTML = '';

        resetMentionState();
    };

    function updateMentionState(range: Range): void {
        const mentionState = detectMentionState(contentEditableDiv, range);

        mentionQuery = mentionState.query;
        searchType = mentionState.type;
        showPopup = mentionState.type !== null;
    }

    function resetMentionState(): void {
        mentionQuery = '';
        searchType = null;
        selectionRange = null;
        showPopup = false;
    }
</script>

<form class="message-composer-bar" on:submit|preventDefault={handleSubmit}
      class:in-fullscreen-page="{threadContentPlacement === 'fullscreen-page'}">

    <MessageFormattingHelpPopover/>

    <div class="composer-wrapper">
        <div class="composer"
             role="textbox"
             tabindex="0"
             contenteditable="true"
             bind:this={contentEditableDiv}
             on:input={handleInput}
             on:keyup={handleInput}
             on:mouseup={handleInput}
             on:focus={handleFocus}
             on:blur={handleBlur}
             on:keydown={handleKeyDown}>
        </div>

        {#if !rawText && !isFocusedIn}
            <span class="placeholder">Vaše zpráva...</span>
        {/if}
    </div>

    <KpIconButton icon="paper-plane" on:click={handleSubmit}/>

    {#if showPopup}
        <div class="popover-container">
            <MentionsPopoverWrapper query={mentionQuery} {searchType} on:mention-select={handleMentionSelect}/>
        </div>
    {/if}
</form>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "../../shared-components/parsed-message-styles.less";

    .message-composer-bar {
        position: relative;
        display: flex;
        align-items: center;
        gap: @spacing-sm;

        &.in-fullscreen-page {
            &:before {
                content: '';
                position: absolute;
                bottom: 100%;
                left: 0;
                right: 0;
                height: @spacing-xxl;
                background: linear-gradient(to bottom, transparent, @themed-body-bg);
            }
        }

        .composer-wrapper {
            position: relative;
            flex-grow: 1;

            .composer {
                width: 100%;
                white-space: pre-wrap;
                border: 1px solid @themed-border-default;
                border-radius: @border-radius-default;
                padding: @spacing-m @spacing-m;

                &:focus,
                &:active,
                &:focus-visible {
                    outline: none !important; // We don't want default focused outline
                }

                :global {
                    .parsed-message-styles();
                }
            }
        }

        .placeholder {
            top: 50%;
            transform: translateY(-50%);
            position: absolute;
            left: 12px;
            pointer-events: none;
            color: @themed-text-muted-label;
        }

        .popover-container {
            position: absolute;
            bottom: calc(100% + @spacing-s);
            left: 0;
            right: 0;
            width: 100%;
        }
    }
</style>
