import type {MessageReceivedEventData, RichThread, Thread, ThreadPlacement} from '../threads.types';
import type {UUID} from 'typings/portaro.be.types';
import type {Observable} from 'rxjs';
import {BehaviorSubject, map} from 'rxjs';
import type {SseService} from 'shared/realtime/sse.service';
import type {ThreadsDataService} from 'src/features/threads/services/threads.data-service';
import type {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
import type CurrentAuthService from 'shared/services/current-auth.service';
import {exists} from 'shared/utils/custom-utils';
import ThreadToastNotificationCard from 'src/features/threads/shared-components/ThreadToastNotificationCard.svelte';
import type {LogService} from 'core/logging/log.service';

export interface ThreadsContext {
    initialized: boolean;
    loadError: boolean;
    threads: Thread[];
    unreadMessages: Map<UUID, number>;
    unreadMentions: Map<UUID, number>;
    unreadThreadsCount: number;
}

type OpenThreadId = `${UUID}-${ThreadPlacement}`;

export class ThreadsContextService {
    public static serviceName = 'threadsContextService';

    private readonly eventTarget: EventTarget;
    private readonly context: BehaviorSubject<ThreadsContext>;
    private readonly openThreads: BehaviorSubject<OpenThreadId[]>;

    /*@ngInject*/
    constructor(private sseService: SseService,
                private threadsDataService: ThreadsDataService,
                private currentAuthService: CurrentAuthService,
                private logService: LogService,
                private toastMessageService: ToastMessageService) {

        this.eventTarget = new EventTarget();
        this.context = new BehaviorSubject<ThreadsContext>(this.createThreadContextObject(false, false, []));
        this.openThreads = new BehaviorSubject<OpenThreadId[]>([]);
    }

    public setup(): void {
        this.sseService.addMessageListener<MessageReceivedEventData>('new-message', (data) => this.handleThreadMessage(data));
        this.sseService.addMessageListener<MessageReceivedEventData>('mention', (data) => this.handleThreadMessage(data));
        this.sseService.addMessageListener<MessageReceivedEventData>('participant-removed', (data) => this.logService.warn('participant-removed', JSON.stringify(data), data));
        this.sseService.addMessageListener<MessageReceivedEventData>('participant-added', (data) => this.logService.warn('participant-added', JSON.stringify(data), data));
        // TODO zatím ještě nejde vůbec smazat thread
        this.sseService.addMessageListener<MessageReceivedEventData>('thread-deleted', (data) => this.logService.warn('thread-deleted', JSON.stringify(data), data));
        this.sseService.addMessageListener<MessageReceivedEventData>('thread-created', (data) => this.logService.warn('thread-created', JSON.stringify(data), data));
        this.sseService.addMessageListener<MessageReceivedEventData>('user-joined', (data) => this.logService.warn('user-joined', JSON.stringify(data), data));
        this.sseService.addMessageListener<MessageReceivedEventData>('user-leaved', (data) => this.logService.warn('user-leaved', JSON.stringify(data), data));
        this.currentAuthService.currentAuth$().subscribe(() => {
            this.loadUserThreads();
        });
    }

    public addOpenThread(thread: Thread, placement: ThreadPlacement): void {
        const currentOpenThreads = this.openThreads.getValue();
        const threadId: OpenThreadId = `${thread.id}-${placement}`;

        if (!currentOpenThreads.includes(threadId)) {
            this.openThreads.next([...currentOpenThreads, threadId]);
        }
    }

    public removeOpenThread(thread: Thread, placement: ThreadPlacement): void {
        const currentOpenThreads = this.openThreads.getValue();
        const threadId: OpenThreadId = `${thread.id}-${placement}`;
        const filteredThreads = currentOpenThreads.filter((id) => id !== threadId);

        if (filteredThreads.length !== currentOpenThreads.length) {
            this.openThreads.next(filteredThreads);
        }
    }

    public addThread(newThread: Thread): void {
        const currentContextValue = this.context.getValue();
        this.context.next({
            ...currentContextValue,
            threads: [newThread, ...currentContextValue.threads]
        });
    }

    public removeThread(threadToRemove: Thread): void {
        const currentContextValue = this.context.getValue();
        this.context.next({
            ...currentContextValue,
            threads: currentContextValue.threads.filter((thread) => thread.id !== threadToRemove.id)
        });
    }

    public async updateThreadInContext(threadToUpdate: Thread): Promise<void> {
        const updatedThread = await this.threadsDataService.getThreadById(threadToUpdate.id);
        if (!exists(updatedThread)) {
            return;
        }

        const currentContextValue = this.context.getValue();
        const updatedThreads = currentContextValue.threads.map((thread) => thread.id === updatedThread.id ? updatedThread : thread);
        this.context.next({
            ...currentContextValue,
            threads: updatedThreads
        });
    }

    public async markMessagesAsRead(thread: Thread): Promise<void> {
        const currentUser = this.currentAuthService.getCurrentAuthValue().activeUser;
        await this.threadsDataService.userReadAllThreadMessages(thread, currentUser.id);

        const currentContextValue = this.context.getValue();

        const unreadMessages = currentContextValue.unreadMessages;
        const unreadMentions = currentContextValue.unreadMentions;
        unreadMessages.delete(thread.id);
        unreadMentions.delete(thread.id);

        this.context.next({
            ...currentContextValue,
            unreadMessages,
            unreadMentions,
            unreadThreadsCount: this.countThreadThreads(unreadMessages)
        });
    }

    public isThreadOpen$(threadId: UUID, placement?: ThreadPlacement): Observable<boolean> {
        return this.openThreads.pipe(
            map((openThreads) => {
                if (!exists(placement)) {
                    return openThreads.some((id) => id.startsWith(threadId));
                }
                return exists(openThreads.find((id) => id === `${threadId}-${placement}`));
            })
        );
    }

    public getContextValue(): ThreadsContext {
        return this.context.getValue();
    }

    public get context$(): Observable<ThreadsContext> {
        return this.context.asObservable();
    }

    public get eventBus(): EventTarget {
        return this.eventTarget;
    }

    private async loadUserThreads(): Promise<void> {
        try {
            const threads = await this.threadsDataService.getUserThreads(this.currentAuthService.getCurrentAuthValue().activeUser.id);
            this.context.next(this.createThreadContextObject(true, false, threads));
        } catch {
            this.context.next(this.createThreadContextObject(true, true, []));
        }
    }

    private createThreadContextObject(initialized: boolean, loadError: boolean, threads: RichThread[]): ThreadsContext {
        const unreadMessages = new Map(threads.map((thread) => [thread.id, thread.unreadMessagesCount]));
        const unreadMentions = new Map(threads.map((thread) => [thread.id, thread.unreadMentionsCount]));

        return {
            initialized,
            loadError,
            threads,
            unreadMessages,
            unreadMentions,
            unreadThreadsCount: this.countThreadThreads(unreadMessages)
        };
    }

    private handleThreadMessage(data: MessageReceivedEventData): void {
        if (this.isThreadOpen(data.threadId)) {
            return;
        }

        const currentContextValue = this.context.getValue();

        const unreadMessages = currentContextValue.unreadMessages;
        const currentUnreadCount = unreadMessages.get(data.threadId) || 0;
        unreadMessages.set(data.threadId, currentUnreadCount + 1);

        this.context.next({
            ...currentContextValue,
            unreadMessages,
            unreadThreadsCount: this.countThreadThreads(unreadMessages)
        });

        this.toastMessageService.showCustomComponentToast(ThreadToastNotificationCard, {
            messageData: data
        });
    }

    private isThreadOpen(threadId: UUID): boolean {
        const currentOpenThreads = this.openThreads.getValue();
        return currentOpenThreads.some((id) => id.startsWith(threadId));
    }

    private countThreadThreads(unreadMessages: Map<UUID, number>): number {
        let unreadThreads = 0;

        unreadMessages.forEach((count) => {
            if (count > 0) {
                unreadThreads++;
            }
        });

        return unreadThreads;
    };
}