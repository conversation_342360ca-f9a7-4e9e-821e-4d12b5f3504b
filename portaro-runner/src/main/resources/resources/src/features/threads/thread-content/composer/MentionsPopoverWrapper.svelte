<script lang="ts">
    import type {MentionSearchType} from './types';
    import type {Rec, RecordSearchParams} from 'typings/portaro.be.types';
    import {createEventDispatcher} from 'svelte';
    import {getInjector} from 'core/svelte-context/context';
    import {SearchManagerBuilderFactoryService} from 'src/features/search/search-manager/search-manager-factory.service';
    import {Kind, SearchType} from 'shared/constants/portaro.constants';
    import {createFloatingActions} from 'svelte-floating-ui';
    import {floatingUiCommonSettings} from 'shared/utils/floating-ui-common-settings';
    import {MENTION_USER_FOND_ID} from 'src/features/threads/thread-content/composer/constants';
    import KpInlineRecordSearchPopover from 'src/features/record/inline-record-search/KpInlineRecordSearchPopover.svelte';

    export let query: string;
    export let searchType: MentionSearchType | null = null;

    const dispatch = createEventDispatcher<{'mention-select': Rec}>();
    const [floatingRef, floatingContent] = createFloatingActions(floatingUiCommonSettings('top-start'));

    const searchManager = getInjector().getByClass(SearchManagerBuilderFactoryService)
        .createBuilder<RecordSearchParams, Rec>()
        .withStaticParams({
            pageSize: 6,
            facetsEnabled: false,
            datasource: 'local',
            rootFond: searchType === 'user' ? [{id: MENTION_USER_FOND_ID}] : undefined,
            q: query,
            kind: [Kind.KIND_RECORD],
            type: SearchType.TYPE_SEARCH_SELECTION
        })
        .createLocalSearch(false);

    function search(q: string) {
        searchManager.newSearchWithPartialParams({q});
    }

    $: search(query);
</script>

<div class="mentions-popover-wrapper" use:floatingRef>
    <KpInlineRecordSearchPopover onSelect="{(rec) => dispatch('mention-select', rec)}"
                                 use={[floatingContent]}
                                 {searchManager}
                                 open/>
</div>

<style lang="less">
    .mentions-popover-wrapper {
        width: 100%;
    }
</style>
