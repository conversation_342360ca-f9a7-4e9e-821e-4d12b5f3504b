<script lang="ts">
    import type {Thread} from 'src/features/threads/threads.types';
    import {createEventDispatcher} from 'svelte';
    import KpGenericTopbar from 'shared/components/kp-generic-topbar/KpGenericTopbar.svelte';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import ThreadIcon from 'src/features/threads/shared-components/ThreadIcon.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpButtonStyleAnchor from 'shared/ui-widgets/button/KpButtonStyleAnchor.svelte';

    export let thread: Thread;

    const dispatch = createEventDispatcher<{'close': void}>();
</script>

<KpGenericTopbar horizontalPadding="16px">
    <KpIconButton icon="arrow-small-left" noBackground on:click={() => dispatch('close')}/>
    <ThreadIcon {thread} sizePx="{24}" tooltipsDisabled/>
    <KpHeading type="h3">{thread.name}</KpHeading>

    <Spacer flex="1"/>

    <KpButtonStyleAnchor buttonSize="xs" href="/#!/thread/{thread.id}">
        <IconedContent icon="globe">Na novém okně</IconedContent>
    </KpButtonStyleAnchor>
</KpGenericTopbar>