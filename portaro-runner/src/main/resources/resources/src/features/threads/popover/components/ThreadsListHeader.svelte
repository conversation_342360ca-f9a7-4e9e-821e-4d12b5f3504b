<script lang="ts">
    import type {Thread} from 'src/features/threads/threads.types';
    import type {ThreadsContext} from 'src/features/threads/services/threads-context.service';
    import {createEventDispatcher, onDestroy} from 'svelte';
    import {getInjector} from 'core/svelte-context/context';
    import {ThreadsContextService} from 'src/features/threads/services/threads-context.service';
    import {cleanup} from 'shared/utils/custom-utils';
    import KpVerticalSeparator from 'shared/ui-widgets/separator/KpVerticalSeparator.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import ThreadChip from 'src/features/threads/popover/components/ThreadChip.svelte';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    export let selectedThread: Thread | null;

    const contextService = getInjector().getByClass(ThreadsContextService);
    const dispatch = createEventDispatcher<{
        'new-thread': void,
        'close': void,
        'thread-select': Thread
    }>();

    let threadsContext: ThreadsContext;
    const threadContextSubscription = contextService.context$.subscribe((currentThreadsContext) => threadsContext = currentThreadsContext);

    onDestroy(() => {
        cleanup(threadContextSubscription);
    });
</script>

<div class="threads-list-header">
    <Flex alignItems="center" columnGap="m" rowGap="sm" wrap="wrap">
        <span class="heading-text">Konverzace</span>

        <KpVerticalSeparator height="12px"/>

        {#each threadsContext.threads as thread(thread.id)}
            <ThreadChip {thread}
                        selected="{selectedThread?.id === thread.id}"
                        on:click={() => dispatch('thread-select', thread)}/>
        {/each}

        {#if threadsContext.threads.length > 0}
            <KpVerticalSeparator height="12px"/>
        {/if}

        <KpButton buttonSize="xs" buttonStyle="success-new" rounded on:click={() => dispatch('new-thread')}>
            <IconedContent icon="add">Nová konverzace</IconedContent>
        </KpButton>
    </Flex>

    <div class="close-button-container">
        <KpIconButton icon="cross-circle" on:click={() => dispatch('close')}/>
    </div>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .threads-list-header {
        display: flex;
        gap: @spacing-ml;
        padding: @spacing-ml;
        border-bottom: 1px solid @themed-border-default;

        .heading-text {
            font-weight: 500;
        }

        .close-button-container {
            margin-left: auto;
        }
    }
</style>