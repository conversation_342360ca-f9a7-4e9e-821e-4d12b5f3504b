<script lang="ts">
    import type {Thread} from 'src/features/threads/threads.types';
    import type {User} from 'typings/portaro.be.types';
    import type {TabButton, TabId} from 'shared/ui-widgets/tabset/types';
    import {createEventDispatcher} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {fade} from 'svelte/transition';
    import {getInjector} from 'core/svelte-context/context';
    import {ThreadsService} from 'src/features/threads/services/threads.service';
    import {ThreadsContextService} from 'src/features/threads/services/threads-context.service';
    import KpGenericTopbar from 'shared/components/kp-generic-topbar/KpGenericTopbar.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import KpButtonTabs from 'shared/ui-widgets/tabset/KpButtonTabs.svelte';
    import NewPrivateThread from './NewPrivateThread.svelte';
    import NewGroupThread from './NewGroupThread.svelte';

    const dispatch = createEventDispatcher<{'back': void, 'thread-created': Thread}>();
    const threadService = getInjector().getByClass(ThreadsService);
    const threadContextService = getInjector().getByClass(ThreadsContextService);

    let threadName = '';
    let createdType: 'group' | 'private' = 'group';
    let addedUsers: User[] = [];
    $: createDisabled = (createdType === 'group' && (!threadName || !threadName.trim())) || createdType === 'private';

    const handleCreateThreadClick = async () => {
        let createdThread: Thread;

        if (createdType === 'private') {
            createdThread = await threadService.createPrivateThread(threadName, addedUsers.at(0));
        } else if (createdType === 'group') {
            createdThread = await threadService.createGroupThread(threadName, addedUsers);
        }

        if (!exists(createdThread)) {
            dispatch('back');
            return;
        }

        threadContextService.addThread(createdThread);
        dispatch('thread-created', createdThread);
    };

    const typeTabs: TabButton[] = [
        {
            id: 'tab-group',
            label: 'Skupinová konverzace',
            icon: 'group'
        },
        {
            id: 'tab-private',
            label: 'Soukromá konverzace',
            icon: 'lock'
        }
    ];

    const handleTypeChange = (event: CustomEvent<TabId>) => {
        threadName = '';
        createdType = event.detail === 'tab-group' ? 'group' : 'private';
    };
</script>

<div class="new-thread-popover-page">
    <KpGenericTopbar horizontalPadding="16px">
        <KpIconButton icon="arrow-small-left" noBackground on:click={() => dispatch('back')}/>
        <span class="heading-text">Nová konverzace</span>
    </KpGenericTopbar>

    <div class="new-thread-form-container">
        <KpButtonTabs tabButtons="{typeTabs}" fillWidth on:tab-change={handleTypeChange}/>

        {#key createdType}
            <div class="anim-container" in:fade={{duration: 250}}>
                {#if createdType === 'private'}
                    <NewPrivateThread/>
                {:else if createdType === 'group'}
                    <NewGroupThread bind:addedUsers bind:threadName/>
                {/if}
            </div>
        {/key}
    </div>

    <div class="create-button-container">
        {#if createdType === 'private'}
            <small class="text-muted text-center">
                Pokud již máte soukromou konverzaci s daným uživatelem, dojde pouze k otevření stávající konverzace
            </small>

            <small class="text-center" style:color="var(--danger-red)">
                Soukromé konverzace zatím nelze vytvářet. Tato funkcionalita bude již brzy implementována.
            </small>
        {/if}

        <KpButton buttonStyle="success-new" isDisabled="{createDisabled || createdType === 'private'}" on:click={handleCreateThreadClick}>
            <IconedContent icon="add">
                {#if createdType === 'private'}
                    Vytvořit soukromou konverzaci
                {:else if createdType === 'group'}
                    Vytvořit skupinovou konverzaci
                {/if}
            </IconedContent>
        </KpButton>
    </div>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro-erp.less";

    .new-thread-popover-page {
        .flex-grow();
        display: flex;
        flex-direction: column;

        .heading-text {
            font-weight: 500;
        }

        .new-thread-form-container {
            .flex-grow();
            gap: @spacing-xxl;
            padding: @spacing-ml;

            .anim-container {
                .flex-grow();
                gap: @spacing-xxl;
            }
        }

        .create-button-container {
            margin-top: auto;
            display: flex;
            flex-shrink: 0;
            flex-direction: column;
            align-items: center;
            gap: @spacing-sm;
            text-align: center;
            justify-content: center;
            padding: @spacing-ml;
            border-top: 1px solid @themed-border-default;
        }
    }
</style>