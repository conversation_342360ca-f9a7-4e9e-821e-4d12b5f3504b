@import (reference) "styles/portaro.themes.less";
@import (reference) "styles/portaro.variables.less";

.parsed-message-styles() {
    .mention {
        position: relative;
        isolation: isolate;
        color: var(--accent-blue-new);
        white-space: nowrap;

        &::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            z-index: -1;
            background-color: @themed-body-bg-blue-highlighted;
            border-radius: @border-radius-sm;
        }

        &.mention-user {
            color: var(--brand-orange-new);

            &::before {
                background-color: @themed-body-bg-orange-highlighted;
            }
        }
    }

    .markdown-symbol {
        color: @themed-text-muted;
        font-weight: normal;
        font-style: normal;
    }

    .markdown-bold {
        font-weight: bold;
    }

    .markdown-italic {
        font-style: italic;
    }

    .markdown-bold-italic {
        font-weight: bold;
        font-style: italic;
    }

    .markdown-strikethrough {
        text-decoration: line-through;
    }

    .markdown-code {
        background-color: @themed-body-bg;
        border-radius: @border-radius-sm;
        color: @themed-text-default;
        padding: 1px 3px;
        font-family: 'Monaco', 'Men<PERSON>', 'Ubuntu Mono', monospace;
        font-size: 0.9em;
    }
}
