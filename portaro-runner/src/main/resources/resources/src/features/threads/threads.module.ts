import register from '@kpsys/angularjs-register';
import {threadRoutes} from 'src/features/threads/page/thread.routes';
import {ThreadsDataService} from 'src/features/threads/services/threads.data-service';
import {ThreadsService} from 'src/features/threads/services/threads.service';
import {ThreadContentService} from './services/thread-content.service';
import {ThreadsContextService} from 'src/features/threads/services/threads-context.service';

export default register('portaro.features.threads', ['portaro.realtime'])
    .config(threadRoutes)
    .service(ThreadsDataService.serviceName, ThreadsDataService)
    .service(ThreadsContextService.serviceName, ThreadsContextService)
    .service(ThreadsService.serviceName, ThreadsService)
    .service(ThreadContentService.serviceName, ThreadContentService)
    .name();