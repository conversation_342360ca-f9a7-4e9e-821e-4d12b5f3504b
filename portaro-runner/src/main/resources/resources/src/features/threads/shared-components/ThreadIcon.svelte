<script lang="ts">
    import type {Thread, ThreadParticipant} from 'src/features/threads/threads.types';
    import {tooltip} from 'shared/ui-widgets/tooltip/use.tooltip';
    import {exists} from 'shared/utils/custom-utils';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';

    export let thread: Thread;
    export let sizePx: number;
    export let tooltipsDisabled = false;

    interface AvatarPosition {
        member: ThreadParticipant;
        top?: number | string;
        right?: number | string;
        bottom?: number | string;
        left?: number | string;
        transform?: string;
    }

    $: calculatePositions(thread.participants);

    let positions: AvatarPosition[] = [];
    let avatarSize: number;

    function calculatePositions(participants: ThreadParticipant[]) {
        const more = participants.length > 3;

        if (participants.length === 1) avatarSize = sizePx;
        else if (participants.length === 2) avatarSize = sizePx / 1.4;
        else if (participants.length === 3) avatarSize = sizePx / 1.75;
        else avatarSize = sizePx / 2;

        if (participants.length === 1) {
            positions = [{member: participants[0], top: 0, right: 0}];
        } else if (participants.length === 2) {
            positions = [
                {member: participants[0], top: 0, right: 0},
                {member: participants[1], left: 0, bottom: 0}
            ];
        } else if (participants.length > 2) {
            positions = [
                {
                    member: participants[0],
                    top: 0,
                    left: more ? 0 : '50%',
                    transform: more ? 'none' : 'translateX(-50%)'
                },
                {
                    member: participants[1],
                    left: more ? 'none' : 0,
                    right: more ? 0 : 'none',
                    bottom: more ? 'none' : 0,
                    top: more ? 0 : 'none'
                },
                {member: participants[2], left: more ? 0 : 'none', right: more ? 'none' : 0, bottom: 0}
            ];
        }
    };
</script>

<div class="thread-icon-container"
     style:width="{sizePx}px"
     style:height="{sizePx}px">

    {#if exists(thread.iconUrl)}
        <img class="icon" alt="icon" src="{thread.iconUrl}"/>
    {:else if thread.participants.length === 0}
        <div class="no-icon" style:--size-px="{sizePx}px">?</div>
    {:else}
        {#key positions}
            {#each positions as position}
                <div class="user-avatar-wrapper"
                     use:tooltip="{{enabled: !tooltipsDisabled, content: position.member.user.text, role: 'tooltip'}}"
                     style="top: {position.top ?? 'none'}; left: {position.left ?? 'none'}; bottom: {position.bottom ?? 'none'}; right: {position.right ?? 'none'}; transform: {position.transform ?? 'none'}">

                    <KpUserAvatar userRecordId="{position.member.user.rid}" sizePx="{avatarSize}"/>
                </div>
            {/each}

            {#if thread.participants.length > 3}
                <div class="other-members" style:--size-px="{avatarSize}px">
                    <span>+{thread.participants.length - 3}</span>
                </div>
            {/if}
        {/key}
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.themes.less";

    .thread-icon-container {
        position: relative;
        flex-shrink: 0;

        .icon {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .no-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: @themed-panel-bg;
            color: @themed-text-muted;
            font-weight: 500;
            font-size: calc(var(--size-px) / 1.5);
            border: 1px solid @themed-border-default;
        }

        .user-avatar-wrapper {
            position: absolute;
        }

        .other-members {
            position: absolute;
            bottom: 0;
            right: 0;
            width: var(--size-px);
            height: var(--size-px);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid @themed-border-default;
            background-color: @themed-panel-bg;

            span {
                line-height: 0;
                font-weight: 500;
                font-size: calc(var(--size-px) / 1.5);
            }
        }
    }
</style>