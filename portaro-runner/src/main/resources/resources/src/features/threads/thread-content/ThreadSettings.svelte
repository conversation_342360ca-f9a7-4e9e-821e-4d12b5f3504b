<script lang="ts">
    import type {Thread, ThreadParticipant, ThreadPlacement} from 'src/features/threads/threads.types';
    import type {Auth, User} from 'typings/portaro.be.types';
    import {createEventDispatcher, onDestroy} from 'svelte';
    import {getThreadContentContext} from 'src/features/threads/thread-content/thread-content.context';
    import {getInjector} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {cleanup, exists} from 'shared/utils/custom-utils';
    import CurrentAuthService from 'shared/services/current-auth.service';
    import {THREAD_USER_SELECTOR_FORCE_SETTINGS} from 'src/features/threads/threads.constants';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';
    import KpValueEditorForceSettings from 'shared/value-editors/kp-value-editor-force-settings/KpValueEditorForceSettings.svelte';
    import KpChipTag from 'shared/ui-widgets/chip/KpChipTag.svelte';

    export let thread: Thread;
    export let placement: ThreadPlacement;

    const context = getThreadContentContext();
    const dispatch = createEventDispatcher<{'close': void}>();
    const currentAuthService = getInjector().getByClass(CurrentAuthService);

    let currentAuth: Auth = currentAuthService.getCurrentAuthValue();
    let threadName = thread.name;
    let userModel: User | null = null;
    let newThreadParticipants: User[] = [];
    let removedThreadParticipants: ThreadParticipant[] = [];
    let saving = false;
    $: savable = threadName !== thread.name || newThreadParticipants.length > 0 || removedThreadParticipants.length > 0;

    const currentAuthSubscription = currentAuthService.currentAuth$().subscribe((auth) => currentAuth = auth);

    onDestroy(() => {
        cleanup(currentAuthSubscription);
    });

    const handleSaveClick = async () => {
        saving = true;
        await context.service.editThread(thread, threadName);

        if (newThreadParticipants.length > 0) {
            await context.service.addThreadParticipants(thread, newThreadParticipants);
        }

        if (removedThreadParticipants.length > 0) {
            await context.service.removeThreadParticipants(thread, removedThreadParticipants.map((participant) => participant.user));
        }

        await context.service.updateThread(thread);

        newThreadParticipants = [];
        removedThreadParticipants = [];

        saving = false;
    };

    const handleUserModelChange = (event: CustomEvent<User>) => {
        userModel = null;

        if (!exists(event.detail)) {
            return;
        }

        if (exists(newThreadParticipants.find((user) => user.id === event.detail.id))) {
            return;
        }

        const currentAuthValue = currentAuthService.getCurrentAuthValue();
        if (event.detail.id === currentAuthValue.activeUser.id) {
            return;
        }

        newThreadParticipants = [...newThreadParticipants, event.detail];
    };

    const handleRemoveParticipantClick = (user: User) => {
        if (newThreadParticipants.find((participant) => participant.id === user.id)) {
            newThreadParticipants = newThreadParticipants.filter((participant) => participant.id !== user.id);
            return;
        }

        const threadParticipant = thread.participants.find((participant) => participant.user.id === user.id);
        if (!exists(threadParticipant)) {
            return;
        }

        removedThreadParticipants = [...removedThreadParticipants, threadParticipant];
    };

    const handleCancelSave = () => {
        threadName = thread.name;
        newThreadParticipants = [];
        removedThreadParticipants = [];
    };

    function getUserParticipants(currentThread: ThreadParticipant[], addedParticipants: User[], removedParticipants: ThreadParticipant[]): User[] {
        let users = currentThread.map((participant) => participant.user);
        users = users.concat(addedParticipants);
        users = users.filter((user) => !removedParticipants.find((participant) => participant.user.id === user.id));
        return users.filter((user, index, array) => array.findIndex((u) => u.id === user.id) === index);
    }
</script>

<div class="thread-settings-container">
    <Flex alignItems="center" gap="s" width="100%">
        <KpHeading type="h3">Nastavení konverzace</KpHeading>
        <KpIconButton icon="cross" noBackground on:click={() => dispatch('close')}/>
    </Flex>

    <Spacer direction="vertical" size="xl"/>

    <div class="thread-settings" class:in-fullscreen-page="{placement === 'fullscreen-page'}">
        <KpValueEditor type="text" placeholder="Název konverzce" bind:model={threadName}/>
        <Spacer direction="vertical" size="xl"/>

        <span class="text-muted">Účastníci ({thread.participants.length})</span>
        <Spacer direction="vertical" size="ml"/>

        <Flex direction="column" gap="m">
            {#each getUserParticipants(thread.participants, newThreadParticipants, removedThreadParticipants) as user (user.id)}
                <Flex alignItems="center" gap="s" width="100%">
                    <KpUserAvatar userRecordId="{user.rid}" sizePx={24}/>
                    <span>{pipe(user, loc())}</span>

                    <Spacer flex="1"/>

                    {#if user.id !== currentAuth.activeUser.id}
                        <KpIconButton icon="trash" on:click={() => handleRemoveParticipantClick(user)}/>
                    {:else}
                        <KpChipTag chipSize="xs" chipStyle="accent-blue-new">Vy</KpChipTag>
                    {/if}
                </Flex>
            {/each}
        </Flex>
        <Spacer direction="vertical" size="m"/>

        <Flex alignItems="center" justifyContent="center">
            <KpValueEditorForceSettings forceSettings="{THREAD_USER_SELECTOR_FORCE_SETTINGS}">
                <KpValueEditor type="user" bind:model={userModel} on:model-change={handleUserModelChange}/>
            </KpValueEditorForceSettings>
        </Flex>

        <Spacer flex="1"/>

        <Flex gap="s" alignItems="center" justifyContent="center">
            <KpButton buttonStyle="danger-new" isDisabled="{!savable || saving}" on:click={handleCancelSave}>
                <IconedContent icon="cross-circle">Zrušit</IconedContent>
            </KpButton>

            <KpButton buttonStyle="success-new" isDisabled="{!savable || saving}" on:click={handleSaveClick}>
                <IconedContent icon="check-circle">Uložit</IconedContent>
            </KpButton>
        </Flex>
    </div>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro-erp.less";

    .thread-settings-container {
        .flex-grow();
        align-items: center;

        .thread-settings {
            .flex-grow();
            width: 100%;

            &.in-fullscreen-page {
                max-width: 720px;
            }
        }
    }
</style>