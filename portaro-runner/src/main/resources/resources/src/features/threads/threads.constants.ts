import type {ForceSetting} from 'shared/value-editors/kp-value-editor-force-settings/types';
import {Kind, SearchType} from 'shared/constants/portaro.constants';

export const OPEN_THREAD_POPOVER_EVENT = 'open-thread-popover-event';

export const THREAD_USER_SELECTOR_FORCE_SETTINGS: ForceSetting[] = [
    {
        type: 'user',
        options: {
            searchParams: {
                kind: Kind.KIND_USER,
                type: SearchType.TYPE_USER_SEARCH
            }
        }
    }
];