<script lang="ts">
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
</script>

<div class="user-selector-container">
    <button type="button" class="user-selector-button">
        <UIcon icon="user"/>
    </button>

    <small class="text-center text-muted">
        V<PERSON><PERSON><PERSON> uživatele kliknutím
    </small>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    @selector-size: 154px;

    .user-selector-container {
        display: flex;
        gap: @spacing-sm;
        flex-direction: column;
        align-items: center;
        text-align: center;

        .user-selector-button {
            background: none;
            border: 1px solid @themed-border-default;
            border-radius: 50%;
            width: @selector-size;
            height: @selector-size;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            outline: 2px solid transparent;
            outline-offset: 2px;
            font-size: 48px;
            color: @themed-text-muted;
            transition: outline-color 0.3s ease-in-out;

            &:hover {
                outline-color: var(--accent-blue-new);
            }
        }
    }
</style>