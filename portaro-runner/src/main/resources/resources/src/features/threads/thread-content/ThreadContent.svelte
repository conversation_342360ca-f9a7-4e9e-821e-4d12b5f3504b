<script lang="ts">
    import type {MessageReceivedEventData, Thread, ThreadPlacement} from 'src/features/threads/threads.types';
    import type {Auth, Message} from 'typings/portaro.be.types';
    import type {SseEventUnsubscriber} from 'shared/realtime/sse.service';
    import type {PaginationData} from 'src/features/search/search-manager/pagination';
    import {SseService} from 'shared/realtime/sse.service';
    import {createThreadContentContext} from 'src/features/threads/thread-content/thread-content.context';
    import {cleanup, exists} from 'shared/utils/custom-utils';
    import {ThreadContentService} from 'src/features/threads/services/thread-content.service';
    import {ThreadsContextService} from 'src/features/threads/services/threads-context.service';
    import {onDestroy, onMount, tick} from 'svelte';
    import {getInjector} from 'core/svelte-context/context';
    import {popInAnim} from 'shared/animations/pop-animations';
    import {fade} from 'svelte/transition';
    import CurrentAuthService from 'shared/services/current-auth.service';
    import MessageComposerBar from 'src/features/threads/thread-content/composer/MessageComposerBar.svelte';
    import ThreadMessageBubble from 'src/features/threads/thread-content/ThreadMessageBubble.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import ThreadSettings from 'src/features/threads/thread-content/ThreadSettings.svelte';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    export let thread: Thread;
    export let placement: ThreadPlacement;

    const service = getInjector().getByClass(ThreadContentService);
    const sseService = getInjector().getByClass(SseService);
    const contentContext = createThreadContentContext(service, thread);
    const currentAuthService = getInjector().getByClass(CurrentAuthService);
    const contextService = getInjector().getByClass(ThreadsContextService);

    const isFullscreenThreadOpen$ = contextService.isThreadOpen$(thread.id, 'fullscreen-page');
    $: shouldHidePopoverThread = placement === 'popover' && $isFullscreenThreadOpen$;

    let messages: Message[] = [];
    let currentAuth: Auth;
    let loading = true;
    let settingsOpened = false;
    let sseEventUnsubscribe: SseEventUnsubscriber;

    const currentAuthSubscription = currentAuthService.currentAuth$().subscribe((auth) => currentAuth = auth);

    function setShowableMessages() {
        return (data: PaginationData<Message>) => messages = data.items.filter((message) => exists(message.activationDate) || message.senderUser.id === currentAuth?.activeUser?.id);
    }

    const settingsOpenedSubscription = contentContext.settingsOpened$.subscribe((opened) => settingsOpened = opened);
    const messagesSubscription = contentContext.searchManager.getPagination().getPaginationData$().subscribe(setShowableMessages());
    const stateSubscription = contentContext.searchManager.getState$().subscribe((state) => {
        if (!loading) {
            return;
        }

        if (state.pageLoadInProgress === null) {
            loading = false;
        }
    });

    onMount(async () => {
        contextService.addOpenThread(thread, placement);
        sseEventUnsubscribe = sseService.addMessageListener<MessageReceivedEventData>('new-message', handleMessageReceived);

        await contextService.markMessagesAsRead(thread);
    });

    onDestroy(() => {
        contextService.removeOpenThread(thread, placement);
        cleanup(messagesSubscription, currentAuthSubscription, stateSubscription, sseEventUnsubscribe, settingsOpenedSubscription);
    });

    const handleSettingsOpenClick = () => {
        settingsOpened = true;
    };

    const handleMessageReceived = (data: MessageReceivedEventData) => {
        if (data.threadId !== thread.id) {
            return;
        }

        messages = [data.message, ...messages];
        contextService.markMessagesAsRead(thread);
    };

    const handleCreateMessage = async (event: CustomEvent<string>) => {
        const newMessage = await contentContext.service.createThreadMessage(thread, event.detail);

        if (!exists(newMessage)) {
            return;
        }

        messages = [newMessage, ...messages];

        tick().then(() => {
            const lastMessageElement = document.getElementById(`message-${newMessage.id}`);
            lastMessageElement?.scrollIntoView({behavior: 'smooth'});
        });
    };

    function shouldShowMessageHeader(message: Message) {
        const index = messages.indexOf(message);
        const beforeMessage = messages[index + 1];

        if (!exists(beforeMessage)) {
            return true;
        }

        if (beforeMessage.senderUser.id !== message.senderUser.id) {
            return true;
        }

        const currentDate = new Date(Date.parse(message.creationDate));
        const beforeDate = new Date(Date.parse(beforeMessage.creationDate));

        if (currentDate.getDate() !== beforeDate.getDate() ||
            currentDate.getMonth() !== beforeDate.getMonth() ||
            currentDate.getFullYear() !== beforeDate.getFullYear()) {
            return true;
        }

        return beforeDate.getHours() !== currentDate.getHours();
    }

    function isAdministrator(currentThread: Thread, auth: Auth): boolean {
        const threadParticipant = currentThread.participants.find((participant) => participant.user.id === auth.activeUser.id);
        if (!exists(threadParticipant)) {
            return false;
        }

        return threadParticipant.administrator;
    }
</script>

{#if !shouldHidePopoverThread}
    <div class="thread-content"
         class:in-fullscreen-page="{placement === 'fullscreen-page'}"
         class:in-popover="{placement === 'popover'}">

        {#key settingsOpened}
            <div class="anim-container" in:fade={{duration: 250}}>
                {#if settingsOpened}
                    <div class="thread-settings-container">
                        <ThreadSettings {placement} {thread} on:close={() => settingsOpened = false}/>
                    </div>
                {/if}

                {#if !settingsOpened}
                    <div class="buttons-container"
                         class:in-fullscreen-page="{placement === 'fullscreen-page'}"
                         class:in-popover="{placement === 'popover'}">

                        <KpIconButton icon="filter"/>

                        {#if isAdministrator(thread, currentAuth)}
                            <KpIconButton icon="settings" on:click={handleSettingsOpenClick}/>
                        {/if}
                    </div>

                    <div class="messages-container">
                        {#each messages as message(message.id)}
                            {@const selfMessage = message.senderUser.id === currentAuth?.activeUser?.id}

                            <div class="message-anim-container"
                                 id="message-{message.id}"
                                 class:self-message="{selfMessage}"
                                 class:other-message="{!selfMessage}"
                                 in:popInAnim={{key: `message-${message.id}`}}>

                                <ThreadMessageBubble {message} withoutHeader="{!shouldShowMessageHeader(message)}"/>
                            </div>
                        {/each}

                        {#if messages.length === 0 && !loading}
                            <div class="no-messages-container">
                                <IconedContent icon="message-slash" orientation="vertical" align="center" justify="center">
                                    V tomto vlákně zatím nebyly odeslány žádné zprávy
                                </IconedContent>
                            </div>
                        {/if}

                        {#if loading}
                            <KpLoadingBlock fillAvailableSpace/>
                        {/if}
                    </div>

                    <div class="thread-content-input">
                        <MessageComposerBar threadContentPlacement="{placement}" on:create-message={handleCreateMessage}/>
                    </div>
                {/if}
            </div>
        {/key}
    </div>
{/if}

{#if shouldHidePopoverThread}
    <Flex direction="column" alignItems="center" justifyContent="center" fillAvailableSpace>
        <IconedContent icon="info" orientation="vertical" align="center" justify="center">
            Tato konverzace je otevřená v celostránkovém zobrazení
        </IconedContent>
    </Flex>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro-erp.less";

    .anim-container {
        .flex-grow();
    }

    .thread-content {
        .flex-grow();
        position: relative;

        &.in-fullscreen-page {
            .thread-content-input {
                padding: 0 @main-padding-horizontal @main-padding-vertical;
            }

            .messages-container {
                padding: @main-padding-vertical @main-padding-horizontal @spacing-xxl;
            }

            .thread-settings-container {
                padding: @main-padding-vertical @main-padding-horizontal;
            }
        }

        &.in-popover {
            .thread-content-input {
                padding: @spacing-ml;
                border-top: 1px solid @themed-border-default;
            }

            .messages-container {
                padding: @spacing-ml;
            }

            .thread-settings-container {
                padding: @spacing-ml;
            }
        }

        .buttons-container {
            position: absolute;
            display: flex;
            z-index: 1;
            gap: @spacing-s;

            &.in-fullscreen-page {
                top: @spacing-l;
                right: @main-padding-horizontal;
            }

            &.in-popover {
                top: @spacing-m;
                right: @spacing-ml;
            }
        }

        .messages-container {
            .flex-grow();
            flex-direction: column-reverse;
            gap: @spacing-sm;
            overflow-y: auto;

            .message-anim-container {
                &.self-message {
                    align-self: flex-end;
                }

                &.other-message {
                    align-self: flex-start;
                }
            }
        }

        .thread-settings-container {
            .flex-grow();
        }

        .no-messages-container {
            .flex-grow();
            align-items: center;
            justify-content: center;
        }
    }
</style>