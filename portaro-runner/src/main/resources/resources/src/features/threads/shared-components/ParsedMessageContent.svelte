<script lang="ts">
    import type {LabeledRecordRef} from 'typings/portaro.be.types';
    import {parseMessageContentWithMentions} from './message-content-parser';
    import Mention from 'src/features/threads/shared-components/Mention.svelte';

    export let messageContent: string;
    export let mentions: LabeledRecordRef[];

    $: parsedParts = parseMessageContentWithMentions(messageContent, mentions);
</script>

{#each parsedParts as part}
    {#if part.isMention && part.mentionData}
        <Mention {...part.mentionData}/>
    {:else}
        {@html part.content}
    {/if}
{/each}