<script lang="ts">
    import type {Thread} from 'src/features/threads/threads.types';
    import type {ThreadsContext} from 'src/features/threads/services/threads-context.service';
    import {getInjector} from 'core/svelte-context/context';
    import {ThreadsContextService} from 'src/features/threads/services/threads-context.service';
    import {onDestroy} from 'svelte';
    import {cleanup} from 'shared/utils/custom-utils';
    import ErpPageLayout from 'src/features/erp/components/ErpPageLayout.svelte';
    import ErpHeadingBar from 'src/features/erp/components/ErpHeadingBar.svelte';
    import ThreadIcon from 'src/features/threads/shared-components/ThreadIcon.svelte';
    import ThreadContent from 'src/features/threads/thread-content/ThreadContent.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    export let thread: Thread;

    const threadContextService = getInjector().getByClass(ThreadsContextService);

    let threadsContext = threadContextService.getContextValue();
    const contextSubscription = threadContextService.context$.subscribe((currentContext) => threadsContext = currentContext);
    $: selectedThread = getSelectedThread(threadsContext, thread);

    onDestroy(() => {
        cleanup(contextSubscription);
    });

    function getSelectedThread(context: ThreadsContext, currentThread: Thread): Thread {
        return context.threads.find((t) => t.id === currentThread.id) ?? currentThread;
    }
</script>

<ErpPageLayout pageClass="kp-message-thread-page" withoutContentPadding>
    <ErpHeadingBar slot="heading" gap="16px">
        <ThreadIcon thread="{selectedThread}" sizePx="{48}"/>
        <KpHeading type="h1">{selectedThread.name}</KpHeading>
    </ErpHeadingBar>

    <div class="thread-content-container">
        <ThreadContent thread="{selectedThread}" placement="fullscreen-page"/>
    </div>
</ErpPageLayout>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro-erp.less";

    .thread-content-container {
        .flex-grow();
        gap: @spacing-ml;
    }

    :global {
        .kp-message-thread-page-container .erp-heading {
            padding: @spacing-ml @main-padding-horizontal !important;
        }
    }
</style>