<script lang="ts">
    import type {Message} from 'typings/portaro.be.types';
    import {MessageMedium, MessageStatus, MessageTopic} from 'typings/portaro.be.types';
    import type {SearchManager} from '../search/search-manager/search-manager';
    import {getLocalization} from 'core/svelte-context/context';
    import {Kind, SearchType} from 'shared/constants/portaro.constants';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpButtonStyleAnchor from 'shared/ui-widgets/button/KpButtonStyleAnchor.svelte';
    import KpSearchPageContainerWithContextActions from 'src/features/search/search-page-container-with-context-actions/KpSearchPageContainerWithContextActions.svelte';

    const localize = getLocalization();

    const staticParams = {
        type: SearchType.TYPE_MESSAGE_SEARCH,
        kind: [Kind.KIND_MESSAGE],
        sorting: '-id',
    } as Record<string, any>;

    function filterUnsentPostReminders(searchManager: SearchManager<Message, any>) {
        searchManager.newSearchWithParams({
            topic: MessageTopic.REMINDER,
            messageMedium: MessageMedium.POST,
            messageStatus: MessageStatus.UNSENT
        });
    }
</script>

<KpSearchPageContainerWithContextActions {staticParams} pageClass="kp-messages-page">
    <svelte:fragment slot="context-actions" let:searchManager>
        <KpButton on:click={() => filterUnsentPostReminders(searchManager)}>{localize(/* @kp-localization message.FilterUnsent */ 'message.FilterUnsent')}</KpButton>
        <KpButtonStyleAnchor href="/api/message/print-reminders" target="_blank">{localize(/* @kp-localization message.PrintUnsent */ 'message.PrintUnsent')}</KpButtonStyleAnchor>
    </svelte:fragment>
</KpSearchPageContainerWithContextActions>