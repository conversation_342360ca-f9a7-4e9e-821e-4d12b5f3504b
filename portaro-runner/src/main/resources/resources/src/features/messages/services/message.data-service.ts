import type {MessageSending, UUID} from 'typings/portaro.be.types';
import type {AjaxService} from 'src/core/data-services/ajax.service';
import {ngAsync} from 'shared/utils/ng-@decorators';

export class MessageDataService {
    public static serviceName = 'messageDataService';

    public static readonly MESSAGE_ROUTE = 'message';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async getMessageSendings(messageId: UUID): Promise<MessageSending[]> {
        return this.ajaxService
            .createRequest(`${MessageDataService.MESSAGE_ROUTE}/${messageId}/sendings`)
            .get();
    }

}