<script lang="ts">
    import type {MessageSending} from 'typings/portaro.be.types';
    import {isMessageSendingEmail, isMessageSendingInternal, isMessageSendingPost, isMessageSendingSms} from './utils';
    import {getDateFormatter} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import SendingSms from './components/SendingSms.svelte';
    import SendingEmail from './components/SendingEmail.svelte';
    import SendingInternal from './components/SendingInternal.svelte';
    import SendingPost from './components/SendingPost.svelte';
    import KpMessageSendingLabeledInfo from './components/KpMessageSendingLabeledInfo.svelte';

    export let sending: MessageSending;

    const dateFormatter = getDateFormatter();
</script>

<div class="kp-message-sending">
    <KpMessageSendingLabeledInfo title="Odesláno:">
        {#if sending.sendingEvent?.createDate}
            {pipe(sending.sendingEvent.createDate, dateFormatter('d.M. HH:mm'))}
        {:else}
            ?
        {/if}
    </KpMessageSendingLabeledInfo>

    <KpMessageSendingLabeledInfo title="Přijato:">
        {#if sending.receiptionEvent?.createDate}
            {pipe(sending.receiptionEvent.createDate, dateFormatter('d.M. HH:mm'))}
        {:else}
            ?
        {/if}
    </KpMessageSendingLabeledInfo>

    <KpMessageSendingLabeledInfo title="Potvrzeno:">
        {#if sending.confirmationEvent?.createDate}
            {pipe(sending.confirmationEvent.createDate, dateFormatter('d.M. HH:mm'))}
        {:else}
            ?
        {/if}
    </KpMessageSendingLabeledInfo>

    {#if isMessageSendingSms(sending)}
        <SendingSms {sending}/>
    {/if}

    {#if isMessageSendingEmail(sending)}
        <SendingEmail {sending}/>
    {/if}

    {#if isMessageSendingInternal(sending)}
        <SendingInternal {sending}/>
    {/if}

    {#if isMessageSendingPost(sending)}
        <SendingPost {sending}/>
    {/if}
</div>

<style lang="less">
    @spacing-double: 20px;
    @spacing: 10px;

    .kp-message-sending {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        column-gap: @spacing-double;
        row-gap: @spacing;
    }
</style>