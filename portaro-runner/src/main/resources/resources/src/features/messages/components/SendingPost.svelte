<script lang="ts">
    import type {MessageSendingPost} from 'typings/portaro.be.types';
    import {exists} from 'shared/utils/custom-utils';
    import {getDateFormatter} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import {Kind} from 'shared/constants/portaro.constants';
    import KpMessageSendingLabeledInfo from './KpMessageSendingLabeledInfo.svelte';
    import Label from 'shared/components/kp-label/Label.svelte';

    export let sending: MessageSendingPost;

    const dateFormatter = getDateFormatter();
</script>

<KpMessageSendingLabeledInfo title="Adresa:">
    {sending.address}
</KpMessageSendingLabeledInfo>

{#if exists(sending.fulltextId)}
    <strong><a href="/files/{sending.fulltextId}" target="_blank">Stáhnout zprávu</a></strong>
{/if}

{#if exists(sending.printingEvent) && exists(sending.printingEvent?.initiatorUser)}
    Vitisknuto <strong>{pipe(sending.printingEvent.createDate, dateFormatter('d.M.yyyy HH:mm'))}</strong>
    uživatelem <strong><Label explicitKind="{Kind.KIND_USER}" labeled="{sending.printingEvent.initiatorUser}"/></strong>
{/if}