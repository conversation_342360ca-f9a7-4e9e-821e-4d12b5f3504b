import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function messageSendingsRoutes($stateProvider: StateProvider) {
    let componentModule: { default: any; };

    $stateProvider
        .state('messages', {
            url: '/messages',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => componentModule.default
            },
            lazyLoad: async () => {
                componentModule = await import(/* webpackChunkName: "messages" */ './KpMessagesPage.svelte');
                return null;
            }
        });
}