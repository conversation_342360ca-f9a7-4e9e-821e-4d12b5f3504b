import type {MessageSending, UUID} from 'typings/portaro.be.types';
import type {Readable} from 'svelte/store';
import type {MessageDataService} from './message.data-service';
import {writable} from 'svelte/store';

export class KpMessageService {
    public static serviceName = 'kpMessageService';

    private messagesSidebarOpened$ = writable<boolean>(false);

    /*@ngInject*/
    constructor(private messageDataService: MessageDataService) {
    }

    public async loadMessageSendings(messageId: UUID): Promise<MessageSending[]> {
        return this.messageDataService.getMessageSendings(messageId);
    }

    public get messagesSidebarOpenedState(): Readable<boolean> {
        return this.messagesSidebarOpened$;
    }

    public setMessagesSidebarOpened(opened: boolean) {
        this.messagesSidebarOpened$.set(opened);
    }
}