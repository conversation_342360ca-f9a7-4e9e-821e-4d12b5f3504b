<script lang="ts">
    import type {MessageSendingEmail} from 'typings/portaro.be.types';
    import {exists} from 'shared/utils/custom-utils';
    import {getSanitize} from 'core/svelte-context/context';
    import KpMessageSendingLabeledInfo from './KpMessageSendingLabeledInfo.svelte';

    export let sending: MessageSendingEmail;

    const sanitizeHtml = getSanitize();
</script>

<KpMessageSendingLabeledInfo title="Odesílatel:">
    {sending.senderEmailAddress}
</KpMessageSendingLabeledInfo>

<KpMessageSendingLabeledInfo title="Příjemce:">
    {sending.recipientEmailAddress}
</KpMessageSendingLabeledInfo>

<KpMessageSendingLabeledInfo title="Předmět:">
    {sending.subject}
</KpMessageSendingLabeledInfo>

<KpMessageSendingLabeledInfo title="Obsah:">
    {@html sanitizeHtml(sending.body)}
</KpMessageSendingLabeledInfo>

{#if exists(sending.attachmentDirectoryId)}
    <strong><a href="/media-viewer?rootDirectory={sending.attachmentDirectoryId}">Přílohy</a></strong>
{/if}

{#if sending.errorMessage}
    <KpMessageSendingLabeledInfo type="error" title="Chyba:">
        {sending.errorMessage}
    </KpMessageSendingLabeledInfo>
{/if}