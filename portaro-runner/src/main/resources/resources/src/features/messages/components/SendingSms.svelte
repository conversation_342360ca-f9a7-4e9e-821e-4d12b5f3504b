<script lang="ts">
    import type {MessageSendingSms} from 'typings/portaro.be.types';
    import KpMessageSendingLabeledInfo from './KpMessageSendingLabeledInfo.svelte';

    export let sending: MessageSendingSms;
</script>

<KpMessageSendingLabeledInfo title="Tel. číslo:">
    {sending.recipientPhoneNumber}
</KpMessageSendingLabeledInfo>

<KpMessageSendingLabeledInfo title="Obsah:">
    {sending.content}
</KpMessageSendingLabeledInfo>

{#if sending.errorMessage}
    <KpMessageSendingLabeledInfo type="error" title="Chyba:">
        {sending.errorMessage}
    </KpMessageSendingLabeledInfo>
{/if}