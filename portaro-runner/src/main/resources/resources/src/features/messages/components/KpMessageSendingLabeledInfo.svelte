<script lang="ts">
    import type {UIcons} from 'shared/ui-widgets/uicons/types';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import {uuid} from 'shared/utils/custom-utils';

    const labeledInfoId = uuid();

    export let title: string;
    export let icon: UIcons | null = null;
    export let type: 'default' | 'error' = 'default';
</script>

<div class="single-info-container {type}-info">
    {#if icon}
        <div class="icon-container">
            <UIcon icon="{icon}"/>
        </div>
    {/if}

    <small class="label-title" id="labeled-info-{labeledInfoId}">{title}</small>

    <small aria-labelledby="labeled-info-{labeledInfoId}">
        <slot/>
    </small>
</div>

<style lang="less">
    .single-info-container {
        text-align: start;
        display: flex;
        gap: 4px;

        &.error-info {
            color: var(--danger-red);
        }

        .icon-container {
            display: flex;
            align-items: center;
            scale: 0.9;
        }

        .label-title {
            font-weight: 500;
        }
    }
</style>