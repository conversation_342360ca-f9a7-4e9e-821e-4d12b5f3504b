import register from '@kpsys/angularjs-register';
import messageSendingsRoutes from './message-sendings.routes';
import {MessageDataService} from './services/message.data-service';
import {KpMessageService} from './services/kp-message.service';

/**
 * @ngdoc module
 * @name portaro.features.messages
 * @module portaro.features.messages
 *
 * @description
 * Messages UC
 */
export default register('portaro.features.messages')
    .config(messageSendingsRoutes)
    .service(MessageDataService.serviceName, MessageDataService)
    .service(KpMessageService.serviceName, KpMessageService)
    .name();