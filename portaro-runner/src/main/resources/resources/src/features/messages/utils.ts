import {exists} from 'shared/utils/custom-utils';
import {MessageMedium} from 'typings/portaro.be.types';
import type {UIcons} from 'shared/ui-widgets/uicons/types';
import type {
    MessageSending,
    MessageSendingEmail,
    MessageSendingInternal,
    MessageSendingPost,
    MessageSendingSms
} from 'typings/portaro.be.types';

export function getSendingIcon(sendingMediumType: MessageMedium): UIcons {
    switch (sendingMediumType) {
        case MessageMedium.EMAIL:
            return 'at';
        case MessageMedium.SMS:
            return 'smartphone';
        case MessageMedium.INTERNAL:
            return 'megaphone';
        case MessageMedium.POST:
            return 'envelope';
    }

    return 'question';
}

export function isMessageSendingSms(obj: MessageSending): obj is MessageSendingSms {
    return exists(obj) && 'recipientPhoneNumber' in obj && exists(obj.recipientPhoneNumber) && 'content' in obj && exists(obj.content);
}

export function isMessageSendingEmail(obj: MessageSending): obj is MessageSendingEmail {
    return exists(obj) && 'recipientEmailAddress' in obj && exists(obj.recipientEmailAddress) && 'senderEmailAddress' in obj && exists(obj.senderEmailAddress);
}

export function isMessageSendingPost(obj: MessageSending): obj is MessageSendingPost {
    return exists(obj) && 'address' in obj && exists(obj.address);
}

export function isMessageSendingInternal(obj: MessageSending): obj is MessageSendingInternal {
    return !isMessageSendingEmail(obj) && !isMessageSendingPost(obj) && !isMessageSendingSms(obj)
        && 'body' in obj && exists(obj.body);
}