<script lang="ts">
    import type {MessageSendingInternal} from 'typings/portaro.be.types';
    import {getSanitize} from 'core/svelte-context/context';
    import KpMessageSendingLabeledInfo from './KpMessageSendingLabeledInfo.svelte';

    export let sending: MessageSendingInternal;

    const sanitizeHtml = getSanitize();
</script>

<KpMessageSendingLabeledInfo title="Obsah:">
    {@html sanitizeHtml(sending.body)}
</KpMessageSendingLabeledInfo>