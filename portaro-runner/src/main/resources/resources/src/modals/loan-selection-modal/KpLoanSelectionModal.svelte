<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import type {LoanSelectionModalModel} from './types';
    import type {Loan} from 'typings/portaro.be.types';
    import {createPayloadActionResponse} from '../modal-utils';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {exists} from 'shared/utils/custom-utils';
    import {onMount} from 'svelte';
    import LoanDataService from '../../features/loan/loan.data-service';
    import KpModalHeaderCloseButton from '../kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalFooterCloseButton from '../kp-modal/KpModalFooterCloseButton.svelte';
    import KpModalTitle from '../kp-modal/KpModalTitle.svelte';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import KpSelectableLoanCard from '../../features/loan/kp-selectable-loan-card/KpSelectableLoanCard.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';

    export let model: LoanSelectionModalModel;
    export let modalWindowActions: ModalWindowActions;

    const localize = getLocalization();
    const loanDataService = getInjector().getByToken<LoanDataService>(LoanDataService.serviceName);

    let loans: Loan[] | undefined;

    const identity = <T>(arg: T) => arg;

    onMount(async () => {
        const filter = model.loansFilter ?? identity;

        loans = filter(await loanDataService.getByExemplarId(model.exemplarId, {
            pageNumber: 1,
            pageSize: 50
        }));
    });

    function selectLoan(selectedLoan: Loan) {
        modalWindowActions.submitPromise(Promise.resolve(createPayloadActionResponse(selectedLoan)));
    }
</script>

<KpModalContent {modalWindowActions} additionalClasses="loan-selection-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>
        <KpModalTitle>
            {localize(/* @kp-localization hledani.vybratZVyhledanych */ 'hledani.vybratZVyhledanych')}
        </KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        {#if exists(loans)}
            {#if loans.length > 0}
                <ul class="loan-cards-list">
                    {#each loans as loan}
                        <li>
                            <KpSelectableLoanCard {loan} on:select={() => selectLoan(loan)}/>
                        </li>
                    {/each}
                </ul>
            {/if}

            {#if loans.length === 0}
                <div class="text-muted text-center">
                    {localize(/* @kp-localization loan.NoReservationsOrOrders */ 'loan.NoReservationsOrOrders')}
                </div>
            {/if}
        {/if}

        {#if !exists(loans)}
            <KpLoadingBlock/>
        {/if}
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpModalFooterCloseButton dataQa="search-selection-close"/>
    </svelte:fragment>
</KpModalContent>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .loan-cards-list {
        display: flex;
        flex-direction: column;
        gap: @spacing-ml;
    }
</style>