<script lang="ts">
    interface CType {
        type: string;
        data: string;
    }

    interface CItem {
        types: CType[];
    }

    let items: CItem[] = [];

    async function dumpItem(clipboardItem: ClipboardItem): Promise<CItem> {
        const types = clipboardItem.types.map(async (type) => {
            return {type, data: await clipboardItem.getType(type).then((blob) => blob.text())}
        })
        return {types: await Promise.all(types)};
    }

    async function dumpClipboard() {
        const clipboardItems = await navigator.clipboard.read();
        items = await Promise.all(clipboardItems.map(dumpItem));
    }
</script>

<div class="container">
    <div class="row col-sm-12">
        <!-- Hic sunt leones! -->
        <h1>Clipboard dump</h1>
        <button on:click={dumpClipboard}>Dump</button>
        <hr>
        {#each items as item}
            <div>
                {#each item.types as type}
                    <b>{type.type}</b>
                    <pre>{type.data}</pre>
                {/each}
            </div>
            <hr>
        {/each}
    </div>
</div>