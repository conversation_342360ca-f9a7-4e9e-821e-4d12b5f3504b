<script lang="ts">
    import KpUniversalForm from 'shared/value-editors/kp-universal-form/KpUniversalForm.svelte';
    import type {TextValueEditorOptions, TextValueEditorValidations} from 'shared/value-editors/internal/editors/text/types';
    import type {NumberValueEditorValidations} from 'shared/value-editors/internal/editors/number/types';
    import type {KpUniversalFormComponentOptions, KpUniversalFormSettings} from 'shared/value-editors/kp-universal-form/types';
    import type {FormGroup} from 'shared/value-editors/internal/forms/form-group';
    import type {FormData} from 'shared/value-editors/internal/forms/types';
    import type {Observable} from 'rxjs';
    import type {PasswordValueEditorOptions} from 'shared/value-editors/internal/editors/password/types';
    import type {ListValueEditorOptions, ListValueEditorValidations} from 'shared/value-editors/internal/meta-editors/list/types';
    import type {RangeValueEditorOptions} from 'shared/value-editors/internal/editors/range/types';
    import type {AutocompleteValueEditorOptions} from 'shared/value-editors/internal/editors/autocomplete/types';
    import type {SingleAcceptableValueEditorOptions} from 'shared/value-editors/internal/editors/single-acceptable/types';
    import type {MultipleAcceptableValueEditorOptions} from 'shared/value-editors/internal/editors/multiple-acceptable/types';
    import type {TelephoneNumberValueEditorOptions} from 'shared/value-editors/internal/editors/telephone-number/types';

    /* const formSettings1: KpUniversalFormSettings<any> = {
        fields: [
            {
                label: 'acceptable root',
                fieldName: 'acceptableRoot',
                editor: {
                    type: 'acceptable-root',
                    // isDisabled: true,
                    validations: {
                        required: true,
                    },
                    options: {
                        optionLabelResolver: ({option}) => option.text,
                        optionIdResolver: ({option}) => option.text,
                        disabledItems: [
                            {
                                text: '1-1-2'
                            },
                            {
                                text: '1-2',
                            }
                        ],
                        acceptableValue: {
                            text: '0',
                            children: [
                                {
                                    text: '1-1',
                                    children: [
                                        {
                                            text: '1-1-1'
                                        },
                                        {
                                            text: '1-1-2'
                                        }
                                    ]
                                },
                                {
                                    text: '1-2',
                                    children: [
                                        {
                                            text: '1-2-1'
                                        },
                                        {
                                            text: '1-2-2'
                                        }
                                    ]
                                }
                            ]
                        }
                    } as AcceptableRootValueEditorOptions<any>
                }
            },
            {
                fieldName: 'multipleAcceptableInline',
                label: 'multi acceptable inline',
                editor: {
                    type: 'multiple-acceptable',
                    validations: {
                        required: true,
                    },
                    options: {
                        reorderable: true,
                        switchToInlineModeThreshold: 3,
                        optionLabelResolver: ({option}) => option.text,
                        optionIdResolver: ({option}) => option.text,
                        // optionDisabledResolver: ({item}) => item.value === 'charlie' || item.value === 'golf',
                        // sortComparator: ({element1, element2}) => element1.value.at(0).localeCompare(element2.value.at(0)) * -1,
                        // sortModel: true,
                        acceptableValues: [
                            {text: 'alpha'},
                            {text: 'beta'},
                            {text: 'charlie'},
                            {text: 'delta'},
                            {text: 'echo'},
                            {text: 'foxtrot'},
                            {text: 'golf'},
                            {text: 'hotel'},
                            {text: 'india'},
                            {text: 'julie'},
                            {text: 'kilo'},
                            {text: 'lima'}],

                    } as MultipleAcceptableValueEditorOptions<any>
                }
            },
            {
                fieldName: 'multipleAcceptableBlock',
                label: 'multi acceptable block',
                editor: {
                    type: 'multiple-acceptable',
                    validations: {
                        // required: true,
                    },
                    isDisabled: true,
                    options: {
                        // reorderable: true,

                        switchToInlineModeThreshold: 0,
                        // emptyAsNull: true,
                        // sortModel: true,
                        optionLabelResolver: ({option}) => option.text,
                        optionIdResolver: ({option}) => option.text,
                        // optionDisabledResolver: ({item}) => item.value === 'c' || item.value === 'g',
                        // sortComparator: ({element1, element2}) => element1.value.localeCompare(element2.value) * -1,
                        // showFirstCount: 3,
                        // acceptableValues: ['one', 'two', 'three', 'four', 'five', 'six'],
                        acceptableValues: [
                            {text: 'alpha'},
                            {text: 'beta'},
                            {text: 'charlie'},
                            {text: 'delta'},
                            {text: 'echo'},
                            {text: 'foxtrot'},
                            {text: 'golf'},
                            {text: 'hotel'},
                            {text: 'india'},
                            {text: 'julie'},
                            {text: 'kilo'},
                            {text: 'some long ass not sensible options that does absolutely nothing'},
                            {text: 'lima'}],

                    } as MultipleAcceptableValueEditorOptions<any>
                }
            },
            {
                fieldName: 'multipleAcceptableBlockSm',
                label: ' multi acceptable block sm',
                editor: {
                    type: 'multiple-acceptable',
                    size: ValueEditorSize.SM,
                    validations: {
                        required: true,
                    },
                    options: {
                        showFirstCount: 3,
                        optionDisabledResolver: ({item}) => item === 'two' || item === 'six',
                        optionIdResolver: ({option}) => option,
                        optionLabelComponent: IdentityLabel,
                        acceptableValues: ['one', 'two', 'three', 'four', 'five', 'six'],

                    } as MultipleAcceptableValueEditorOptions<any>
                }
            },
            {
                fieldName: 'multipleAcceptableBlockXs',
                label: ' multi acceptable block xs',
                editor: {
                    type: 'multiple-acceptable',
                    size: ValueEditorSize.XS,
                    validations: {
                        required: true,
                    },
                    options: {
                        showFirstCount: 3,
                        optionIdResolver: ({option}) => option,
                        optionLabelComponent: IdentityLabel,
                        acceptableValues: ['one', 'two', 'three', 'four', 'five', 'six'],

                    } as MultipleAcceptableValueEditorOptions<any>
                }
            },
            {
                fieldName: 'acceptableBlock',
                label: 'acceptable block',
                editor: {
                    type: 'single-acceptable',
                    size: ValueEditorSize.SM,
                    isDisabled: true,
                    validations: {required: true},
                    options: {
                        allowSelectNull: true,
                        optionLabelResolver: ({option}) => option.text,
                        optionIdResolver: ({option}) => option.text,
                        // optionDisabledResolver: ({item}) => item?.value === 'g',
                        acceptableValues: [
                            {text: 'a'},
                            {text: 'b'},
                            {text: 'c'},
                            {text: 'd'},
                            {text: 'e'},
                            {text: 'f'},
                            {text: 'g'},
                            {text: 'h'}],
                        modelAsArray: true

                    } as SingleAcceptableValueEditorOptions<any>
                }
            },
            {
                fieldName: 'acceptableInline',
                label: 'acceptable inline',
                editor: {
                    type: 'single-acceptable',
                    // placeholder: 'Select...',
                    // validations: {required: true},
                    isDisabled: true,
                    options: {
                        allowSelectNull: true,
                        // optionDisabledResolver: ({item}) => item?.value === 'g',
                        optionIdResolver: ({option}) => option.text,
                        optionLabelResolver: ({option}) => option.text,
                        acceptableValues: [
                            {text: 'a'},
                            {text: 'b'},
                            {text: 'c'},
                            {text: 'd'},
                            {text: 'e'},
                            {text: 'f'},
                            {text: 'g'},
                            {text: 'h'}],
                        modelAsArray: true,
                        switchToInlineModeThreshold: 3,
                    } as SingleAcceptableValueEditorOptions<any>
                }
            },
            {
                fieldName: 'acceptableBlockBasic',
                label: 'acceptable block basic',
                editor: {
                    type: 'single-acceptable',
                    // validations: {required: true},
                    options: {
                        allowSelectNull: true,
                        switchToInlineModeThreshold: 0,
                        optionIdResolver: ({option}) => option,
                        optionLabelComponent: IdentityLabel,
                        optionDisabledResolver: ({item}) => item === 'four',
                        acceptableValues: ['one', 'two', 'three', 'four', 'five', 'six'],

                    } as SingleAcceptableValueEditorOptions<any>
                }
            },
            {
                fieldName: 'acceptableInlineBasic',
                label: 'acceptable inline basic',
                editor: {
                    type: 'single-acceptable',
                    // placeholder: 'Select...',
                    validations: {required: true},
                    options: {
                        allowSelectNull: true,
                        optionDisabledResolver: ({item}) => item === 'four',
                        optionIdResolver: ({option}) => option,
                        optionLabelComponent: IdentityLabel,
                        acceptableValues: ['one', 'two', 'three', 'four', 'five', 'six'],
                        modelAsArray: true,
                        switchToInlineModeThreshold: 3,
                    } as SingleAcceptableValueEditorOptions<any>
                }
            },
            {
                fieldName: 'autocomplete',
                label: 'autocomplete',
                editor: {
                    type: 'autocomplete',
                    validations: {
                        required: true,
                    },
                    options: {
                        dataSource: () => Promise.resolve(['one', 'two', 'three', 'four', 'five', 'six'])
                    } as AutocompleteValueEditorOptions
                }
            },
            {
                fieldName: 'html',
                label: 'html',
                editor: {
                    type: 'html',
                    options: {} as HtmlValueEditorOptions
                }
            },
            {
                fieldName: 'year',
                label: 'year',
                editor: {
                    type: 'year',
                    validations: {
                        minDate: 1985,
                        maxDate: 2015
                    } as YearValueEditorValidations
                }
            },
            {
                fieldName: 'date',
                label: 'date',
                editor: {
                    type: 'date',
                    validations: {
                        minDate: '1985-01-01T00:00:00.000+01:00',
                        maxDate: '2015-01-01T00:00:00.000+01:00'
                    } as DateValueEditorValidations
                }
            },
            {
                fieldName: 'num1',
                label: 'form label 1',
                hint: 'Toto je test hintu',
                editor: {
                    editorName: 'formName1',
                    type: 'number',
                    options: {...numberOptions1} as NumberValueEditorOptions,
                    validations: {max: 100, min: -100, required: true} as NumberValueEditorValidations
                }
            },
            {
                fieldName: 'num2',
                label: 'form label 2',
                editor: {
                    editorName: 'formName2',
                    type: 'number',
                    options: {...numberOptions1} as NumberValueEditorOptions,
                    validations: {max: 100, min: -100} as NumberValueEditorValidations
                }
            },
            {
                fieldName: 'num8',
                label: 'form label test',
                editor: {
                    editorName: 'formNameTestGenerable',
                    type: 'generable-text',
                    validations: {
                        required: true,
                        minlength: 3,
                        maxlength: 8
                    } as TextValueEditorValidations,
                    options: {
                        requestFunction: () => {
                            return new Promise((resolve, reject) => {
                                setTimeout(() => {
                                    reject(new Error('Error'));
                                }, 1000);
                            })
                        },
                    } as GenerableTextValueEditorOptions,
                }
            },

            {
                fieldName: 'numTestTextOrSelect',
                label: 'form label TextOrSelect',
                editor: {
                    editorName: 'formNameTestTextOrSelect',
                    type: 'text-or-select',
                    options: {
                        canDoAction: true,
                        dataSource: () => {
                            return Promise.resolve(['one', 'two', 'three'])
                        },
                    } as TextOrSelectValueEditorOptions,
                }
            },

            {
                fieldName: 'numTestSignature',
                label: 'Signatura',
                editor: {
                    editorName: 'formNameTestSignature',
                    type: 'signature',
                    validations: {
                        required: true
                    },
                    options: {
                        canDoAction: true,
                        dataSource: () => {
                            return Promise.resolve(['one', 'two', 'three'])
                        },
                    } as TextOrSelectValueEditorOptions,
                }
            },
            {
                fieldName: 'numTestAccessNumber',
                label: 'Přírůstkové číslo',
                editor: {
                    editorName: 'formNameTestAccessNumber',
                    type: 'access-number',
                    validations: {
                        required: true
                    },
                    options: {
                        canDoAction: true,
                        dataSource: () => {
                            return Promise.resolve(['one', 'two', 'three'])
                        },
                    } as TextOrSelectValueEditorOptions,
                }
            },
            {
                fieldName: 'formNameTestMetadata',
                label: 'Hlavní název',
                editor: {
                    editorName: 'formNameTestMetadataEditor',
                    type: 'text-with-metadata',
                    validations: {
                        required: true
                    } as TextValueEditorValidations,
                }
            },
            {
                fieldName: 'formNameTestScannable',
                label: 'form label Scanable Text',
                editor: {
                    editorName: 'formNameTestScannable',
                    type: 'scannable-text',
                    options: {
                        autosubmit: true
                    } as ScannableTextValueEditorOptions
                }
            },
            {
                fieldName: 'formNameTestPassword',
                label: 'form label Password',
                editor: {
                    editorName: 'formNameTestPassword',
                    type: 'password',
                    validations: {
                        notBlank: true
                    } as TextValueEditorValidations,
                    options: {
                        withConfirmation: true,
                    } as PasswordValueEditorOptions,
                }
            },
            {
                fieldName: 'formNameTestBooleanXS',
                label: 'form label Boolean',
                editor: {
                    editorName: 'formNameTestBooleanXS',
                    type: 'boolean',
                    size: ValueEditorSize.XS,
                    options: {
                        emptyAsNull: true,
                    },
                    validations: {
                        required: true,
                        assertTrue: true
                    } as BooleanValueEditorValidations
                }
            },
            {
                fieldName: 'formNameTestBooleansm',
                label: 'form label Boolean',
                editor: {
                    editorName: 'formNameTestBooleansm',
                    type: 'boolean',
                    size: ValueEditorSize.SM,
                    validations: {assertTrue: true} as BooleanValueEditorValidations
                }
            },
            {
                fieldName: 'formNameTestBooleanMD',
                label: 'form label Boolean',
                editor: {
                    editorName: 'formNameTestBooleanMD',
                    type: 'boolean',
                    isDisabled: true,
                    size: ValueEditorSize.MD,
                    validations: {required: true, assertTrue: true} as BooleanValueEditorValidations,
                    localizations: {description: 'nejaky popisek'}
                }
            },
            {
                fieldName: 'formNameTestHidden',
                label: 'form label Hidden',
                editor: {
                    editorName: 'formNameTestHidden',
                    type: 'hidden',
                }
            },
            {
                fieldName: 'formNameTestRange',
                label: 'form label Range',
                editor: {
                    editorName: 'formNameTestRange',
                    type: 'range',
                    // isDisabled: true,
                    options: {
                        pitPoints: [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
                    } as RangeValueEditorOptions,
                    localizations: {description: 'Some description'}
                }
            },
            {
                fieldName: 'formNameTestSearchOrEdit',
                label: 'form label Search or Edit',
                editor: {
                    editorName: 'formNameTestSearchOrEdit',
                    type: 'search-or-edit',
                    placeholder: 'Search some value...',
                    // isFocused: true,
                    validations: { required: true },
                    options: {
                        searchModelFunction: () => new Promise<any>((resolve) => setTimeout(() => resolve({value: 123}), 1000)),
                        editModelFunction: () => new Promise<any>((resolve) => setTimeout(() => resolve(''), 1000)),
                        allowToDeleteValue: true,
                        emptyAsNull: true,
                    } as SearchOrEditValueEditorOptions
                }
            },
            {
                fieldName: 'formNameTestField7',
                label: 'form label Field7',
                editor: {
                    editorName: 'formNameTestField7',
                    type: 'field7',
                    placeholder: 'Search some value...',
                    options: {
                        searchParams: {
                            rootFond: {
                                id: 1,
                                name: 'Monografie',
                                order: 1,
                                ofAuthority: false,
                                enabled: true,
                                editable: true,
                                periodical: false,
                                withExemplars: true,
                                forSourceDocument: false,
                                text: 'Monografie',
                                ofDocument: true
                            }
                        }

                    } as SearchOrEditValueEditorOptions<string>
                }
            },
            {
                fieldName: 'formNameTestField8',
                label: 'form label Field8',
                editor: {
                    editorName: 'formNameTestField8',
                    type: 'field8',
                    placeholder: 'Search some value...',
                    options: {
                        searchParams: {
                            rootFond: {
                                editable: true,
                                enabled: true,
                                forSourceDocument: false,
                                id: 1,
                                name: 'Monografie',
                                ofAuthority: false,
                                ofDocument: true,
                                order: 1,
                                periodical: false,
                                text: 'Monografie',
                                withExemplars: true
                            }
                        }

                    } as SearchOrEditValueEditorOptions<string>
                }
            },
            {
                fieldName: 'formNameTestRecord',
                label: 'form label editable-record-search',
                editor: {
                    editorName: 'formNameTestRecord',
                    type: 'editable-record-search',
                    options: {
                        searchParams: {
                            focusedFieldTypeId: 'a773.h',
                            rootFond: [55],
                            kind: 'record',
                            type: 'search-selection',
                        }

                    } as SearchOrEditValueEditorOptions<string>
                }
            },
            {
                fieldName: 'formNameTestRichRecordDocument',
                label: 'form label record-search',
                editor: {
                    editorName: 'formNameTestRichRecordDocument',
                    type: 'record-search',
                    options: {
                        searchParams: {
                            kind: 'record',
                            type: 'search-selection',
                        }
                    } as SearchOrEditValueEditorOptions<string>
                }
            },
            {
                fieldName: 'formNameTestRichRecordAuthority',
                label: 'form label record-search',
                editor: {
                    editorName: 'formNameTestRichRecordAuthority',
                    type: 'record-search',
                    options: {
                        searchParams: {
                            kind: 'record',
                            type: 'search-selection',
                        }
                    } as SearchOrEditValueEditorOptions<string>
                }
            },
            {
                fieldName: 'formNameTestUser',
                label: 'form label user',
                editor: {
                    editorName: 'formNameTestUser',
                    type: 'user',
                    options: {
                        searchParams: {
                            kind: 'user',
                            type: 'user-search',
                        }
                    } as SearchOrEditValueEditorOptions<string>
                }
            },
            {
                fieldName: 'formNameTestNumberRange',
                label: 'form label NumberRange',
                editor: {
                    editorName: 'formNameTestNumberRange',
                    type: 'number-range',
                    validations: {
                        max: 15
                    } as NumberRangeValueEditorValidations,
                    options: {
                        emptyAsNull: true,
                    }
                }
            },
            {
                fieldName: 'num3',
                label: 'form label 3i',
                hint: 'Hello from field hint',
                editor: {
                    editorName: 'formName3',
                    placeholder: 'some placeholder text',
                    type: 'text',
                    options: {...options1} as TextValueEditorOptions,
                    // validations: {required: true, async: true},
                    localizations: {
                        description: 'Toto je nejaky popis tohoto pole. Proste description.'
                    }
                }
            },
            {
                fieldName: 'textik',
                label: 'pre/su',
                editor: {
                    editorName: 'formName4',
                    placeholder: 'some placeholder text',
                    type: 'text',
                    options: {...options2} as TextValueEditorOptions,
                }
            },
            {
                fieldName: 'area',
                label: 'area',
                editor: {
                    editorName: 'formName5',
                    placeholder: 'some placeholder text',
                    type: 'text',
                    options: {...options3} as TextValueEditorOptions,
                }
            },
            {
                fieldName: 'html',
                label: 'html',
                editor: {
                    editorName: 'formName6',
                    placeholder: 'hic sunt leones',
                    type: 'text',
                    options: {...options4} as TextValueEditorOptions,
                    validations: {required: true},
                }
            },
            {
                fieldName: 'someObject',
                label: 'object',
                editor: {
                    type: 'object',
                    options: {
                        labelsWidth: 2,
                        fields: [
                            {
                                fieldName: 'name1',
                                label: 'label 1',
                                editor: {
                                    editorName: 'obj-name1',
                                    type: 'number',
                                    options: {...numberOptions1} as NumberValueEditorOptions,
                                    validations: {...numberValidations} as NumberValueEditorValidations
                                }
                            },
                            {
                                fieldName: 'name2',
                                label: 'label 2',
                                editor: {
                                    editorName: 'obj-name2',
                                    type: 'number',
                                    options: {...numberOptions2} as NumberValueEditorOptions,
                                    validations: {...numberValidations} as NumberValueEditorValidations
                                }
                            },
                        ]
                    } as ObjectValueEditorOptions<any>
                }
            },
            {
                fieldName: 'someList',
                label: 'LIST i',
                editor: {
                    editorName: 'someList',
                    type: 'list',
                    placeholder: 'click add to add',
                    options: {
                        sendWholeForm: true,
                        subEditor: {
                            options: {
                                type: 'rich-textarea'
                            } as TextValueEditorOptions,
                            validations: {
                                required: true,
                                minlength: 3,
                                maxlength: 8
                            } as TextValueEditorValidations
                        }
                    } as ListValueEditorOptions,
                    validations: {
                        required: true,
                        maxCount: 3
                    } as ListValueEditorValidations,
                }
            }
        ]
    } */
    const formSettings1: KpUniversalFormSettings<any> = {
        id: '',
        text: '',
        fields: [
            /* basic editors */
            {
                fieldName: 'inlineRecordSearch',
                label: 'Inline record search',
                editor: {
                    type: 'inline-record-search',
                    options: {
                        emptyAsNull: true,
                    },
                }
            },
            {
                fieldName: 'plainText',
                label: 'Text',
                editor: {
                    type: 'text',
                    options: {type: 'text', trim: true} as TextValueEditorOptions,
                }
            },
            {
                fieldName: 'telephoneNumber',
                label: 'Telefonní číslo',
                editor: {
                    type: 'telephone-number',
                    options: {forceSmsCapableOnly: false} as TelephoneNumberValueEditorOptions,
                }
            },
            {
                fieldName: 'telephoneNumberSmsOnly',
                label: 'SMS telefonní číslo',
                editor: {
                    type: 'telephone-number',
                    options: {forceSmsCapableOnly: true} as TelephoneNumberValueEditorOptions,
                }
            },
            {
                fieldName: 'plainNumber',
                label: 'Číslo',
                editor: {
                    type: 'number',
                }
            },
            {
                fieldName: 'autocomplete',
                label: 'Našeptávač (kontinenty)',
                editor: {
                    type: 'autocomplete',
                    options: {
                        dataSource: () => Promise.resolve(['Asie', 'Afrika', 'Amerika', 'Evropa', 'Austrálie a Oceánie', 'Antarktida'])
                    } as AutocompleteValueEditorOptions
                }
            },
            {
                fieldName: 'year',
                label: 'Rok',
                editor: {
                    type: 'year',
                }
            },
            {
                fieldName: 'bool',
                label: 'Přepínač (checkbox)',
                editor: {
                    type: 'boolean'
                }
            },
            {
                fieldName: 'password',
                label: 'Nastavení hesla',
                editor: {
                    type: 'password',
                    options: {
                        withConfirmation: true,
                    } as PasswordValueEditorOptions,
                }
            },

            /* complex editors */
            {
                fieldName: 'date',
                label: 'date',
                editor: {
                    type: 'date',
                }
            },
            {
                fieldName: 'range',
                label: 'Číselný rozsah s posuvníkem',
                editor: {
                    type: 'range',
                    options: {
                        pitPoints: [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
                    } as RangeValueEditorOptions,
                }
            },
            {
                fieldName: 'numberRange',
                label: 'Číselný rozsah',
                editor: {
                    type: 'number-range',
                    options: {
                        emptyAsNull: true,
                    }
                }
            },
            {
                fieldName: 'acceptableBlock',
                label: 'Přepínač s nabídkou možností (radio button)',
                editor: {
                    type: 'single-acceptable',
                    options: {
                        optionLabelResolver: ({option}) => option.text,
                        optionIdResolver: ({option}) => option.text,
                        acceptableValues: [
                            {text: 'alpha'},
                            {text: 'beta'},
                            {text: 'charlie'},
                            {text: 'delta'},
                            {text: 'echo'},
                            {text: 'foxtrot'},
                            {text: 'golf'},
                            {text: 'hotel'},
                            {text: 'india'},
                            {text: 'julie'},
                            {text: 'kilo'},
                            {text: 'lima'}],
                    } as SingleAcceptableValueEditorOptions<any>
                }
            },
            {
                fieldName: 'acceptableInline',
                label: 'Nabídka rozbalovací (select)',
                editor: {
                    type: 'single-acceptable',
                    options: {
                        optionIdResolver: ({option}) => option.text,
                        optionLabelResolver: ({option}) => option.text,
                        acceptableValues: [
                            {text: 'alpha'},
                            {text: 'beta'},
                            {text: 'charlie'},
                            {text: 'delta'},
                            {text: 'echo'},
                            {text: 'foxtrot'},
                            {text: 'golf'},
                            {text: 'hotel'},
                            {text: 'india'},
                            {text: 'julie'},
                            {text: 'kilo'},
                            {text: 'lima'}],
                        switchToInlineModeThreshold: 3,
                    } as SingleAcceptableValueEditorOptions<any>
                }
            },
            {
                fieldName: 'multipleAcceptableBlock',
                label: 'Výběr z možností (seznam zaškrtávacích políček)',
                editor: {
                    type: 'multiple-acceptable',
                    options: {
                        switchToInlineModeThreshold: 0,
                        showFirstCount: 7,
                        optionLabelResolver: ({option}) => option.text,
                        optionIdResolver: ({option}) => option.text,
                        acceptableValues: [
                            {text: 'alpha'},
                            {text: 'beta'},
                            {text: 'charlie'},
                            {text: 'delta'},
                            {text: 'echo'},
                            {text: 'foxtrot'},
                            {text: 'golf'},
                            {text: 'hotel'},
                            {text: 'india'},
                            {text: 'julie'},
                            {text: 'kilo'},
                            {text: 'lima'}],
                    } as MultipleAcceptableValueEditorOptions<any>
                }
            },
            {
                fieldName: 'multipleAcceptableInline',
                label: 'Výběr z možností jednořádkový (multi select)',
                editor: {
                    type: 'multiple-acceptable',
                    options: {
                        switchToInlineModeThreshold: 3,
                        optionLabelResolver: ({option}) => option.text,
                        optionIdResolver: ({option}) => option.text,
                        acceptableValues: [
                            {text: 'alpha'},
                            {text: 'beta'},
                            {text: 'charlie'},
                            {text: 'delta'},
                            {text: 'echo'},
                            {text: 'foxtrot'},
                            {text: 'golf'},
                            {text: 'hotel'},
                            {text: 'india'},
                            {text: 'julie'},
                            {text: 'kilo'},
                            {text: 'lima'}],
                    } as MultipleAcceptableValueEditorOptions<any>
                }
            },

            /* special cases */
            {
                fieldName: 'errorText',
                label: 'Text který je nutné zadat',
                editor: {
                    type: 'text',
                    options: {type: 'text', trim: true} as TextValueEditorOptions,
                    validations: {required: true}
                }
            },
            {
                fieldName: 'errorNumber',
                label: 'Číslo v rozsahu 0 až 100',
                editor: {
                    type: 'number',
                    validations: {
                        max: 100,
                        min: 0
                    } as NumberValueEditorValidations,
                }
            },
            {
                fieldName: 'errorEmail',
                label: 'E-mail musí být ve správném formátu',
                editor: {
                    type: 'text',
                    options: {type: 'email', trim: true} as TextValueEditorOptions,
                    validations: {notBlank: true, minlength: 3} as TextValueEditorValidations
                }
            },
            {
                fieldName: 'descriptionTest',
                label: 'Pole s popisem',
                editor: {
                    type: 'text',
                    localizations: {description: 'Popis tohoto formulářového prvku'}
                }
            },
            {
                fieldName: 'list',
                label: 'Dynamický seznam polí (seznam emailů)',
                editor: {
                    type: 'list',
                    options: {
                        subEditor: {
                            type: 'text',
                            options: {
                                type: 'email'
                            } as TextValueEditorOptions,
                            validations: {
                                required: true,
                                minlength: 3,
                            } as TextValueEditorValidations
                        }
                    } as ListValueEditorOptions,
                    validations: {
                        required: true,
                        maxCount: 3
                    } as ListValueEditorValidations,
                }
            }
        ]
    }

    const formOptions1: KpUniversalFormComponentOptions = {
        autofocusFirstField: false
    }

    let model4: Record<string, any> = {}

    let universalForm;
    let formData$: Observable<FormData<any>>;
    $: formData$ = (universalForm?.getFormController() as FormGroup<any>)?.getFormData$();

    let forceShowErrors = false;
</script>

<!--
<div style="margin: 4em 0">
    <KpDataGrid/>
</div>

<div style="margin: 4em 0">
    <KpCollectionsGrid/>
</div>
-->

<div aria-hidden="true">
    <button class="btn btn-default" aria-hidden="true" tabindex="-1"
            on:click={() => forceShowErrors = !forceShowErrors}>Force show errors [{forceShowErrors}]
    </button>
</div>


<KpUniversalForm bind:this={universalForm}
                 formId="formId1"
                 formName="formName1"
                 bind:model={model4}
                 formSettings={formSettings1}
                 options={formOptions1}
                 forceShowErrors={forceShowErrors}
                 labelsWidth={2}/>

<pre aria-hidden="true">{JSON.stringify(model4, null, 2)}</pre>
<pre aria-hidden="true">{JSON.stringify($formData$, null, 2)}</pre>
