<script lang="ts">
    import KpSvelteTest from './KpSvelteTest.svelte';
    import Loc from 'shared/components/kp-markdown/Loc.svelte';

    export let name = '';
    export let input = 0;
</script>

<div class="container" data-routeid="test-route-1">
    <Loc code="test.markdownTestLoc" params="{['PARAMETER TEST 1', 'PARAMETER TEST 2']}"/>

    <div class="row col-sm-12">
        <div aria-hidden="true">{name} - {input}</div>
        <KpSvelteTest/>
    </div>
</div>