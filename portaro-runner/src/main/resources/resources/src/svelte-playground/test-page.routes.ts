import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function testRoutes($stateProvider: StateProvider) {
    let componentModule1: { default: any; };
    let componentModule2: { default: any; };

    $stateProvider
        .state({
            name: 'test-page',
            url: '/test-page',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => componentModule1.default,
                props: () => ({
                    name: 'hello from lazy loaded component',
                    input: 1
                })
            },
            lazyLoad: async () => {
                componentModule1 = await import(/* webpackChunkName: "test-route-1" */ './TestRoute1.svelte');
                return null;
            }
        })
        .state({
            name: 'test-page-svelte',
            url: '/test-page-svelte',
            component: KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => componentModule2.default,
            },
            lazyLoad: async () => {
                componentModule2 = await import(/* webpackChunkName: "test-route-2" */ './TestRoute2.svelte');
                return null;
            }
        })
}
