import type {SvelteComponent} from 'svelte';
import {ClassInjector} from 'core/injector';
import {VoidLogger} from 'core/logging/void-logger';
import {
    CURRENT_LANGUAGE_KEY,
    DATE_FORMATTER_KEY,
    GLOBAL_EVENT_BUS_KEY,
    INJECTOR_KEY,
    LOCALIZE_KEY,
    LOGGER_KEY,
    SANITIZE_HTML_KEY,
    SERVER_URL_KEY
} from 'core/utils';
import type {
    Injector,
    SvelteComponentConstructor,
    SvelteComponentOptions,
    SvelteContext
} from 'core/types';

interface RenderOptions {
    container: HTMLElement;
    appendToBody: boolean;
}

export interface TestContext<T> {
    container: HTMLElement,
    componentInstance: SvelteComponent<T> & T,
    unmount: () => void;
}

export function createTestContext(injector: Injector): SvelteContext {
    const testContext = new Map<string, any>();
    testContext.set(INJECTOR_KEY, injector);
    testContext.set(LOCALIZE_KEY, (code: string) => code);
    testContext.set(SANITIZE_HTML_KEY, (html: string) => html);
    testContext.set(DATE_FORMATTER_KEY, () => (date: string | number) => date.toString());
    testContext.set(LOGGER_KEY, new VoidLogger());
    testContext.set(CURRENT_LANGUAGE_KEY, 'en');
    testContext.set(SERVER_URL_KEY, 'http://localhost');
    testContext.set(GLOBAL_EVENT_BUS_KEY, new EventTarget());
    return testContext;
}

export function render<T>(Component: SvelteComponentConstructor<T>,
                          componentOptions: Omit<SvelteComponentOptions<T>, 'target'>,
                          renderOptions: Partial<RenderOptions> = {appendToBody: false}): TestContext<T> {

    const container = renderOptions.container ?? document.createElement('div');

    if (renderOptions.appendToBody) {
        document.body.appendChild(container);
    }

    const componentInstance = new Component({target: container, ...componentOptions}) as SvelteComponent<T> & T;
    const unmount = () => {
        if (renderOptions.appendToBody) {
            container.remove();
        }
        componentInstance.$destroy();
    };

    return {container, componentInstance, unmount};
}

export function click(element: HTMLElement): void {
    element.click();
}

export class SvelteComponentEventsListener {
    private cleanupHandlers: (() => void)[] = [];

    private constructor(private component: SvelteComponent) {
    }

    public static getFor(component: SvelteComponent): SvelteComponentEventsListener {
        return new SvelteComponentEventsListener(component);
    }

    public on(eventName: string, handler: (_: any) => void): SvelteComponentEventsListener {
        const cleanupHandler = this.component.$on(eventName, handler);
        this.cleanupHandlers.push(cleanupHandler);
        return this;
    }

    public cleanup(): void {
        this.cleanupHandlers.forEach((cleanupHandler) => cleanupHandler());
    }
}

export class ManualInjector extends ClassInjector implements Injector {

    constructor(private dependencies: Map<string, any> = new Map()) {
        super();
    }

    public register(token: string, object: any): ManualInjector {
        this.dependencies.set(token, object);
        return this;
    }

    public getByToken<T>(token: string): T {
        return this.dependencies.get(token);
    }
}