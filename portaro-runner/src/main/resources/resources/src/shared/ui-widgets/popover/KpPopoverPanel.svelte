<script lang="ts">
    import './popover-transitions.less';
    import type {ActionArray} from 'shared/utils/svelte-actions-utils';
    import {assertExists, exists, uuid} from 'shared/utils/custom-utils';
    import {useActions} from 'shared/utils/svelte-actions-utils';
    import {createEventDispatcher} from 'svelte';
    import {stopEventsPropagation} from 'shared/svelte-actions/use.stop-events-propagation';

    export let id: string = uuid();
    export let open = false;
    export let use: ActionArray = [];
    export let additionalClasses = '';
    export let popoverMode: 'auto' | 'manual' = 'auto';

    const dispatch = createEventDispatcher<{'popover-close': void; 'popover-open': void}>();
    let popoverRef: HTMLElement;

    $: if (exists(popoverRef)) handleOpenState(open);

    export function togglePopover() {
        assertExists(popoverRef);
        popoverRef.togglePopover();
    }

    function handleOpenState(shouldOpen: boolean) {
        assertExists(popoverRef);
        if (shouldOpen) {
            showPopover();
            return;
        }

        const shouldClose = !shouldOpen;
        if (shouldClose) {
            hidePopover();
            return;
        }
    }

    function showPopover() {
        assertExists(popoverRef);
        popoverRef.showPopover();
    }

    function hidePopover() {
        assertExists(popoverRef);
        popoverRef.hidePopover();
    }

    function syncPopoverState(event: ToggleEvent) {
        open = event.newState === 'open';
    }

    function dispatchChangeEvents(event: ToggleEvent) {
        if (event.newState === 'open') {
            dispatch('popover-open');
        } else {
            dispatch('popover-close');
        }
    }
</script>

<div {id}
     class="kp-popover-panel {additionalClasses}"
     popover="{popoverMode}"
     tabindex="-1"
     use:stopEventsPropagation={['click', 'mousedown', 'mouseup', 'contextmenu', 'keydown', 'keyup', 'keypress']}
     use:useActions={use}
     on:toggle={syncPopoverState}
     on:toggle={dispatchChangeEvents}
     bind:this={popoverRef}>

    {#if exists($$slots['popover-title'])}
        <div class="title">
            <slot name="popover-title"/>
        </div>
    {/if}

    <div class="content">
        <slot {open} {togglePopover}/>
    </div>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .kp-popover-panel {
        :global(&) {
            --popover-bg-color: var(--body-bg);
            --popover-border-color: var(--border-default);
            --popover-border-radius: var(--portaro-border-radius-large);
            --popover-box-shadow: 0 5px 10px rgba(0, 0, 0, .2);

            --popover-title-padding: 8px 14px;
            --popover-title-bg-color: var(--panel-bg);
            --popover-title-border-color: var(--border-muted);

            --popover-content-padding: 9px 14px;
        }

        max-width: 800px; // TODO: should be depended on screen size
        max-height: 50dvh;
        overflow-y: auto;
        z-index: @popover-z-index; // to fix clipping during closing animation (maybe only if popover is inside modal)
        text-align: left;
        color: inherit;
        white-space: normal;

        background-color: var(--popover-bg-color);
        border: 1px solid var(--popover-border-color);
        border-radius: var(--popover-border-radius);
        box-shadow: var(--popover-box-shadow);

        .title {
            padding: var(--popover-title-padding);
            background-color: var(--popover-title-bg-color);
            border-bottom: 1px solid var(--popover-title-border-color);
        }

        .content {
            background-color: transparent;
            padding: var(--popover-content-padding);
        }
    }
</style>