<script lang="ts">
    import type {ButtonStyle, ComponentSize} from '../types';
    import type {Placement} from '@floating-ui/core';
    import {exists, uuid as generateUuid} from 'shared/utils/custom-utils';
    import {createFloatingActions} from 'svelte-floating-ui';
    import {floatingUiCommonSettings} from 'shared/utils/floating-ui-common-settings';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpPopoverPanel from 'shared/ui-widgets/popover/KpPopoverPanel.svelte';

    export let id: string = generateUuid();
    export let buttonStyle: ButtonStyle = 'default';
    export let buttonSize: ComponentSize = 'md';
    export let placement: Placement = 'bottom';
    export let additionalPopoverButtonClasses = '';
    export let additionalPopoverPanelClasses = '';

    $: buttonId = `${id}-popover-button`;
    $: panelId = `${id}-popover-panel`;

    let popoverOpen = false;

    const [floatingRef, floatingContent] = createFloatingActions(floatingUiCommonSettings(placement));
</script>

{#if !exists($$slots['whole-button'])}
    <KpButton id="{buttonId}"
              {buttonSize}
              {buttonStyle}
              additionalClasses="{additionalPopoverButtonClasses}"
              use={[floatingRef]}
              popovertarget="{panelId}">

        <slot name="button" {popoverOpen}/>
    </KpButton>
{:else}
    <slot name="whole-button" {buttonId} {panelId} {floatingRef} {popoverOpen}/>
{/if}

{#if exists($$slots['popover-title'])}
    <KpPopoverPanel id="{panelId}"
                    use={[floatingContent]}
                    additionalClasses="{additionalPopoverPanelClasses}"
                    on:popover-open
                    on:popover-close
                    bind:open={popoverOpen}
                    let:togglePopover
                    let:open>

        <slot name="popover-title" slot="popover-title"/>
        <slot name="popover-content" {open} {togglePopover}/>
    </KpPopoverPanel>
{:else}
    <KpPopoverPanel id="{panelId}"
                    use={[floatingContent]}
                    additionalClasses="{additionalPopoverPanelClasses}"
                    on:popover-open
                    on:popover-close
                    bind:open={popoverOpen}
                    let:togglePopover
                    let:open>

        <slot name="popover-content" {open} {togglePopover}/>
    </KpPopoverPanel>
{/if}
