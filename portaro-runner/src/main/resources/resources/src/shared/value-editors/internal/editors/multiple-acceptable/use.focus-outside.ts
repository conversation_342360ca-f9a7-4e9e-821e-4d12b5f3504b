import type {ActionReturn} from 'svelte/action';
import {isNotNull} from 'shared/utils/custom-utils';
import {assertIsElement, assertIsNode} from 'shared/utils/types-utils';
import {tick} from 'svelte';

export const FOCUS_OUTSIDE_EVENT_TYPE = 'focus-outside';
type OnFocusOutsideEventType = `on:${typeof FOCUS_OUTSIDE_EVENT_TYPE}`;

type Attributes = {
    [key in OnFocusOutsideEventType]?: (e: CustomEvent<void>) => void;
};

interface Parameters {
    ignoreLostFocusCause: boolean;

}

export function focusOutside(node: HTMLElement, params: Parameters = {ignoreLostFocusCause: true}): ActionReturn<Parameters, Attributes> {

    const focusoutListener = async (event: FocusEvent) => {
        const focusOrigin = event.target;
        const newFocusTarget = event.relatedTarget;

        if (isNotNull(newFocusTarget)) {
            assertIsNode(newFocusTarget);
            if (!node.contains(newFocusTarget)) {
                node.dispatchEvent(new CustomEvent(FOCUS_OUTSIDE_EVENT_TYPE))
            }
            return;
        }

        if (params.ignoreLostFocusCause) {
            node.dispatchEvent(new CustomEvent(FOCUS_OUTSIDE_EVENT_TYPE));
            return;
        }

        await tick();
        assertIsElement(focusOrigin);
        if (isElementStillFocusable(focusOrigin)) {
            node.dispatchEvent(new CustomEvent(FOCUS_OUTSIDE_EVENT_TYPE))
        }
    }

    const abortController = new AbortController();
    node.addEventListener('focusout', (event) => {focusoutListener(event)}, {signal: abortController.signal});

    return {
        destroy: () => abortController.abort()
    }
}

function isElementStillFocusable(focusOrigin: Element): boolean {
    if (!focusOrigin.isConnected) { // element removed from DOM
        return false;
    }

    if ('disabled' in focusOrigin && focusOrigin.disabled) {
        return false
    }

    const style = getComputedStyle(focusOrigin);
    return !(style.display === 'none' || style.visibility === 'hidden');
}