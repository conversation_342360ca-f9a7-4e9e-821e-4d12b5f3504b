import type {IHttpProvider, IParseProvider} from 'angular';
import type {DataViewPagesConfiguration} from 'src/features/erp-sutor/pages/data-view/types';
import type {ErpFondPermissionsContextService} from 'src/features/erp/services/erp-fond-permissions-context.service';
import type {AjaxServiceProvider} from 'core/data-services/ajax.service';
import type {FileDownloadServiceProvider} from 'core/data-services/file-download.service';
import type {SseService} from 'shared/realtime/sse.service';
import type {ThreadsContextService} from 'src/features/threads/services/threads-context.service';
import type ObservableAjaxServiceProvider from 'core/data-services/observable-ajax.service';
import {DateTime, Settings} from 'luxon';
import {initUrlUtilsHelper} from 'shared/utils/url-utils';
import {initNgDecoratorsHelper} from 'shared/utils/ng-@decorators';
import {LogService} from 'core/logging/log.service';
import {ignoreUnusedProperties} from 'shared/utils/custom-utils';
import register from '@kpsys/angularjs-register';
import ModalConversationInterceptorService from './modals/modal-conversation-interceptor.service';
import {LOG_LEVELS} from 'core/logging/constants';
import ngSanitize from 'angular-sanitize';
import uiRouter from '@uirouter/angularjs';

import 'angular';
import 'slick-carousel';
import 'angular-slick-carousel';

// App modules
import componentsModule from './shared/components/components.module';
import filtersModule from './shared/filters/filters.module';
import providersModule from './shared/providers/providers.module';
import servicesModule from './shared/services/services.module';
import dataServiceModule from './shared/data-services/data-services.module';
import mainMenuModule from 'shared/components/kp-main-menu/kp-main-menu.module';
import headerModule from 'shared/components/kp-header/kp-header.module';
import footerModule from 'shared/components/kp-footer/kp-footer.module';
import searchModule from './features/search/search.module';
import modalsModule from './modals/modals.module';
import loginModule from './shared/login/login.module';
import editationModule from './features/record-editation/record-editation.module';
import barcodeScannerModule from './shared/barcode-scanner/barcode-scanner.module';
import fileModule from './features/file/file.module';
import loanCategoriesModule from './features/loan-categories/loan-categories.module';
import exemplarStatusModule from './features/exemplar-status/exemplar-statuses.module';
import acquisitionModule from './features/acquisition/acquisition.module';
import apidocsModule from './features/apidocs/apidocs.module';
import changelogModule from './features/changelog/changelog.module';
import favouritesModule from './features/favourites/favourites.module';
import fondsModule from './features/fonds/fonds.module';
import homeModule from './features/home/<USER>';
import inventoryModule from './features/inventory/inventory.module';
import thesaurusModule from './features/thesaurus/thesaurus.module';
import userModule from './features/user/user.module';
import recordModule from './features/record/record.module';
import sdiRequestsModule from './features/sdi-requests/sdi-requests.module';
import settingsModule from './features/settings/settings.module';
import utilsModule from './features/util/utils.module';
import reportServerModule from './features/report-server/report-server.module';
import messagesModule from './features/messages/messages.module';
import localizationModule from './features/localizations/localizations.module';
import coreModule from './core/core.module';
import sveltePlaygroundModule from './svelte-playground/svelte-playground.module';
import pageModule from './shared/page/page.module';
import globalSearchModule from 'shared/global-search/kp-global-search.module';
import departmentsModule from './features/departments/departments.module';
import filtersPageModule from './features/filters/filters-page.module';
import locationsModule from './features/locations/locations.module';
import paymentModule from './features/payment/payment.module';
import facetTypesModule from './features/facet-types/facet-types.module';
import tenderModule from './features/tender/tender.module';
import recordExportModule from './features/record-exports/record-export.module';
import valueEditorsModule from './shared/value-editors/value-editors.portaro.module';
import statsModule from './features/stats/stats.module';
import verbisboxModule from './features/verbisbox/verbisbox.module';
import discountApprovingModule from 'src/features/discount-approving/lib/discount-approving.module';
import customElementsModule from './shared/custom-elements/custom-elements.module';
import seekingsModule from './features/ill/seekings.module';
import loanModule from './features/loan/loan.module';
import attributionsModule from './features/attributions/attributions.module';
import mediaViewerModule from './features/media-viewer/media-viewer.module';
import recordGridModule from './features/record-grid/record-grid.module';
import definedActionsModule from 'shared/defined-actions/defined-actions.module';
import stateManagerModule from 'shared/state-manager/state-manager.module';
import historicalPlacesModule from './features/historical-places/historical-places.module';
import exemplarPlacementsModule from './features/exemplar-placements/exemplar-placements.module';
import sutorModule from 'src/features/erp-sutor/sutor.module';
import mailerModule from 'shared/mailer/mailer.module';
import erpModule from './features/erp/erp.module';
import threadsModule from 'src/features/threads/threads.module';
import customPagesModule from 'src/features/custom-pages/custom-pages.module';
import realtimeModule from 'shared/realtime/realtime.module';
import networkInfoModule from 'shared/network-info/network-info.module';
import modalDialogsModule from 'shared/modal-dialogs/modal-dialogs.module';
import {setupGlobalErrorLogging} from 'core/logging/setup';
import {setupDebugListeners} from 'core/debug-util/setup';
import {InMemoryLogger} from 'core/logging/in-memory-logger';
import type {PortaroVersion} from 'typings/portaro.be.types';

declare global {
    interface Window {
        // Configuration
        portaroConfiguration: {
            loggingLevel: string;
            cameraScannerEnabled: boolean;
            globalSearchInputEnabled: boolean;
            forAdministrationOnly: boolean;
            isSutorSutinLayout: boolean;
        };

        // Custom data-iew pages (with tabbed data-grids)
        customDataViewPagesConfiguration: DataViewPagesConfiguration,

        // Application info
        serverUrl: string;
        portaroVersion: PortaroVersion;
    }
}

// Set default locale
Settings.defaultLocale = DateTime.local().resolvedLocaleOptions().locale;

/**
 * @ngdoc module
 * @name portaro
 * @module portaro
 */
const portaro = register('portaro', [
    'slickCarousel',
    ngSanitize,
    uiRouter,

    componentsModule,
    providersModule,
    servicesModule,
    dataServiceModule,
    filtersModule,
    globalSearchModule,
    barcodeScannerModule,
    pageModule,
    mainMenuModule,
    headerModule,
    footerModule,
    modalsModule,
    loginModule,
    searchModule,
    loanCategoriesModule,
    departmentsModule,
    filtersPageModule,
    exemplarStatusModule,
    locationsModule,
    paymentModule,
    facetTypesModule,
    tenderModule,
    recordExportModule,
    loanModule,
    acquisitionModule,
    apidocsModule,
    changelogModule,
    homeModule,
    thesaurusModule,
    userModule,
    recordModule,
    favouritesModule,
    fondsModule,
    inventoryModule,
    seekingsModule,
    sdiRequestsModule,
    editationModule,
    settingsModule,
    utilsModule,
    messagesModule,
    reportServerModule,
    localizationModule,
    fileModule,
    coreModule,
    sveltePlaygroundModule,
    valueEditorsModule,
    statsModule,
    verbisboxModule,
    customElementsModule,
    attributionsModule,
    mediaViewerModule,
    recordGridModule,
    definedActionsModule,
    stateManagerModule,
    historicalPlacesModule,
    exemplarPlacementsModule,
    sutorModule,
    mailerModule,
    erpModule,
    discountApprovingModule,
    threadsModule,
    customPagesModule,
    realtimeModule,
    networkInfoModule,
    modalDialogsModule
])
    .constant('portaroVersion', window.portaroVersion)
    .constant('API_BASE_URL', '/api')
    .constant('SERVER_URL', window.serverUrl)
    .constant('CAMERA_SCANNER_ENABLED', window.portaroConfiguration.cameraScannerEnabled)
    .constant('GLOBAL_SEARCH_INPUT_ENABLED', window.portaroConfiguration.globalSearchInputEnabled)
    .constant('FOR_ADMINISTRATION_ONLY', window.portaroConfiguration.forAdministrationOnly)
    .constant('GLOBAL_EVENT_BUS', new EventTarget()) // TODO: Remove when migrating from Angular bs
    .config(/*@ngInject*/ ($parseProvider: IParseProvider) => $parseProvider.addLiteral('Infinity', Number.POSITIVE_INFINITY))
    .config(/*@ngInject*/ ($httpProvider: IHttpProvider) => $httpProvider.interceptors.push(ModalConversationInterceptorService.serviceName))
    .config(/*@ngInject*/ ($httpProvider: IHttpProvider) => {
        $httpProvider.defaults.cache = false;
        Object.assign($httpProvider.defaults.headers, {get: {'If-Modified-Since': 0}});
    })
    .config(/*@ngInject*/ ($urlRouterProvider) => $urlRouterProvider.otherwise(() => {
        const replacingRegex = /(\/+$)/;
        const trimmedCurrentUrl = window.location.pathname.replace(replacingRegex, '');

        if (trimmedCurrentUrl === '') {
            return '/';
        }
    }))
    .config(/*@ngInject*/ (ajaxServiceProvider: AjaxServiceProvider, fileDownloadServiceProvider: FileDownloadServiceProvider, observableAjaxServiceProvider: ObservableAjaxServiceProvider, API_BASE_URL: string) => {
        ajaxServiceProvider.setBaseUrl(API_BASE_URL);
        observableAjaxServiceProvider.setBaseUrl(API_BASE_URL);
        fileDownloadServiceProvider.setBaseUrl(API_BASE_URL);
    })
    .config(/*@ngInject*/ ($sanitizeProvider) => $sanitizeProvider.addValidAttrs(['style']))
    .run(initUrlUtilsHelper)
    .run(initNgDecoratorsHelper)
    .run(/*@ngInject*/ (urlStateManagerService) => {
        ignoreUnusedProperties(urlStateManagerService);
        /* to bootstrap urlStateManagerService just before $locationChangeSuccess event fired */
    })
    .run(/*@ngInject*/ (logService: LogService) => {
        const inMemoryLogger = new InMemoryLogger(100);
        logService
            .addLogger(inMemoryLogger)
            .setLoggingLevel(LOG_LEVELS[window.portaroConfiguration.loggingLevel] ?? LogService.defaultLevel);

        setupGlobalErrorLogging(logService);
        setupDebugListeners(inMemoryLogger);
    })
    // Initialize ERP stuff - SSE, threads, fond permissions
    .run(/*@ngInject*/ (sseService: SseService, threadsContextService: ThreadsContextService, erpFondPermissionsContextService: ErpFondPermissionsContextService) => {
        erpFondPermissionsContextService.setup();

        if (window.portaroConfiguration.isSutorSutinLayout) {
            sseService.setup();
            threadsContextService.setup();
        }
    })
    .module();

export default portaro;
