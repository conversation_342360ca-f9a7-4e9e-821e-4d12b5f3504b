
# Svelte Code Style & Svelte Migration Guide

## Style Guide

- nepsat styl do sablony - pouzivat css v casti `<style>`

- psat male komponenty

- vyextrahovat z templatu male reusable utility/hloupe komponenty

- velke angularjs komponenty rozdelit na mensi (hlavne pokud se nekde opakuje kod sablony)

### TypeScript importy

Typescript importy ktere se importuji jen kvuli deklaraci typu:

```typescript
import {SomeType} from 'path/some-type'

let x: SomeType;
```

a ne k pouziti jako hodnota (napr. funkce, konstruktor):

```typescript
import {SomeType, fce} from 'path/some-type'

let x = new SomeType();
fce();
```

***se kvuli kompilaci svelte komponent musi importovat jako `import type`***

```typescript
import type {SomeType} from 'path/some-type'

let x: SomeType;
```

viz. [ts official docs](https://www.typescriptlang.org/docs/handbook/release-notes/typescript-3-8.html#type-only-imports-and-export)

### Types Refactoring

Pri refactoringu se snazte pridavat chybejici typy, tak aby co nejvic veci bylo otypovanych.

Jde zj. parametry a navratove hodnoty exportovanych funkci a public metod exportovanych trid
(***I pres to, ze to IDE a intellisence to rozpoznavaji automaticky.***).

Ale taky o veci prepisovane z javascriptu a chybejici typy pro DTO z backendu.

### Struktura Projektu

Bylo by dobre se ridit napr. strukturou jako pouziva Angular 2.

Delit moduly na:

- **core** 
  - jeden hlavni modul resici infrastrukturu (security, routing, http, atd)
  - `src/core`
  
- **shared**
  - jeden modul obsahujici spolecnou obecnou funkcionalitu, utility funkce a obecne/utility komponenty, ui-widgety
  - muze se delit na submoduly
  - `src/shared`

- **feature**
  - modul obsahujici vsechen kod, ktery spolu logicky souvisy (servicy, presentery, komponenty, definice typu, util funkce)
  - Napr. editace zaznamu nebo revize.
  - jednotlive moduly v `src/features`

- **modal**
  - modul ktery obsahje komponentu representujici modalni dialog a souvisejici funkce
  - jednotlive moduly v `src/modals`

- page (TBD)
  - modul ktery komponentu representujici jednou stranku aplikace + routovaci pravidla
  - jednotlive moduly v `src/pages`


### Naming Conventions

`PascalCase.svelte` for svelte component files and `kebab-case.ts` for typescript files.
Use `use.kebab-case.ts` for svelte action files and `some-service.service.ts` for services.

Slot and event names are also `kebab-case`.

### Pridavani Zavislosti (Dependency Encapsulation)
Cilem je snizit zasah zavislosti (ciziho kodu) do portara.
Pri pouziti nejakeho ciziho kodu (angular, bootstrap komponenty, nejaka knihovna/modul)
je nejlepsi kolem takoveho kodu napsat nejaky wrapper, ktery definuje API
pro pouziti v portaru. Toto se hodi hlavne pokud danou knihovnu pouzivame na vice mistech. 
Pri vymene dane zavislosti pak staci jen predelat implementaci
wrapperu a neni treba delat vetsi zasahy vsude v kodu.

Dobrym prikladem pak je treba `Injector`, `LocalizeFunction` a `SanitizeHtmlFunction` ze `core/utils`.
Jejich soucasne implementace vyuzivaji angular,
ale potom bude stacit jen zmenit jejich implementaci bez zasahu v miste jejich pouziti.

Ze starsiho kodu je docela dobrym prikladem `ToasterService`
(az na toaster-outlet komponentu bez wrapperu v sablone, ale jedna se o jedine pouziti, tak to tolik nevadi)


## Architektura

Cilem je co nejmene logiky v UI komponentach, ktere jsou zavisle na frameworku. 
Proto bude rozhrani mezi UI a business logikou resit specialni servica - presenter.

Presenter bude mit pomoci DI injektovane potrebne servicy 
a bude resit presentacni logiku (komunikace se servicami a priprava dat pro UI). 
V presenteru bude vetsina kodu, ktery se ted nachazi v angularjs controllerech. 
Samotna UI (svelte) komponenta si pak pri inicializaci pomoci dependency injektoru
(viz. [nize](#inicializace-svelte-komponenty-injektovani-portaro-zavislosti))
injektne prislusny presenter a ten pouziva.

**Presenter musi byt stateless (bezstavovy)** aby sel sdilet vsemi instancemi dane komponenty. 
Pokud by bylo nutne aby byl presenter stateful (stavovy) je potreba implementovat factory
ktera ho bude intanciovat a tu injektovat jednotlivym komponentam.

Ciste UI komponenty (napr formatovani dat) ktere nepotrebuji zadne Portaro zavislosti nemusi mit vlastni presenter.

### Komunikace komponent

Smer dolu: props.

Smer nahoru: events ([createEventDispatcher](https://svelte.dev/docs#createEventDispatcher))

Jinak pouzit nejakou servisu (a pripadne RxJs).

### Postup Migrace AngularJS Komponent a Direktiv se Sablonami

1. Vytvorit presenter

2. Presunout co nejvice logiky z kontroleru do presenteru (zj. tu, ktera je nezavisla na UI)

3. Vytvorit novou Svelte komponentu a inicializovat ji
   ([injektnout dependencies](#inicializace-svelte-komponenty-injektovani-portaro-zavislosti))

4. Do casti `<script>` presunout a upravit zbytek kodu z kontroleru 
   (obvykle veci zavisle na life cyklu komponenty v ngOnInit, ngPostLink, onDestroy, atd.)
   Angular-specific funkce nahradit [necim jinym](#migrace-angular-funkci)

5. do casti `<template>` presunout a upravit sablonu z `.ftl` souboru (viz. pravidla 
   [pro angular direktivy](#migrace-angular-direktiv),
   [portaro direktivy](#migrace-portaro-direktiv-todo) a
   [lokalizaci](#migrace-lokalizace-v-sablonach))

6. do casti `<style>` presunout obsah z `.less` souboru prislusejiciho komponente

Pokud je komponenta moc velka, nebojte se ji rozdelit do mensich pod-komponent.


## Struktura Svelte Komponenty

V tomto poradi:

- importy

- deklarace custom typů a interfaců

- deklarace props

- instanciovani zavislosti (svelte funkce, lokalizace, presenter, ...)

- inicializace promenych

- onMount, beforeUpdate, $, afterUpdate, onDestroy

- helper funkce

### Inicializace Svelte Komponenty (Injektovani Portaro Zavislosti)

```ts
// utilities provided by root component or svelte-component-wrapper using the svelte context:
import {getInjector, getLocalization, getSanitize, getDateFormatter} from '<path-to-svelte-utils>/core/svelte-context/context';

const injector = getInjector(); // get injector for DI container
const localize = getLocalization(); // get localization function for message localization
const sanitizeHtml = getSanitize(); // get function for sanitizing raw htlm befero appending it to the DOM
const dateFormatter = getDateFormatter(); // replacement for jpDate angular filter, unlike simple filters (pure functions) this requires injections of other services
// all these injections are optional, use only those required by the component

const presenter = injector.getByToken<KpDocumentMiniPresenter>(KpDocumentMiniPresenter.presenterName); // inject presenter or presenter factory via the injector
```

Injector je wrapper (facade) nad DI kontejnerem. 
Zatim je pouzity $injector z angularu, pozdeji bude nahrazen necim jinym.
Pro distribuci injektoru (a lokalizacni funkce) do komponent se pouziva 
[Svelte Context API](https://svelte.dev/tutorial/context-api).
Je to takove primitivni DI, je to lepsi nez klasicky import (natvrdo zadratovana zavislost).
Hodi se to hlavne na testovani (injektovani testovaciho kontejneru). 

### Dokumentace svelte komponent

zatim se nikde nepouziva

```html
<!--
 @component
 Here's some documentation for this component. It will show up on hover for
 JavaScript/TypeScript projects using a LSP-compatible editor such as VSCode or
 Vim/Neovim with coc.nvim.

 - You can use markdown here.
 - You can use code blocks here.
 - JSDoc/TSDoc will be respected by LSP-compatible editors.
 - Indentation will be respected as much as possible.
-->

<!-- @component You can use a single line, too -->

<!-- @component But only the last documentation comment will be used -->
```


## Pouziti kp-svelte-component-wrapper

Pro integraci svelte komponent do angularu slouzi kp-svelte-component-wrapper komponenta.

```html
<kp-svelte-component-wrapper
        component="::$ctrl.svelteComponent"
        props="::{name: $ctrl.name, input: $ctrl.input}"
        ng-on-change="$ctrl.onChange($event.originalEvent)"
        ng-on-form_submit="$ctrl.onEvent($event.originalEvent)">
</kp-svelte-component-wrapper>
```

`component` = importovana svelte komponenta

`props` = input props svelte componenty, jsou reaktivni (zmena z angularu se projevi ve vnorene svelte komponente)

`ng-on-` = navazani event listeneru na svelte komponentu, soucasti je jmeno eventu.
Priklad: `ng-on-change` posloucha na event type `'change'` a `ng-on-form_submit` posloucha na `'formSubmit'`.
Dalsi info viz. oficialni angularjs dokumentace direktivy ngOn.

`::` znaci one-time binding coz optimalizace aby angular nekontroloval zmenu bindingu (muze snizit overhead).
Hodi se pokud se props nemeni.

Pri migraci se wrapper nahradi nativni svelte komponentou:

```html
<SvelteComponent name={name} input={input} on:change={onChange} on:formSubmit={onEvent}></SvelteComponent>

<!-- nebo zkracene -->
<SvelteComponent {name} {input} on:change={onChange} on:formSubmit={onEvent}/>
```


## Migrace Lokalizace v Sablonach

Soucasny zpusob reseni lokalizace uz neni ve svelte mozny.

```
${loc.loc("commons.navstivene")?noEsc}
```

Ve svelte komponentach je nutne pouzit novou lokalizacni funkci `LocalizeFunction`.
Tuto funkci si komponenta muze injektnout ze svelte kontextu 
(viz [inicializace komponenty](#inicializace-svelte-komponenty-injektovani-portaro-zavislosti)).
Funkci jde potom normalne pouzit v sablone:

```
{localize(/* @kp-localization commons.navstivene */ 'commons.navstivene')}
```

Komentar `/* @kp-localization commons.navstivene */` je nutny k tomu aby nas webpack plugin behem buildu
nasel vsechny potrebne lokalizacni hlasky a nacetl si je do freemarker sablony.
(Hlasky pak lokalizuje server pri nacteni stranky).

***Bez tohoto komentare nemusi lokalizace fungovat!***


## Migrace Angular Direktiv

ng-class -> pouzit direktivu `class:`

ng-style -> predelat na css class a pouzit direktivu `class:`

ng-click -> pouzit directivu `on:` pr.: (`ng-click="callback()"` -> `on:click={callback}`)

ng-change -> on:change

ng-on-event -> on:event

ng-on-long_event_name -> on:longEventName

ng-src -> nativni src atribut + interpolace (`src={url}`)

ng-href -> nativni href atribut + interpolace

ng-if -> svelte `{#if ...}` template syntax

ng-disabled -> pouzit direktivu use: a novou svelte action useDisabledWhen (`use:useDisabledWhen={condition}`)

ng-show, ng-hide -> `hidden={condition}` nebo pouzit css (class s `content: none` + direktiva `class:`)
pripadne jako ng-if protoze ng-show/ng-hide meni jen viditelnost elementu a ng-if manipuluje primo s DOMem.
Pokud se neco meni casto je lepsi jen schovavat (napr loading spinner) jinak staci prima manipulace ({#if}).
V angular komponentach se nekdy pouziva ng-show spatne misto ng-if, bylo by dobre to pri prepisovani udelat takto.

ng-bind -> template interpolation ({...})

ng-repaeat -> svelte `{#each ...}` template syntax

ng-cloak, ng-init, ng-controller -> smazat, uz by to nemelo byt potreba

ng-bind-html -> svelte `{@html ...}` template syntax - 
NUTNE RUCNE PROHNAT HTML SANITIZE FILTREM (`SanitizeHtmlFunction`) pokud neni oznaceno jako safe ($sce.trustAsHtml)!
TODO udelat samostatnou komponentu pro tento ucel

ng-attr -> nativni atributy + interpolace nebo custom direktiva

ng-form -> pokud to bude potreba tak nativni form atribut nebo dle zvolene knihovny na formulare

ng-include -> pokud jde o angular template tak se to nahradi normalne komponentami,
pokud jde o dynamicky vyber templaty/komponenty pouzit svelte postup napr.: `<svelte:component>`

ng-keypres -> pokud je to mozne tak on:keypress, pouzit custom svelte directivu (s nativni implementaci)

ng-model -> pro two-way (obousmerny) data binding pouzit direktivu bind: nebo bind:group (pro select/radio buttons)

ng-value, ng-checked -> potreba resit individualne, ridit se svelte dokumentaci pro data binding

ng-model-options, ng-max-length, ng-min-length, ng-required, ng-pattern, ng-options, ng-trim -> pouzit value editor nebo jen form-control

ng-mousemove, ng-mouseenter -> stejne jako u keypress

ng-non-bindable -> nema primou alternativu, ale asi nebude potreba. Pokud ano tak je nutne escapovat templatu rucne

ng-ref -> pozit svelte direktivu `bind:this`

ng-submit -> on:submit

ng-switch -> bud rozdelit do if-elseif bloku ({#if}) nebo pouzit `<svelte:component>` pro dynamicky vyber komponenty

ng-transclude -> pouzit `<slot>`


### Migrace Angular Pipes/Filters

Angular pipy/filtry (z modulu `portaro.shared.filters`) je mozno pouzit.
V `shared/utils/pipes.ts` se vytvori funkce ktera dany filter vytvori a zavola se se spravnymi paramtery.
Tuto funkci je mozne pouzit pomoci utility funkce `pipe` ze `core/utils.ts`,
ktera podobne jako rxjs pipe postupne aplikuje jednotlive filtrovaci funkce.
Ukazka pouziti je v nektere z ukazkovych komponent (viz. [nize](#ukazkove-komponenty)).


## Migrace Bootstrap Komponent (TODO)

Zamer je uzavrit bootstrap komponenty (tvorene css styly) napr.: panel, table atd. do samostatnych UI komponent.
Cilem je DRY a znovupouzitelnost aby je slo potom hromadne nahradit.

table -> KpGenericTable
button -> KpButton, KpButtonStyleAnchor
~~dropdown -> KpDropDownButton, KpDropDownButtonGroup~~
panel -> KpGenericPanel

## Migrace Portaro Direktiv (TODO)

~~kp-href -> nativni href~~

~~div + jp-loading -> pouzit novou komponentu KpLoadingBlock~~

~~span + jp-loading -> pouzit novou komponentu KpLoadingInline~~

~~uib-dropdown (pokud nema append-to-body nebo on-hover) -> (KpDropdownButton / KpDropdownButtonGroup) + KpDropdownMenuItem~~

~~uib-tooltip -> tooltip action~~

~~uib-popover -> KpPopover~~

## Migrace Angular Funkci

angular.copy -> implementovat custom deep copy funkci nebo pouzit lodash

angular.element -> nemela by byt potreba ale nutno resit individualne

angular.extend -> Object.assign nebo JS spread (...) syntax

angular.forEach -> Array.forEach nebo neco podobneho nativniho

angular.identity -> vytvorit custom identity fci, pokud to vubec bude potreba

angular.injector -> nemel by byt potreba

angular.isObject -> resit individualne (mozna custom fce)

angular.isNumber -> resit individualne (mozna custom fce)

angular.isArray -> resit nativni Array.isArray() nebo individualne

angular.isDefined -> funkce `isDefined` z `custom-utils.ts`

angular.isUndefined -> funkce `isUndefined` z `custom-utils.ts`

angular.noop -> nemela by byt potreba ale nutno resit individualne

angular.toJson -> pouzit nativni JSON.stringify

angular.merge -> merge z knihovny lodash

## Svelte Speciality

### rekurzivni komponenty

Svelte komponenta nemuze importovat sama sebe. Nutne pouzit `<svelte:self>`. 
Viz. [docs](https://svelte.dev/docs#svelte_self)

### Ostatni Poznatky

Funkce pouzita v sablone musi mit dynamickou promenou jako argument aby se provedla.
Funguje je to i pro metody objektu, ktere se meni.

```html
<!-- toto se provede jen jednou na zacatku -->
<span>{fn()}</span>

<!-- toto se aktualizuje vzdy kdyz se zmeni hodnota arg -->
<span>{fn(arg)}</span>

<!-- toto se aktualizuje vzdy kdyz se zmeni hodnota arg -->
<span>{arg.fn()}</span>
```


## Ukazkove Komponenty

- KpCover.svelte

- KpDocumentMini.svelte

- KpVisitedPanel.svelte


## Testovani Svelte Komponent

Pro testovani jsem vytvoril par utility funkci v `test-utils/utils`.

`createTestContext` vytvori testovaci svelte kontext

`render` vyrenderuje komponentu, parametry: svelte komponenta,
options svelte komponenty (props a kontext) a render options.

Funkce render defaultne vyrenderuje komponentu v novem prazdnem div kontejneru.
Vlastni kontejner pro renderovani lze funkci predat pomoci render options.
Pokud je potreba kontejner vlozit do dokumentu obsahuje render options flag `appendToBody`,
ktery appendne kontejner k body (default je false).

Funkce vraci referenci na kontejner element (`container`), instanci svelte komponenty (`componentInstance`)
a funkci `unmount` pro zniceni komponenty a cleanup.

funkce `click` a `input` simuluji user input nad specifikovanym elementem.

Trida `SvelteComponentEventsListener` je utilitka k testovani eventu vysilanych z komponenty.

Pro provedeni renderovacich zmen v komponente je nutne zavolat asynchronni funkci `tick` ze svelte.

Pro slozitejsi interakci (typicky cekani na animaci) je moznou pouzit knihovnu `@testing-library/svelte`.

```ts
 const {componentInstance, container, unmount} 
    = render(KpSvelteTest, {props: {name: 'TEST-NAME1', input: 666}});

 const handler1 = jasmine.createSpy();
 const handler2 = jasmine.createSpy();

 const eventsListener = SvelteComponentEventsListener
    .getFor(componentInstance)
    .on('testUpperCase', handler1)
    .on('testUpperCaseX', handler2);

 const btn: HTMLButtonElement = container.querySelector('button#event-test');
 click(btn);

 expect(container.querySelector('#name-test').textContent).toBe('Hello TEST-NAME1!');
 expect(container.querySelector('#input-test').textContent).toBe('666');

 expect(handler1).toHaveBeenCalled();
 expect(handler2).not.toHaveBeenCalled();

 eventsListener.cleanup();
 unmount();
```

```ts
const {container, componentInstance, unmount} 
    = render(KpVisitedPanel, {context: testContext});

expect(componentInstance).toBeDefined();

await tick();

expect(container.children.length).toBe(0);

unmount();
```

Ukazka testu je testovani `KpVisitedPanel.svelte` komponenty (`kp-visited-panel.spec.ts`).


## IntelliJ Idea

### Localization - Live Template (sablona/makro s naseptavanim)

Pridani live template pro specifikaci lokalizacniho klice na frontendu.

File > Settings > Editor > Live Templates > user (pokud existuje) > Add (+) > Live Template

Abbreviation: `kploc` (muze byt libovolne) - to co IDE nabidne expandovat

Template text: `/* @kp-localization $MESSAGE_KEY$ */ '$MESSAGE_KEY$'`

Description: (libovolne)

### Svelte File Template - Sablona pro novy .svelte soubor

Pridani sablony pro novou svelte komponentu (prazdny .svelte file).

File > Settings > Editor > File and Code Templates > Create Template (+)

Name: `Svelte Component Template` (libovolne)

Extensions: `svelte`

```html
<script lang="ts">

</script>

<template>

</template>

<style lang="less">

</style>
```

### Vypnuti Zbytecnych Code Inspections (pro lidi s OCD)

Protoze svelte props promene jsou jen deklarovany, Idea ukazuje warning:
`Variable might not have been initialized`.
Pokud to chcete vypnout pak je nutne zmenit highliting level na no highlighting u prislusneho Code Inspection
(pripadne ho uplne vypnout).

File > Settings > Editor > Inspections > JavaScript and TypeScript > Unused Symbols > **Unused assignment**


## Build Process (NPM, Webpack, atd.)

### svelte-check

svelte-check je nastroj ktery provadi kontrolu svelte komponent 
(kontrola pouziti css, typova kontrola kodu i sablon, atd.). Normalni svelte compiler jen kompiluje.
Warningy a errory z TS compileru (dle tsconfigu) ignoruje. Proto je nutne kod kontrolovat pomoci svelte-checku.

svelte-check kontroluje i soubory importovane do svelte komponent!

svelte-check bez erroru a warningu je podminkou produkcniho buildu (stejne jako uspesne testy).

npm skripty

- `svelte-check` spust svelte-check a vypis errory + warningy
- `svelte-check:watch` spust svelte-check ve watch modu
- `svelte-check:hints` spust svelte-check a vypis i hinty

### ESLint

ESLint je nahrada TSLintu. ESLint lze pouzit v IntelliJ Idea.
Samotny build kontroluje ESLint webpack plugin (nahrada za tslint-loader).

npm skripty

- `lint` spust eslint
- `lint:fix` spust eslint aby opravil chyby a warningy ktere zvladne opravit

#### IDE nastaveni

1 Vypnout TS Lint

File > Settings > Languages & Frameworks > TypeScript > TSLint

2 Zapnout ES Lint (mela by stacit automaticka konfigurace)

File > Settings > Languages & Frameworks > JavaScript > Code Quality Tools > ESLint


## Nakonec Zbyva Vyresit (TODO)

- dokumentaci svelte komponent (mozna toto: [docs generator](https://www.npmjs.com/package/sveltedoc-parser))

- routing

- DI system

- HTML sanitization - DOMPurify

- HTTP klient - axios.js nebo custom fetch implementace


