import _import from "eslint-plugin-import";
import jsdoc from "eslint-plugin-jsdoc";
import unicorn from "eslint-plugin-unicorn";
import typescriptEslint from "@typescript-eslint/eslint-plugin";
import stylistic from '@stylistic/eslint-plugin'
import testingLibrary from "eslint-plugin-testing-library";
import {fixupPluginRules} from "@eslint/compat";
import globals from "globals";
import tsParser from "@typescript-eslint/parser";
import parser from "svelte-eslint-parser";
import path from "node:path";
import {fileURLToPath} from "node:url";
import js from "@eslint/js";
import {FlatCompat} from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended,
    allConfig: js.configs.all
});

export default [{
    ignores: [
        "**/node_modules", // ignore dependencies
        "**/dist",
        "**/*.js", // ignore javascript files
        "**/test", // ignore tests config directory
        "**/webpack", // ignore webpack config directory
        "**/webpack.config.ts", // ignore root webpack config
    ],
}, ...compat.extends(
    "plugin:@typescript-eslint/recommended",
    "plugin:@typescript-eslint/recommended-requiring-type-checking",
    "plugin:storybook/recommended",
    "plugin:testing-library/dom",
    "plugin:svelte/recommended",
), {
    plugins: {
        import: fixupPluginRules(_import),
        jsdoc,
        unicorn,
        "@typescript-eslint": typescriptEslint,
        '@stylistic': stylistic,
        "testing-library": testingLibrary,
    },

    languageOptions: {
        globals: {
            ...globals.browser,
            ...globals.es2022 // keep this in sync with target version in tsconfig.frontend.json and svelte-preprocess.config.js
        },

        parser: tsParser,
        ecmaVersion: 2022,
        sourceType: "module",

        parserOptions: {
            project: ["tsconfig.frontend.json", "tsconfig.svelte.json"],
            extraFileExtensions: [".svelte"],
        },
    },

    settings: {
        "testing-library/utils-module": "off", //https://github.com/testing-library/eslint-plugin-testing-library#switching-all-aggressive-reporting-mechanisms-off
        "testing-library/custom-renders": "off", //https://github.com/testing-library/eslint-plugin-testing-library#switching-all-aggressive-reporting-mechanisms-off
        "testing-library/custom-queries": "off", //https://github.com/testing-library/eslint-plugin-testing-library#switching-all-aggressive-reporting-mechanisms-off
    },

    rules: {
        "@typescript-eslint/adjacent-overload-signatures": "error",
        "@typescript-eslint/array-type": ["off", {
            default: "array-simple",
        }],
        "@typescript-eslint/no-restricted-types": ["error", {
            types: {
                object: {
                    message: "Use `Record<string, any>`",
                    fixWith: "Record<string, any>",
                },
            },
        }],
        "@typescript-eslint/consistent-type-assertions": "error",
        "@typescript-eslint/dot-notation": "error",
        "@typescript-eslint/member-ordering": "off",
        "@typescript-eslint/naming-convention": "off",
        "@typescript-eslint/no-empty-function": "error",
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/no-misused-new": "error",
        "@typescript-eslint/no-namespace": "error",
        "@typescript-eslint/no-parameter-properties": "off",
        "@typescript-eslint/no-shadow": ["error", {
            hoist: "all",
        }],
        "@typescript-eslint/no-unused-expressions": "error",
        "@typescript-eslint/no-use-before-define": "off",
        "@typescript-eslint/no-var-requires": "error",
        "@typescript-eslint/prefer-for-of": "error",
        "@typescript-eslint/prefer-function-type": "error",
        "@typescript-eslint/prefer-namespace-keyword": "error",
        "@stylistic/quotes": ["error", "single"],
        "@typescript-eslint/triple-slash-reference": ["error", {
            path: "always",
            types: "prefer-import",
            lib: "always",
        }],
        "@typescript-eslint/unified-signatures": "error",
        "@typescript-eslint/ban-ts-comment": ["error", {
            "ts-ignore": "allow-with-description",
        }],
        "@typescript-eslint/no-unsafe-member-access": "off", // todo enable after adding all missing typings
        "@typescript-eslint/no-unsafe-call": "off", // todo enable after adding all missing typings and removing angularjs
        "@typescript-eslint/no-unsafe-assignment": "off", // todo enable after adding all missing typings
        "@typescript-eslint/no-unsafe-return": "off", // because data services do not explicitly cast provided values
        "@typescript-eslint/unbound-method": ["error", { ignoreStatic: true }],
        "@typescript-eslint/no-floating-promises": "off", // because we are ignoring async functions return values
        "@typescript-eslint/require-await": "error",
        "@typescript-eslint/restrict-template-expressions": "off",
        "@typescript-eslint/no-misused-promises": "error",
        "@typescript-eslint/explicit-module-boundary-types": "off", // todo enable after adding all missing typings
        "@typescript-eslint/no-inferrable-types": ["warn", {
            ignoreParameters: true,
        }],
        "@typescript-eslint/prefer-regexp-exec": "off",
        "@typescript-eslint/await-thenable": "error",
        "@typescript-eslint/consistent-type-imports": ["error", {
            prefer: "type-imports",
            disallowTypeAnnotations: true,
        }],
        "@typescript-eslint/no-unsafe-argument": "off", // hundreds of errors otherwise
        "@typescript-eslint/no-require-imports": "off", // FIXME
        "@typescript-eslint/prefer-promise-reject-errors": "off",
        "@typescript-eslint/only-throw-error": "off",
        "@typescript-eslint/no-invalid-this": "error",
        "@stylistic/arrow-parens": ["error", "always"],
        "@stylistic/comma-dangle": "off",
        complexity: "off",
        "constructor-super": "error",
        "dot-notation": "error",
        eqeqeq: ["error", "always"],
        "guard-for-in": "error",

        "id-blacklist": [
            "error",
            "any",
            "Number",
            "number",
            "String",
            "string",
            "Boolean",
            "boolean",
            "Undefined",
            "undefined",
        ],

        "id-match": "error",
        "import/order": "off",
        "jsdoc/check-alignment": "error",
        "jsdoc/check-indentation": "error",
        "max-classes-per-file": ["error", 2],
        "@stylistic/max-len": "off",
        "@stylistic/new-parens": "error",
        "no-bitwise": "error",
        "no-caller": "error",
        "no-cond-assign": "error",
        "no-console": "error",
        "no-debugger": "error",
        "no-empty": "error",
        "no-empty-function": "off", // used ts rule
        "no-eval": "error",
        "no-fallthrough": "off",
        "no-invalid-this": "off",
        "no-new-wrappers": "error",
        "no-return-await": "error",
        "no-shadow": "off", // used ts rule
        "no-throw-literal": "error",
        "@stylistic/no-trailing-spaces": "off",
        "no-undef-init": "error",
        "no-underscore-dangle": "off",
        "no-unsafe-finally": "error",
        "no-unused-expressions": "error",
        "no-unused-labels": "error",
        "no-use-before-define": "off",
        "no-var": "error",
        "object-shorthand": "error",
        "one-var": ["error", "never"],
        "prefer-const": "error",
        "prefer-template": "error",
        radix: "error",
        "@stylistic/spaced-comment": ["error", "always", {
            line: {
                markers: ["/"],
                exceptions: ["@ngInject"],
            },
            block: {
                markers: ["/"],
                exceptions: ["@ngInject"],
                balanced: true,
            },
        }],
        "unicorn/filename-case": ["error", {
            cases: {
                kebabCase: true, // for .ts files
                pascalCase: true, // for .svelte files
            },

            ignore: ["<EMAIL>"],
        }],
        "use-isnan": "error",
        "valid-typeof": "off",
        "svelte/valid-compile": "off", // todo: eslint-plugin-svelte doesnt support LESS styles yet
        "svelte/no-unused-svelte-ignore": "off",  // for svelte-ignore comments
        "svelte/no-at-html-tags": "off", // for injecting server-rendered content
        "testing-library/no-node-access": "off", // we use native query selectors in most cases
    },
}, {
    files: ["**/*.svelte"],

    languageOptions: {
        parser: parser,
        ecmaVersion: 2022,
        sourceType: "script",

        parserOptions: {
            parser: "@typescript-eslint/parser",
        },
    },
}];