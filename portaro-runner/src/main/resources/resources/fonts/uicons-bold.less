/*!
*   _____       _             __                 _____                               ______ _       _   _
*  |_   _|     | |           / _|               |_   _|                             |  ____| |     | | (_)
*    | |  _ __ | |_ ___ _ __| |_ __ _  ___ ___    | |  ___ ___  _ __  ___   ______  | |__  | | __ _| |_ _  ___ ___  _ __
*    | | | '_ \| __/ _ \ '__|  _/ _` |/ __/ _ \   | | / __/ _ \| '_ \/ __| |______| |  __| | |/ _` | __| |/ __/ _ \| '_ \
*   _| |_| | | | ||  __/ |  | || (_| | (_|  __/  _| || (_| (_) | | | \__ \          | |    | | (_| | |_| | (_| (_) | | | |
*  |_____|_| |_|\__\___|_|  |_| \__,_|\___\___| |_____\___\___/|_| |_|___/          |_|    |_|\__,_|\__|_|\___\___/|_| |_|
*
*                         UIcons 2.3.0 - https://www.flaticon.com/uicons/interface-icons
*/
@font-face {
    font-family: "uicons-bold-rounded";
    src: url("./uicons-bold-rounded.woff2") format("woff2");
    font-display: swap;
}

span[class^="fi-br-"]:before, span[class*="fi-br-"]:before {
    font-family: uicons-bold-rounded !important;
    font-style: normal;
    font-weight: normal !important;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.fi-br-zoom-out:before {
    content: "\f101";
}

.fi-br-zoom-in:before {
    content: "\f102";
}

.fi-br-z:before {
    content: "\f103";
}

.fi-br-yin-yang:before {
    content: "\f104";
}

.fi-br-yen:before {
    content: "\f105";
}

.fi-br-y:before {
    content: "\f106";
}

.fi-br-x:before {
    content: "\f107";
}

.fi-br-x-ray:before {
    content: "\f108";
}

.fi-br-wrench-simple:before {
    content: "\f109";
}

.fi-br-wrench-alt:before {
    content: "\f10a";
}

.fi-br-wreath:before {
    content: "\f10b";
}

.fi-br-worm:before {
    content: "\f10c";
}

.fi-br-world:before {
    content: "\f10d";
}

.fi-br-workshop:before {
    content: "\f10e";
}

.fi-br-workflow:before {
    content: "\f10f";
}

.fi-br-workflow-setting:before {
    content: "\f110";
}

.fi-br-workflow-setting-alt:before {
    content: "\f111";
}

.fi-br-workflow-alt:before {
    content: "\f112";
}

.fi-br-work-in-progress:before {
    content: "\f113";
}

.fi-br-won-sign:before {
    content: "\f114";
}

.fi-br-woman-scientist:before {
    content: "\f115";
}

.fi-br-woman-head:before {
    content: "\f116";
}

.fi-br-wishlist-star:before {
    content: "\f117";
}

.fi-br-wishlist-heart:before {
    content: "\f118";
}

.fi-br-wink:before {
    content: "\f119";
}

.fi-br-wine-glass-empty:before {
    content: "\f11a";
}

.fi-br-wine-glass-crack:before {
    content: "\f11b";
}

.fi-br-wine-bottle:before {
    content: "\f11c";
}

.fi-br-windsock:before {
    content: "\f11d";
}

.fi-br-window-restore:before {
    content: "\f11e";
}

.fi-br-window-minimize:before {
    content: "\f11f";
}

.fi-br-window-maximize:before {
    content: "\f120";
}

.fi-br-window-frame:before {
    content: "\f121";
}

.fi-br-window-frame-open:before {
    content: "\f122";
}

.fi-br-window-alt:before {
    content: "\f123";
}

.fi-br-wind:before {
    content: "\f124";
}

.fi-br-wind-warning:before {
    content: "\f125";
}

.fi-br-wind-turbine:before {
    content: "\f126";
}

.fi-br-wifi:before {
    content: "\f127";
}

.fi-br-wifi-slash:before {
    content: "\f128";
}

.fi-br-wifi-exclamation:before {
    content: "\f129";
}

.fi-br-wifi-alt:before {
    content: "\f12a";
}

.fi-br-wifi-2:before {
    content: "\f12b";
}

.fi-br-wifi-1:before {
    content: "\f12c";
}

.fi-br-whistle:before {
    content: "\f12d";
}

.fi-br-wheelchair:before {
    content: "\f12e";
}

.fi-br-wheelchair-move:before {
    content: "\f12f";
}

.fi-br-wheat:before {
    content: "\f130";
}

.fi-br-wheat-slash:before {
    content: "\f131";
}

.fi-br-wheat-awn:before {
    content: "\f132";
}

.fi-br-wheat-awn-slash:before {
    content: "\f133";
}

.fi-br-wheat-awn-circle-exclamation:before {
    content: "\f134";
}

.fi-br-whale:before {
    content: "\f135";
}

.fi-br-wednesday:before {
    content: "\f136";
}

.fi-br-webcam:before {
    content: "\f137";
}

.fi-br-webcam-slash:before {
    content: "\f138";
}

.fi-br-waveform:before {
    content: "\f139";
}

.fi-br-waveform-path:before {
    content: "\f13a";
}

.fi-br-wave-triangle:before {
    content: "\f13b";
}

.fi-br-wave-square:before {
    content: "\f13c";
}

.fi-br-wave-sine:before {
    content: "\f13d";
}

.fi-br-watermelon:before {
    content: "\f13e";
}

.fi-br-water:before {
    content: "\f13f";
}

.fi-br-water-rise:before {
    content: "\f140";
}

.fi-br-water-lower:before {
    content: "\f141";
}

.fi-br-water-ladder:before {
    content: "\f142";
}

.fi-br-water-bottle:before {
    content: "\f143";
}

.fi-br-watch:before {
    content: "\f144";
}

.fi-br-watch-smart:before {
    content: "\f145";
}

.fi-br-watch-fitness:before {
    content: "\f146";
}

.fi-br-watch-calculator:before {
    content: "\f147";
}

.fi-br-waste:before {
    content: "\f148";
}

.fi-br-waste-pollution:before {
    content: "\f149";
}

.fi-br-washer:before {
    content: "\f14a";
}

.fi-br-warranty:before {
    content: "\f14b";
}

.fi-br-warehouse-alt:before {
    content: "\f14c";
}

.fi-br-wallet:before {
    content: "\f14d";
}

.fi-br-wallet-arrow:before {
    content: "\f14e";
}

.fi-br-walking:before {
    content: "\f14f";
}

.fi-br-walkie-talkie:before {
    content: "\f150";
}

.fi-br-walker:before {
    content: "\f151";
}

.fi-br-wagon-covered:before {
    content: "\f152";
}

.fi-br-waffle:before {
    content: "\f153";
}

.fi-br-w:before {
    content: "\f154";
}

.fi-br-vr-cardboard:before {
    content: "\f155";
}

.fi-br-vote-yea:before {
    content: "\f156";
}

.fi-br-vote-nay:before {
    content: "\f157";
}

.fi-br-volume:before {
    content: "\f158";
}

.fi-br-volume-slash:before {
    content: "\f159";
}

.fi-br-volume-off:before {
    content: "\f15a";
}

.fi-br-volume-mute:before {
    content: "\f15b";
}

.fi-br-volume-down:before {
    content: "\f15c";
}

.fi-br-volleyball:before {
    content: "\f15d";
}

.fi-br-volcano:before {
    content: "\f15e";
}

.fi-br-voicemail:before {
    content: "\f15f";
}

.fi-br-visit:before {
    content: "\f160";
}

.fi-br-viruses:before {
    content: "\f161";
}

.fi-br-virus:before {
    content: "\f162";
}

.fi-br-virus-slash:before {
    content: "\f163";
}

.fi-br-violin:before {
    content: "\f164";
}

.fi-br-vihara:before {
    content: "\f165";
}

.fi-br-videoconference:before {
    content: "\f166";
}

.fi-br-video-slash:before {
    content: "\f167";
}

.fi-br-video-plus:before {
    content: "\f168";
}

.fi-br-video-duration:before {
    content: "\f169";
}

.fi-br-video-camera:before {
    content: "\f16a";
}

.fi-br-video-camera-alt:before {
    content: "\f16b";
}

.fi-br-video-arrow-up-right:before {
    content: "\f16c";
}

.fi-br-video-arrow-down-left:before {
    content: "\f16d";
}

.fi-br-vest:before {
    content: "\f16e";
}

.fi-br-vest-patches:before {
    content: "\f16f";
}

.fi-br-vector:before {
    content: "\f170";
}

.fi-br-vector-polygon:before {
    content: "\f171";
}

.fi-br-vector-circle:before {
    content: "\f172";
}

.fi-br-vector-alt:before {
    content: "\f173";
}

.fi-br-vault:before {
    content: "\f174";
}

.fi-br-value-absolute:before {
    content: "\f175";
}

.fi-br-vacuum:before {
    content: "\f176";
}

.fi-br-vacuum-robot:before {
    content: "\f177";
}

.fi-br-v:before {
    content: "\f178";
}

.fi-br-ux:before {
    content: "\f179";
}

.fi-br-ux-browser:before {
    content: "\f17a";
}

.fi-br-utility-pole:before {
    content: "\f17b";
}

.fi-br-utility-pole-double:before {
    content: "\f17c";
}

.fi-br-utensils:before {
    content: "\f17d";
}

.fi-br-utensils-slash:before {
    content: "\f17e";
}

.fi-br-users:before {
    content: "\f17f";
}

.fi-br-users-slash:before {
    content: "\f180";
}

.fi-br-users-medical:before {
    content: "\f181";
}

.fi-br-users-gear:before {
    content: "\f182";
}

.fi-br-users-class:before {
    content: "\f183";
}

.fi-br-users-alt:before {
    content: "\f184";
}

.fi-br-user:before {
    content: "\f185";
}

.fi-br-user-xmark:before {
    content: "\f186";
}

.fi-br-user-visor:before {
    content: "\f187";
}

.fi-br-user-unlock:before {
    content: "\f188";
}

.fi-br-user-time:before {
    content: "\f189";
}

.fi-br-user-tag:before {
    content: "\f18a";
}

.fi-br-user-slash:before {
    content: "\f18b";
}

.fi-br-user-shield:before {
    content: "\f18c";
}

.fi-br-user-salary:before {
    content: "\f18d";
}

.fi-br-user-robot:before {
    content: "\f18e";
}

.fi-br-user-robot-xmarks:before {
    content: "\f18f";
}

.fi-br-user-police:before {
    content: "\f190";
}

.fi-br-user-pilot:before {
    content: "\f191";
}

.fi-br-user-pilot-tie:before {
    content: "\f192";
}

.fi-br-user-pen:before {
    content: "\f193";
}

.fi-br-user-nurse:before {
    content: "\f194";
}

.fi-br-user-ninja:before {
    content: "\f195";
}

.fi-br-user-music:before {
    content: "\f196";
}

.fi-br-user-minus:before {
    content: "\f197";
}

.fi-br-user-md:before {
    content: "\f198";
}

.fi-br-user-md-chat:before {
    content: "\f199";
}

.fi-br-user-lock:before {
    content: "\f19a";
}

.fi-br-user-key:before {
    content: "\f19b";
}

.fi-br-user-interface-ui:before {
    content: "\f19c";
}

.fi-br-user-injured:before {
    content: "\f19d";
}

.fi-br-user-helmet-safety:before {
    content: "\f19e";
}

.fi-br-user-headset:before {
    content: "\f19f";
}

.fi-br-user-graduate:before {
    content: "\f1a0";
}

.fi-br-user-gear:before {
    content: "\f1a1";
}

.fi-br-user-crown:before {
    content: "\f1a2";
}

.fi-br-user-cowboy:before {
    content: "\f1a3";
}

.fi-br-user-chef:before {
    content: "\f1a4";
}

.fi-br-user-check:before {
    content: "\f1a5";
}

.fi-br-user-astronaut:before {
    content: "\f1a6";
}

.fi-br-user-alien:before {
    content: "\f1a7";
}

.fi-br-user-add:before {
    content: "\f1a8";
}

.fi-br-usd-square:before {
    content: "\f1a9";
}

.fi-br-usd-circle:before {
    content: "\f1aa";
}

.fi-br-usb-pendrive:before {
    content: "\f1ab";
}

.fi-br-url:before {
    content: "\f1ac";
}

.fi-br-upload:before {
    content: "\f1ad";
}

.fi-br-up:before {
    content: "\f1ae";
}

.fi-br-up-right:before {
    content: "\f1af";
}

.fi-br-up-right-from-square:before {
    content: "\f1b0";
}

.fi-br-up-left:before {
    content: "\f1b1";
}

.fi-br-unlock:before {
    content: "\f1b2";
}

.fi-br-universal-access:before {
    content: "\f1b3";
}

.fi-br-uniform-martial-arts:before {
    content: "\f1b4";
}

.fi-br-unicorn:before {
    content: "\f1b5";
}

.fi-br-undo:before {
    content: "\f1b6";
}

.fi-br-undo-alt:before {
    content: "\f1b7";
}

.fi-br-underline:before {
    content: "\f1b8";
}

.fi-br-umbrella:before {
    content: "\f1b9";
}

.fi-br-umbrella-beach:before {
    content: "\f1ba";
}

.fi-br-ui-ux:before {
    content: "\f1bb";
}

.fi-br-ufo:before {
    content: "\f1bc";
}

.fi-br-ufo-beam:before {
    content: "\f1bd";
}

.fi-br-u:before {
    content: "\f1be";
}

.fi-br-typewriter:before {
    content: "\f1bf";
}

.fi-br-tv-retro:before {
    content: "\f1c0";
}

.fi-br-tv-music:before {
    content: "\f1c1";
}

.fi-br-turtle:before {
    content: "\f1c2";
}

.fi-br-turntable:before {
    content: "\f1c3";
}

.fi-br-turkey:before {
    content: "\f1c4";
}

.fi-br-tugrik-sign:before {
    content: "\f1c5";
}

.fi-br-tuesday:before {
    content: "\f1c6";
}

.fi-br-tubes:before {
    content: "\f1c7";
}

.fi-br-tty:before {
    content: "\f1c8";
}

.fi-br-tty-answer:before {
    content: "\f1c9";
}

.fi-br-tshirt:before {
    content: "\f1ca";
}

.fi-br-trust:before {
    content: "\f1cb";
}

.fi-br-trust-alt:before {
    content: "\f1cc";
}

.fi-br-trumpet:before {
    content: "\f1cd";
}

.fi-br-truck-tow:before {
    content: "\f1ce";
}

.fi-br-truck-side:before {
    content: "\f1cf";
}

.fi-br-truck-ramp:before {
    content: "\f1d0";
}

.fi-br-truck-plow:before {
    content: "\f1d1";
}

.fi-br-truck-pickup:before {
    content: "\f1d2";
}

.fi-br-truck-moving:before {
    content: "\f1d3";
}

.fi-br-truck-monster:before {
    content: "\f1d4";
}

.fi-br-truck-medical:before {
    content: "\f1d5";
}

.fi-br-truck-loading:before {
    content: "\f1d6";
}

.fi-br-truck-front:before {
    content: "\f1d7";
}

.fi-br-truck-flatbed:before {
    content: "\f1d8";
}

.fi-br-truck-couch:before {
    content: "\f1d9";
}

.fi-br-truck-container:before {
    content: "\f1da";
}

.fi-br-truck-container-empty:before {
    content: "\f1db";
}

.fi-br-truck-check:before {
    content: "\f1dc";
}

.fi-br-truck-box:before {
    content: "\f1dd";
}

.fi-br-truck-bolt:before {
    content: "\f1de";
}

.fi-br-truck-arrow-right:before {
    content: "\f1df";
}

.fi-br-truck-arrow-left:before {
    content: "\f1e0";
}

.fi-br-trophy:before {
    content: "\f1e1";
}

.fi-br-trophy-star:before {
    content: "\f1e2";
}

.fi-br-trillium:before {
    content: "\f1e3";
}

.fi-br-triangle:before {
    content: "\f1e4";
}

.fi-br-triangle-warning:before {
    content: "\f1e5";
}

.fi-br-triangle-person-digging:before {
    content: "\f1e6";
}

.fi-br-triangle-music:before {
    content: "\f1e7";
}

.fi-br-trees:before {
    content: "\f1e8";
}

.fi-br-trees-alt:before {
    content: "\f1e9";
}

.fi-br-tree:before {
    content: "\f1ea";
}

.fi-br-tree-deciduous:before {
    content: "\f1eb";
}

.fi-br-tree-christmas:before {
    content: "\f1ec";
}

.fi-br-tree-alt:before {
    content: "\f1ed";
}

.fi-br-treatment:before {
    content: "\f1ee";
}

.fi-br-treasure-chest:before {
    content: "\f1ef";
}

.fi-br-trash:before {
    content: "\f1f0";
}

.fi-br-trash-xmark:before {
    content: "\f1f1";
}

.fi-br-trash-undo:before {
    content: "\f1f2";
}

.fi-br-trash-undo-alt:before {
    content: "\f1f3";
}

.fi-br-trash-slash:before {
    content: "\f1f4";
}

.fi-br-trash-restore:before {
    content: "\f1f5";
}

.fi-br-trash-restore-alt:before {
    content: "\f1f6";
}

.fi-br-trash-plus:before {
    content: "\f1f7";
}

.fi-br-trash-list:before {
    content: "\f1f8";
}

.fi-br-trash-clock:before {
    content: "\f1f9";
}

.fi-br-trash-check:before {
    content: "\f1fa";
}

.fi-br-trash-can-slash:before {
    content: "\f1fb";
}

.fi-br-trash-can-plus:before {
    content: "\f1fc";
}

.fi-br-trash-can-list:before {
    content: "\f1fd";
}

.fi-br-trash-can-clock:before {
    content: "\f1fe";
}

.fi-br-trash-can-check:before {
    content: "\f1ff";
}

.fi-br-transporter:before {
    content: "\f200";
}

.fi-br-transporter-empty:before {
    content: "\f201";
}

.fi-br-transporter-7:before {
    content: "\f202";
}

.fi-br-transporter-6:before {
    content: "\f203";
}

.fi-br-transporter-5:before {
    content: "\f204";
}

.fi-br-transporter-4:before {
    content: "\f205";
}

.fi-br-transporter-3:before {
    content: "\f206";
}

.fi-br-transporter-2:before {
    content: "\f207";
}

.fi-br-transporter-1:before {
    content: "\f208";
}

.fi-br-transgender:before {
    content: "\f209";
}

.fi-br-transformer-bolt:before {
    content: "\f20a";
}

.fi-br-transformation-shapes:before {
    content: "\f20b";
}

.fi-br-transformation-design:before {
    content: "\f20c";
}

.fi-br-transformation-circle:before {
    content: "\f20d";
}

.fi-br-transformation-block:before {
    content: "\f20e";
}

.fi-br-transform:before {
    content: "\f20f";
}

.fi-br-tram:before {
    content: "\f210";
}

.fi-br-train:before {
    content: "\f211";
}

.fi-br-train-tram:before {
    content: "\f212";
}

.fi-br-train-subway-tunnel:before {
    content: "\f213";
}

.fi-br-train-station:before {
    content: "\f214";
}

.fi-br-train-station-building:before {
    content: "\f215";
}

.fi-br-train-side:before {
    content: "\f216";
}

.fi-br-train-journey:before {
    content: "\f217";
}

.fi-br-trailer:before {
    content: "\f218";
}

.fi-br-traffic-light:before {
    content: "\f219";
}

.fi-br-traffic-light-stop:before {
    content: "\f21a";
}

.fi-br-traffic-light-slow:before {
    content: "\f21b";
}

.fi-br-traffic-light-go:before {
    content: "\f21c";
}

.fi-br-traffic-cone:before {
    content: "\f21d";
}

.fi-br-trademark:before {
    content: "\f21e";
}

.fi-br-tractor:before {
    content: "\f21f";
}

.fi-br-track:before {
    content: "\f220";
}

.fi-br-tower-control:before {
    content: "\f221";
}

.fi-br-tour-virtual:before {
    content: "\f222";
}

.fi-br-tour-guide-people:before {
    content: "\f223";
}

.fi-br-total:before {
    content: "\f224";
}

.fi-br-tornado:before {
    content: "\f225";
}

.fi-br-torii-gate:before {
    content: "\f226";
}

.fi-br-toothbrush:before {
    content: "\f227";
}

.fi-br-tooth:before {
    content: "\f228";
}

.fi-br-tools:before {
    content: "\f229";
}

.fi-br-tool-marquee:before {
    content: "\f22a";
}

.fi-br-tool-crop:before {
    content: "\f22b";
}

.fi-br-tool-box:before {
    content: "\f22c";
}

.fi-br-tombstone:before {
    content: "\f22d";
}

.fi-br-tombstone-alt:before {
    content: "\f22e";
}

.fi-br-tomato:before {
    content: "\f22f";
}

.fi-br-token:before {
    content: "\f230";
}

.fi-br-toilet:before {
    content: "\f231";
}

.fi-br-toilet-paper-under:before {
    content: "\f232";
}

.fi-br-toilet-paper-under-slash:before {
    content: "\f233";
}

.fi-br-toilet-paper-slash:before {
    content: "\f234";
}

.fi-br-toilet-paper-blank:before {
    content: "\f235";
}

.fi-br-toilet-paper-blank-under:before {
    content: "\f236";
}

.fi-br-toggle-on:before {
    content: "\f237";
}

.fi-br-toggle-off:before {
    content: "\f238";
}

.fi-br-together-people:before {
    content: "\f239";
}

.fi-br-to-do:before {
    content: "\f23a";
}

.fi-br-to-do-alt:before {
    content: "\f23b";
}

.fi-br-tired:before {
    content: "\f23c";
}

.fi-br-tire:before {
    content: "\f23d";
}

.fi-br-tire-rugged:before {
    content: "\f23e";
}

.fi-br-tire-pressure-warning:before {
    content: "\f23f";
}

.fi-br-tire-flat:before {
    content: "\f240";
}

.fi-br-tip-coin:before {
    content: "\f241";
}

.fi-br-tint-slash:before {
    content: "\f242";
}

.fi-br-times-hexagon:before {
    content: "\f243";
}

.fi-br-time-twenty-four:before {
    content: "\f244";
}

.fi-br-time-quarter-to:before {
    content: "\f245";
}

.fi-br-time-quarter-past:before {
    content: "\f246";
}

.fi-br-time-past:before {
    content: "\f247";
}

.fi-br-time-oclock:before {
    content: "\f248";
}

.fi-br-time-half-past:before {
    content: "\f249";
}

.fi-br-time-forward:before {
    content: "\f24a";
}

.fi-br-time-forward-ten:before {
    content: "\f24b";
}

.fi-br-time-forward-sixty:before {
    content: "\f24c";
}

.fi-br-time-fast:before {
    content: "\f24d";
}

.fi-br-time-delete:before {
    content: "\f24e";
}

.fi-br-time-check:before {
    content: "\f24f";
}

.fi-br-time-add:before {
    content: "\f250";
}

.fi-br-tilde:before {
    content: "\f251";
}

.fi-br-tie:before {
    content: "\f252";
}

.fi-br-tickets-airline:before {
    content: "\f253";
}

.fi-br-ticket:before {
    content: "\f254";
}

.fi-br-ticket-alt:before {
    content: "\f255";
}

.fi-br-ticket-airline:before {
    content: "\f256";
}

.fi-br-thursday:before {
    content: "\f257";
}

.fi-br-thunderstorm:before {
    content: "\f258";
}

.fi-br-thunderstorm-sun:before {
    content: "\f259";
}

.fi-br-thunderstorm-moon:before {
    content: "\f25a";
}

.fi-br-thumbtack:before {
    content: "\f25b";
}

.fi-br-thought-bubble:before {
    content: "\f25c";
}

.fi-br-third:before {
    content: "\f25d";
}

.fi-br-third-medal:before {
    content: "\f25e";
}

.fi-br-third-laurel:before {
    content: "\f25f";
}

.fi-br-third-award:before {
    content: "\f260";
}

.fi-br-theta:before {
    content: "\f261";
}

.fi-br-thermometer-three-quarters:before {
    content: "\f262";
}

.fi-br-thermometer-quarter:before {
    content: "\f263";
}

.fi-br-thermometer-half:before {
    content: "\f264";
}

.fi-br-thermometer-full:before {
    content: "\f265";
}

.fi-br-thermometer-empty:before {
    content: "\f266";
}

.fi-br-thermometer-alt:before {
    content: "\f267";
}

.fi-br-theater-masks:before {
    content: "\f268";
}

.fi-br-text:before {
    content: "\f269";
}

.fi-br-text-width:before {
    content: "\f26a";
}

.fi-br-text-slash:before {
    content: "\f26b";
}

.fi-br-text-size:before {
    content: "\f26c";
}

.fi-br-text-shadow:before {
    content: "\f26d";
}

.fi-br-text-height:before {
    content: "\f26e";
}

.fi-br-text-check:before {
    content: "\f26f";
}

.fi-br-text-box:before {
    content: "\f270";
}

.fi-br-text-box-edit:before {
    content: "\f271";
}

.fi-br-text-box-dots:before {
    content: "\f272";
}

.fi-br-test:before {
    content: "\f273";
}

.fi-br-test-tube:before {
    content: "\f274";
}

.fi-br-terrace:before {
    content: "\f275";
}

.fi-br-terminal:before {
    content: "\f276";
}

.fi-br-tents:before {
    content: "\f277";
}

.fi-br-tent-arrows-down:before {
    content: "\f278";
}

.fi-br-tent-arrow-turn-left:before {
    content: "\f279";
}

.fi-br-tent-arrow-left-right:before {
    content: "\f27a";
}

.fi-br-tent-arrow-down-to-line:before {
    content: "\f27b";
}

.fi-br-tennis:before {
    content: "\f27c";
}

.fi-br-tenge:before {
    content: "\f27d";
}

.fi-br-template:before {
    content: "\f27e";
}

.fi-br-template-alt:before {
    content: "\f27f";
}

.fi-br-temperature-up:before {
    content: "\f280";
}

.fi-br-temperature-low:before {
    content: "\f281";
}

.fi-br-temperature-list:before {
    content: "\f282";
}

.fi-br-temperature-high:before {
    content: "\f283";
}

.fi-br-temperature-frigid:before {
    content: "\f284";
}

.fi-br-temperature-down:before {
    content: "\f285";
}

.fi-br-telescope:before {
    content: "\f286";
}

.fi-br-teeth-open:before {
    content: "\f287";
}

.fi-br-teddy-bear:before {
    content: "\f288";
}

.fi-br-team-check:before {
    content: "\f289";
}

.fi-br-team-check-alt:before {
    content: "\f28a";
}

.fi-br-taxi:before {
    content: "\f28b";
}

.fi-br-taxi-bus:before {
    content: "\f28c";
}

.fi-br-tax:before {
    content: "\f28d";
}

.fi-br-tax-alt:before {
    content: "\f28e";
}

.fi-br-target:before {
    content: "\f28f";
}

.fi-br-target-audience:before {
    content: "\f290";
}

.fi-br-tape:before {
    content: "\f291";
}

.fi-br-tap:before {
    content: "\f292";
}

.fi-br-tank-water:before {
    content: "\f293";
}

.fi-br-tamale:before {
    content: "\f294";
}

.fi-br-tally:before {
    content: "\f295";
}

.fi-br-tally-4:before {
    content: "\f296";
}

.fi-br-tally-3:before {
    content: "\f297";
}

.fi-br-tally-2:before {
    content: "\f298";
}

.fi-br-tally-1:before {
    content: "\f299";
}

.fi-br-tags:before {
    content: "\f29a";
}

.fi-br-taco:before {
    content: "\f29b";
}

.fi-br-tachometer:before {
    content: "\f29c";
}

.fi-br-tachometer-slowest:before {
    content: "\f29d";
}

.fi-br-tachometer-slow:before {
    content: "\f29e";
}

.fi-br-tachometer-fastest:before {
    content: "\f29f";
}

.fi-br-tachometer-fast:before {
    content: "\f2a0";
}

.fi-br-tachometer-average:before {
    content: "\f2a1";
}

.fi-br-tachometer-alt-slowest:before {
    content: "\f2a2";
}

.fi-br-tachometer-alt-slow:before {
    content: "\f2a3";
}

.fi-br-tachometer-alt-fastest:before {
    content: "\f2a4";
}

.fi-br-tachometer-alt-average:before {
    content: "\f2a5";
}

.fi-br-tablet:before {
    content: "\f2a6";
}

.fi-br-tablet-rugged:before {
    content: "\f2a7";
}

.fi-br-tablet-android:before {
    content: "\f2a8";
}

.fi-br-tablet-android-alt:before {
    content: "\f2a9";
}

.fi-br-table:before {
    content: "\f2aa";
}

.fi-br-table-tree:before {
    content: "\f2ab";
}

.fi-br-table-rows:before {
    content: "\f2ac";
}

.fi-br-table-pivot:before {
    content: "\f2ad";
}

.fi-br-table-picnic:before {
    content: "\f2ae";
}

.fi-br-table-list:before {
    content: "\f2af";
}

.fi-br-table-layout:before {
    content: "\f2b0";
}

.fi-br-table-columns:before {
    content: "\f2b1";
}

.fi-br-tab-folder:before {
    content: "\f2b2";
}

.fi-br-t:before {
    content: "\f2b3";
}

.fi-br-syringe:before {
    content: "\f2b4";
}

.fi-br-synagogue:before {
    content: "\f2b5";
}

.fi-br-symbols:before {
    content: "\f2b6";
}

.fi-br-symbol:before {
    content: "\f2b7";
}

.fi-br-sword:before {
    content: "\f2b8";
}

.fi-br-swipe-up:before {
    content: "\f2b9";
}

.fi-br-swipe-right:before {
    content: "\f2ba";
}

.fi-br-swipe-left:before {
    content: "\f2bb";
}

.fi-br-swipe-down:before {
    content: "\f2bc";
}

.fi-br-swimming-pool:before {
    content: "\f2bd";
}

.fi-br-swimmer:before {
    content: "\f2be";
}

.fi-br-swatchbook:before {
    content: "\f2bf";
}

.fi-br-svg:before {
    content: "\f2c0";
}

.fi-br-sushi:before {
    content: "\f2c1";
}

.fi-br-sushi-roll:before {
    content: "\f2c2";
}

.fi-br-sushi-alt:before {
    content: "\f2c3";
}

.fi-br-surprise:before {
    content: "\f2c4";
}

.fi-br-surfing:before {
    content: "\f2c5";
}

.fi-br-supplier:before {
    content: "\f2c6";
}

.fi-br-supplier-alt:before {
    content: "\f2c7";
}

.fi-br-superscript:before {
    content: "\f2c8";
}

.fi-br-sunset:before {
    content: "\f2c9";
}

.fi-br-sunrise:before {
    content: "\f2ca";
}

.fi-br-sunrise-alt:before {
    content: "\f2cb";
}

.fi-br-sunglasses:before {
    content: "\f2cc";
}

.fi-br-sunglasses-alt:before {
    content: "\f2cd";
}

.fi-br-sunday:before {
    content: "\f2ce";
}

.fi-br-sun:before {
    content: "\f2cf";
}

.fi-br-sun-plant-wilt:before {
    content: "\f2d0";
}

.fi-br-sun-dust:before {
    content: "\f2d1";
}

.fi-br-summer:before {
    content: "\f2d2";
}

.fi-br-suitcase-alt:before {
    content: "\f2d3";
}

.fi-br-subway:before {
    content: "\f2d4";
}

.fi-br-subtitles:before {
    content: "\f2d5";
}

.fi-br-subscription:before {
    content: "\f2d6";
}

.fi-br-subscription-alt:before {
    content: "\f2d7";
}

.fi-br-subscript:before {
    content: "\f2d8";
}

.fi-br-student:before {
    content: "\f2d9";
}

.fi-br-student-alt:before {
    content: "\f2da";
}

.fi-br-stroopwafel:before {
    content: "\f2db";
}

.fi-br-strikethrough:before {
    content: "\f2dc";
}

.fi-br-stretcher:before {
    content: "\f2dd";
}

.fi-br-street-view:before {
    content: "\f2de";
}

.fi-br-strawberry:before {
    content: "\f2df";
}

.fi-br-story-fantasy:before {
    content: "\f2e0";
}

.fi-br-story-fairy-tale:before {
    content: "\f2e1";
}

.fi-br-story-book:before {
    content: "\f2e2";
}

.fi-br-store-slash:before {
    content: "\f2e3";
}

.fi-br-store-lock:before {
    content: "\f2e4";
}

.fi-br-store-alt:before {
    content: "\f2e5";
}

.fi-br-stopwatch:before {
    content: "\f2e6";
}

.fi-br-stop:before {
    content: "\f2e7";
}

.fi-br-stop-circle:before {
    content: "\f2e8";
}

.fi-br-stomach:before {
    content: "\f2e9";
}

.fi-br-stocking:before {
    content: "\f2ea";
}

.fi-br-sticker:before {
    content: "\f2eb";
}

.fi-br-stethoscope:before {
    content: "\f2ec";
}

.fi-br-sterling-sign:before {
    content: "\f2ed";
}

.fi-br-step-forward:before {
    content: "\f2ee";
}

.fi-br-step-backward:before {
    content: "\f2ef";
}

.fi-br-steering-wheel:before {
    content: "\f2f0";
}

.fi-br-steak:before {
    content: "\f2f1";
}

.fi-br-stats:before {
    content: "\f2f2";
}

.fi-br-state-country:before {
    content: "\f2f3";
}

.fi-br-stars:before {
    content: "\f2f4";
}

.fi-br-starfighter:before {
    content: "\f2f5";
}

.fi-br-star:before {
    content: "\f2f6";
}

.fi-br-star-shooting:before {
    content: "\f2f7";
}

.fi-br-star-sharp-half:before {
    content: "\f2f8";
}

.fi-br-star-sharp-half-stroke:before {
    content: "\f2f9";
}

.fi-br-star-of-david:before {
    content: "\f2fa";
}

.fi-br-star-octogram:before {
    content: "\f2fb";
}

.fi-br-star-exclamation:before {
    content: "\f2fc";
}

.fi-br-star-comment-alt:before {
    content: "\f2fd";
}

.fi-br-star-christmas:before {
    content: "\f2fe";
}

.fi-br-star-and-crescent:before {
    content: "\f2ff";
}

.fi-br-standard-definition:before {
    content: "\f300";
}

.fi-br-stamp:before {
    content: "\f301";
}

.fi-br-stairs:before {
    content: "\f302";
}

.fi-br-stage:before {
    content: "\f303";
}

.fi-br-stage-theatre:before {
    content: "\f304";
}

.fi-br-stage-concert:before {
    content: "\f305";
}

.fi-br-staff:before {
    content: "\f306";
}

.fi-br-squirrel:before {
    content: "\f307";
}

.fi-br-squircle:before {
    content: "\f308";
}

.fi-br-squid:before {
    content: "\f309";
}

.fi-br-square:before {
    content: "\f30a";
}

.fi-br-square-z:before {
    content: "\f30b";
}

.fi-br-square-y:before {
    content: "\f30c";
}

.fi-br-square-x:before {
    content: "\f30d";
}

.fi-br-square-w:before {
    content: "\f30e";
}

.fi-br-square-v:before {
    content: "\f30f";
}

.fi-br-square-up-right:before {
    content: "\f310";
}

.fi-br-square-u:before {
    content: "\f311";
}

.fi-br-square-terminal:before {
    content: "\f312";
}

.fi-br-square-t:before {
    content: "\f313";
}

.fi-br-square-star:before {
    content: "\f314";
}

.fi-br-square-small:before {
    content: "\f315";
}

.fi-br-square-s:before {
    content: "\f316";
}

.fi-br-square-root:before {
    content: "\f317";
}

.fi-br-square-r:before {
    content: "\f318";
}

.fi-br-square-quote:before {
    content: "\f319";
}

.fi-br-square-q:before {
    content: "\f31a";
}

.fi-br-square-poll-vertical:before {
    content: "\f31b";
}

.fi-br-square-poll-horizontal:before {
    content: "\f31c";
}

.fi-br-square-plus:before {
    content: "\f31d";
}

.fi-br-square-phone-hangup:before {
    content: "\f31e";
}

.fi-br-square-p:before {
    content: "\f31f";
}

.fi-br-square-o:before {
    content: "\f320";
}

.fi-br-square-n:before {
    content: "\f321";
}

.fi-br-square-minus:before {
    content: "\f322";
}

.fi-br-square-m:before {
    content: "\f323";
}

.fi-br-square-l:before {
    content: "\f324";
}

.fi-br-square-kanban:before {
    content: "\f325";
}

.fi-br-square-k:before {
    content: "\f326";
}

.fi-br-square-j:before {
    content: "\f327";
}

.fi-br-square-info:before {
    content: "\f328";
}

.fi-br-square-i:before {
    content: "\f329";
}

.fi-br-square-heart:before {
    content: "\f32a";
}

.fi-br-square-h:before {
    content: "\f32b";
}

.fi-br-square-g:before {
    content: "\f32c";
}

.fi-br-square-f:before {
    content: "\f32d";
}

.fi-br-square-exclamation:before {
    content: "\f32e";
}

.fi-br-square-ellipsis:before {
    content: "\f32f";
}

.fi-br-square-ellipsis-vertical:before {
    content: "\f330";
}

.fi-br-square-e:before {
    content: "\f331";
}

.fi-br-square-divide:before {
    content: "\f332";
}

.fi-br-square-dashed:before {
    content: "\f333";
}

.fi-br-square-d:before {
    content: "\f334";
}

.fi-br-square-code:before {
    content: "\f335";
}

.fi-br-square-c:before {
    content: "\f336";
}

.fi-br-square-bolt:before {
    content: "\f337";
}

.fi-br-square-b:before {
    content: "\f338";
}

.fi-br-square-a:before {
    content: "\f339";
}

.fi-br-square-9:before {
    content: "\f33a";
}

.fi-br-square-8:before {
    content: "\f33b";
}

.fi-br-square-7:before {
    content: "\f33c";
}

.fi-br-square-6:before {
    content: "\f33d";
}

.fi-br-square-5:before {
    content: "\f33e";
}

.fi-br-square-4:before {
    content: "\f33f";
}

.fi-br-square-3:before {
    content: "\f340";
}

.fi-br-square-2:before {
    content: "\f341";
}

.fi-br-square-1:before {
    content: "\f342";
}

.fi-br-square-0:before {
    content: "\f343";
}

.fi-br-spy:before {
    content: "\f344";
}

.fi-br-sprinkler:before {
    content: "\f345";
}

.fi-br-spring-calendar:before {
    content: "\f346";
}

.fi-br-spray-can:before {
    content: "\f347";
}

.fi-br-spray-can-sparkles:before {
    content: "\f348";
}

.fi-br-spoon:before {
    content: "\f349";
}

.fi-br-splotch:before {
    content: "\f34a";
}

.fi-br-split:before {
    content: "\f34b";
}

.fi-br-spinner:before {
    content: "\f34c";
}

.fi-br-spider:before {
    content: "\f34d";
}

.fi-br-spider-web:before {
    content: "\f34e";
}

.fi-br-spider-black-widow:before {
    content: "\f34f";
}

.fi-br-sphere:before {
    content: "\f350";
}

.fi-br-speedometer-kpi:before {
    content: "\f351";
}

.fi-br-speedometer-arrow:before {
    content: "\f352";
}

.fi-br-speakers:before {
    content: "\f353";
}

.fi-br-speaker:before {
    content: "\f354";
}

.fi-br-sparkles:before {
    content: "\f355";
}

.fi-br-spaghetti-monster-flying:before {
    content: "\f356";
}

.fi-br-spade:before {
    content: "\f357";
}

.fi-br-space-station-moon:before {
    content: "\f358";
}

.fi-br-space-station-moon-alt:before {
    content: "\f359";
}

.fi-br-space-shuttle:before {
    content: "\f35a";
}

.fi-br-spa:before {
    content: "\f35b";
}

.fi-br-source-document:before {
    content: "\f35c";
}

.fi-br-source-document-alt:before {
    content: "\f35d";
}

.fi-br-source-data:before {
    content: "\f35e";
}

.fi-br-soup:before {
    content: "\f35f";
}

.fi-br-sort:before {
    content: "\f360";
}

.fi-br-sort-size-up:before {
    content: "\f361";
}

.fi-br-sort-size-down:before {
    content: "\f362";
}

.fi-br-sort-shapes-up:before {
    content: "\f363";
}

.fi-br-sort-shapes-down:before {
    content: "\f364";
}

.fi-br-sort-numeric-down:before {
    content: "\f365";
}

.fi-br-sort-numeric-down-alt:before {
    content: "\f366";
}

.fi-br-sort-down:before {
    content: "\f367";
}

.fi-br-sort-circle:before {
    content: "\f368";
}

.fi-br-sort-circle-up:before {
    content: "\f369";
}

.fi-br-sort-circle-down:before {
    content: "\f36a";
}

.fi-br-sort-amount-up:before {
    content: "\f36b";
}

.fi-br-sort-amount-up-alt:before {
    content: "\f36c";
}

.fi-br-sort-amount-down:before {
    content: "\f36d";
}

.fi-br-sort-amount-down-alt:before {
    content: "\f36e";
}

.fi-br-sort-alt:before {
    content: "\f36f";
}

.fi-br-sort-alpha-up:before {
    content: "\f370";
}

.fi-br-sort-alpha-up-alt:before {
    content: "\f371";
}

.fi-br-sort-alpha-down:before {
    content: "\f372";
}

.fi-br-sort-alpha-down-alt:before {
    content: "\f373";
}

.fi-br-sold-signal:before {
    content: "\f374";
}

.fi-br-sold-house:before {
    content: "\f375";
}

.fi-br-solar-system:before {
    content: "\f376";
}

.fi-br-solar-panel:before {
    content: "\f377";
}

.fi-br-sofa:before {
    content: "\f378";
}

.fi-br-sofa-size:before {
    content: "\f379";
}

.fi-br-socks:before {
    content: "\f37a";
}

.fi-br-social-network:before {
    content: "\f37b";
}

.fi-br-soap:before {
    content: "\f37c";
}

.fi-br-soap-alt:before {
    content: "\f37d";
}

.fi-br-snowplow:before {
    content: "\f37e";
}

.fi-br-snowmobile:before {
    content: "\f37f";
}

.fi-br-snowman-head:before {
    content: "\f380";
}

.fi-br-snowman-alt:before {
    content: "\f381";
}

.fi-br-snowflakes:before {
    content: "\f382";
}

.fi-br-snowflake:before {
    content: "\f383";
}

.fi-br-snowflake-droplets:before {
    content: "\f384";
}

.fi-br-snowboarding:before {
    content: "\f385";
}

.fi-br-snow-blowing:before {
    content: "\f386";
}

.fi-br-snooze:before {
    content: "\f387";
}

.fi-br-snap:before {
    content: "\f388";
}

.fi-br-snake:before {
    content: "\f389";
}

.fi-br-smoking:before {
    content: "\f38a";
}

.fi-br-smoking-ban:before {
    content: "\f38b";
}

.fi-br-smoke:before {
    content: "\f38c";
}

.fi-br-smog:before {
    content: "\f38d";
}

.fi-br-smiley-comment-alt:before {
    content: "\f38e";
}

.fi-br-smile-plus:before {
    content: "\f38f";
}

.fi-br-smile-beam:before {
    content: "\f390";
}

.fi-br-smartphone:before {
    content: "\f391";
}

.fi-br-sliders-v:before {
    content: "\f392";
}

.fi-br-sliders-v-square:before {
    content: "\f393";
}

.fi-br-sliders-h-square:before {
    content: "\f394";
}

.fi-br-sleigh:before {
    content: "\f395";
}

.fi-br-sledding:before {
    content: "\f396";
}

.fi-br-slash:before {
    content: "\f397";
}

.fi-br-skull:before {
    content: "\f398";
}

.fi-br-skull-crossbones:before {
    content: "\f399";
}

.fi-br-skull-cow:before {
    content: "\f39a";
}

.fi-br-skin:before {
    content: "\f39b";
}

.fi-br-skin-hair:before {
    content: "\f39c";
}

.fi-br-skin-drop:before {
    content: "\f39d";
}

.fi-br-skin-arrow:before {
    content: "\f39e";
}

.fi-br-skill:before {
    content: "\f39f";
}

.fi-br-skill-user:before {
    content: "\f3a0";
}

.fi-br-skill-alt:before {
    content: "\f3a1";
}

.fi-br-skiing:before {
    content: "\f3a2";
}

.fi-br-skiing-nordic:before {
    content: "\f3a3";
}

.fi-br-ski-lift:before {
    content: "\f3a4";
}

.fi-br-ski-jump:before {
    content: "\f3a5";
}

.fi-br-ski-boot-ski:before {
    content: "\f3a6";
}

.fi-br-skewer:before {
    content: "\f3a7";
}

.fi-br-skeleton:before {
    content: "\f3a8";
}

.fi-br-skating:before {
    content: "\f3a9";
}

.fi-br-skateboard:before {
    content: "\f3aa";
}

.fi-br-sitemap:before {
    content: "\f3ab";
}

.fi-br-site:before {
    content: "\f3ac";
}

.fi-br-site-browser:before {
    content: "\f3ad";
}

.fi-br-site-alt:before {
    content: "\f3ae";
}

.fi-br-siren-on:before {
    content: "\f3af";
}

.fi-br-sink:before {
    content: "\f3b0";
}

.fi-br-sim-cards:before {
    content: "\f3b1";
}

.fi-br-sim-card:before {
    content: "\f3b2";
}

.fi-br-signature:before {
    content: "\f3b3";
}

.fi-br-signature-slash:before {
    content: "\f3b4";
}

.fi-br-signature-lock:before {
    content: "\f3b5";
}

.fi-br-signal-stream:before {
    content: "\f3b6";
}

.fi-br-signal-stream-slash:before {
    content: "\f3b7";
}

.fi-br-signal-bars-weak:before {
    content: "\f3b8";
}

.fi-br-signal-bars-good:before {
    content: "\f3b9";
}

.fi-br-signal-bars-fair:before {
    content: "\f3ba";
}

.fi-br-signal-alt:before {
    content: "\f3bb";
}

.fi-br-signal-alt-slash:before {
    content: "\f3bc";
}

.fi-br-signal-alt-2:before {
    content: "\f3bd";
}

.fi-br-signal-alt-1:before {
    content: "\f3be";
}

.fi-br-sign-up:before {
    content: "\f3bf";
}

.fi-br-sign-out-alt:before {
    content: "\f3c0";
}

.fi-br-sign-in-alt:before {
    content: "\f3c1";
}

.fi-br-sign-hanging:before {
    content: "\f3c2";
}

.fi-br-sigma:before {
    content: "\f3c3";
}

.fi-br-sidebar:before {
    content: "\f3c4";
}

.fi-br-sidebar-flip:before {
    content: "\f3c5";
}

.fi-br-shuttlecock:before {
    content: "\f3c6";
}

.fi-br-shuttle-van:before {
    content: "\f3c7";
}

.fi-br-shuffle:before {
    content: "\f3c8";
}

.fi-br-shrimp:before {
    content: "\f3c9";
}

.fi-br-shredder:before {
    content: "\f3ca";
}

.fi-br-shower:before {
    content: "\f3cb";
}

.fi-br-shower-down:before {
    content: "\f3cc";
}

.fi-br-shovel:before {
    content: "\f3cd";
}

.fi-br-shovel-snow:before {
    content: "\f3ce";
}

.fi-br-shopping-cart:before {
    content: "\f3cf";
}

.fi-br-shopping-cart-check:before {
    content: "\f3d0";
}

.fi-br-shopping-cart-add:before {
    content: "\f3d1";
}

.fi-br-shopping-basket:before {
    content: "\f3d2";
}

.fi-br-shopping-bag:before {
    content: "\f3d3";
}

.fi-br-shopping-bag-add:before {
    content: "\f3d4";
}

.fi-br-shop:before {
    content: "\f3d5";
}

.fi-br-shop-slash:before {
    content: "\f3d6";
}

.fi-br-shop-lock:before {
    content: "\f3d7";
}

.fi-br-shoe-prints:before {
    content: "\f3d8";
}

.fi-br-shish-kebab:before {
    content: "\f3d9";
}

.fi-br-shirt:before {
    content: "\f3da";
}

.fi-br-shirt-tank-top:before {
    content: "\f3db";
}

.fi-br-shirt-running:before {
    content: "\f3dc";
}

.fi-br-shirt-long-sleeve:before {
    content: "\f3dd";
}

.fi-br-shipping-timed:before {
    content: "\f3de";
}

.fi-br-shipping-fast:before {
    content: "\f3df";
}

.fi-br-ship:before {
    content: "\f3e0";
}

.fi-br-ship-side:before {
    content: "\f3e1";
}

.fi-br-shield:before {
    content: "\f3e2";
}

.fi-br-shield-xmark:before {
    content: "\f3e3";
}

.fi-br-shield-virus:before {
    content: "\f3e4";
}

.fi-br-shield-slash:before {
    content: "\f3e5";
}

.fi-br-shield-plus:before {
    content: "\f3e6";
}

.fi-br-shield-minus:before {
    content: "\f3e7";
}

.fi-br-shield-keyhole:before {
    content: "\f3e8";
}

.fi-br-shield-interrogation:before {
    content: "\f3e9";
}

.fi-br-shield-exclamation:before {
    content: "\f3ea";
}

.fi-br-shield-dog:before {
    content: "\f3eb";
}

.fi-br-shield-cross:before {
    content: "\f3ec";
}

.fi-br-shield-check:before {
    content: "\f3ed";
}

.fi-br-shield-cat:before {
    content: "\f3ee";
}

.fi-br-shield-alt:before {
    content: "\f3ef";
}

.fi-br-shelves:before {
    content: "\f3f0";
}

.fi-br-shekel-sign:before {
    content: "\f3f1";
}

.fi-br-sheep:before {
    content: "\f3f2";
}

.fi-br-share:before {
    content: "\f3f3";
}

.fi-br-share-square:before {
    content: "\f3f4";
}

.fi-br-share-alt-square:before {
    content: "\f3f5";
}

.fi-br-settings:before {
    content: "\f3f6";
}

.fi-br-settings-sliders:before {
    content: "\f3f7";
}

.fi-br-sensor:before {
    content: "\f3f8";
}

.fi-br-sensor-smoke:before {
    content: "\f3f9";
}

.fi-br-sensor-on:before {
    content: "\f3fa";
}

.fi-br-sensor-fire:before {
    content: "\f3fb";
}

.fi-br-sensor-alert:before {
    content: "\f3fc";
}

.fi-br-send-money:before {
    content: "\f3fd";
}

.fi-br-send-money-smartphone:before {
    content: "\f3fe";
}

.fi-br-send-backward:before {
    content: "\f3ff";
}

.fi-br-send-back:before {
    content: "\f400";
}

.fi-br-selling:before {
    content: "\f401";
}

.fi-br-seller:before {
    content: "\f402";
}

.fi-br-seller-store:before {
    content: "\f403";
}

.fi-br-sell:before {
    content: "\f404";
}

.fi-br-selection:before {
    content: "\f405";
}

.fi-br-seedling:before {
    content: "\f406";
}

.fi-br-second:before {
    content: "\f407";
}

.fi-br-second-medal:before {
    content: "\f408";
}

.fi-br-second-laurel:before {
    content: "\f409";
}

.fi-br-second-award:before {
    content: "\f40a";
}

.fi-br-seat-airline:before {
    content: "\f40b";
}

.fi-br-search:before {
    content: "\f40c";
}

.fi-br-search-location:before {
    content: "\f40d";
}

.fi-br-search-heart:before {
    content: "\f40e";
}

.fi-br-search-dollar:before {
    content: "\f40f";
}

.fi-br-search-alt:before {
    content: "\f410";
}

.fi-br-seal:before {
    content: "\f411";
}

.fi-br-seal-question:before {
    content: "\f412";
}

.fi-br-seal-exclamation:before {
    content: "\f413";
}

.fi-br-sd-cards:before {
    content: "\f414";
}

.fi-br-sd-card:before {
    content: "\f415";
}

.fi-br-scythe:before {
    content: "\f416";
}

.fi-br-scrubber:before {
    content: "\f417";
}

.fi-br-scroll:before {
    content: "\f418";
}

.fi-br-scroll-torah:before {
    content: "\f419";
}

.fi-br-scroll-old:before {
    content: "\f41a";
}

.fi-br-script:before {
    content: "\f41b";
}

.fi-br-scribble:before {
    content: "\f41c";
}

.fi-br-screwdriver:before {
    content: "\f41d";
}

.fi-br-screencast:before {
    content: "\f41e";
}

.fi-br-screen:before {
    content: "\f41f";
}

.fi-br-scissors:before {
    content: "\f420";
}

.fi-br-school:before {
    content: "\f421";
}

.fi-br-school-bus:before {
    content: "\f422";
}

.fi-br-scarf:before {
    content: "\f423";
}

.fi-br-scarecrow:before {
    content: "\f424";
}

.fi-br-scanner-touchscreen:before {
    content: "\f425";
}

.fi-br-scanner-keyboard:before {
    content: "\f426";
}

.fi-br-scanner-image:before {
    content: "\f427";
}

.fi-br-scanner-gun:before {
    content: "\f428";
}

.fi-br-scalpel:before {
    content: "\f429";
}

.fi-br-scalpel-path:before {
    content: "\f42a";
}

.fi-br-scale:before {
    content: "\f42b";
}

.fi-br-scale-comparison:before {
    content: "\f42c";
}

.fi-br-scale-comparison-alt:before {
    content: "\f42d";
}

.fi-br-saxophone:before {
    content: "\f42e";
}

.fi-br-sax-hot:before {
    content: "\f42f";
}

.fi-br-sausage:before {
    content: "\f430";
}

.fi-br-sauce:before {
    content: "\f431";
}

.fi-br-saturday:before {
    content: "\f432";
}

.fi-br-satellite:before {
    content: "\f433";
}

.fi-br-satellite-dish:before {
    content: "\f434";
}

.fi-br-sandwich:before {
    content: "\f435";
}

.fi-br-sandwich-alt:before {
    content: "\f436";
}

.fi-br-salt-shaker:before {
    content: "\f437";
}

.fi-br-salt-pepper:before {
    content: "\f438";
}

.fi-br-salary-alt:before {
    content: "\f439";
}

.fi-br-salad:before {
    content: "\f43a";
}

.fi-br-sailboat:before {
    content: "\f43b";
}

.fi-br-safe-box:before {
    content: "\f43c";
}

.fi-br-sad:before {
    content: "\f43d";
}

.fi-br-sad-tear:before {
    content: "\f43e";
}

.fi-br-sad-cry:before {
    content: "\f43f";
}

.fi-br-sack:before {
    content: "\f440";
}

.fi-br-sack-dollar:before {
    content: "\f441";
}

.fi-br-s:before {
    content: "\f442";
}

.fi-br-rv:before {
    content: "\f443";
}

.fi-br-rupiah-sign:before {
    content: "\f444";
}

.fi-br-rupee-sign:before {
    content: "\f445";
}

.fi-br-running:before {
    content: "\f446";
}

.fi-br-rules:before {
    content: "\f447";
}

.fi-br-rules-alt:before {
    content: "\f448";
}

.fi-br-ruler-vertical:before {
    content: "\f449";
}

.fi-br-ruler-triangle:before {
    content: "\f44a";
}

.fi-br-ruler-horizontal:before {
    content: "\f44b";
}

.fi-br-ruler-combined:before {
    content: "\f44c";
}

.fi-br-rugby:before {
    content: "\f44d";
}

.fi-br-rugby-helmet:before {
    content: "\f44e";
}

.fi-br-ruble-sign:before {
    content: "\f44f";
}

.fi-br-rss:before {
    content: "\f450";
}

.fi-br-rss-alt:before {
    content: "\f451";
}

.fi-br-router:before {
    content: "\f452";
}

.fi-br-route:before {
    content: "\f453";
}

.fi-br-route-interstate:before {
    content: "\f454";
}

.fi-br-route-highway:before {
    content: "\f455";
}

.fi-br-rotate-square:before {
    content: "\f456";
}

.fi-br-rotate-right:before {
    content: "\f457";
}

.fi-br-rotate-left:before {
    content: "\f458";
}

.fi-br-rotate-exclamation:before {
    content: "\f459";
}

.fi-br-room-service:before {
    content: "\f45a";
}

.fi-br-roller-coaster:before {
    content: "\f45b";
}

.fi-br-rocket:before {
    content: "\f45c";
}

.fi-br-rocket-lunch:before {
    content: "\f45d";
}

.fi-br-robot:before {
    content: "\f45e";
}

.fi-br-road:before {
    content: "\f45f";
}

.fi-br-risk:before {
    content: "\f460";
}

.fi-br-risk-alt:before {
    content: "\f461";
}

.fi-br-rings-wedding:before {
    content: "\f462";
}

.fi-br-ring:before {
    content: "\f463";
}

.fi-br-ring-diamond:before {
    content: "\f464";
}

.fi-br-right:before {
    content: "\f465";
}

.fi-br-ribbon:before {
    content: "\f466";
}

.fi-br-rhombus:before {
    content: "\f467";
}

.fi-br-rewind:before {
    content: "\f468";
}

.fi-br-review:before {
    content: "\f469";
}

.fi-br-revenue-alt:before {
    content: "\f46a";
}

.fi-br-restroom-simple:before {
    content: "\f46b";
}

.fi-br-restock:before {
    content: "\f46c";
}

.fi-br-restaurant:before {
    content: "\f46d";
}

.fi-br-resources:before {
    content: "\f46e";
}

.fi-br-resize:before {
    content: "\f46f";
}

.fi-br-reservation-table:before {
    content: "\f470";
}

.fi-br-reservation-smartphone:before {
    content: "\f471";
}

.fi-br-republican:before {
    content: "\f472";
}

.fi-br-reply-all:before {
    content: "\f473";
}

.fi-br-replace:before {
    content: "\f474";
}

.fi-br-rent:before {
    content: "\f475";
}

.fi-br-rent-signal:before {
    content: "\f476";
}

.fi-br-remove-user:before {
    content: "\f477";
}

.fi-br-remove-folder:before {
    content: "\f478";
}

.fi-br-registration-paper:before {
    content: "\f479";
}

.fi-br-registered:before {
    content: "\f47a";
}

.fi-br-region-pin:before {
    content: "\f47b";
}

.fi-br-region-pin-alt:before {
    content: "\f47c";
}

.fi-br-refund:before {
    content: "\f47d";
}

.fi-br-refund-alt:before {
    content: "\f47e";
}

.fi-br-refrigerator:before {
    content: "\f47f";
}

.fi-br-refresh:before {
    content: "\f480";
}

.fi-br-reflect:before {
    content: "\f481";
}

.fi-br-reflect-vertical:before {
    content: "\f482";
}

.fi-br-reflect-horizontal:before {
    content: "\f483";
}

.fi-br-refer:before {
    content: "\f484";
}

.fi-br-refer-arrow:before {
    content: "\f485";
}

.fi-br-reel:before {
    content: "\f486";
}

.fi-br-redo:before {
    content: "\f487";
}

.fi-br-redo-alt:before {
    content: "\f488";
}

.fi-br-recycle:before {
    content: "\f489";
}

.fi-br-rectangles-mixed:before {
    content: "\f48a";
}

.fi-br-rectangle-xmark:before {
    content: "\f48b";
}

.fi-br-rectangle-vertical:before {
    content: "\f48c";
}

.fi-br-rectangle-vertical-history:before {
    content: "\f48d";
}

.fi-br-rectangle-pro:before {
    content: "\f48e";
}

.fi-br-rectangle-panoramic:before {
    content: "\f48f";
}

.fi-br-rectangle-list:before {
    content: "\f490";
}

.fi-br-rectangle-horizontal:before {
    content: "\f491";
}

.fi-br-rectangle-history-circle-plus:before {
    content: "\f492";
}

.fi-br-rectangle-code:before {
    content: "\f493";
}

.fi-br-rectangle-barcode:before {
    content: "\f494";
}

.fi-br-rectabgle-vertical:before {
    content: "\f495";
}

.fi-br-record-vinyl:before {
    content: "\f496";
}

.fi-br-recipe:before {
    content: "\f497";
}

.fi-br-recipe-book:before {
    content: "\f498";
}

.fi-br-receipt:before {
    content: "\f499";
}

.fi-br-rec:before {
    content: "\f49a";
}

.fi-br-react:before {
    content: "\f49b";
}

.fi-br-razor-barber:before {
    content: "\f49c";
}

.fi-br-raygun:before {
    content: "\f49d";
}

.fi-br-ranking-stars:before {
    content: "\f49e";
}

.fi-br-ranking-star:before {
    content: "\f49f";
}

.fi-br-ranking-podium:before {
    content: "\f4a0";
}

.fi-br-ranking-podium-empty:before {
    content: "\f4a1";
}

.fi-br-rank:before {
    content: "\f4a2";
}

.fi-br-ramp-loading:before {
    content: "\f4a3";
}

.fi-br-ram:before {
    content: "\f4a4";
}

.fi-br-raindrops:before {
    content: "\f4a5";
}

.fi-br-rainbow:before {
    content: "\f4a6";
}

.fi-br-radish:before {
    content: "\f4a7";
}

.fi-br-radio:before {
    content: "\f4a8";
}

.fi-br-radio-alt:before {
    content: "\f4a9";
}

.fi-br-radiation:before {
    content: "\f4aa";
}

.fi-br-radiation-alt:before {
    content: "\f4ab";
}

.fi-br-radar:before {
    content: "\f4ac";
}

.fi-br-racquet:before {
    content: "\f4ad";
}

.fi-br-rabbit:before {
    content: "\f4ae";
}

.fi-br-rabbit-fast:before {
    content: "\f4af";
}

.fi-br-r:before {
    content: "\f4b0";
}

.fi-br-quote-right:before {
    content: "\f4b1";
}

.fi-br-quiz:before {
    content: "\f4b2";
}

.fi-br-quiz-alt:before {
    content: "\f4b3";
}

.fi-br-queue:before {
    content: "\f4b4";
}

.fi-br-queue-signal:before {
    content: "\f4b5";
}

.fi-br-queue-line:before {
    content: "\f4b6";
}

.fi-br-queue-alt:before {
    content: "\f4b7";
}

.fi-br-question:before {
    content: "\f4b8";
}

.fi-br-question-square:before {
    content: "\f4b9";
}

.fi-br-qrcode:before {
    content: "\f4ba";
}

.fi-br-qr-scan:before {
    content: "\f4bb";
}

.fi-br-q:before {
    content: "\f4bc";
}

.fi-br-pyramid:before {
    content: "\f4bd";
}

.fi-br-puzzle-pieces:before {
    content: "\f4be";
}

.fi-br-puzzle-piece:before {
    content: "\f4bf";
}

.fi-br-puzzle-alt:before {
    content: "\f4c0";
}

.fi-br-pumpkin:before {
    content: "\f4c1";
}

.fi-br-pumpkin-alt:before {
    content: "\f4c2";
}

.fi-br-pumpkin-alt-2:before {
    content: "\f4c3";
}

.fi-br-pump:before {
    content: "\f4c4";
}

.fi-br-pump-medical:before {
    content: "\f4c5";
}

.fi-br-pulse:before {
    content: "\f4c6";
}

.fi-br-protractor:before {
    content: "\f4c7";
}

.fi-br-projector:before {
    content: "\f4c8";
}

.fi-br-productivity:before {
    content: "\f4c9";
}

.fi-br-process:before {
    content: "\f4ca";
}

.fi-br-procedures:before {
    content: "\f4cb";
}

.fi-br-problem-solving:before {
    content: "\f4cc";
}

.fi-br-priority-importance:before {
    content: "\f4cd";
}

.fi-br-priority-arrows:before {
    content: "\f4ce";
}

.fi-br-priority-arrow:before {
    content: "\f4cf";
}

.fi-br-print:before {
    content: "\f4d0";
}

.fi-br-print-slash:before {
    content: "\f4d1";
}

.fi-br-print-magnifying-glass:before {
    content: "\f4d2";
}

.fi-br-preview:before {
    content: "\f4d3";
}

.fi-br-presentation:before {
    content: "\f4d4";
}

.fi-br-prescription:before {
    content: "\f4d5";
}

.fi-br-prescription-bottle:before {
    content: "\f4d6";
}

.fi-br-prescription-bottle-pill:before {
    content: "\f4d7";
}

.fi-br-prescription-bottle-alt:before {
    content: "\f4d8";
}

.fi-br-praying-hands:before {
    content: "\f4d9";
}

.fi-br-practice:before {
    content: "\f4da";
}

.fi-br-power:before {
    content: "\f4db";
}

.fi-br-pound:before {
    content: "\f4dc";
}

.fi-br-potato:before {
    content: "\f4dd";
}

.fi-br-pot:before {
    content: "\f4de";
}

.fi-br-portrait:before {
    content: "\f4df";
}

.fi-br-portal-exit:before {
    content: "\f4e0";
}

.fi-br-portal-enter:before {
    content: "\f4e1";
}

.fi-br-popsicle:before {
    content: "\f4e2";
}

.fi-br-popcorn:before {
    content: "\f4e3";
}

.fi-br-poop:before {
    content: "\f4e4";
}

.fi-br-pool-8-ball:before {
    content: "\f4e5";
}

.fi-br-poo:before {
    content: "\f4e6";
}

.fi-br-poo-bolt:before {
    content: "\f4e7";
}

.fi-br-pompebled:before {
    content: "\f4e8";
}

.fi-br-pollution:before {
    content: "\f4e9";
}

.fi-br-poll-h:before {
    content: "\f4ea";
}

.fi-br-police-box:before {
    content: "\f4eb";
}

.fi-br-poker-chip:before {
    content: "\f4ec";
}

.fi-br-point-of-sale:before {
    content: "\f4ed";
}

.fi-br-point-of-sale-signal:before {
    content: "\f4ee";
}

.fi-br-point-of-sale-bill:before {
    content: "\f4ef";
}

.fi-br-podium:before {
    content: "\f4f0";
}

.fi-br-podium-star:before {
    content: "\f4f1";
}

.fi-br-podcast:before {
    content: "\f4f2";
}

.fi-br-plus:before {
    content: "\f4f3";
}

.fi-br-plus-small:before {
    content: "\f4f4";
}

.fi-br-plus-minus:before {
    content: "\f4f5";
}

.fi-br-plus-hexagon:before {
    content: "\f4f6";
}

.fi-br-plug:before {
    content: "\f4f7";
}

.fi-br-plug-connection:before {
    content: "\f4f8";
}

.fi-br-plug-cable:before {
    content: "\f4f9";
}

.fi-br-plug-alt:before {
    content: "\f4fa";
}

.fi-br-playing-cards:before {
    content: "\f4fb";
}

.fi-br-play:before {
    content: "\f4fc";
}

.fi-br-play-pause:before {
    content: "\f4fd";
}

.fi-br-play-circle:before {
    content: "\f4fe";
}

.fi-br-play-alt:before {
    content: "\f4ff";
}

.fi-br-plate:before {
    content: "\f500";
}

.fi-br-plate-wheat:before {
    content: "\f501";
}

.fi-br-plate-utensils:before {
    content: "\f502";
}

.fi-br-plant-wilt:before {
    content: "\f503";
}

.fi-br-planet-ringed:before {
    content: "\f504";
}

.fi-br-planet-moon:before {
    content: "\f505";
}

.fi-br-plane:before {
    content: "\f506";
}

.fi-br-plane-tail:before {
    content: "\f507";
}

.fi-br-plane-slash:before {
    content: "\f508";
}

.fi-br-plane-prop:before {
    content: "\f509";
}

.fi-br-plane-departure:before {
    content: "\f50a";
}

.fi-br-plane-arrival:before {
    content: "\f50b";
}

.fi-br-plane-alt:before {
    content: "\f50c";
}

.fi-br-plan:before {
    content: "\f50d";
}

.fi-br-plan-strategy:before {
    content: "\f50e";
}

.fi-br-place-of-worship:before {
    content: "\f50f";
}

.fi-br-pinata:before {
    content: "\f510";
}

.fi-br-pizza-slice:before {
    content: "\f511";
}

.fi-br-pipe-smoking:before {
    content: "\f512";
}

.fi-br-ping-pong:before {
    content: "\f513";
}

.fi-br-pineapple:before {
    content: "\f514";
}

.fi-br-pineapple-alt:before {
    content: "\f515";
}

.fi-br-pills:before {
    content: "\f516";
}

.fi-br-piggy-bank:before {
    content: "\f517";
}

.fi-br-pig:before {
    content: "\f518";
}

.fi-br-pig-face:before {
    content: "\f519";
}

.fi-br-pie:before {
    content: "\f51a";
}

.fi-br-picture:before {
    content: "\f51b";
}

.fi-br-picpeople:before {
    content: "\f51c";
}

.fi-br-picpeople-filled:before {
    content: "\f51d";
}

.fi-br-picnic:before {
    content: "\f51e";
}

.fi-br-picking:before {
    content: "\f51f";
}

.fi-br-pickaxe:before {
    content: "\f520";
}

.fi-br-piano:before {
    content: "\f521";
}

.fi-br-piano-keyboard:before {
    content: "\f522";
}

.fi-br-physics:before {
    content: "\f523";
}

.fi-br-photo-video:before {
    content: "\f524";
}

.fi-br-photo-film-music:before {
    content: "\f525";
}

.fi-br-photo-capture:before {
    content: "\f526";
}

.fi-br-phone-slash:before {
    content: "\f527";
}

.fi-br-phone-rotary:before {
    content: "\f528";
}

.fi-br-phone-plus:before {
    content: "\f529";
}

.fi-br-phone-pause:before {
    content: "\f52a";
}

.fi-br-phone-office:before {
    content: "\f52b";
}

.fi-br-phone-flip:before {
    content: "\f52c";
}

.fi-br-phone-cross:before {
    content: "\f52d";
}

.fi-br-phone-call:before {
    content: "\f52e";
}

.fi-br-pharmacy:before {
    content: "\f52f";
}

.fi-br-pets:before {
    content: "\f530";
}

.fi-br-peso-sign:before {
    content: "\f531";
}

.fi-br-peseta-sign:before {
    content: "\f532";
}

.fi-br-person-walking-with-cane:before {
    content: "\f533";
}

.fi-br-person-simple:before {
    content: "\f534";
}

.fi-br-person-sign:before {
    content: "\f535";
}

.fi-br-person-shelter:before {
    content: "\f536";
}

.fi-br-person-seat:before {
    content: "\f537";
}

.fi-br-person-seat-reclined:before {
    content: "\f538";
}

.fi-br-person-pregnant:before {
    content: "\f539";
}

.fi-br-person-praying:before {
    content: "\f53a";
}

.fi-br-person-dress:before {
    content: "\f53b";
}

.fi-br-person-dress-simple:before {
    content: "\f53c";
}

.fi-br-person-dolly:before {
    content: "\f53d";
}

.fi-br-person-dolly-empty:before {
    content: "\f53e";
}

.fi-br-person-cv:before {
    content: "\f53f";
}

.fi-br-person-circle-xmark:before {
    content: "\f540";
}

.fi-br-person-circle-question:before {
    content: "\f541";
}

.fi-br-person-circle-plus:before {
    content: "\f542";
}

.fi-br-person-circle-minus:before {
    content: "\f543";
}

.fi-br-person-circle-exclamation:before {
    content: "\f544";
}

.fi-br-person-circle-check:before {
    content: "\f545";
}

.fi-br-person-carry-box:before {
    content: "\f546";
}

.fi-br-percentage:before {
    content: "\f547";
}

.fi-br-percent-90:before {
    content: "\f548";
}

.fi-br-percent-80:before {
    content: "\f549";
}

.fi-br-percent-75:before {
    content: "\f54a";
}

.fi-br-percent-70:before {
    content: "\f54b";
}

.fi-br-percent-60:before {
    content: "\f54c";
}

.fi-br-percent-50:before {
    content: "\f54d";
}

.fi-br-percent-40:before {
    content: "\f54e";
}

.fi-br-percent-30:before {
    content: "\f54f";
}

.fi-br-percent-25:before {
    content: "\f550";
}

.fi-br-percent-20:before {
    content: "\f551";
}

.fi-br-percent-100:before {
    content: "\f552";
}

.fi-br-percent-10:before {
    content: "\f553";
}

.fi-br-pepper:before {
    content: "\f554";
}

.fi-br-pepper-hot:before {
    content: "\f555";
}

.fi-br-pepper-alt:before {
    content: "\f556";
}

.fi-br-people:before {
    content: "\f557";
}

.fi-br-people-roof:before {
    content: "\f558";
}

.fi-br-people-poll:before {
    content: "\f559";
}

.fi-br-people-pants:before {
    content: "\f55a";
}

.fi-br-people-dress:before {
    content: "\f55b";
}

.fi-br-people-carry-box:before {
    content: "\f55c";
}

.fi-br-people-arrows-left-right:before {
    content: "\f55d";
}

.fi-br-pennant:before {
    content: "\f55e";
}

.fi-br-pending:before {
    content: "\f55f";
}

.fi-br-pencil:before {
    content: "\f560";
}

.fi-br-pencil-slash:before {
    content: "\f561";
}

.fi-br-pencil-ruler:before {
    content: "\f562";
}

.fi-br-pencil-paintbrush:before {
    content: "\f563";
}

.fi-br-pen-swirl:before {
    content: "\f564";
}

.fi-br-pen-square:before {
    content: "\f565";
}

.fi-br-pen-slash:before {
    content: "\f566";
}

.fi-br-pen-nib:before {
    content: "\f567";
}

.fi-br-pen-nib-slash:before {
    content: "\f568";
}

.fi-br-pen-field:before {
    content: "\f569";
}

.fi-br-pen-fancy:before {
    content: "\f56a";
}

.fi-br-pen-fancy-slash:before {
    content: "\f56b";
}

.fi-br-pen-clip:before {
    content: "\f56c";
}

.fi-br-pen-clip-slash:before {
    content: "\f56d";
}

.fi-br-pen-circle:before {
    content: "\f56e";
}

.fi-br-pedestal:before {
    content: "\f56f";
}

.fi-br-pear:before {
    content: "\f570";
}

.fi-br-peapod:before {
    content: "\f571";
}

.fi-br-peanuts:before {
    content: "\f572";
}

.fi-br-peanut:before {
    content: "\f573";
}

.fi-br-peach:before {
    content: "\f574";
}

.fi-br-peace:before {
    content: "\f575";
}

.fi-br-payroll:before {
    content: "\f576";
}

.fi-br-payroll-calendar:before {
    content: "\f577";
}

.fi-br-payment-pos:before {
    content: "\f578";
}

.fi-br-paw:before {
    content: "\f579";
}

.fi-br-paw-heart:before {
    content: "\f57a";
}

.fi-br-paw-claws:before {
    content: "\f57b";
}

.fi-br-pause:before {
    content: "\f57c";
}

.fi-br-pause-circle:before {
    content: "\f57d";
}

.fi-br-pattern:before {
    content: "\f57e";
}

.fi-br-paste:before {
    content: "\f57f";
}

.fi-br-password:before {
    content: "\f580";
}

.fi-br-password-smartphone:before {
    content: "\f581";
}

.fi-br-password-email:before {
    content: "\f582";
}

.fi-br-password-computer:before {
    content: "\f583";
}

.fi-br-password-alt:before {
    content: "\f584";
}

.fi-br-passport:before {
    content: "\f585";
}

.fi-br-party-horn:before {
    content: "\f586";
}

.fi-br-party-bell:before {
    content: "\f587";
}

.fi-br-parking:before {
    content: "\f588";
}

.fi-br-parking-slash:before {
    content: "\f589";
}

.fi-br-parking-circle:before {
    content: "\f58a";
}

.fi-br-parking-circle-slash:before {
    content: "\f58b";
}

.fi-br-paragraph:before {
    content: "\f58c";
}

.fi-br-paragraph-left:before {
    content: "\f58d";
}

.fi-br-parachute-box:before {
    content: "\f58e";
}

.fi-br-paperclip-vertical:before {
    content: "\f58f";
}

.fi-br-paper-plane:before {
    content: "\f590";
}

.fi-br-paper-plane-top:before {
    content: "\f591";
}

.fi-br-panorama:before {
    content: "\f592";
}

.fi-br-pancakes:before {
    content: "\f593";
}

.fi-br-pan:before {
    content: "\f594";
}

.fi-br-pan-frying:before {
    content: "\f595";
}

.fi-br-pan-food:before {
    content: "\f596";
}

.fi-br-pallet:before {
    content: "\f597";
}

.fi-br-pallet-alt:before {
    content: "\f598";
}

.fi-br-palette:before {
    content: "\f599";
}

.fi-br-paintbrush-pencil:before {
    content: "\f59a";
}

.fi-br-paint-roller:before {
    content: "\f59b";
}

.fi-br-paint-brush:before {
    content: "\f59c";
}

.fi-br-paid:before {
    content: "\f59d";
}

.fi-br-pager:before {
    content: "\f59e";
}

.fi-br-page-break:before {
    content: "\f59f";
}

.fi-br-padlock-check:before {
    content: "\f5a0";
}

.fi-br-package:before {
    content: "\f5a1";
}

.fi-br-p:before {
    content: "\f5a2";
}

.fi-br-overview:before {
    content: "\f5a3";
}

.fi-br-overline:before {
    content: "\f5a4";
}

.fi-br-oven:before {
    content: "\f5a5";
}

.fi-br-oval:before {
    content: "\f5a6";
}

.fi-br-oval-alt:before {
    content: "\f5a7";
}

.fi-br-outdent:before {
    content: "\f5a8";
}

.fi-br-otter:before {
    content: "\f5a9";
}

.fi-br-otp:before {
    content: "\f5aa";
}

.fi-br-ornament:before {
    content: "\f5ab";
}

.fi-br-organization-chart:before {
    content: "\f5ac";
}

.fi-br-operation:before {
    content: "\f5ad";
}

.fi-br-opacity:before {
    content: "\f5ae";
}

.fi-br-onion:before {
    content: "\f5af";
}

.fi-br-onboarding:before {
    content: "\f5b0";
}

.fi-br-omega:before {
    content: "\f5b1";
}

.fi-br-om:before {
    content: "\f5b2";
}

.fi-br-olives:before {
    content: "\f5b3";
}

.fi-br-olive:before {
    content: "\f5b4";
}

.fi-br-olive-oil:before {
    content: "\f5b5";
}

.fi-br-oil-temp:before {
    content: "\f5b6";
}

.fi-br-oil-can:before {
    content: "\f5b7";
}

.fi-br-octagon:before {
    content: "\f5b8";
}

.fi-br-octagon-xmark:before {
    content: "\f5b9";
}

.fi-br-octagon-plus:before {
    content: "\f5ba";
}

.fi-br-octagon-minus:before {
    content: "\f5bb";
}

.fi-br-octagon-exclamation:before {
    content: "\f5bc";
}

.fi-br-octagon-divide:before {
    content: "\f5bd";
}

.fi-br-octagon-check:before {
    content: "\f5be";
}

.fi-br-objects-column:before {
    content: "\f5bf";
}

.fi-br-object-union:before {
    content: "\f5c0";
}

.fi-br-object-ungroup:before {
    content: "\f5c1";
}

.fi-br-object-subtract:before {
    content: "\f5c2";
}

.fi-br-object-intersect:before {
    content: "\f5c3";
}

.fi-br-object-group:before {
    content: "\f5c4";
}

.fi-br-object-exclude:before {
    content: "\f5c5";
}

.fi-br-o:before {
    content: "\f5c6";
}

.fi-br-notes:before {
    content: "\f5c7";
}

.fi-br-notes-medical:before {
    content: "\f5c8";
}

.fi-br-notebook:before {
    content: "\f5c9";
}

.fi-br-notebook-alt:before {
    content: "\f5ca";
}

.fi-br-note:before {
    content: "\f5cb";
}

.fi-br-note-sticky:before {
    content: "\f5cc";
}

.fi-br-note-medical:before {
    content: "\f5cd";
}

.fi-br-notdef:before {
    content: "\f5ce";
}

.fi-br-not-found:before {
    content: "\f5cf";
}

.fi-br-not-found-magnifying-glass:before {
    content: "\f5d0";
}

.fi-br-not-found-alt:before {
    content: "\f5d1";
}

.fi-br-not-equal:before {
    content: "\f5d2";
}

.fi-br-nose:before {
    content: "\f5d3";
}

.fi-br-noodles:before {
    content: "\f5d4";
}

.fi-br-no-people:before {
    content: "\f5d5";
}

.fi-br-no-fee:before {
    content: "\f5d6";
}

.fi-br-nfc:before {
    content: "\f5d7";
}

.fi-br-nfc-trash:before {
    content: "\f5d8";
}

.fi-br-nfc-slash:before {
    content: "\f5d9";
}

.fi-br-nfc-pen:before {
    content: "\f5da";
}

.fi-br-nfc-magnifying-glass:before {
    content: "\f5db";
}

.fi-br-nfc-lock:before {
    content: "\f5dc";
}

.fi-br-newspaper:before {
    content: "\f5dd";
}

.fi-br-newspaper-open:before {
    content: "\f5de";
}

.fi-br-neuter:before {
    content: "\f5df";
}

.fi-br-network:before {
    content: "\f5e0";
}

.fi-br-network-cloud:before {
    content: "\f5e1";
}

.fi-br-network-analytic:before {
    content: "\f5e2";
}

.fi-br-nesting-dolls:before {
    content: "\f5e3";
}

.fi-br-navigation:before {
    content: "\f5e4";
}

.fi-br-narwhal:before {
    content: "\f5e5";
}

.fi-br-naira-sign:before {
    content: "\f5e6";
}

.fi-br-n:before {
    content: "\f5e7";
}

.fi-br-music:before {
    content: "\f5e8";
}

.fi-br-music-slash:before {
    content: "\f5e9";
}

.fi-br-music-note:before {
    content: "\f5ea";
}

.fi-br-music-note-slash:before {
    content: "\f5eb";
}

.fi-br-music-file:before {
    content: "\f5ec";
}

.fi-br-music-alt:before {
    content: "\f5ed";
}

.fi-br-mushroom:before {
    content: "\f5ee";
}

.fi-br-mushroom-alt:before {
    content: "\f5ef";
}

.fi-br-muscle:before {
    content: "\f5f0";
}

.fi-br-multiple:before {
    content: "\f5f1";
}

.fi-br-multiple-alt:before {
    content: "\f5f2";
}

.fi-br-mug:before {
    content: "\f5f3";
}

.fi-br-mug-tea:before {
    content: "\f5f4";
}

.fi-br-mug-tea-saucer:before {
    content: "\f5f5";
}

.fi-br-mug-marshmallows:before {
    content: "\f5f6";
}

.fi-br-mug-hot:before {
    content: "\f5f7";
}

.fi-br-mug-hot-alt:before {
    content: "\f5f8";
}

.fi-br-mug-alt:before {
    content: "\f5f9";
}

.fi-br-mp3-player:before {
    content: "\f5fa";
}

.fi-br-move-to-folder:before {
    content: "\f5fb";
}

.fi-br-move-to-folder-2:before {
    content: "\f5fc";
}

.fi-br-mouse:before {
    content: "\f5fd";
}

.fi-br-mountains:before {
    content: "\f5fe";
}

.fi-br-mountain:before {
    content: "\f5ff";
}

.fi-br-mountain-city:before {
    content: "\f600";
}

.fi-br-mound:before {
    content: "\f601";
}

.fi-br-motorcycle:before {
    content: "\f602";
}

.fi-br-mosquito:before {
    content: "\f603";
}

.fi-br-mosquito-net:before {
    content: "\f604";
}

.fi-br-mosque:before {
    content: "\f605";
}

.fi-br-mosque-moon:before {
    content: "\f606";
}

.fi-br-mosque-alt:before {
    content: "\f607";
}

.fi-br-mortar-pestle:before {
    content: "\f608";
}

.fi-br-moped:before {
    content: "\f609";
}

.fi-br-moon:before {
    content: "\f60a";
}

.fi-br-moon-stars:before {
    content: "\f60b";
}

.fi-br-monument:before {
    content: "\f60c";
}

.fi-br-monkey:before {
    content: "\f60d";
}

.fi-br-money:before {
    content: "\f60e";
}

.fi-br-money-transfer-smartphone:before {
    content: "\f60f";
}

.fi-br-money-transfer-coin-arrow:before {
    content: "\f610";
}

.fi-br-money-transfer-alt:before {
    content: "\f611";
}

.fi-br-money-simple-from-bracket:before {
    content: "\f612";
}

.fi-br-money-from-bracket:before {
    content: "\f613";
}

.fi-br-money-coin-transfer:before {
    content: "\f614";
}

.fi-br-money-check:before {
    content: "\f615";
}

.fi-br-money-check-edit:before {
    content: "\f616";
}

.fi-br-money-check-edit-alt:before {
    content: "\f617";
}

.fi-br-money-bills:before {
    content: "\f618";
}

.fi-br-money-bills-simple:before {
    content: "\f619";
}

.fi-br-money-bill-wave:before {
    content: "\f61a";
}

.fi-br-money-bill-wave-alt:before {
    content: "\f61b";
}

.fi-br-money-bill-transfer:before {
    content: "\f61c";
}

.fi-br-money-bill-simple:before {
    content: "\f61d";
}

.fi-br-monday:before {
    content: "\f61e";
}

.fi-br-module:before {
    content: "\f61f";
}

.fi-br-model-cube:before {
    content: "\f620";
}

.fi-br-model-cube-space:before {
    content: "\f621";
}

.fi-br-model-cube-arrows:before {
    content: "\f622";
}

.fi-br-mode:before {
    content: "\f623";
}

.fi-br-mode-portrait:before {
    content: "\f624";
}

.fi-br-mode-landscape:before {
    content: "\f625";
}

.fi-br-mode-alt:before {
    content: "\f626";
}

.fi-br-mockup:before {
    content: "\f627";
}

.fi-br-mobile:before {
    content: "\f628";
}

.fi-br-mobile-notch:before {
    content: "\f629";
}

.fi-br-mobile-button:before {
    content: "\f62a";
}

.fi-br-mix:before {
    content: "\f62b";
}

.fi-br-mistletoe:before {
    content: "\f62c";
}

.fi-br-minus:before {
    content: "\f62d";
}

.fi-br-minus-small:before {
    content: "\f62e";
}

.fi-br-minus-hexagon:before {
    content: "\f62f";
}

.fi-br-minus-circle:before {
    content: "\f630";
}

.fi-br-mind-share:before {
    content: "\f631";
}

.fi-br-mill-sign:before {
    content: "\f632";
}

.fi-br-milk:before {
    content: "\f633";
}

.fi-br-milk-alt:before {
    content: "\f634";
}

.fi-br-microwave:before {
    content: "\f635";
}

.fi-br-microscope:before {
    content: "\f636";
}

.fi-br-microphone:before {
    content: "\f637";
}

.fi-br-microphone-slash:before {
    content: "\f638";
}

.fi-br-microphone-alt:before {
    content: "\f639";
}

.fi-br-microchip:before {
    content: "\f63a";
}

.fi-br-microchip-ai:before {
    content: "\f63b";
}

.fi-br-method:before {
    content: "\f63c";
}

.fi-br-meter:before {
    content: "\f63d";
}

.fi-br-meter-fire:before {
    content: "\f63e";
}

.fi-br-meter-droplet:before {
    content: "\f63f";
}

.fi-br-meter-bolt:before {
    content: "\f640";
}

.fi-br-meteor:before {
    content: "\f641";
}

.fi-br-messages:before {
    content: "\f642";
}

.fi-br-messages-question:before {
    content: "\f643";
}

.fi-br-messages-dollar:before {
    content: "\f644";
}

.fi-br-message-xmark:before {
    content: "\f645";
}

.fi-br-message-text:before {
    content: "\f646";
}

.fi-br-message-sms:before {
    content: "\f647";
}

.fi-br-message-slash:before {
    content: "\f648";
}

.fi-br-message-quote:before {
    content: "\f649";
}

.fi-br-message-question:before {
    content: "\f64a";
}

.fi-br-message-image:before {
    content: "\f64b";
}

.fi-br-message-dollar:before {
    content: "\f64c";
}

.fi-br-message-code:before {
    content: "\f64d";
}

.fi-br-message-bot:before {
    content: "\f64e";
}

.fi-br-message-arrow-up:before {
    content: "\f64f";
}

.fi-br-message-arrow-up-right:before {
    content: "\f650";
}

.fi-br-message-arrow-down:before {
    content: "\f651";
}

.fi-br-mercury:before {
    content: "\f652";
}

.fi-br-menu-dots:before {
    content: "\f653";
}

.fi-br-menu-dots-vertical:before {
    content: "\f654";
}

.fi-br-menu-burger:before {
    content: "\f655";
}

.fi-br-memory:before {
    content: "\f656";
}

.fi-br-memo:before {
    content: "\f657";
}

.fi-br-memo-pad:before {
    content: "\f658";
}

.fi-br-memo-circle-check:before {
    content: "\f659";
}

.fi-br-membership:before {
    content: "\f65a";
}

.fi-br-membership-vip:before {
    content: "\f65b";
}

.fi-br-melon:before {
    content: "\f65c";
}

.fi-br-melon-alt:before {
    content: "\f65d";
}

.fi-br-meh-rolling-eyes:before {
    content: "\f65e";
}

.fi-br-meh-blank:before {
    content: "\f65f";
}

.fi-br-megaphone:before {
    content: "\f660";
}

.fi-br-meeting:before {
    content: "\f661";
}

.fi-br-meeting-alt:before {
    content: "\f662";
}

.fi-br-medicine:before {
    content: "\f663";
}

.fi-br-medical-star:before {
    content: "\f664";
}

.fi-br-medal:before {
    content: "\f665";
}

.fi-br-meat:before {
    content: "\f666";
}

.fi-br-mattress-pillow:before {
    content: "\f667";
}

.fi-br-match-fire:before {
    content: "\f668";
}

.fi-br-massage:before {
    content: "\f669";
}

.fi-br-mask-snorkel:before {
    content: "\f66a";
}

.fi-br-mask-face:before {
    content: "\f66b";
}

.fi-br-mask-carnival:before {
    content: "\f66c";
}

.fi-br-martini-glass-empty:before {
    content: "\f66d";
}

.fi-br-martini-glass-citrus:before {
    content: "\f66e";
}

.fi-br-mars-stroke-up:before {
    content: "\f66f";
}

.fi-br-mars-stroke-right:before {
    content: "\f670";
}

.fi-br-marketplace:before {
    content: "\f671";
}

.fi-br-marketplace-store:before {
    content: "\f672";
}

.fi-br-marketplace-alt:before {
    content: "\f673";
}

.fi-br-marker:before {
    content: "\f674";
}

.fi-br-marker-time:before {
    content: "\f675";
}

.fi-br-map:before {
    content: "\f676";
}

.fi-br-map-pin:before {
    content: "\f677";
}

.fi-br-map-marker:before {
    content: "\f678";
}

.fi-br-map-marker-smile:before {
    content: "\f679";
}

.fi-br-map-marker-slash:before {
    content: "\f67a";
}

.fi-br-map-marker-question:before {
    content: "\f67b";
}

.fi-br-map-marker-plus:before {
    content: "\f67c";
}

.fi-br-map-marker-minus:before {
    content: "\f67d";
}

.fi-br-map-marker-home:before {
    content: "\f67e";
}

.fi-br-map-marker-edit:before {
    content: "\f67f";
}

.fi-br-map-marker-cross:before {
    content: "\f680";
}

.fi-br-map-marker-check:before {
    content: "\f681";
}

.fi-br-manhole:before {
    content: "\f682";
}

.fi-br-mango:before {
    content: "\f683";
}

.fi-br-mandolin:before {
    content: "\f684";
}

.fi-br-manat-sign:before {
    content: "\f685";
}

.fi-br-man-scientist:before {
    content: "\f686";
}

.fi-br-man-head:before {
    content: "\f687";
}

.fi-br-male:before {
    content: "\f688";
}

.fi-br-male-couple:before {
    content: "\f689";
}

.fi-br-makeup-brush:before {
    content: "\f68a";
}

.fi-br-mailbox:before {
    content: "\f68b";
}

.fi-br-magnet:before {
    content: "\f68c";
}

.fi-br-magnet-user:before {
    content: "\f68d";
}

.fi-br-magic-wand:before {
    content: "\f68e";
}

.fi-br-mace:before {
    content: "\f68f";
}

.fi-br-m:before {
    content: "\f690";
}

.fi-br-lungs:before {
    content: "\f691";
}

.fi-br-lungs-virus:before {
    content: "\f692";
}

.fi-br-luggage-rolling:before {
    content: "\f693";
}

.fi-br-luggage-cart:before {
    content: "\f694";
}

.fi-br-luchador:before {
    content: "\f695";
}

.fi-br-low-vision:before {
    content: "\f696";
}

.fi-br-loveseat:before {
    content: "\f697";
}

.fi-br-locust:before {
    content: "\f698";
}

.fi-br-lock:before {
    content: "\f699";
}

.fi-br-lock-open-alt:before {
    content: "\f69a";
}

.fi-br-lock-hashtag:before {
    content: "\f69b";
}

.fi-br-lock-alt:before {
    content: "\f69c";
}

.fi-br-location-exclamation:before {
    content: "\f69d";
}

.fi-br-location-dot-slash:before {
    content: "\f69e";
}

.fi-br-location-crosshairs:before {
    content: "\f69f";
}

.fi-br-location-crosshairs-slash:before {
    content: "\f6a0";
}

.fi-br-location-arrow:before {
    content: "\f6a1";
}

.fi-br-location-alt:before {
    content: "\f6a2";
}

.fi-br-lobster:before {
    content: "\f6a3";
}

.fi-br-loan:before {
    content: "\f6a4";
}

.fi-br-loading:before {
    content: "\f6a5";
}

.fi-br-live:before {
    content: "\f6a6";
}

.fi-br-live-alt:before {
    content: "\f6a7";
}

.fi-br-litecoin-sign:before {
    content: "\f6a8";
}

.fi-br-list:before {
    content: "\f6a9";
}

.fi-br-list-timeline:before {
    content: "\f6aa";
}

.fi-br-list-music:before {
    content: "\f6ab";
}

.fi-br-list-dropdown:before {
    content: "\f6ac";
}

.fi-br-list-check:before {
    content: "\f6ad";
}

.fi-br-lira-sign:before {
    content: "\f6ae";
}

.fi-br-lipstick:before {
    content: "\f6af";
}

.fi-br-lips:before {
    content: "\f6b0";
}

.fi-br-lion:before {
    content: "\f6b1";
}

.fi-br-lion-head:before {
    content: "\f6b2";
}

.fi-br-link:before {
    content: "\f6b3";
}

.fi-br-link-slash:before {
    content: "\f6b4";
}

.fi-br-link-slash-alt:before {
    content: "\f6b5";
}

.fi-br-link-horizontal:before {
    content: "\f6b6";
}

.fi-br-link-horizontal-slash:before {
    content: "\f6b7";
}

.fi-br-link-alt:before {
    content: "\f6b8";
}

.fi-br-line-width:before {
    content: "\f6b9";
}

.fi-br-limit-speedometer:before {
    content: "\f6ba";
}

.fi-br-lights-holiday:before {
    content: "\f6bb";
}

.fi-br-lightbulb-slash:before {
    content: "\f6bc";
}

.fi-br-lightbulb-setting:before {
    content: "\f6bd";
}

.fi-br-lightbulb-question:before {
    content: "\f6be";
}

.fi-br-lightbulb-on:before {
    content: "\f6bf";
}

.fi-br-lightbulb-head:before {
    content: "\f6c0";
}

.fi-br-lightbulb-exclamation:before {
    content: "\f6c1";
}

.fi-br-lightbulb-dollar:before {
    content: "\f6c2";
}

.fi-br-light-switch:before {
    content: "\f6c3";
}

.fi-br-light-switch-on:before {
    content: "\f6c4";
}

.fi-br-light-switch-off:before {
    content: "\f6c5";
}

.fi-br-light-emergency:before {
    content: "\f6c6";
}

.fi-br-light-emergency-on:before {
    content: "\f6c7";
}

.fi-br-light-ceiling:before {
    content: "\f6c8";
}

.fi-br-life:before {
    content: "\f6c9";
}

.fi-br-life-ring:before {
    content: "\f6ca";
}

.fi-br-license:before {
    content: "\f6cb";
}

.fi-br-level-up:before {
    content: "\f6cc";
}

.fi-br-level-up-alt:before {
    content: "\f6cd";
}

.fi-br-level-down:before {
    content: "\f6ce";
}

.fi-br-level-down-alt:before {
    content: "\f6cf";
}

.fi-br-lettuce:before {
    content: "\f6d0";
}

.fi-br-letter-case:before {
    content: "\f6d1";
}

.fi-br-lesson:before {
    content: "\f6d2";
}

.fi-br-lesson-class:before {
    content: "\f6d3";
}

.fi-br-less-than:before {
    content: "\f6d4";
}

.fi-br-less-than-equal:before {
    content: "\f6d5";
}

.fi-br-lemon:before {
    content: "\f6d6";
}

.fi-br-legal:before {
    content: "\f6d7";
}

.fi-br-left:before {
    content: "\f6d8";
}

.fi-br-leave:before {
    content: "\f6d9";
}

.fi-br-leafy-green:before {
    content: "\f6da";
}

.fi-br-leaf:before {
    content: "\f6db";
}

.fi-br-leaf-oak:before {
    content: "\f6dc";
}

.fi-br-leaf-maple:before {
    content: "\f6dd";
}

.fi-br-leaf-heart:before {
    content: "\f6de";
}

.fi-br-leadership:before {
    content: "\f6df";
}

.fi-br-leadership-alt:before {
    content: "\f6e0";
}

.fi-br-leaderboard:before {
    content: "\f6e1";
}

.fi-br-leaderboard-trophy:before {
    content: "\f6e2";
}

.fi-br-leaderboard-alt:before {
    content: "\f6e3";
}

.fi-br-leader:before {
    content: "\f6e4";
}

.fi-br-leader-alt:before {
    content: "\f6e5";
}

.fi-br-layout-fluid:before {
    content: "\f6e6";
}

.fi-br-layers:before {
    content: "\f6e7";
}

.fi-br-layer-plus:before {
    content: "\f6e8";
}

.fi-br-layer-minus:before {
    content: "\f6e9";
}

.fi-br-lawyer-woman:before {
    content: "\f6ea";
}

.fi-br-lawyer-man:before {
    content: "\f6eb";
}

.fi-br-laugh:before {
    content: "\f6ec";
}

.fi-br-laugh-wink:before {
    content: "\f6ed";
}

.fi-br-laugh-squint:before {
    content: "\f6ee";
}

.fi-br-laugh-beam:before {
    content: "\f6ef";
}

.fi-br-lasso:before {
    content: "\f6f0";
}

.fi-br-lasso-sparkles:before {
    content: "\f6f1";
}

.fi-br-lari-sign:before {
    content: "\f6f2";
}

.fi-br-laptop:before {
    content: "\f6f3";
}

.fi-br-laptop-slash:before {
    content: "\f6f4";
}

.fi-br-laptop-mobile:before {
    content: "\f6f5";
}

.fi-br-laptop-medical:before {
    content: "\f6f6";
}

.fi-br-laptop-code:before {
    content: "\f6f7";
}

.fi-br-laptop-arrow-down:before {
    content: "\f6f8";
}

.fi-br-language:before {
    content: "\f6f9";
}

.fi-br-landmark-alt:before {
    content: "\f6fa";
}

.fi-br-land-location:before {
    content: "\f6fb";
}

.fi-br-land-layers:before {
    content: "\f6fc";
}

.fi-br-land-layer-location:before {
    content: "\f6fd";
}

.fi-br-lamp:before {
    content: "\f6fe";
}

.fi-br-lamp-street:before {
    content: "\f6ff";
}

.fi-br-lamp-floor:before {
    content: "\f700";
}

.fi-br-lamp-desk:before {
    content: "\f701";
}

.fi-br-lambda:before {
    content: "\f702";
}

.fi-br-lacrosse-stick:before {
    content: "\f703";
}

.fi-br-lacrosse-stick-ball:before {
    content: "\f704";
}

.fi-br-label:before {
    content: "\f705";
}

.fi-br-l:before {
    content: "\f706";
}

.fi-br-kpi:before {
    content: "\f707";
}

.fi-br-kpi-evaluation:before {
    content: "\f708";
}

.fi-br-knitting:before {
    content: "\f709";
}

.fi-br-knife:before {
    content: "\f70a";
}

.fi-br-knife-kitchen:before {
    content: "\f70b";
}

.fi-br-kiwi-fruit:before {
    content: "\f70c";
}

.fi-br-kiwi-bird:before {
    content: "\f70d";
}

.fi-br-kite:before {
    content: "\f70e";
}

.fi-br-kiss:before {
    content: "\f70f";
}

.fi-br-kiss-wink-heart:before {
    content: "\f710";
}

.fi-br-kiss-beam:before {
    content: "\f711";
}

.fi-br-kip-sign:before {
    content: "\f712";
}

.fi-br-kidneys:before {
    content: "\f713";
}

.fi-br-keynote:before {
    content: "\f714";
}

.fi-br-keyboard:before {
    content: "\f715";
}

.fi-br-keyboard-left:before {
    content: "\f716";
}

.fi-br-keyboard-down:before {
    content: "\f717";
}

.fi-br-keyboard-brightness:before {
    content: "\f718";
}

.fi-br-keyboard-brightness-low:before {
    content: "\f719";
}

.fi-br-key:before {
    content: "\f71a";
}

.fi-br-key-skeleton-left-right:before {
    content: "\f71b";
}

.fi-br-kerning:before {
    content: "\f71c";
}

.fi-br-kazoo:before {
    content: "\f71d";
}

.fi-br-kaaba:before {
    content: "\f71e";
}

.fi-br-k:before {
    content: "\f71f";
}

.fi-br-jug:before {
    content: "\f720";
}

.fi-br-jug-bottle:before {
    content: "\f721";
}

.fi-br-jug-alt:before {
    content: "\f722";
}

.fi-br-jpg:before {
    content: "\f723";
}

.fi-br-joystick:before {
    content: "\f724";
}

.fi-br-journey:before {
    content: "\f725";
}

.fi-br-journal:before {
    content: "\f726";
}

.fi-br-journal-alt:before {
    content: "\f727";
}

.fi-br-joint:before {
    content: "\f728";
}

.fi-br-javascript:before {
    content: "\f729";
}

.fi-br-jar-wheat:before {
    content: "\f72a";
}

.fi-br-jar-alt:before {
    content: "\f72b";
}

.fi-br-jam:before {
    content: "\f72c";
}

.fi-br-j:before {
    content: "\f72d";
}

.fi-br-italic:before {
    content: "\f72e";
}

.fi-br-italian-lira-sign:before {
    content: "\f72f";
}

.fi-br-it:before {
    content: "\f730";
}

.fi-br-it-computer:before {
    content: "\f731";
}

.fi-br-it-alt:before {
    content: "\f732";
}

.fi-br-issue-loupe:before {
    content: "\f733";
}

.fi-br-island-tropical:before {
    content: "\f734";
}

.fi-br-invite:before {
    content: "\f735";
}

.fi-br-invite-alt:before {
    content: "\f736";
}

.fi-br-investment:before {
    content: "\f737";
}

.fi-br-invest:before {
    content: "\f738";
}

.fi-br-inventory-alt:before {
    content: "\f739";
}

.fi-br-introduction:before {
    content: "\f73a";
}

.fi-br-introduction-handshake:before {
    content: "\f73b";
}

.fi-br-intersection:before {
    content: "\f73c";
}

.fi-br-interrogation:before {
    content: "\f73d";
}

.fi-br-interlining:before {
    content: "\f73e";
}

.fi-br-interactive:before {
    content: "\f73f";
}

.fi-br-integral:before {
    content: "\f740";
}

.fi-br-insight:before {
    content: "\f741";
}

.fi-br-insight-head:before {
    content: "\f742";
}

.fi-br-insight-alt:before {
    content: "\f743";
}

.fi-br-insert:before {
    content: "\f744";
}

.fi-br-insert-square:before {
    content: "\f745";
}

.fi-br-insert-arrows:before {
    content: "\f746";
}

.fi-br-insert-alt:before {
    content: "\f747";
}

.fi-br-input-text:before {
    content: "\f748";
}

.fi-br-input-pipe:before {
    content: "\f749";
}

.fi-br-input-numeric:before {
    content: "\f74a";
}

.fi-br-inhaler:before {
    content: "\f74b";
}

.fi-br-information:before {
    content: "\f74c";
}

.fi-br-info:before {
    content: "\f74d";
}

.fi-br-info-guide:before {
    content: "\f74e";
}

.fi-br-infinity:before {
    content: "\f74f";
}

.fi-br-industry-windows:before {
    content: "\f750";
}

.fi-br-industry-alt:before {
    content: "\f751";
}

.fi-br-indian-rupee-sign:before {
    content: "\f752";
}

.fi-br-indent:before {
    content: "\f753";
}

.fi-br-incognito:before {
    content: "\f754";
}

.fi-br-inboxes:before {
    content: "\f755";
}

.fi-br-inbox:before {
    content: "\f756";
}

.fi-br-inbox-out:before {
    content: "\f757";
}

.fi-br-inbox-in:before {
    content: "\f758";
}

.fi-br-images:before {
    content: "\f759";
}

.fi-br-images-user:before {
    content: "\f75a";
}

.fi-br-image-slash:before {
    content: "\f75b";
}

.fi-br-igloo:before {
    content: "\f75c";
}

.fi-br-id-card-clip-alt:before {
    content: "\f75d";
}

.fi-br-id-badge:before {
    content: "\f75e";
}

.fi-br-icon-star:before {
    content: "\f75f";
}

.fi-br-icicles:before {
    content: "\f760";
}

.fi-br-ice-skate:before {
    content: "\f761";
}

.fi-br-ice-cream:before {
    content: "\f762";
}

.fi-br-i:before {
    content: "\f763";
}

.fi-br-hurricane:before {
    content: "\f764";
}

.fi-br-hundred-points:before {
    content: "\f765";
}

.fi-br-humidity:before {
    content: "\f766";
}

.fi-br-hryvnia:before {
    content: "\f767";
}

.fi-br-hr:before {
    content: "\f768";
}

.fi-br-hr-person:before {
    content: "\f769";
}

.fi-br-hr-group:before {
    content: "\f76a";
}

.fi-br-house-window:before {
    content: "\f76b";
}

.fi-br-house-user:before {
    content: "\f76c";
}

.fi-br-house-turret:before {
    content: "\f76d";
}

.fi-br-house-tsunami:before {
    content: "\f76e";
}

.fi-br-house-tree:before {
    content: "\f76f";
}

.fi-br-house-signal:before {
    content: "\f770";
}

.fi-br-house-return:before {
    content: "\f771";
}

.fi-br-house-night:before {
    content: "\f772";
}

.fi-br-house-medical:before {
    content: "\f773";
}

.fi-br-house-leave:before {
    content: "\f774";
}

.fi-br-house-laptop:before {
    content: "\f775";
}

.fi-br-house-flood:before {
    content: "\f776";
}

.fi-br-house-day:before {
    content: "\f777";
}

.fi-br-house-crack:before {
    content: "\f778";
}

.fi-br-house-chimney:before {
    content: "\f779";
}

.fi-br-house-chimney-window:before {
    content: "\f77a";
}

.fi-br-house-chimney-user:before {
    content: "\f77b";
}

.fi-br-house-chimney-medical:before {
    content: "\f77c";
}

.fi-br-house-chimney-heart:before {
    content: "\f77d";
}

.fi-br-house-chimney-crack:before {
    content: "\f77e";
}

.fi-br-house-chimney-blank:before {
    content: "\f77f";
}

.fi-br-house-building:before {
    content: "\f780";
}

.fi-br-house-blank:before {
    content: "\f781";
}

.fi-br-hourglass:before {
    content: "\f782";
}

.fi-br-hourglass-start:before {
    content: "\f783";
}

.fi-br-hourglass-end:before {
    content: "\f784";
}

.fi-br-hotel:before {
    content: "\f785";
}

.fi-br-hotdog:before {
    content: "\f786";
}

.fi-br-hot-tub:before {
    content: "\f787";
}

.fi-br-hospitals:before {
    content: "\f788";
}

.fi-br-hospital:before {
    content: "\f789";
}

.fi-br-hospital-user:before {
    content: "\f78a";
}

.fi-br-hospital-symbol:before {
    content: "\f78b";
}

.fi-br-hose:before {
    content: "\f78c";
}

.fi-br-hose-reel:before {
    content: "\f78d";
}

.fi-br-horse:before {
    content: "\f78e";
}

.fi-br-horse-saddle:before {
    content: "\f78f";
}

.fi-br-horse-head:before {
    content: "\f790";
}

.fi-br-horizontal-rule:before {
    content: "\f791";
}

.fi-br-hood-cloak:before {
    content: "\f792";
}

.fi-br-honey-pot:before {
    content: "\f793";
}

.fi-br-home:before {
    content: "\f794";
}

.fi-br-home-location:before {
    content: "\f795";
}

.fi-br-home-location-alt:before {
    content: "\f796";
}

.fi-br-home-heart:before {
    content: "\f797";
}

.fi-br-holly-berry:before {
    content: "\f798";
}

.fi-br-hockey-sticks:before {
    content: "\f799";
}

.fi-br-hockey-stick-puck:before {
    content: "\f79a";
}

.fi-br-hockey-puck:before {
    content: "\f79b";
}

.fi-br-hockey-mask:before {
    content: "\f79c";
}

.fi-br-hippo:before {
    content: "\f79d";
}

.fi-br-hiking:before {
    content: "\f79e";
}

.fi-br-highlighter:before {
    content: "\f79f";
}

.fi-br-highlighter-line:before {
    content: "\f7a0";
}

.fi-br-high-definition:before {
    content: "\f7a1";
}

.fi-br-hexagon:before {
    content: "\f7a2";
}

.fi-br-hexagon-exclamation:before {
    content: "\f7a3";
}

.fi-br-hexagon-divide:before {
    content: "\f7a4";
}

.fi-br-hexagon-check:before {
    content: "\f7a5";
}

.fi-br-helmet-battle:before {
    content: "\f7a6";
}

.fi-br-helicopter-side:before {
    content: "\f7a7";
}

.fi-br-heat:before {
    content: "\f7a8";
}

.fi-br-heart:before {
    content: "\f7a9";
}

.fi-br-heart-rate:before {
    content: "\f7aa";
}

.fi-br-heart-half:before {
    content: "\f7ab";
}

.fi-br-heart-half-stroke:before {
    content: "\f7ac";
}

.fi-br-heart-crack:before {
    content: "\f7ad";
}

.fi-br-heart-arrow:before {
    content: "\f7ae";
}

.fi-br-headset:before {
    content: "\f7af";
}

.fi-br-headphones:before {
    content: "\f7b0";
}

.fi-br-heading:before {
    content: "\f7b1";
}

.fi-br-head-vr:before {
    content: "\f7b2";
}

.fi-br-head-side:before {
    content: "\f7b3";
}

.fi-br-head-side-virus:before {
    content: "\f7b4";
}

.fi-br-head-side-thinking:before {
    content: "\f7b5";
}

.fi-br-head-side-medical:before {
    content: "\f7b6";
}

.fi-br-head-side-mask:before {
    content: "\f7b7";
}

.fi-br-head-side-heart:before {
    content: "\f7b8";
}

.fi-br-head-side-headphones:before {
    content: "\f7b9";
}

.fi-br-head-side-cough:before {
    content: "\f7ba";
}

.fi-br-head-side-cough-slash:before {
    content: "\f7bb";
}

.fi-br-head-side-brain:before {
    content: "\f7bc";
}

.fi-br-hdd:before {
    content: "\f7bd";
}

.fi-br-hat-wizard:before {
    content: "\f7be";
}

.fi-br-hat-witch:before {
    content: "\f7bf";
}

.fi-br-hat-winter:before {
    content: "\f7c0";
}

.fi-br-hat-santa:before {
    content: "\f7c1";
}

.fi-br-hat-cowboy:before {
    content: "\f7c2";
}

.fi-br-hat-cowboy-side:before {
    content: "\f7c3";
}

.fi-br-hat-chef:before {
    content: "\f7c4";
}

.fi-br-hat-birthday:before {
    content: "\f7c5";
}

.fi-br-hastag:before {
    content: "\f7c6";
}

.fi-br-hashtag-lock:before {
    content: "\f7c7";
}

.fi-br-hard-hat:before {
    content: "\f7c8";
}

.fi-br-happy:before {
    content: "\f7c9";
}

.fi-br-handshake:before {
    content: "\f7ca";
}

.fi-br-handshake-simple-slash:before {
    content: "\f7cb";
}

.fi-br-handshake-angle:before {
    content: "\f7cc";
}

.fi-br-hands-usd:before {
    content: "\f7cd";
}

.fi-br-hands-holding:before {
    content: "\f7ce";
}

.fi-br-hands-holding-diamond:before {
    content: "\f7cf";
}

.fi-br-hands-heart:before {
    content: "\f7d0";
}

.fi-br-hands-clapping:before {
    content: "\f7d1";
}

.fi-br-hands-bubbles:before {
    content: "\f7d2";
}

.fi-br-handmade:before {
    content: "\f7d3";
}

.fi-br-hand:before {
    content: "\f7d4";
}

.fi-br-hand-wave:before {
    content: "\f7d5";
}

.fi-br-hand-spock:before {
    content: "\f7d6";
}

.fi-br-hand-sparkles:before {
    content: "\f7d7";
}

.fi-br-hand-scissors:before {
    content: "\f7d8";
}

.fi-br-hand-point-ribbon:before {
    content: "\f7d9";
}

.fi-br-hand-peace:before {
    content: "\f7da";
}

.fi-br-hand-paper:before {
    content: "\f7db";
}

.fi-br-hand-middle-finger:before {
    content: "\f7dc";
}

.fi-br-hand-love:before {
    content: "\f7dd";
}

.fi-br-hand-lizard:before {
    content: "\f7de";
}

.fi-br-hand-horns:before {
    content: "\f7df";
}

.fi-br-hand-holding-water:before {
    content: "\f7e0";
}

.fi-br-hand-holding-usd:before {
    content: "\f7e1";
}

.fi-br-hand-holding-skull:before {
    content: "\f7e2";
}

.fi-br-hand-holding-seeding:before {
    content: "\f7e3";
}

.fi-br-hand-holding-medical:before {
    content: "\f7e4";
}

.fi-br-hand-holding-magic:before {
    content: "\f7e5";
}

.fi-br-hand-holding-droplet:before {
    content: "\f7e6";
}

.fi-br-hand-holding-box:before {
    content: "\f7e7";
}

.fi-br-hand-heart:before {
    content: "\f7e8";
}

.fi-br-hand-fist:before {
    content: "\f7e9";
}

.fi-br-hand-fingers-crossed:before {
    content: "\f7ea";
}

.fi-br-hand-dots:before {
    content: "\f7eb";
}

.fi-br-hand-bill:before {
    content: "\f7ec";
}

.fi-br-hand-back-point-right:before {
    content: "\f7ed";
}

.fi-br-hand-back-point-ribbon:before {
    content: "\f7ee";
}

.fi-br-hand-back-point-left:before {
    content: "\f7ef";
}

.fi-br-hand-back-point-down:before {
    content: "\f7f0";
}

.fi-br-hand-back-fist:before {
    content: "\f7f1";
}

.fi-br-hamsa:before {
    content: "\f7f2";
}

.fi-br-hammer:before {
    content: "\f7f3";
}

.fi-br-hammer-war:before {
    content: "\f7f4";
}

.fi-br-hammer-crash:before {
    content: "\f7f5";
}

.fi-br-hamburger:before {
    content: "\f7f6";
}

.fi-br-hamburger-soda:before {
    content: "\f7f7";
}

.fi-br-hair-clipper:before {
    content: "\f7f8";
}

.fi-br-h4:before {
    content: "\f7f9";
}

.fi-br-h3:before {
    content: "\f7fa";
}

.fi-br-h2:before {
    content: "\f7fb";
}

.fi-br-h1:before {
    content: "\f7fc";
}

.fi-br-h:before {
    content: "\f7fd";
}

.fi-br-h-square:before {
    content: "\f7fe";
}

.fi-br-gym:before {
    content: "\f7ff";
}

.fi-br-gun-squirt:before {
    content: "\f800";
}

.fi-br-guitars:before {
    content: "\f801";
}

.fi-br-guitar:before {
    content: "\f802";
}

.fi-br-guitar-electric:before {
    content: "\f803";
}

.fi-br-guide:before {
    content: "\f804";
}

.fi-br-guide-alt:before {
    content: "\f805";
}

.fi-br-guarani-sign:before {
    content: "\f806";
}

.fi-br-grocery-basket:before {
    content: "\f807";
}

.fi-br-grocery-bag:before {
    content: "\f808";
}

.fi-br-grip-vertical:before {
    content: "\f809";
}

.fi-br-grip-lines:before {
    content: "\f80a";
}

.fi-br-grip-lines-vertical:before {
    content: "\f80b";
}

.fi-br-grip-horizontal:before {
    content: "\f80c";
}

.fi-br-grip-dots:before {
    content: "\f80d";
}

.fi-br-grip-dots-vertical:before {
    content: "\f80e";
}

.fi-br-grin:before {
    content: "\f80f";
}

.fi-br-grin-wink:before {
    content: "\f810";
}

.fi-br-grin-tongue:before {
    content: "\f811";
}

.fi-br-grin-tongue-wink:before {
    content: "\f812";
}

.fi-br-grin-tongue-squint:before {
    content: "\f813";
}

.fi-br-grin-tears:before {
    content: "\f814";
}

.fi-br-grin-stars:before {
    content: "\f815";
}

.fi-br-grin-squint:before {
    content: "\f816";
}

.fi-br-grin-squint-tears:before {
    content: "\f817";
}

.fi-br-grin-hearts:before {
    content: "\f818";
}

.fi-br-grin-beam:before {
    content: "\f819";
}

.fi-br-grin-beam-sweat:before {
    content: "\f81a";
}

.fi-br-grin-alt:before {
    content: "\f81b";
}

.fi-br-grimace:before {
    content: "\f81c";
}

.fi-br-grill:before {
    content: "\f81d";
}

.fi-br-grill-hot-alt:before {
    content: "\f81e";
}

.fi-br-grid:before {
    content: "\f81f";
}

.fi-br-grid-dividers:before {
    content: "\f820";
}

.fi-br-grid-alt:before {
    content: "\f821";
}

.fi-br-greater-than:before {
    content: "\f822";
}

.fi-br-greater-than-equal:before {
    content: "\f823";
}

.fi-br-grate:before {
    content: "\f824";
}

.fi-br-grate-droplet:before {
    content: "\f825";
}

.fi-br-graphic-tablet:before {
    content: "\f826";
}

.fi-br-graphic-style:before {
    content: "\f827";
}

.fi-br-graph-curve:before {
    content: "\f828";
}

.fi-br-grape:before {
    content: "\f829";
}

.fi-br-gramophone:before {
    content: "\f82a";
}

.fi-br-graduation-cap:before {
    content: "\f82b";
}

.fi-br-government-user:before {
    content: "\f82c";
}

.fi-br-government-flag:before {
    content: "\f82d";
}

.fi-br-gopuram:before {
    content: "\f82e";
}

.fi-br-golf:before {
    content: "\f82f";
}

.fi-br-golf-club:before {
    content: "\f830";
}

.fi-br-golf-ball:before {
    content: "\f831";
}

.fi-br-goal-net:before {
    content: "\f832";
}

.fi-br-globe:before {
    content: "\f833";
}

.fi-br-globe-snow:before {
    content: "\f834";
}

.fi-br-globe-alt:before {
    content: "\f835";
}

.fi-br-glasses:before {
    content: "\f836";
}

.fi-br-glass:before {
    content: "\f837";
}

.fi-br-glass-whiskey:before {
    content: "\f838";
}

.fi-br-glass-whiskey-rocks:before {
    content: "\f839";
}

.fi-br-glass-water-droplet:before {
    content: "\f83a";
}

.fi-br-glass-half:before {
    content: "\f83b";
}

.fi-br-glass-empty:before {
    content: "\f83c";
}

.fi-br-glass-citrus:before {
    content: "\f83d";
}

.fi-br-glass-cheers:before {
    content: "\f83e";
}

.fi-br-glass-champagne:before {
    content: "\f83f";
}

.fi-br-gingerbread-man:before {
    content: "\f840";
}

.fi-br-gifts:before {
    content: "\f841";
}

.fi-br-gift:before {
    content: "\f842";
}

.fi-br-gift-card:before {
    content: "\f843";
}

.fi-br-gif:before {
    content: "\f844";
}

.fi-br-gif-square:before {
    content: "\f845";
}

.fi-br-ghost:before {
    content: "\f846";
}

.fi-br-gem:before {
    content: "\f847";
}

.fi-br-gears:before {
    content: "\f848";
}

.fi-br-gavel:before {
    content: "\f849";
}

.fi-br-gauge-circle-plus:before {
    content: "\f84a";
}

.fi-br-gauge-circle-minus:before {
    content: "\f84b";
}

.fi-br-gauge-circle-bolt:before {
    content: "\f84c";
}

.fi-br-gas-pump:before {
    content: "\f84d";
}

.fi-br-gas-pump-slash:before {
    content: "\f84e";
}

.fi-br-gas-pump-alt:before {
    content: "\f84f";
}

.fi-br-garlic:before {
    content: "\f850";
}

.fi-br-garlic-alt:before {
    content: "\f851";
}

.fi-br-garage:before {
    content: "\f852";
}

.fi-br-garage-open:before {
    content: "\f853";
}

.fi-br-garage-car:before {
    content: "\f854";
}

.fi-br-gamepad:before {
    content: "\f855";
}

.fi-br-game-board-alt:before {
    content: "\f856";
}

.fi-br-gallery:before {
    content: "\f857";
}

.fi-br-gallery-thumbnails:before {
    content: "\f858";
}

.fi-br-galaxy:before {
    content: "\f859";
}

.fi-br-galaxy-star:before {
    content: "\f85a";
}

.fi-br-galaxy-planet:before {
    content: "\f85b";
}

.fi-br-galaxy-alt:before {
    content: "\f85c";
}

.fi-br-g:before {
    content: "\f85d";
}

.fi-br-funnel-dollar:before {
    content: "\f85e";
}

.fi-br-function:before {
    content: "\f85f";
}

.fi-br-function-square:before {
    content: "\f860";
}

.fi-br-function-process:before {
    content: "\f861";
}

.fi-br-ftp:before {
    content: "\f862";
}

.fi-br-frog:before {
    content: "\f863";
}

.fi-br-friday:before {
    content: "\f864";
}

.fi-br-french-fries:before {
    content: "\f865";
}

.fi-br-franc-sign:before {
    content: "\f866";
}

.fi-br-frame:before {
    content: "\f867";
}

.fi-br-fox:before {
    content: "\f868";
}

.fi-br-forward:before {
    content: "\f869";
}

.fi-br-forward-fast:before {
    content: "\f86a";
}

.fi-br-fort:before {
    content: "\f86b";
}

.fi-br-form:before {
    content: "\f86c";
}

.fi-br-forklift:before {
    content: "\f86d";
}

.fi-br-fork:before {
    content: "\f86e";
}

.fi-br-football:before {
    content: "\f86f";
}

.fi-br-fondue-pot:before {
    content: "\f870";
}

.fi-br-following:before {
    content: "\f871";
}

.fi-br-followcollection:before {
    content: "\f872";
}

.fi-br-follow-folder:before {
    content: "\f873";
}

.fi-br-folders:before {
    content: "\f874";
}

.fi-br-folder:before {
    content: "\f875";
}

.fi-br-folder-xmark:before {
    content: "\f876";
}

.fi-br-folder-upload:before {
    content: "\f877";
}

.fi-br-folder-tree:before {
    content: "\f878";
}

.fi-br-folder-times:before {
    content: "\f879";
}

.fi-br-folder-open:before {
    content: "\f87a";
}

.fi-br-folder-minus:before {
    content: "\f87b";
}

.fi-br-folder-download:before {
    content: "\f87c";
}

.fi-br-fog:before {
    content: "\f87d";
}

.fi-br-flying-disc:before {
    content: "\f87e";
}

.fi-br-fly-insect:before {
    content: "\f87f";
}

.fi-br-flux-capacitor:before {
    content: "\f880";
}

.fi-br-flute:before {
    content: "\f881";
}

.fi-br-flushed:before {
    content: "\f882";
}

.fi-br-flushed-face:before {
    content: "\f883";
}

.fi-br-flower:before {
    content: "\f884";
}

.fi-br-flower-tulip:before {
    content: "\f885";
}

.fi-br-flower-daffodil:before {
    content: "\f886";
}

.fi-br-flower-butterfly:before {
    content: "\f887";
}

.fi-br-flower-bouquet:before {
    content: "\f888";
}

.fi-br-florin-sign:before {
    content: "\f889";
}

.fi-br-floppy-disks:before {
    content: "\f88a";
}

.fi-br-floppy-disk-pen:before {
    content: "\f88b";
}

.fi-br-floppy-disk-circle-xmark:before {
    content: "\f88c";
}

.fi-br-floppy-disk-circle-arrow-right:before {
    content: "\f88d";
}

.fi-br-floor:before {
    content: "\f88e";
}

.fi-br-floor-layer:before {
    content: "\f88f";
}

.fi-br-floor-alt:before {
    content: "\f890";
}

.fi-br-flip-horizontal:before {
    content: "\f891";
}

.fi-br-flatbread:before {
    content: "\f892";
}

.fi-br-flatbread-stuffed:before {
    content: "\f893";
}

.fi-br-flask:before {
    content: "\f894";
}

.fi-br-flask-potion:before {
    content: "\f895";
}

.fi-br-flask-poison:before {
    content: "\f896";
}

.fi-br-flashlight:before {
    content: "\f897";
}

.fi-br-flame:before {
    content: "\f898";
}

.fi-br-flag:before {
    content: "\f899";
}

.fi-br-flag-usa:before {
    content: "\f89a";
}

.fi-br-flag-checkered:before {
    content: "\f89b";
}

.fi-br-flag-alt:before {
    content: "\f89c";
}

.fi-br-fishing-rod:before {
    content: "\f89d";
}

.fi-br-fish:before {
    content: "\f89e";
}

.fi-br-fish-cooked:before {
    content: "\f89f";
}

.fi-br-fish-bones:before {
    content: "\f8a0";
}

.fi-br-first:before {
    content: "\f8a1";
}

.fi-br-first-medal:before {
    content: "\f8a2";
}

.fi-br-first-laurel:before {
    content: "\f8a3";
}

.fi-br-first-award:before {
    content: "\f8a4";
}

.fi-br-fireplace:before {
    content: "\f8a5";
}

.fi-br-fire-smoke:before {
    content: "\f8a6";
}

.fi-br-fire-hydrant:before {
    content: "\f8a7";
}

.fi-br-fire-flame-simple:before {
    content: "\f8a8";
}

.fi-br-fire-flame-curved:before {
    content: "\f8a9";
}

.fi-br-fire-extinguisher:before {
    content: "\f8aa";
}

.fi-br-fire-burner:before {
    content: "\f8ab";
}

.fi-br-fingerprint:before {
    content: "\f8ac";
}

.fi-br-filters:before {
    content: "\f8ad";
}

.fi-br-filter:before {
    content: "\f8ae";
}

.fi-br-filter-slash:before {
    content: "\f8af";
}

.fi-br-films:before {
    content: "\f8b0";
}

.fi-br-film:before {
    content: "\f8b1";
}

.fi-br-film-slash:before {
    content: "\f8b2";
}

.fi-br-film-canister:before {
    content: "\f8b3";
}

.fi-br-fill:before {
    content: "\f8b4";
}

.fi-br-files-medical:before {
    content: "\f8b5";
}

.fi-br-file:before {
    content: "\f8b6";
}

.fi-br-file-zipper:before {
    content: "\f8b7";
}

.fi-br-file-word:before {
    content: "\f8b8";
}

.fi-br-file-video:before {
    content: "\f8b9";
}

.fi-br-file-user:before {
    content: "\f8ba";
}

.fi-br-file-upload:before {
    content: "\f8bb";
}

.fi-br-file-spreadsheet:before {
    content: "\f8bc";
}

.fi-br-file-signature:before {
    content: "\f8bd";
}

.fi-br-file-psd:before {
    content: "\f8be";
}

.fi-br-file-prescription:before {
    content: "\f8bf";
}

.fi-br-file-powerpoint:before {
    content: "\f8c0";
}

.fi-br-file-pdf:before {
    content: "\f8c1";
}

.fi-br-file-minus:before {
    content: "\f8c2";
}

.fi-br-file-medical:before {
    content: "\f8c3";
}

.fi-br-file-medical-alt:before {
    content: "\f8c4";
}

.fi-br-file-invoice:before {
    content: "\f8c5";
}

.fi-br-file-invoice-dollar:before {
    content: "\f8c6";
}

.fi-br-file-import:before {
    content: "\f8c7";
}

.fi-br-file-image:before {
    content: "\f8c8";
}

.fi-br-file-export:before {
    content: "\f8c9";
}

.fi-br-file-exclamation:before {
    content: "\f8ca";
}

.fi-br-file-excel:before {
    content: "\f8cb";
}

.fi-br-file-eps:before {
    content: "\f8cc";
}

.fi-br-file-edit:before {
    content: "\f8cd";
}

.fi-br-file-download:before {
    content: "\f8ce";
}

.fi-br-file-csv:before {
    content: "\f8cf";
}

.fi-br-file-code:before {
    content: "\f8d0";
}

.fi-br-file-circle-info:before {
    content: "\f8d1";
}

.fi-br-file-chart-pie:before {
    content: "\f8d2";
}

.fi-br-file-chart-line:before {
    content: "\f8d3";
}

.fi-br-file-binary:before {
    content: "\f8d4";
}

.fi-br-file-audio:before {
    content: "\f8d5";
}

.fi-br-file-ai:before {
    content: "\f8d6";
}

.fi-br-fighter-jet:before {
    content: "\f8d7";
}

.fi-br-field-hockey:before {
    content: "\f8d8";
}

.fi-br-ferris-wheel:before {
    content: "\f8d9";
}

.fi-br-fence:before {
    content: "\f8da";
}

.fi-br-female:before {
    content: "\f8db";
}

.fi-br-female-couple:before {
    content: "\f8dc";
}

.fi-br-feedback:before {
    content: "\f8dd";
}

.fi-br-feedback-review:before {
    content: "\f8de";
}

.fi-br-feedback-hand:before {
    content: "\f8df";
}

.fi-br-feedback-alt:before {
    content: "\f8e0";
}

.fi-br-fee:before {
    content: "\f8e1";
}

.fi-br-fee-receipt:before {
    content: "\f8e2";
}

.fi-br-features:before {
    content: "\f8e3";
}

.fi-br-features-alt:before {
    content: "\f8e4";
}

.fi-br-feather:before {
    content: "\f8e5";
}

.fi-br-feather-pointed:before {
    content: "\f8e6";
}

.fi-br-fax:before {
    content: "\f8e7";
}

.fi-br-faucet:before {
    content: "\f8e8";
}

.fi-br-faucet-drip:before {
    content: "\f8e9";
}

.fi-br-farm:before {
    content: "\f8ea";
}

.fi-br-fan:before {
    content: "\f8eb";
}

.fi-br-fan-table:before {
    content: "\f8ec";
}

.fi-br-family:before {
    content: "\f8ed";
}

.fi-br-family-pants:before {
    content: "\f8ee";
}

.fi-br-family-dress:before {
    content: "\f8ef";
}

.fi-br-falafel:before {
    content: "\f8f0";
}

.fi-br-fail:before {
    content: "\f8f1";
}

.fi-br-face-zipper:before {
    content: "\f8f2";
}

.fi-br-face-zany:before {
    content: "\f8f3";
}

.fi-br-face-worried:before {
    content: "\f8f4";
}

.fi-br-face-woozy:before {
    content: "\f8f5";
}

.fi-br-face-weary:before {
    content: "\f8f6";
}

.fi-br-face-vomit:before {
    content: "\f8f7";
}

.fi-br-face-viewfinder:before {
    content: "\f8f8";
}

.fi-br-face-unamused:before {
    content: "\f8f9";
}

.fi-br-face-tongue-sweat:before {
    content: "\f8fa";
}

.fi-br-face-tongue-money:before {
    content: "\f8fb";
}

.fi-br-face-tissue:before {
    content: "\f8fc";
}

.fi-br-face-thinking:before {
    content: "\f8fd";
}

.fi-br-face-thermometer:before {
    content: "\f8fe";
}

.fi-br-face-swear:before {
    content: "\f8ff";
}

.fi-br-face-sunglasses:before {
    content: "\f900";
}

.fi-br-face-sunglasses-alt:before {
    content: "\f901";
}

.fi-br-face-smirking:before {
    content: "\f902";
}

.fi-br-face-smiling-hands:before {
    content: "\f903";
}

.fi-br-face-smile-upside-down:before {
    content: "\f904";
}

.fi-br-face-smile-tongue:before {
    content: "\f905";
}

.fi-br-face-smile-tear:before {
    content: "\f906";
}

.fi-br-face-smile-horns:before {
    content: "\f907";
}

.fi-br-face-smile-hearts:before {
    content: "\f908";
}

.fi-br-face-smile-halo:before {
    content: "\f909";
}

.fi-br-face-sleepy:before {
    content: "\f90a";
}

.fi-br-face-sleeping:before {
    content: "\f90b";
}

.fi-br-face-shush:before {
    content: "\f90c";
}

.fi-br-face-scream:before {
    content: "\f90d";
}

.fi-br-face-sad-sweat:before {
    content: "\f90e";
}

.fi-br-face-relieved:before {
    content: "\f90f";
}

.fi-br-face-raised-eyebrow:before {
    content: "\f910";
}

.fi-br-face-pleading:before {
    content: "\f911";
}

.fi-br-face-persevering:before {
    content: "\f912";
}

.fi-br-face-pensive:before {
    content: "\f913";
}

.fi-br-face-party:before {
    content: "\f914";
}

.fi-br-face-nose-steam:before {
    content: "\f915";
}

.fi-br-face-nauseated:before {
    content: "\f916";
}

.fi-br-face-monocle:before {
    content: "\f917";
}

.fi-br-face-mask:before {
    content: "\f918";
}

.fi-br-face-lying:before {
    content: "\f919";
}

.fi-br-face-icicles:before {
    content: "\f91a";
}

.fi-br-face-hushed:before {
    content: "\f91b";
}

.fi-br-face-head-bandage:before {
    content: "\f91c";
}

.fi-br-face-hand-yawn:before {
    content: "\f91d";
}

.fi-br-face-grin-tongue-wink:before {
    content: "\f91e";
}

.fi-br-face-glasses:before {
    content: "\f91f";
}

.fi-br-face-fearful:before {
    content: "\f920";
}

.fi-br-face-eyes-xmarks:before {
    content: "\f921";
}

.fi-br-face-expressionless:before {
    content: "\f922";
}

.fi-br-face-explode:before {
    content: "\f923";
}

.fi-br-face-drooling:before {
    content: "\f924";
}

.fi-br-face-downcast-sweat:before {
    content: "\f925";
}

.fi-br-face-disguise:before {
    content: "\f926";
}

.fi-br-face-disappointed:before {
    content: "\f927";
}

.fi-br-face-cowboy-hat:before {
    content: "\f928";
}

.fi-br-face-confused:before {
    content: "\f929";
}

.fi-br-face-confounded:before {
    content: "\f92a";
}

.fi-br-face-beam-hand-over-mouth:before {
    content: "\f92b";
}

.fi-br-face-awesome:before {
    content: "\f92c";
}

.fi-br-face-astonished:before {
    content: "\f92d";
}

.fi-br-face-anxious-sweat:before {
    content: "\f92e";
}

.fi-br-face-anguished:before {
    content: "\f92f";
}

.fi-br-face-angry-horns:before {
    content: "\f930";
}

.fi-br-fabric:before {
    content: "\f931";
}

.fi-br-f:before {
    content: "\f932";
}

.fi-br-eyes:before {
    content: "\f933";
}

.fi-br-eye:before {
    content: "\f934";
}

.fi-br-eye-dropper:before {
    content: "\f935";
}

.fi-br-eye-dropper-half:before {
    content: "\f936";
}

.fi-br-eye-crossed:before {
    content: "\f937";
}

.fi-br-external-world:before {
    content: "\f938";
}

.fi-br-external-hard-drive:before {
    content: "\f939";
}

.fi-br-expense:before {
    content: "\f93a";
}

.fi-br-expense-bill:before {
    content: "\f93b";
}

.fi-br-expand:before {
    content: "\f93c";
}

.fi-br-expand-arrows:before {
    content: "\f93d";
}

.fi-br-expand-arrows-alt:before {
    content: "\f93e";
}

.fi-br-exit:before {
    content: "\f93f";
}

.fi-br-exit-alt:before {
    content: "\f940";
}

.fi-br-exclamation:before {
    content: "\f941";
}

.fi-br-exchange:before {
    content: "\f942";
}

.fi-br-exchange-cryptocurrency:before {
    content: "\f943";
}

.fi-br-exchange-alt:before {
    content: "\f944";
}

.fi-br-euro:before {
    content: "\f945";
}

.fi-br-ethernet:before {
    content: "\f946";
}

.fi-br-escalator:before {
    content: "\f947";
}

.fi-br-eraser:before {
    content: "\f948";
}

.fi-br-equals:before {
    content: "\f949";
}

.fi-br-equality:before {
    content: "\f94a";
}

.fi-br-envelopes:before {
    content: "\f94b";
}

.fi-br-envelope:before {
    content: "\f94c";
}

.fi-br-envelope-plus:before {
    content: "\f94d";
}

.fi-br-envelope-open:before {
    content: "\f94e";
}

.fi-br-envelope-open-text:before {
    content: "\f94f";
}

.fi-br-envelope-open-dollar:before {
    content: "\f950";
}

.fi-br-envelope-marker:before {
    content: "\f951";
}

.fi-br-envelope-download:before {
    content: "\f952";
}

.fi-br-envelope-dot:before {
    content: "\f953";
}

.fi-br-envelope-bulk:before {
    content: "\f954";
}

.fi-br-envelope-ban:before {
    content: "\f955";
}

.fi-br-enter:before {
    content: "\f956";
}

.fi-br-engine:before {
    content: "\f957";
}

.fi-br-engine-warning:before {
    content: "\f958";
}

.fi-br-empty-set:before {
    content: "\f959";
}

.fi-br-employees:before {
    content: "\f95a";
}

.fi-br-employees-woman-man:before {
    content: "\f95b";
}

.fi-br-employee-man:before {
    content: "\f95c";
}

.fi-br-employee-man-alt:before {
    content: "\f95d";
}

.fi-br-employee-alt:before {
    content: "\f95e";
}

.fi-br-elevator:before {
    content: "\f95f";
}

.fi-br-elephant:before {
    content: "\f960";
}

.fi-br-eject:before {
    content: "\f961";
}

.fi-br-egg:before {
    content: "\f962";
}

.fi-br-egg-fried:before {
    content: "\f963";
}

.fi-br-effect:before {
    content: "\f964";
}

.fi-br-edit:before {
    content: "\f965";
}

.fi-br-edit-alt:before {
    content: "\f966";
}

.fi-br-eclipse:before {
    content: "\f967";
}

.fi-br-eclipse-alt:before {
    content: "\f968";
}

.fi-br-earth-europa:before {
    content: "\f969";
}

.fi-br-earth-asia:before {
    content: "\f96a";
}

.fi-br-earth-americas:before {
    content: "\f96b";
}

.fi-br-earth-africa:before {
    content: "\f96c";
}

.fi-br-earnings:before {
    content: "\f96d";
}

.fi-br-ear:before {
    content: "\f96e";
}

.fi-br-ear-muffs:before {
    content: "\f96f";
}

.fi-br-ear-deaf:before {
    content: "\f970";
}

.fi-br-e:before {
    content: "\f971";
}

.fi-br-e-learning:before {
    content: "\f972";
}

.fi-br-duration:before {
    content: "\f973";
}

.fi-br-duration-alt:before {
    content: "\f974";
}

.fi-br-duplicate:before {
    content: "\f975";
}

.fi-br-dungeon:before {
    content: "\f976";
}

.fi-br-dumpster:before {
    content: "\f977";
}

.fi-br-dumpster-fire:before {
    content: "\f978";
}

.fi-br-duck:before {
    content: "\f979";
}

.fi-br-dryer:before {
    content: "\f97a";
}

.fi-br-dryer-alt:before {
    content: "\f97b";
}

.fi-br-drumstick:before {
    content: "\f97c";
}

.fi-br-drumstick-bite:before {
    content: "\f97d";
}

.fi-br-drum:before {
    content: "\f97e";
}

.fi-br-drum-steelpan:before {
    content: "\f97f";
}

.fi-br-drop-down:before {
    content: "\f980";
}

.fi-br-drone:before {
    content: "\f981";
}

.fi-br-drone-front:before {
    content: "\f982";
}

.fi-br-drone-alt:before {
    content: "\f983";
}

.fi-br-driver-woman:before {
    content: "\f984";
}

.fi-br-driver-man:before {
    content: "\f985";
}

.fi-br-drink-alt:before {
    content: "\f986";
}

.fi-br-dreidel:before {
    content: "\f987";
}

.fi-br-drawer:before {
    content: "\f988";
}

.fi-br-drawer-alt:before {
    content: "\f989";
}

.fi-br-draw-square:before {
    content: "\f98a";
}

.fi-br-draw-polygon:before {
    content: "\f98b";
}

.fi-br-dragon:before {
    content: "\f98c";
}

.fi-br-drafting-compass:before {
    content: "\f98d";
}

.fi-br-download:before {
    content: "\f98e";
}

.fi-br-down:before {
    content: "\f98f";
}

.fi-br-down-to-line:before {
    content: "\f990";
}

.fi-br-down-right:before {
    content: "\f991";
}

.fi-br-down-left:before {
    content: "\f992";
}

.fi-br-down-left-and-up-right-to-center:before {
    content: "\f993";
}

.fi-br-dove:before {
    content: "\f994";
}

.fi-br-dot-pending:before {
    content: "\f995";
}

.fi-br-dot-circle:before {
    content: "\f996";
}

.fi-br-door-open:before {
    content: "\f997";
}

.fi-br-door-closed:before {
    content: "\f998";
}

.fi-br-donut:before {
    content: "\f999";
}

.fi-br-dong-sign:before {
    content: "\f99a";
}

.fi-br-donate:before {
    content: "\f99b";
}

.fi-br-domino-effect:before {
    content: "\f99c";
}

.fi-br-dolphin:before {
    content: "\f99d";
}

.fi-br-dolly-flatbed:before {
    content: "\f99e";
}

.fi-br-dolly-flatbed-empty:before {
    content: "\f99f";
}

.fi-br-dolly-flatbed-alt:before {
    content: "\f9a0";
}

.fi-br-dollar:before {
    content: "\f9a1";
}

.fi-br-dog:before {
    content: "\f9a2";
}

.fi-br-dog-leashed:before {
    content: "\f9a3";
}

.fi-br-document:before {
    content: "\f9a4";
}

.fi-br-document-signed:before {
    content: "\f9a5";
}

.fi-br-document-paid:before {
    content: "\f9a6";
}

.fi-br-doctor:before {
    content: "\f9a7";
}

.fi-br-do-not-enter:before {
    content: "\f9a8";
}

.fi-br-dna:before {
    content: "\f9a9";
}

.fi-br-dizzy:before {
    content: "\f9aa";
}

.fi-br-divide:before {
    content: "\f9ab";
}

.fi-br-distribute-spacing-vertical:before {
    content: "\f9ac";
}

.fi-br-distribute-spacing-horizontal:before {
    content: "\f9ad";
}

.fi-br-display-slash:before {
    content: "\f9ae";
}

.fi-br-display-medical:before {
    content: "\f9af";
}

.fi-br-display-code:before {
    content: "\f9b0";
}

.fi-br-display-arrow-down:before {
    content: "\f9b1";
}

.fi-br-disk:before {
    content: "\f9b2";
}

.fi-br-disease:before {
    content: "\f9b3";
}

.fi-br-discover:before {
    content: "\f9b4";
}

.fi-br-disco-ball:before {
    content: "\f9b5";
}

.fi-br-disc-drive:before {
    content: "\f9b6";
}

.fi-br-direction-signal:before {
    content: "\f9b7";
}

.fi-br-direction-signal-arrow:before {
    content: "\f9b8";
}

.fi-br-diploma:before {
    content: "\f9b9";
}

.fi-br-dinner:before {
    content: "\f9ba";
}

.fi-br-digital-tachograph:before {
    content: "\f9bb";
}

.fi-br-digging:before {
    content: "\f9bc";
}

.fi-br-dice:before {
    content: "\f9bd";
}

.fi-br-dice-two:before {
    content: "\f9be";
}

.fi-br-dice-three:before {
    content: "\f9bf";
}

.fi-br-dice-six:before {
    content: "\f9c0";
}

.fi-br-dice-one:before {
    content: "\f9c1";
}

.fi-br-dice-four:before {
    content: "\f9c2";
}

.fi-br-dice-d8:before {
    content: "\f9c3";
}

.fi-br-dice-d6:before {
    content: "\f9c4";
}

.fi-br-dice-d4:before {
    content: "\f9c5";
}

.fi-br-dice-d20:before {
    content: "\f9c6";
}

.fi-br-dice-d12:before {
    content: "\f9c7";
}

.fi-br-dice-d10:before {
    content: "\f9c8";
}

.fi-br-dice-alt:before {
    content: "\f9c9";
}

.fi-br-diary-clasp:before {
    content: "\f9ca";
}

.fi-br-diary-bookmarks:before {
    content: "\f9cb";
}

.fi-br-diary-bookmark-down:before {
    content: "\f9cc";
}

.fi-br-diamond:before {
    content: "\f9cd";
}

.fi-br-diamond-turn-right:before {
    content: "\f9ce";
}

.fi-br-diamond-exclamation:before {
    content: "\f9cf";
}

.fi-br-dial:before {
    content: "\f9d0";
}

.fi-br-dial-off:before {
    content: "\f9d1";
}

.fi-br-dial-min:before {
    content: "\f9d2";
}

.fi-br-dial-med:before {
    content: "\f9d3";
}

.fi-br-dial-med-low:before {
    content: "\f9d4";
}

.fi-br-dial-max:before {
    content: "\f9d5";
}

.fi-br-dial-low:before {
    content: "\f9d6";
}

.fi-br-dial-high:before {
    content: "\f9d7";
}

.fi-br-diagram-venn:before {
    content: "\f9d8";
}

.fi-br-diagram-successor:before {
    content: "\f9d9";
}

.fi-br-diagram-subtask:before {
    content: "\f9da";
}

.fi-br-diagram-sankey:before {
    content: "\f9db";
}

.fi-br-diagram-project:before {
    content: "\f9dc";
}

.fi-br-diagram-previous:before {
    content: "\f9dd";
}

.fi-br-diagram-predecessor:before {
    content: "\f9de";
}

.fi-br-diagram-next:before {
    content: "\f9df";
}

.fi-br-diagram-nested:before {
    content: "\f9e0";
}

.fi-br-diagram-lean-canvas:before {
    content: "\f9e1";
}

.fi-br-diagram-cells:before {
    content: "\f9e2";
}

.fi-br-dharmachakra:before {
    content: "\f9e3";
}

.fi-br-dewpoint:before {
    content: "\f9e4";
}

.fi-br-devices:before {
    content: "\f9e5";
}

.fi-br-desktop-wallpaper:before {
    content: "\f9e6";
}

.fi-br-desktop-arrow-down:before {
    content: "\f9e7";
}

.fi-br-description:before {
    content: "\f9e8";
}

.fi-br-description-alt:before {
    content: "\f9e9";
}

.fi-br-deposit:before {
    content: "\f9ea";
}

.fi-br-deposit-alt:before {
    content: "\f9eb";
}

.fi-br-department:before {
    content: "\f9ec";
}

.fi-br-department-structure:before {
    content: "\f9ed";
}

.fi-br-democrat:before {
    content: "\f9ee";
}

.fi-br-delete:before {
    content: "\f9ef";
}

.fi-br-delete-user:before {
    content: "\f9f0";
}

.fi-br-delete-right:before {
    content: "\f9f1";
}

.fi-br-delete-document:before {
    content: "\f9f2";
}

.fi-br-deer:before {
    content: "\f9f3";
}

.fi-br-deer-rudolph:before {
    content: "\f9f4";
}

.fi-br-debt:before {
    content: "\f9f5";
}

.fi-br-database:before {
    content: "\f9f6";
}

.fi-br-data-transfer:before {
    content: "\f9f7";
}

.fi-br-dashboard:before {
    content: "\f9f8";
}

.fi-br-dart:before {
    content: "\f9f9";
}

.fi-br-damage:before {
    content: "\f9fa";
}

.fi-br-daily-calendar:before {
    content: "\f9fb";
}

.fi-br-dagger:before {
    content: "\f9fc";
}

.fi-br-d:before {
    content: "\f9fd";
}

.fi-br-customize:before {
    content: "\f9fe";
}

.fi-br-customize-edit:before {
    content: "\f9ff";
}

.fi-br-customize-computer:before {
    content: "\fa00";
}

.fi-br-customization:before {
    content: "\fa01";
}

.fi-br-customization-cogwheel:before {
    content: "\fa02";
}

.fi-br-customer-service:before {
    content: "\fa03";
}

.fi-br-customer-care:before {
    content: "\fa04";
}

.fi-br-custard:before {
    content: "\fa05";
}

.fi-br-curve:before {
    content: "\fa06";
}

.fi-br-curve-arrow:before {
    content: "\fa07";
}

.fi-br-curve-alt:before {
    content: "\fa08";
}

.fi-br-cursor:before {
    content: "\fa09";
}

.fi-br-cursor-text:before {
    content: "\fa0a";
}

.fi-br-cursor-text-alt:before {
    content: "\fa0b";
}

.fi-br-cursor-plus:before {
    content: "\fa0c";
}

.fi-br-cursor-finger:before {
    content: "\fa0d";
}

.fi-br-curling:before {
    content: "\fa0e";
}

.fi-br-cupcake:before {
    content: "\fa0f";
}

.fi-br-cupcake-alt:before {
    content: "\fa10";
}

.fi-br-cup-togo:before {
    content: "\fa11";
}

.fi-br-cup-straw:before {
    content: "\fa12";
}

.fi-br-cup-straw-swoosh:before {
    content: "\fa13";
}

.fi-br-cucumber:before {
    content: "\fa14";
}

.fi-br-cubes:before {
    content: "\fa15";
}

.fi-br-cubes-stacked:before {
    content: "\fa16";
}

.fi-br-cube:before {
    content: "\fa17";
}

.fi-br-crystal-ball:before {
    content: "\fa18";
}

.fi-br-cryptocurrency:before {
    content: "\fa19";
}

.fi-br-cruzeiro-sign:before {
    content: "\fa1a";
}

.fi-br-crutches:before {
    content: "\fa1b";
}

.fi-br-crutch:before {
    content: "\fa1c";
}

.fi-br-crown:before {
    content: "\fa1d";
}

.fi-br-crow:before {
    content: "\fa1e";
}

.fi-br-cross:before {
    content: "\fa1f";
}

.fi-br-cross-small:before {
    content: "\fa20";
}

.fi-br-cross-religion:before {
    content: "\fa21";
}

.fi-br-cross-circle:before {
    content: "\fa22";
}

.fi-br-croissant:before {
    content: "\fa23";
}

.fi-br-crm-computer:before {
    content: "\fa24";
}

.fi-br-crm-alt:before {
    content: "\fa25";
}

.fi-br-cricket:before {
    content: "\fa26";
}

.fi-br-credit-card:before {
    content: "\fa27";
}

.fi-br-cream:before {
    content: "\fa28";
}

.fi-br-crate-empty:before {
    content: "\fa29";
}

.fi-br-crab:before {
    content: "\fa2a";
}

.fi-br-cowbell:before {
    content: "\fa2b";
}

.fi-br-cowbell-more:before {
    content: "\fa2c";
}

.fi-br-cowbell-circle-plus:before {
    content: "\fa2d";
}

.fi-br-cow:before {
    content: "\fa2e";
}

.fi-br-cow-alt:before {
    content: "\fa2f";
}

.fi-br-couple:before {
    content: "\fa30";
}

.fi-br-couch:before {
    content: "\fa31";
}

.fi-br-corporate:before {
    content: "\fa32";
}

.fi-br-corporate-alt:before {
    content: "\fa33";
}

.fi-br-corn:before {
    content: "\fa34";
}

.fi-br-copyright:before {
    content: "\fa35";
}

.fi-br-copy:before {
    content: "\fa36";
}

.fi-br-copy-image:before {
    content: "\fa37";
}

.fi-br-copy-alt:before {
    content: "\fa38";
}

.fi-br-cookie:before {
    content: "\fa39";
}

.fi-br-cookie-alt:before {
    content: "\fa3a";
}

.fi-br-conveyor-belt:before {
    content: "\fa3b";
}

.fi-br-conveyor-belt-empty:before {
    content: "\fa3c";
}

.fi-br-conveyor-belt-alt:before {
    content: "\fa3d";
}

.fi-br-convert-shapes:before {
    content: "\fa3e";
}

.fi-br-convert-document:before {
    content: "\fa3f";
}

.fi-br-container-storage:before {
    content: "\fa40";
}

.fi-br-constellation:before {
    content: "\fa41";
}

.fi-br-confetti:before {
    content: "\fa42";
}

.fi-br-condition:before {
    content: "\fa43";
}

.fi-br-condition-alt:before {
    content: "\fa44";
}

.fi-br-concierge-bell:before {
    content: "\fa45";
}

.fi-br-computer:before {
    content: "\fa46";
}

.fi-br-computer-speaker:before {
    content: "\fa47";
}

.fi-br-computer-mouse:before {
    content: "\fa48";
}

.fi-br-computer-classic:before {
    content: "\fa49";
}

.fi-br-compress:before {
    content: "\fa4a";
}

.fi-br-compress-alt:before {
    content: "\fa4b";
}

.fi-br-compliance:before {
    content: "\fa4c";
}

.fi-br-compliance-document:before {
    content: "\fa4d";
}

.fi-br-compliance-clipboard:before {
    content: "\fa4e";
}

.fi-br-completed:before {
    content: "\fa4f";
}

.fi-br-compass-slash:before {
    content: "\fa50";
}

.fi-br-comments:before {
    content: "\fa51";
}

.fi-br-comments-question:before {
    content: "\fa52";
}

.fi-br-comments-question-check:before {
    content: "\fa53";
}

.fi-br-comments-dollar:before {
    content: "\fa54";
}

.fi-br-comment:before {
    content: "\fa55";
}

.fi-br-comment-xmark:before {
    content: "\fa56";
}

.fi-br-comment-user:before {
    content: "\fa57";
}

.fi-br-comment-text:before {
    content: "\fa58";
}

.fi-br-comment-sms:before {
    content: "\fa59";
}

.fi-br-comment-smile:before {
    content: "\fa5a";
}

.fi-br-comment-slash:before {
    content: "\fa5b";
}

.fi-br-comment-quote:before {
    content: "\fa5c";
}

.fi-br-comment-question:before {
    content: "\fa5d";
}

.fi-br-comment-pen:before {
    content: "\fa5e";
}

.fi-br-comment-minus:before {
    content: "\fa5f";
}

.fi-br-comment-medical:before {
    content: "\fa60";
}

.fi-br-comment-info:before {
    content: "\fa61";
}

.fi-br-comment-image:before {
    content: "\fa62";
}

.fi-br-comment-heart:before {
    content: "\fa63";
}

.fi-br-comment-exclamation:before {
    content: "\fa64";
}

.fi-br-comment-dots:before {
    content: "\fa65";
}

.fi-br-comment-dollar:before {
    content: "\fa66";
}

.fi-br-comment-code:before {
    content: "\fa67";
}

.fi-br-comment-check:before {
    content: "\fa68";
}

.fi-br-comment-arrow-up:before {
    content: "\fa69";
}

.fi-br-comment-arrow-up-right:before {
    content: "\fa6a";
}

.fi-br-comment-arrow-down:before {
    content: "\fa6b";
}

.fi-br-comment-alt:before {
    content: "\fa6c";
}

.fi-br-comment-alt-music:before {
    content: "\fa6d";
}

.fi-br-comment-alt-minus:before {
    content: "\fa6e";
}

.fi-br-comment-alt-middle:before {
    content: "\fa6f";
}

.fi-br-comment-alt-middle-top:before {
    content: "\fa70";
}

.fi-br-comment-alt-medical:before {
    content: "\fa71";
}

.fi-br-comment-alt-edit:before {
    content: "\fa72";
}

.fi-br-comment-alt-dots:before {
    content: "\fa73";
}

.fi-br-comment-alt-check:before {
    content: "\fa74";
}

.fi-br-command:before {
    content: "\fa75";
}

.fi-br-comet:before {
    content: "\fa76";
}

.fi-br-columns-3:before {
    content: "\fa77";
}

.fi-br-colon-sign:before {
    content: "\fa78";
}

.fi-br-coins:before {
    content: "\fa79";
}

.fi-br-coin:before {
    content: "\fa7a";
}

.fi-br-coin-up-arrow:before {
    content: "\fa7b";
}

.fi-br-coffin:before {
    content: "\fa7c";
}

.fi-br-coffin-cross:before {
    content: "\fa7d";
}

.fi-br-coffee:before {
    content: "\fa7e";
}

.fi-br-coffee-pot:before {
    content: "\fa7f";
}

.fi-br-coffee-beans:before {
    content: "\fa80";
}

.fi-br-coffee-bean:before {
    content: "\fa81";
}

.fi-br-code-simple:before {
    content: "\fa82";
}

.fi-br-code-pull-request:before {
    content: "\fa83";
}

.fi-br-code-pull-request-draft:before {
    content: "\fa84";
}

.fi-br-code-pull-request-closed:before {
    content: "\fa85";
}

.fi-br-code-merge:before {
    content: "\fa86";
}

.fi-br-code-fork:before {
    content: "\fa87";
}

.fi-br-code-compare:before {
    content: "\fa88";
}

.fi-br-code-commit:before {
    content: "\fa89";
}

.fi-br-code-branch:before {
    content: "\fa8a";
}

.fi-br-coconut:before {
    content: "\fa8b";
}

.fi-br-cocktail:before {
    content: "\fa8c";
}

.fi-br-cocktail-alt:before {
    content: "\fa8d";
}

.fi-br-club:before {
    content: "\fa8e";
}

.fi-br-clover-alt:before {
    content: "\fa8f";
}

.fi-br-clouds:before {
    content: "\fa90";
}

.fi-br-clouds-sun:before {
    content: "\fa91";
}

.fi-br-clouds-moon:before {
    content: "\fa92";
}

.fi-br-cloud:before {
    content: "\fa93";
}

.fi-br-cloud-upload:before {
    content: "\fa94";
}

.fi-br-cloud-upload-alt:before {
    content: "\fa95";
}

.fi-br-cloud-sun:before {
    content: "\fa96";
}

.fi-br-cloud-sun-rain:before {
    content: "\fa97";
}

.fi-br-cloud-snow:before {
    content: "\fa98";
}

.fi-br-cloud-sleet:before {
    content: "\fa99";
}

.fi-br-cloud-showers:before {
    content: "\fa9a";
}

.fi-br-cloud-showers-heavy:before {
    content: "\fa9b";
}

.fi-br-cloud-share:before {
    content: "\fa9c";
}

.fi-br-cloud-rainbow:before {
    content: "\fa9d";
}

.fi-br-cloud-rain:before {
    content: "\fa9e";
}

.fi-br-cloud-question:before {
    content: "\fa9f";
}

.fi-br-cloud-moon:before {
    content: "\faa0";
}

.fi-br-cloud-moon-rain:before {
    content: "\faa1";
}

.fi-br-cloud-meatball:before {
    content: "\faa2";
}

.fi-br-cloud-hail:before {
    content: "\faa3";
}

.fi-br-cloud-hail-mixed:before {
    content: "\faa4";
}

.fi-br-cloud-exclamation:before {
    content: "\faa5";
}

.fi-br-cloud-drizzle:before {
    content: "\faa6";
}

.fi-br-cloud-download:before {
    content: "\faa7";
}

.fi-br-cloud-download-alt:before {
    content: "\faa8";
}

.fi-br-cloud-disabled:before {
    content: "\faa9";
}

.fi-br-cloud-code:before {
    content: "\faaa";
}

.fi-br-cloud-check:before {
    content: "\faab";
}

.fi-br-clothes-hanger:before {
    content: "\faac";
}

.fi-br-closed-captioning-slash:before {
    content: "\faad";
}

.fi-br-clone:before {
    content: "\faae";
}

.fi-br-clock:before {
    content: "\faaf";
}

.fi-br-clock-up-arrow:before {
    content: "\fab0";
}

.fi-br-clock-two:before {
    content: "\fab1";
}

.fi-br-clock-two-thirty:before {
    content: "\fab2";
}

.fi-br-clock-twelve:before {
    content: "\fab3";
}

.fi-br-clock-twelve-thirty:before {
    content: "\fab4";
}

.fi-br-clock-three:before {
    content: "\fab5";
}

.fi-br-clock-three-thirty:before {
    content: "\fab6";
}

.fi-br-clock-ten:before {
    content: "\fab7";
}

.fi-br-clock-ten-thirty:before {
    content: "\fab8";
}

.fi-br-clock-six:before {
    content: "\fab9";
}

.fi-br-clock-six-thirty:before {
    content: "\faba";
}

.fi-br-clock-seven:before {
    content: "\fabb";
}

.fi-br-clock-seven-thirty:before {
    content: "\fabc";
}

.fi-br-clock-one:before {
    content: "\fabd";
}

.fi-br-clock-one-thirty:before {
    content: "\fabe";
}

.fi-br-clock-nine:before {
    content: "\fabf";
}

.fi-br-clock-nine-thirty:before {
    content: "\fac0";
}

.fi-br-clock-four-thirty:before {
    content: "\fac1";
}

.fi-br-clock-five:before {
    content: "\fac2";
}

.fi-br-clock-five-thirty:before {
    content: "\fac3";
}

.fi-br-clock-eleven:before {
    content: "\fac4";
}

.fi-br-clock-eleven-thirty:before {
    content: "\fac5";
}

.fi-br-clock-eight-thirty:before {
    content: "\fac6";
}

.fi-br-clock-desk:before {
    content: "\fac7";
}

.fi-br-clipboard:before {
    content: "\fac8";
}

.fi-br-clipboard-user:before {
    content: "\fac9";
}

.fi-br-clipboard-prescription:before {
    content: "\faca";
}

.fi-br-clipboard-list:before {
    content: "\facb";
}

.fi-br-clipboard-list-check:before {
    content: "\facc";
}

.fi-br-clipboard-exclamation:before {
    content: "\facd";
}

.fi-br-clipboard-check:before {
    content: "\face";
}

.fi-br-clip:before {
    content: "\facf";
}

.fi-br-clear-alt:before {
    content: "\fad0";
}

.fi-br-claw-marks:before {
    content: "\fad1";
}

.fi-br-clarinet:before {
    content: "\fad2";
}

.fi-br-clapperboard:before {
    content: "\fad3";
}

.fi-br-clapperboard-play:before {
    content: "\fad4";
}

.fi-br-city:before {
    content: "\fad5";
}

.fi-br-citrus:before {
    content: "\fad6";
}

.fi-br-citrus-slice:before {
    content: "\fad7";
}

.fi-br-circle:before {
    content: "\fad8";
}

.fi-br-circle-z:before {
    content: "\fad9";
}

.fi-br-circle-y:before {
    content: "\fada";
}

.fi-br-circle-xmark:before {
    content: "\fadb";
}

.fi-br-circle-x:before {
    content: "\fadc";
}

.fi-br-circle-waveform-lines:before {
    content: "\fadd";
}

.fi-br-circle-w:before {
    content: "\fade";
}

.fi-br-circle-video:before {
    content: "\fadf";
}

.fi-br-circle-v:before {
    content: "\fae0";
}

.fi-br-circle-user:before {
    content: "\fae1";
}

.fi-br-circle-u:before {
    content: "\fae2";
}

.fi-br-circle-trash:before {
    content: "\fae3";
}

.fi-br-circle-three-quarters:before {
    content: "\fae4";
}

.fi-br-circle-t:before {
    content: "\fae5";
}

.fi-br-circle-star:before {
    content: "\fae6";
}

.fi-br-circle-small:before {
    content: "\fae7";
}

.fi-br-circle-s:before {
    content: "\fae8";
}

.fi-br-circle-r:before {
    content: "\fae9";
}

.fi-br-circle-quarters-alt:before {
    content: "\faea";
}

.fi-br-circle-quarter:before {
    content: "\faeb";
}

.fi-br-circle-q:before {
    content: "\faec";
}

.fi-br-circle-phone:before {
    content: "\faed";
}

.fi-br-circle-phone-hangup:before {
    content: "\faee";
}

.fi-br-circle-phone-flip:before {
    content: "\faef";
}

.fi-br-circle-p:before {
    content: "\faf0";
}

.fi-br-circle-o:before {
    content: "\faf1";
}

.fi-br-circle-n:before {
    content: "\faf2";
}

.fi-br-circle-microphone:before {
    content: "\faf3";
}

.fi-br-circle-microphone-lines:before {
    content: "\faf4";
}

.fi-br-circle-m:before {
    content: "\faf5";
}

.fi-br-circle-l:before {
    content: "\faf6";
}

.fi-br-circle-k:before {
    content: "\faf7";
}

.fi-br-circle-j:before {
    content: "\faf8";
}

.fi-br-circle-i:before {
    content: "\faf9";
}

.fi-br-circle-heart:before {
    content: "\fafa";
}

.fi-br-circle-half:before {
    content: "\fafb";
}

.fi-br-circle-half-stroke:before {
    content: "\fafc";
}

.fi-br-circle-h:before {
    content: "\fafd";
}

.fi-br-circle-g:before {
    content: "\fafe";
}

.fi-br-circle-f:before {
    content: "\faff";
}

.fi-br-circle-exclamation-check:before {
    content: "\fb00";
}

.fi-br-circle-envelope:before {
    content: "\fb01";
}

.fi-br-circle-ellipsis:before {
    content: "\fb02";
}

.fi-br-circle-ellipsis-vertical:before {
    content: "\fb03";
}

.fi-br-circle-e:before {
    content: "\fb04";
}

.fi-br-circle-divide:before {
    content: "\fb05";
}

.fi-br-circle-dashed:before {
    content: "\fb06";
}

.fi-br-circle-d:before {
    content: "\fb07";
}

.fi-br-circle-camera:before {
    content: "\fb08";
}

.fi-br-circle-calendar:before {
    content: "\fb09";
}

.fi-br-circle-c:before {
    content: "\fb0a";
}

.fi-br-circle-bookmark:before {
    content: "\fb0b";
}

.fi-br-circle-book-open:before {
    content: "\fb0c";
}

.fi-br-circle-bolt:before {
    content: "\fb0d";
}

.fi-br-circle-b:before {
    content: "\fb0e";
}

.fi-br-circle-a:before {
    content: "\fb0f";
}

.fi-br-circle-9:before {
    content: "\fb10";
}

.fi-br-circle-8:before {
    content: "\fb11";
}

.fi-br-circle-7:before {
    content: "\fb12";
}

.fi-br-circle-6:before {
    content: "\fb13";
}

.fi-br-circle-5:before {
    content: "\fb14";
}

.fi-br-circle-4:before {
    content: "\fb15";
}

.fi-br-circle-3:before {
    content: "\fb16";
}

.fi-br-circle-2:before {
    content: "\fb17";
}

.fi-br-circle-1:before {
    content: "\fb18";
}

.fi-br-circle-0:before {
    content: "\fb19";
}

.fi-br-church:before {
    content: "\fb1a";
}

.fi-br-choose:before {
    content: "\fb1b";
}

.fi-br-choose-alt:before {
    content: "\fb1c";
}

.fi-br-chocolate:before {
    content: "\fb1d";
}

.fi-br-chimney:before {
    content: "\fb1e";
}

.fi-br-child:before {
    content: "\fb1f";
}

.fi-br-child-head:before {
    content: "\fb20";
}

.fi-br-chevron-double-up:before {
    content: "\fb21";
}

.fi-br-chevron-double-down:before {
    content: "\fb22";
}

.fi-br-chess:before {
    content: "\fb23";
}

.fi-br-chess-rook:before {
    content: "\fb24";
}

.fi-br-chess-rook-alt:before {
    content: "\fb25";
}

.fi-br-chess-queen:before {
    content: "\fb26";
}

.fi-br-chess-queen-alt:before {
    content: "\fb27";
}

.fi-br-chess-piece:before {
    content: "\fb28";
}

.fi-br-chess-pawn:before {
    content: "\fb29";
}

.fi-br-chess-pawn-alt:before {
    content: "\fb2a";
}

.fi-br-chess-knight:before {
    content: "\fb2b";
}

.fi-br-chess-knight-alt:before {
    content: "\fb2c";
}

.fi-br-chess-king:before {
    content: "\fb2d";
}

.fi-br-chess-king-alt:before {
    content: "\fb2e";
}

.fi-br-chess-clock:before {
    content: "\fb2f";
}

.fi-br-chess-clock-alt:before {
    content: "\fb30";
}

.fi-br-chess-board:before {
    content: "\fb31";
}

.fi-br-chess-bishop:before {
    content: "\fb32";
}

.fi-br-cherry:before {
    content: "\fb33";
}

.fi-br-cheeseburger:before {
    content: "\fb34";
}

.fi-br-cheese:before {
    content: "\fb35";
}

.fi-br-cheese-alt:before {
    content: "\fb36";
}

.fi-br-checkbox:before {
    content: "\fb37";
}

.fi-br-check:before {
    content: "\fb38";
}

.fi-br-check-out-calendar:before {
    content: "\fb39";
}

.fi-br-check-in-calendar:before {
    content: "\fb3a";
}

.fi-br-check-double:before {
    content: "\fb3b";
}

.fi-br-check-circle:before {
    content: "\fb3c";
}

.fi-br-cheap:before {
    content: "\fb3d";
}

.fi-br-cheap-stack:before {
    content: "\fb3e";
}

.fi-br-cheap-stack-dollar:before {
    content: "\fb3f";
}

.fi-br-cheap-dollar:before {
    content: "\fb40";
}

.fi-br-cheap-bill:before {
    content: "\fb41";
}

.fi-br-chatbot:before {
    content: "\fb42";
}

.fi-br-chatbot-speech-bubble:before {
    content: "\fb43";
}

.fi-br-chat-arrow-grow:before {
    content: "\fb44";
}

.fi-br-chat-arrow-down:before {
    content: "\fb45";
}

.fi-br-chart-waterfall:before {
    content: "\fb46";
}

.fi-br-chart-user:before {
    content: "\fb47";
}

.fi-br-chart-tree:before {
    content: "\fb48";
}

.fi-br-chart-tree-map:before {
    content: "\fb49";
}

.fi-br-chart-simple:before {
    content: "\fb4a";
}

.fi-br-chart-simple-horizontal:before {
    content: "\fb4b";
}

.fi-br-chart-set-theory:before {
    content: "\fb4c";
}

.fi-br-chart-scatter:before {
    content: "\fb4d";
}

.fi-br-chart-scatter-bubble:before {
    content: "\fb4e";
}

.fi-br-chart-scatter-3d:before {
    content: "\fb4f";
}

.fi-br-chart-radar:before {
    content: "\fb50";
}

.fi-br-chart-pyramid:before {
    content: "\fb51";
}

.fi-br-chart-pie:before {
    content: "\fb52";
}

.fi-br-chart-pie-alt:before {
    content: "\fb53";
}

.fi-br-chart-network:before {
    content: "\fb54";
}

.fi-br-chart-mixed:before {
    content: "\fb55";
}

.fi-br-chart-line-up:before {
    content: "\fb56";
}

.fi-br-chart-histogram:before {
    content: "\fb57";
}

.fi-br-chart-gantt:before {
    content: "\fb58";
}

.fi-br-chart-connected:before {
    content: "\fb59";
}

.fi-br-chart-candlestick:before {
    content: "\fb5a";
}

.fi-br-chart-bullet:before {
    content: "\fb5b";
}

.fi-br-chart-area:before {
    content: "\fb5c";
}

.fi-br-charging-station:before {
    content: "\fb5d";
}

.fi-br-channel:before {
    content: "\fb5e";
}

.fi-br-challenge:before {
    content: "\fb5f";
}

.fi-br-challenge-alt:before {
    content: "\fb60";
}

.fi-br-chalkboard:before {
    content: "\fb61";
}

.fi-br-chalkboard-user:before {
    content: "\fb62";
}

.fi-br-chair:before {
    content: "\fb63";
}

.fi-br-chair-office:before {
    content: "\fb64";
}

.fi-br-cent-sign:before {
    content: "\fb65";
}

.fi-br-cello:before {
    content: "\fb66";
}

.fi-br-cedi-sign:before {
    content: "\fb67";
}

.fi-br-cauldron:before {
    content: "\fb68";
}

.fi-br-category:before {
    content: "\fb69";
}

.fi-br-category-alt:before {
    content: "\fb6a";
}

.fi-br-catalog:before {
    content: "\fb6b";
}

.fi-br-catalog-magazine:before {
    content: "\fb6c";
}

.fi-br-catalog-alt:before {
    content: "\fb6d";
}

.fi-br-cat:before {
    content: "\fb6e";
}

.fi-br-cat-space:before {
    content: "\fb6f";
}

.fi-br-cat-head:before {
    content: "\fb70";
}

.fi-br-castle:before {
    content: "\fb71";
}

.fi-br-cassette-vhs:before {
    content: "\fb72";
}

.fi-br-cassette-tape:before {
    content: "\fb73";
}

.fi-br-cash-register:before {
    content: "\fb74";
}

.fi-br-cart-shopping-fast:before {
    content: "\fb75";
}

.fi-br-cart-minus:before {
    content: "\fb76";
}

.fi-br-cart-arrow-down:before {
    content: "\fb77";
}

.fi-br-cars:before {
    content: "\fb78";
}

.fi-br-cars-crash:before {
    content: "\fb79";
}

.fi-br-carrot:before {
    content: "\fb7a";
}

.fi-br-caret-up:before {
    content: "\fb7b";
}

.fi-br-caret-square-right:before {
    content: "\fb7c";
}

.fi-br-caret-square-left_1:before {
    content: "\fb7d";
}

.fi-br-caret-square-left:before {
    content: "\fb7e";
}

.fi-br-caret-square-down:before {
    content: "\fb7f";
}

.fi-br-caret-right:before {
    content: "\fb80";
}

.fi-br-caret-quare-up:before {
    content: "\fb81";
}

.fi-br-caret-left:before {
    content: "\fb82";
}

.fi-br-caret-down:before {
    content: "\fb83";
}

.fi-br-caret-circle-up:before {
    content: "\fb84";
}

.fi-br-caret-circle-right:before {
    content: "\fb85";
}

.fi-br-caret-circle-down:before {
    content: "\fb86";
}

.fi-br-care:before {
    content: "\fb87";
}

.fi-br-card-spade:before {
    content: "\fb88";
}

.fi-br-card-heart:before {
    content: "\fb89";
}

.fi-br-card-diamond:before {
    content: "\fb8a";
}

.fi-br-card-club:before {
    content: "\fb8b";
}

.fi-br-caravan:before {
    content: "\fb8c";
}

.fi-br-caravan-alt:before {
    content: "\fb8d";
}

.fi-br-car:before {
    content: "\fb8e";
}

.fi-br-car-wash:before {
    content: "\fb8f";
}

.fi-br-car-tilt:before {
    content: "\fb90";
}

.fi-br-car-side:before {
    content: "\fb91";
}

.fi-br-car-side-bolt:before {
    content: "\fb92";
}

.fi-br-car-rear:before {
    content: "\fb93";
}

.fi-br-car-mechanic:before {
    content: "\fb94";
}

.fi-br-car-journey:before {
    content: "\fb95";
}

.fi-br-car-garage:before {
    content: "\fb96";
}

.fi-br-car-crash:before {
    content: "\fb97";
}

.fi-br-car-circle-bolt:before {
    content: "\fb98";
}

.fi-br-car-bus:before {
    content: "\fb99";
}

.fi-br-car-bump:before {
    content: "\fb9a";
}

.fi-br-car-building:before {
    content: "\fb9b";
}

.fi-br-car-bolt:before {
    content: "\fb9c";
}

.fi-br-car-battery:before {
    content: "\fb9d";
}

.fi-br-car-alt:before {
    content: "\fb9e";
}

.fi-br-capsules:before {
    content: "\fb9f";
}

.fi-br-canned-food:before {
    content: "\fba0";
}

.fi-br-candy:before {
    content: "\fba1";
}

.fi-br-candy-sweet:before {
    content: "\fba2";
}

.fi-br-candy-corn:before {
    content: "\fba3";
}

.fi-br-candy-cane:before {
    content: "\fba4";
}

.fi-br-candy-bar:before {
    content: "\fba5";
}

.fi-br-candy-alt:before {
    content: "\fba6";
}

.fi-br-candle-holder:before {
    content: "\fba7";
}

.fi-br-can-food:before {
    content: "\fba8";
}

.fi-br-camping:before {
    content: "\fba9";
}

.fi-br-campfire:before {
    content: "\fbaa";
}

.fi-br-camera:before {
    content: "\fbab";
}

.fi-br-camera-viewfinder:before {
    content: "\fbac";
}

.fi-br-camera-slash:before {
    content: "\fbad";
}

.fi-br-camera-security:before {
    content: "\fbae";
}

.fi-br-camera-rotate:before {
    content: "\fbaf";
}

.fi-br-camera-retro:before {
    content: "\fbb0";
}

.fi-br-camera-movie:before {
    content: "\fbb1";
}

.fi-br-camera-cctv:before {
    content: "\fbb2";
}

.fi-br-camcorder:before {
    content: "\fbb3";
}

.fi-br-call-outgoing:before {
    content: "\fbb4";
}

.fi-br-call-missed:before {
    content: "\fbb5";
}

.fi-br-call-incoming:before {
    content: "\fbb6";
}

.fi-br-call-history:before {
    content: "\fbb7";
}

.fi-br-call-duration:before {
    content: "\fbb8";
}

.fi-br-calendars:before {
    content: "\fbb9";
}

.fi-br-calendar:before {
    content: "\fbba";
}

.fi-br-calendar-xmark:before {
    content: "\fbbb";
}

.fi-br-calendar-week:before {
    content: "\fbbc";
}

.fi-br-calendar-star:before {
    content: "\fbbd";
}

.fi-br-calendar-salary:before {
    content: "\fbbe";
}

.fi-br-calendar-plus:before {
    content: "\fbbf";
}

.fi-br-calendar-pen:before {
    content: "\fbc0";
}

.fi-br-calendar-minus:before {
    content: "\fbc1";
}

.fi-br-calendar-lines:before {
    content: "\fbc2";
}

.fi-br-calendar-lines-pen:before {
    content: "\fbc3";
}

.fi-br-calendar-image:before {
    content: "\fbc4";
}

.fi-br-calendar-heart:before {
    content: "\fbc5";
}

.fi-br-calendar-exclamation:before {
    content: "\fbc6";
}

.fi-br-calendar-days:before {
    content: "\fbc7";
}

.fi-br-calendar-day:before {
    content: "\fbc8";
}

.fi-br-calendar-clock:before {
    content: "\fbc9";
}

.fi-br-calendar-check:before {
    content: "\fbca";
}

.fi-br-calendar-arrow-up:before {
    content: "\fbcb";
}

.fi-br-calendar-arrow-down:before {
    content: "\fbcc";
}

.fi-br-calculator:before {
    content: "\fbcd";
}

.fi-br-calculator-simple:before {
    content: "\fbce";
}

.fi-br-calculator-money:before {
    content: "\fbcf";
}

.fi-br-calculator-bill:before {
    content: "\fbd0";
}

.fi-br-cake-wedding:before {
    content: "\fbd1";
}

.fi-br-cake-slice:before {
    content: "\fbd2";
}

.fi-br-cake-birthday:before {
    content: "\fbd3";
}

.fi-br-cactus:before {
    content: "\fbd4";
}

.fi-br-cabin:before {
    content: "\fbd5";
}

.fi-br-c:before {
    content: "\fbd6";
}

.fi-br-butterfly:before {
    content: "\fbd7";
}

.fi-br-business-time:before {
    content: "\fbd8";
}

.fi-br-bus:before {
    content: "\fbd9";
}

.fi-br-bus-alt:before {
    content: "\fbda";
}

.fi-br-burrito:before {
    content: "\fbdb";
}

.fi-br-burger-glass:before {
    content: "\fbdc";
}

.fi-br-burger-fries:before {
    content: "\fbdd";
}

.fi-br-burger-alt:before {
    content: "\fbde";
}

.fi-br-bullseye:before {
    content: "\fbdf";
}

.fi-br-bullseye-pointer:before {
    content: "\fbe0";
}

.fi-br-bullseye-arrow:before {
    content: "\fbe1";
}

.fi-br-bullhorn:before {
    content: "\fbe2";
}

.fi-br-bullet:before {
    content: "\fbe3";
}

.fi-br-bulb:before {
    content: "\fbe4";
}

.fi-br-building:before {
    content: "\fbe5";
}

.fi-br-building-ngo:before {
    content: "\fbe6";
}

.fi-br-building-circle-arrow-right:before {
    content: "\fbe7";
}

.fi-br-builder:before {
    content: "\fbe8";
}

.fi-br-build:before {
    content: "\fbe9";
}

.fi-br-build-alt:before {
    content: "\fbea";
}

.fi-br-bugs:before {
    content: "\fbeb";
}

.fi-br-bug:before {
    content: "\fbec";
}

.fi-br-bug-slash:before {
    content: "\fbed";
}

.fi-br-budget:before {
    content: "\fbee";
}

.fi-br-budget-alt:before {
    content: "\fbef";
}

.fi-br-bucket:before {
    content: "\fbf0";
}

.fi-br-brush:before {
    content: "\fbf1";
}

.fi-br-browsers:before {
    content: "\fbf2";
}

.fi-br-browser:before {
    content: "\fbf3";
}

.fi-br-browser-ui:before {
    content: "\fbf4";
}

.fi-br-broom:before {
    content: "\fbf5";
}

.fi-br-broom-ball:before {
    content: "\fbf6";
}

.fi-br-broken-image:before {
    content: "\fbf7";
}

.fi-br-broccoli:before {
    content: "\fbf8";
}

.fi-br-broadcast-tower:before {
    content: "\fbf9";
}

.fi-br-bring-front:before {
    content: "\fbfa";
}

.fi-br-bring-forward:before {
    content: "\fbfb";
}

.fi-br-brightness:before {
    content: "\fbfc";
}

.fi-br-brightness-low:before {
    content: "\fbfd";
}

.fi-br-briefcase:before {
    content: "\fbfe";
}

.fi-br-briefcase-blank:before {
    content: "\fbff";
}

.fi-br-briefcase-arrow-right:before {
    content: "\fc00";
}

.fi-br-bread:before {
    content: "\fc01";
}

.fi-br-bread-slice:before {
    content: "\fc02";
}

.fi-br-bread-slice-butter:before {
    content: "\fc03";
}

.fi-br-bread-loaf:before {
    content: "\fc04";
}

.fi-br-branding:before {
    content: "\fc05";
}

.fi-br-brand:before {
    content: "\fc06";
}

.fi-br-brain:before {
    content: "\fc07";
}

.fi-br-brain-circuit:before {
    content: "\fc08";
}

.fi-br-braille:before {
    content: "\fc09";
}

.fi-br-braille-z:before {
    content: "\fc0a";
}

.fi-br-braille-y:before {
    content: "\fc0b";
}

.fi-br-braille-x:before {
    content: "\fc0c";
}

.fi-br-braille-w:before {
    content: "\fc0d";
}

.fi-br-braille-v:before {
    content: "\fc0e";
}

.fi-br-braille-u:before {
    content: "\fc0f";
}

.fi-br-braille-t:before {
    content: "\fc10";
}

.fi-br-braille-s:before {
    content: "\fc11";
}

.fi-br-braille-r:before {
    content: "\fc12";
}

.fi-br-braille-q:before {
    content: "\fc13";
}

.fi-br-braille-p:before {
    content: "\fc14";
}

.fi-br-braille-o:before {
    content: "\fc15";
}

.fi-br-braille-n:before {
    content: "\fc16";
}

.fi-br-braille-n-alt:before {
    content: "\fc17";
}

.fi-br-braille-m:before {
    content: "\fc18";
}

.fi-br-braille-l:before {
    content: "\fc19";
}

.fi-br-braille-k:before {
    content: "\fc1a";
}

.fi-br-braille-j:before {
    content: "\fc1b";
}

.fi-br-braille-i:before {
    content: "\fc1c";
}

.fi-br-braille-h:before {
    content: "\fc1d";
}

.fi-br-braille-g:before {
    content: "\fc1e";
}

.fi-br-braille-e:before {
    content: "\fc1f";
}

.fi-br-braille-d:before {
    content: "\fc20";
}

.fi-br-braille-c:before {
    content: "\fc21";
}

.fi-br-braille-b:before {
    content: "\fc22";
}

.fi-br-braille-a:before {
    content: "\fc23";
}

.fi-br-brackets-square:before {
    content: "\fc24";
}

.fi-br-brackets-round:before {
    content: "\fc25";
}

.fi-br-brackets-curly:before {
    content: "\fc26";
}

.fi-br-bracket-square:before {
    content: "\fc27";
}

.fi-br-bracket-square-right:before {
    content: "\fc28";
}

.fi-br-bracket-round:before {
    content: "\fc29";
}

.fi-br-bracket-round-right:before {
    content: "\fc2a";
}

.fi-br-bracket-curly:before {
    content: "\fc2b";
}

.fi-br-bracket-curly-right:before {
    content: "\fc2c";
}

.fi-br-boxing-glove:before {
    content: "\fc2d";
}

.fi-br-boxes:before {
    content: "\fc2e";
}

.fi-br-box:before {
    content: "\fc2f";
}

.fi-br-box-up:before {
    content: "\fc30";
}

.fi-br-box-tissue:before {
    content: "\fc31";
}

.fi-br-box-open:before {
    content: "\fc32";
}

.fi-br-box-open-full:before {
    content: "\fc33";
}

.fi-br-box-heart:before {
    content: "\fc34";
}

.fi-br-box-fragile:before {
    content: "\fc35";
}

.fi-br-box-dollar:before {
    content: "\fc36";
}

.fi-br-box-circle-check:before {
    content: "\fc37";
}

.fi-br-box-check:before {
    content: "\fc38";
}

.fi-br-box-ballot:before {
    content: "\fc39";
}

.fi-br-box-alt:before {
    content: "\fc3a";
}

.fi-br-bowling:before {
    content: "\fc3b";
}

.fi-br-bowling-pins:before {
    content: "\fc3c";
}

.fi-br-bowling-ball:before {
    content: "\fc3d";
}

.fi-br-bowl-spoon:before {
    content: "\fc3e";
}

.fi-br-bowl-soft-serve:before {
    content: "\fc3f";
}

.fi-br-bowl-scoops:before {
    content: "\fc40";
}

.fi-br-bowl-scoop:before {
    content: "\fc41";
}

.fi-br-bowl-rice:before {
    content: "\fc42";
}

.fi-br-bowl-chopsticks:before {
    content: "\fc43";
}

.fi-br-bowl-chopsticks-noodles:before {
    content: "\fc44";
}

.fi-br-bow-arrow:before {
    content: "\fc45";
}

.fi-br-bottle:before {
    content: "\fc46";
}

.fi-br-bottle-droplet:before {
    content: "\fc47";
}

.fi-br-boss:before {
    content: "\fc48";
}

.fi-br-border-top:before {
    content: "\fc49";
}

.fi-br-border-style:before {
    content: "\fc4a";
}

.fi-br-border-style-alt:before {
    content: "\fc4b";
}

.fi-br-border-right:before {
    content: "\fc4c";
}

.fi-br-border-outer:before {
    content: "\fc4d";
}

.fi-br-border-none:before {
    content: "\fc4e";
}

.fi-br-border-left:before {
    content: "\fc4f";
}

.fi-br-border-inner:before {
    content: "\fc50";
}

.fi-br-border-center-v:before {
    content: "\fc51";
}

.fi-br-border-center-h:before {
    content: "\fc52";
}

.fi-br-border-bottom:before {
    content: "\fc53";
}

.fi-br-border-all:before {
    content: "\fc54";
}

.fi-br-booth-curtain:before {
    content: "\fc55";
}

.fi-br-boot:before {
    content: "\fc56";
}

.fi-br-boot-heeled:before {
    content: "\fc57";
}

.fi-br-books:before {
    content: "\fc58";
}

.fi-br-books-medical:before {
    content: "\fc59";
}

.fi-br-bookmark:before {
    content: "\fc5a";
}

.fi-br-bookmark-slash:before {
    content: "\fc5b";
}

.fi-br-book:before {
    content: "\fc5c";
}

.fi-br-book-user:before {
    content: "\fc5d";
}

.fi-br-book-tanakh:before {
    content: "\fc5e";
}

.fi-br-book-spells:before {
    content: "\fc5f";
}

.fi-br-book-section:before {
    content: "\fc60";
}

.fi-br-book-quran:before {
    content: "\fc61";
}

.fi-br-book-plus:before {
    content: "\fc62";
}

.fi-br-book-open-reader:before {
    content: "\fc63";
}

.fi-br-book-open-cover:before {
    content: "\fc64";
}

.fi-br-book-medical:before {
    content: "\fc65";
}

.fi-br-book-heart:before {
    content: "\fc66";
}

.fi-br-book-font:before {
    content: "\fc67";
}

.fi-br-book-dead:before {
    content: "\fc68";
}

.fi-br-book-copy:before {
    content: "\fc69";
}

.fi-br-book-circle-arrow-up:before {
    content: "\fc6a";
}

.fi-br-book-circle-arrow-right:before {
    content: "\fc6b";
}

.fi-br-book-bookmark:before {
    content: "\fc6c";
}

.fi-br-book-atlas:before {
    content: "\fc6d";
}

.fi-br-book-arrow-up:before {
    content: "\fc6e";
}

.fi-br-book-arrow-right:before {
    content: "\fc6f";
}

.fi-br-book-alt:before {
    content: "\fc70";
}

.fi-br-bonus:before {
    content: "\fc71";
}

.fi-br-bonus-star:before {
    content: "\fc72";
}

.fi-br-bong:before {
    content: "\fc73";
}

.fi-br-bone:before {
    content: "\fc74";
}

.fi-br-bone-break:before {
    content: "\fc75";
}

.fi-br-bomb:before {
    content: "\fc76";
}

.fi-br-bolt:before {
    content: "\fc77";
}

.fi-br-bolt-slash:before {
    content: "\fc78";
}

.fi-br-bolt-auto:before {
    content: "\fc79";
}

.fi-br-bold:before {
    content: "\fc7a";
}

.fi-br-blueprint:before {
    content: "\fc7b";
}

.fi-br-blueberries:before {
    content: "\fc7c";
}

.fi-br-blood:before {
    content: "\fc7d";
}

.fi-br-blood-test-tube:before {
    content: "\fc7e";
}

.fi-br-blood-test-tube-alt:before {
    content: "\fc7f";
}

.fi-br-blog-text:before {
    content: "\fc80";
}

.fi-br-blog-pencil:before {
    content: "\fc81";
}

.fi-br-blockchain-3:before {
    content: "\fc82";
}

.fi-br-block:before {
    content: "\fc83";
}

.fi-br-block-quote:before {
    content: "\fc84";
}

.fi-br-block-question:before {
    content: "\fc85";
}

.fi-br-block-brick:before {
    content: "\fc86";
}

.fi-br-blinds:before {
    content: "\fc87";
}

.fi-br-blinds-raised:before {
    content: "\fc88";
}

.fi-br-blinds-open:before {
    content: "\fc89";
}

.fi-br-blender:before {
    content: "\fc8a";
}

.fi-br-blender-phone:before {
    content: "\fc8b";
}

.fi-br-blanket:before {
    content: "\fc8c";
}

.fi-br-bitcoin-sign:before {
    content: "\fc8d";
}

.fi-br-bird:before {
    content: "\fc8e";
}

.fi-br-biohazard:before {
    content: "\fc8f";
}

.fi-br-bio:before {
    content: "\fc90";
}

.fi-br-bio-leaves:before {
    content: "\fc91";
}

.fi-br-binoculars:before {
    content: "\fc92";
}

.fi-br-binary:before {
    content: "\fc93";
}

.fi-br-binary-slash:before {
    content: "\fc94";
}

.fi-br-binary-lock:before {
    content: "\fc95";
}

.fi-br-binary-circle-check:before {
    content: "\fc96";
}

.fi-br-billiard:before {
    content: "\fc97";
}

.fi-br-bill-arrow:before {
    content: "\fc98";
}

.fi-br-biking:before {
    content: "\fc99";
}

.fi-br-biking-mountain:before {
    content: "\fc9a";
}

.fi-br-bike:before {
    content: "\fc9b";
}

.fi-br-bicycle-journey:before {
    content: "\fc9c";
}

.fi-br-bible:before {
    content: "\fc9d";
}

.fi-br-betamax:before {
    content: "\fc9e";
}

.fi-br-benefit-porcent:before {
    content: "\fc9f";
}

.fi-br-bench-tree:before {
    content: "\fca0";
}

.fi-br-bells:before {
    content: "\fca1";
}

.fi-br-bell:before {
    content: "\fca2";
}

.fi-br-bell-slash:before {
    content: "\fca3";
}

.fi-br-bell-school:before {
    content: "\fca4";
}

.fi-br-bell-school-slash:before {
    content: "\fca5";
}

.fi-br-bell-ring:before {
    content: "\fca6";
}

.fi-br-bell-concierge:before {
    content: "\fca7";
}

.fi-br-beer:before {
    content: "\fca8";
}

.fi-br-beer-mug-empty:before {
    content: "\fca9";
}

.fi-br-bee:before {
    content: "\fcaa";
}

.fi-br-bed:before {
    content: "\fcab";
}

.fi-br-bed-empty:before {
    content: "\fcac";
}

.fi-br-bed-bunk:before {
    content: "\fcad";
}

.fi-br-bed-alt:before {
    content: "\fcae";
}

.fi-br-beacon:before {
    content: "\fcaf";
}

.fi-br-battery-three-quarters:before {
    content: "\fcb0";
}

.fi-br-battery-slash:before {
    content: "\fcb1";
}

.fi-br-battery-quarter:before {
    content: "\fcb2";
}

.fi-br-battery-half:before {
    content: "\fcb3";
}

.fi-br-battery-full:before {
    content: "\fcb4";
}

.fi-br-battery-exclamation:before {
    content: "\fcb5";
}

.fi-br-battery-empty:before {
    content: "\fcb6";
}

.fi-br-battery-bolt:before {
    content: "\fcb7";
}

.fi-br-bath:before {
    content: "\fcb8";
}

.fi-br-bat:before {
    content: "\fcb9";
}

.fi-br-basketball:before {
    content: "\fcba";
}

.fi-br-basketball-hoop:before {
    content: "\fcbb";
}

.fi-br-basket-shopping-simple:before {
    content: "\fcbc";
}

.fi-br-baseball:before {
    content: "\fcbd";
}

.fi-br-baseball-alt:before {
    content: "\fcbe";
}

.fi-br-bars-staggered:before {
    content: "\fcbf";
}

.fi-br-bars-sort:before {
    content: "\fcc0";
}

.fi-br-bars-progress:before {
    content: "\fcc1";
}

.fi-br-bars-filter:before {
    content: "\fcc2";
}

.fi-br-barcode:before {
    content: "\fcc3";
}

.fi-br-barcode-scan:before {
    content: "\fcc4";
}

.fi-br-barcode-read:before {
    content: "\fcc5";
}

.fi-br-barber-shop:before {
    content: "\fcc6";
}

.fi-br-barber-pole:before {
    content: "\fcc7";
}

.fi-br-banner:before {
    content: "\fcc8";
}

.fi-br-banner-5:before {
    content: "\fcc9";
}

.fi-br-banner-4:before {
    content: "\fcca";
}

.fi-br-banner-3:before {
    content: "\fccb";
}

.fi-br-banner-2:before {
    content: "\fccc";
}

.fi-br-bank:before {
    content: "\fccd";
}

.fi-br-banjo:before {
    content: "\fcce";
}

.fi-br-bangladeshi-taka-sign:before {
    content: "\fccf";
}

.fi-br-band-aid:before {
    content: "\fcd0";
}

.fi-br-banana:before {
    content: "\fcd1";
}

.fi-br-ban:before {
    content: "\fcd2";
}

.fi-br-ban-bug:before {
    content: "\fcd3";
}

.fi-br-ballot:before {
    content: "\fcd4";
}

.fi-br-ballot-check:before {
    content: "\fcd5";
}

.fi-br-balloons:before {
    content: "\fcd6";
}

.fi-br-balloon:before {
    content: "\fcd7";
}

.fi-br-ball-pile:before {
    content: "\fcd8";
}

.fi-br-balance-scale-right:before {
    content: "\fcd9";
}

.fi-br-balance-scale-left:before {
    content: "\fcda";
}

.fi-br-baht-sign:before {
    content: "\fcdb";
}

.fi-br-bahai:before {
    content: "\fcdc";
}

.fi-br-baguette:before {
    content: "\fcdd";
}

.fi-br-bags-shopping:before {
    content: "\fcde";
}

.fi-br-bag-seedling:before {
    content: "\fcdf";
}

.fi-br-badminton:before {
    content: "\fce0";
}

.fi-br-badget-check-alt:before {
    content: "\fce1";
}

.fi-br-badger-honey:before {
    content: "\fce2";
}

.fi-br-badge:before {
    content: "\fce3";
}

.fi-br-badge-sheriff:before {
    content: "\fce4";
}

.fi-br-badge-percent:before {
    content: "\fce5";
}

.fi-br-badge-leaf:before {
    content: "\fce6";
}

.fi-br-badge-dollar:before {
    content: "\fce7";
}

.fi-br-badge-check:before {
    content: "\fce8";
}

.fi-br-bacterium:before {
    content: "\fce9";
}

.fi-br-bacteria:before {
    content: "\fcea";
}

.fi-br-bacon:before {
    content: "\fceb";
}

.fi-br-backpack:before {
    content: "\fcec";
}

.fi-br-background:before {
    content: "\fced";
}

.fi-br-baby:before {
    content: "\fcee";
}

.fi-br-baby-carriage:before {
    content: "\fcef";
}

.fi-br-b:before {
    content: "\fcf0";
}

.fi-br-axe:before {
    content: "\fcf1";
}

.fi-br-axe-battle:before {
    content: "\fcf2";
}

.fi-br-award:before {
    content: "\fcf3";
}

.fi-br-avocado:before {
    content: "\fcf4";
}

.fi-br-austral-sign:before {
    content: "\fcf5";
}

.fi-br-audit:before {
    content: "\fcf6";
}

.fi-br-audit-alt:before {
    content: "\fcf7";
}

.fi-br-audio-description-slash:before {
    content: "\fcf8";
}

.fi-br-auction:before {
    content: "\fcf9";
}

.fi-br-aubergine:before {
    content: "\fcfa";
}

.fi-br-attribution-pencil:before {
    content: "\fcfb";
}

.fi-br-attribution-pen:before {
    content: "\fcfc";
}

.fi-br-at:before {
    content: "\fcfd";
}

.fi-br-astonished-face:before {
    content: "\fcfe";
}

.fi-br-asterik:before {
    content: "\fcff";
}

.fi-br-assistive-listening-systems:before {
    content: "\fd00";
}

.fi-br-assign:before {
    content: "\fd01";
}

.fi-br-assessment:before {
    content: "\fd02";
}

.fi-br-assessment-alt:before {
    content: "\fd03";
}

.fi-br-assept-document:before {
    content: "\fd04";
}

.fi-br-artificial-intelligence:before {
    content: "\fd05";
}

.fi-br-arrows:before {
    content: "\fd06";
}

.fi-br-arrows-to-line:before {
    content: "\fd07";
}

.fi-br-arrows-to-eye:before {
    content: "\fd08";
}

.fi-br-arrows-to-dotted-line:before {
    content: "\fd09";
}

.fi-br-arrows-retweet:before {
    content: "\fd0a";
}

.fi-br-arrows-repeat:before {
    content: "\fd0b";
}

.fi-br-arrows-repeat-1:before {
    content: "\fd0c";
}

.fi-br-arrows-h:before {
    content: "\fd0d";
}

.fi-br-arrows-h-copy:before {
    content: "\fd0e";
}

.fi-br-arrows-from-line:before {
    content: "\fd0f";
}

.fi-br-arrows-from-dotted-line:before {
    content: "\fd10";
}

.fi-br-arrows-cross:before {
    content: "\fd11";
}

.fi-br-arrows-alt:before {
    content: "\fd12";
}

.fi-br-arrows-alt-v:before {
    content: "\fd13";
}

.fi-br-arrows-alt-h:before {
    content: "\fd14";
}

.fi-br-arrow-up:before {
    content: "\fd15";
}

.fi-br-arrow-up-to-dotted-line:before {
    content: "\fd16";
}

.fi-br-arrow-up-square-triangle:before {
    content: "\fd17";
}

.fi-br-arrow-up-small-big:before {
    content: "\fd18";
}

.fi-br-arrow-up-right:before {
    content: "\fd19";
}

.fi-br-arrow-up-right-from-square:before {
    content: "\fd1a";
}

.fi-br-arrow-up-right-and-arrow-down-left-from-center:before {
    content: "\fd1b";
}

.fi-br-arrow-up-left:before {
    content: "\fd1c";
}

.fi-br-arrow-up-left-from-circle:before {
    content: "\fd1d";
}

.fi-br-arrow-up-from-square:before {
    content: "\fd1e";
}

.fi-br-arrow-up-from-dotted-line:before {
    content: "\fd1f";
}

.fi-br-arrow-turn-down-right:before {
    content: "\fd20";
}

.fi-br-arrow-turn-down-left:before {
    content: "\fd21";
}

.fi-br-arrow-trend-up:before {
    content: "\fd22";
}

.fi-br-arrow-trend-down:before {
    content: "\fd23";
}

.fi-br-arrow-to-top:before {
    content: "\fd24";
}

.fi-br-arrow-to-right:before {
    content: "\fd25";
}

.fi-br-arrow-to-left:before {
    content: "\fd26";
}

.fi-br-arrow-to-bottom:before {
    content: "\fd27";
}

.fi-br-arrow-square-up:before {
    content: "\fd28";
}

.fi-br-arrow-square-right:before {
    content: "\fd29";
}

.fi-br-arrow-square-left:before {
    content: "\fd2a";
}

.fi-br-arrow-square-down:before {
    content: "\fd2b";
}

.fi-br-arrow-small-up:before {
    content: "\fd2c";
}

.fi-br-arrow-small-right:before {
    content: "\fd2d";
}

.fi-br-arrow-small-left:before {
    content: "\fd2e";
}

.fi-br-arrow-small-down:before {
    content: "\fd2f";
}

.fi-br-arrow-right:before {
    content: "\fd30";
}

.fi-br-arrow-right-to-bracket:before {
    content: "\fd31";
}

.fi-br-arrow-progress:before {
    content: "\fd32";
}

.fi-br-arrow-left:before {
    content: "\fd33";
}

.fi-br-arrow-left-from-line:before {
    content: "\fd34";
}

.fi-br-arrow-from-top:before {
    content: "\fd35";
}

.fi-br-arrow-from-right:before {
    content: "\fd36";
}

.fi-br-arrow-from-left:before {
    content: "\fd37";
}

.fi-br-arrow-from-bottom:before {
    content: "\fd38";
}

.fi-br-arrow-down:before {
    content: "\fd39";
}

.fi-br-arrow-down-triangle-square:before {
    content: "\fd3a";
}

.fi-br-arrow-down-to-square:before {
    content: "\fd3b";
}

.fi-br-arrow-down-to-dotted-line:before {
    content: "\fd3c";
}

.fi-br-arrow-down-small-big:before {
    content: "\fd3d";
}

.fi-br-arrow-down-left:before {
    content: "\fd3e";
}

.fi-br-arrow-down-from-dotted-line:before {
    content: "\fd3f";
}

.fi-br-arrow-comparison:before {
    content: "\fd40";
}

.fi-br-arrow-circle-up:before {
    content: "\fd41";
}

.fi-br-arrow-circle-right:before {
    content: "\fd42";
}

.fi-br-arrow-circle-left:before {
    content: "\fd43";
}

.fi-br-arrow-circle-down:before {
    content: "\fd44";
}

.fi-br-arrow-alt-up:before {
    content: "\fd45";
}

.fi-br-arrow-alt-to-top:before {
    content: "\fd46";
}

.fi-br-arrow-alt-to-right:before {
    content: "\fd47";
}

.fi-br-arrow-alt-to-left:before {
    content: "\fd48";
}

.fi-br-arrow-alt-to-bottom:before {
    content: "\fd49";
}

.fi-br-arrow-alt-square-up:before {
    content: "\fd4a";
}

.fi-br-arrow-alt-square-right:before {
    content: "\fd4b";
}

.fi-br-arrow-alt-square-left:before {
    content: "\fd4c";
}

.fi-br-arrow-alt-square-down:before {
    content: "\fd4d";
}

.fi-br-arrow-alt-right:before {
    content: "\fd4e";
}

.fi-br-arrow-alt-left:before {
    content: "\fd4f";
}

.fi-br-arrow-alt-from-top:before {
    content: "\fd50";
}

.fi-br-arrow-alt-from-right:before {
    content: "\fd51";
}

.fi-br-arrow-alt-from-left:before {
    content: "\fd52";
}

.fi-br-arrow-alt-from-bottom:before {
    content: "\fd53";
}

.fi-br-arrow-alt-down:before {
    content: "\fd54";
}

.fi-br-arrow-alt-circle-up:before {
    content: "\fd55";
}

.fi-br-arrow-alt-circle-right:before {
    content: "\fd56";
}

.fi-br-arrow-alt-circle-left:before {
    content: "\fd57";
}

.fi-br-arrow-alt-circle-down:before {
    content: "\fd58";
}

.fi-br-archway:before {
    content: "\fd59";
}

.fi-br-archive:before {
    content: "\fd5a";
}

.fi-br-apps:before {
    content: "\fd5b";
}

.fi-br-apps-sort:before {
    content: "\fd5c";
}

.fi-br-apps-delete:before {
    content: "\fd5d";
}

.fi-br-apps-add:before {
    content: "\fd5e";
}

.fi-br-apple-whole:before {
    content: "\fd5f";
}

.fi-br-apple-crate:before {
    content: "\fd60";
}

.fi-br-apple-core:before {
    content: "\fd61";
}

.fi-br-api:before {
    content: "\fd62";
}

.fi-br-aperture:before {
    content: "\fd63";
}

.fi-br-apartment:before {
    content: "\fd64";
}

.fi-br-answer:before {
    content: "\fd65";
}

.fi-br-answer-alt:before {
    content: "\fd66";
}

.fi-br-ankh:before {
    content: "\fd67";
}

.fi-br-animated-icon:before {
    content: "\fd68";
}

.fi-br-angry:before {
    content: "\fd69";
}

.fi-br-angle:before {
    content: "\fd6a";
}

.fi-br-angle-up:before {
    content: "\fd6b";
}

.fi-br-angle-square-up:before {
    content: "\fd6c";
}

.fi-br-angle-square-right:before {
    content: "\fd6d";
}

.fi-br-angle-square-left:before {
    content: "\fd6e";
}

.fi-br-angle-square-down:before {
    content: "\fd6f";
}

.fi-br-angle-small-up:before {
    content: "\fd70";
}

.fi-br-angle-small-right:before {
    content: "\fd71";
}

.fi-br-angle-small-left:before {
    content: "\fd72";
}

.fi-br-angle-small-down:before {
    content: "\fd73";
}

.fi-br-angle-right:before {
    content: "\fd74";
}

.fi-br-angle-left:before {
    content: "\fd75";
}

.fi-br-angle-down:before {
    content: "\fd76";
}

.fi-br-angle-double-small-up:before {
    content: "\fd77";
}

.fi-br-angle-double-small-right:before {
    content: "\fd78";
}

.fi-br-angle-double-small-left:before {
    content: "\fd79";
}

.fi-br-angle-double-small-down:before {
    content: "\fd7a";
}

.fi-br-angle-double-right:before {
    content: "\fd7b";
}

.fi-br-angle-double-left:before {
    content: "\fd7c";
}

.fi-br-angle-circle-up:before {
    content: "\fd7d";
}

.fi-br-angle-circle-right:before {
    content: "\fd7e";
}

.fi-br-angle-circle-left:before {
    content: "\fd7f";
}

.fi-br-angle-circle-down:before {
    content: "\fd80";
}

.fi-br-angle-90:before {
    content: "\fd81";
}

.fi-br-angel:before {
    content: "\fd82";
}

.fi-br-anchor:before {
    content: "\fd83";
}

.fi-br-analyse:before {
    content: "\fd84";
}

.fi-br-analyse-alt:before {
    content: "\fd85";
}

.fi-br-ambulance:before {
    content: "\fd86";
}

.fi-br-align-slash:before {
    content: "\fd87";
}

.fi-br-align-left:before {
    content: "\fd88";
}

.fi-br-align-justify:before {
    content: "\fd89";
}

.fi-br-align-center:before {
    content: "\fd8a";
}

.fi-br-alien:before {
    content: "\fd8b";
}

.fi-br-alicorn:before {
    content: "\fd8c";
}

.fi-br-algorithm:before {
    content: "\fd8d";
}

.fi-br-album:before {
    content: "\fd8e";
}

.fi-br-album-collection:before {
    content: "\fd8f";
}

.fi-br-album-circle-user:before {
    content: "\fd90";
}

.fi-br-album-circle-plus:before {
    content: "\fd91";
}

.fi-br-alarm-snooze:before {
    content: "\fd92";
}

.fi-br-alarm-plus:before {
    content: "\fd93";
}

.fi-br-alarm-exclamation:before {
    content: "\fd94";
}

.fi-br-alarm-clock:before {
    content: "\fd95";
}

.fi-br-airplay:before {
    content: "\fd96";
}

.fi-br-airplane-journey:before {
    content: "\fd97";
}

.fi-br-air-pollution:before {
    content: "\fd98";
}

.fi-br-air-freshener:before {
    content: "\fd99";
}

.fi-br-air-conditioner:before {
    content: "\fd9a";
}

.fi-br-age:before {
    content: "\fd9b";
}

.fi-br-age-restriction-zero:before {
    content: "\fd9c";
}

.fi-br-age-restriction-twenty-one:before {
    content: "\fd9d";
}

.fi-br-age-restriction-twelve:before {
    content: "\fd9e";
}

.fi-br-age-restriction-three:before {
    content: "\fd9f";
}

.fi-br-age-restriction-thirteen:before {
    content: "\fda0";
}

.fi-br-age-restriction-sixteen:before {
    content: "\fda1";
}

.fi-br-age-restriction-six:before {
    content: "\fda2";
}

.fi-br-age-restriction-seven:before {
    content: "\fda3";
}

.fi-br-age-restriction-eighteen:before {
    content: "\fda4";
}

.fi-br-age-alt:before {
    content: "\fda5";
}

.fi-br-admin:before {
    content: "\fda6";
}

.fi-br-admin-alt:before {
    content: "\fda7";
}

.fi-br-address-card:before {
    content: "\fda8";
}

.fi-br-address-book:before {
    content: "\fda9";
}

.fi-br-add:before {
    content: "\fdaa";
}

.fi-br-add-image:before {
    content: "\fdab";
}

.fi-br-add-folder:before {
    content: "\fdac";
}

.fi-br-add-document:before {
    content: "\fdad";
}

.fi-br-ad:before {
    content: "\fdae";
}

.fi-br-ad-paid:before {
    content: "\fdaf";
}

.fi-br-acorn:before {
    content: "\fdb0";
}

.fi-br-accident:before {
    content: "\fdb1";
}

.fi-br-a:before {
    content: "\fdb2";
}

.fi-br-Siren:before {
    content: "\fdb3";
}

.fi-br-Sickle:before {
    content: "\fdb4";
}

.fi-br-Revenue:before {
    content: "\fdb5";
}

.fi-br-QR:before {
    content: "\fdb6";
}

.fi-br-Pi:before {
    content: "\fdb7";
}

.fi-br-Cannabis:before {
    content: "\fdb8";
}

.fi-br-CV:before {
    content: "\fdb9";
}

.fi-br-CRM:before {
    content: "\fdba";
}

.fi-br-Butter:before {
    content: "\fdbb";
}

.fi-br-Booking:before {
    content: "\fdbc";
}

.fi-br-9:before {
    content: "\fdbd";
}

.fi-br-8:before {
    content: "\fdbe";
}

.fi-br-7:before {
    content: "\fdbf";
}

.fi-br-6:before {
    content: "\fdc0";
}

.fi-br-5:before {
    content: "\fdc1";
}

.fi-br-404:before {
    content: "\fdc2";
}

.fi-br-4:before {
    content: "\fdc3";
}

.fi-br-360-degrees:before {
    content: "\fdc4";
}

.fi-br-3:before {
    content: "\fdc5";
}

.fi-br-2:before {
    content: "\fdc6";
}

.fi-br-1:before {
    content: "\fdc7";
}

.fi-br-0:before {
    content: "\fdc8";
}
