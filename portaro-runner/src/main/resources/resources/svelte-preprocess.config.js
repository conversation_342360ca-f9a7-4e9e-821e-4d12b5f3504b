const path = require('path');
const sveltePreprocess = require('svelte-preprocess');

module.exports = {
    preprocess: sveltePreprocess({
        sourceMap: true,
        typescript: {
            tsconfigFile: path.resolve(__dirname, 'tsconfig.svelte.json'),
            compilerOptions: {
                target: 'es2022' // svelte preprocess overrides target from tsconfig with default value of es6
                                 // this setting overrides even that
                                 // keep this synchronized this value with env settings of eslint and with target in tsconfig.frontend.json
            }
        },
        less: {
            paths: [
                path.resolve(__dirname), // to allow absolute paths (relative path to project root directory) with LESS imports in <style> tag, e.g. @import "styles/partials/dropdowns"
                path.resolve(__dirname, 'node_modules') // to allow for bootstrap less files import without node_modules/ prefix, e.g. @import "bootstrap-less/bootstrap/variables"
            ]
        }
    })
};