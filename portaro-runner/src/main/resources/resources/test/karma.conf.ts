import {parseArgs} from 'node:util';
import {resolve} from 'path';

import commonConfigurationFactory from './karma.conf/karma.common.conf';
import localChromiumConfigFactory from './karma.conf/karma.local-chromium.conf';
import localFirefoxConfigFactory from './karma.conf/karma.local-firefox.conf';

// set environment to 'test' to enable code instrumentation by babel-plugin-istanbul during bundling (for code coverage reporting)
process.env.NODE_ENV = 'test';

const root_dir = resolve(__dirname, '..');

const {values: {launcher: launcherOption}} = parseArgs({
    args: process.argv,
    options: {
        launcher: {
            type: 'string',
            default: 'chromium'
        }
    },
    allowPositionals: true
});

let launcherConfiguration;
switch (launcherOption) {
    case 'chromium':
        console.log('Using chromium configuration');
        launcherConfiguration = localChromiumConfigFactory();
        break;
    case 'firefox':
        console.log('Using firefox configuration');
        launcherConfiguration = localFirefoxConfigFactory();
        break;
    default:
        console.error('Invalid launcher option. Allowed values are: [chromium, firefox]. \nUsage: npm test -- --launcher chromium');
        throw new Error('Invalid launcher option');
}

// simplified assignInWith from lodash
function mergeConfigs(...sources: Record<string, any>[]) {
    const result = {};

    sources.forEach(source => {
        if (source === null || source === undefined) {
            return;
        }

        for (const key in source) { // Include inherited properties.
            const objValue = result[key];
            const srcValue = source[key];

            // Concatenate arrays if both values are arrays; otherwise, use srcValue.
            result[key] = Array.isArray(objValue) && Array.isArray(srcValue)
                ? objValue.concat(srcValue)
                : srcValue;
        }
    });

    return result;
}

const mergedConfig = mergeConfigs(commonConfigurationFactory(root_dir), launcherConfiguration);

export default (config) => {
    config.set(mergedConfig);
};
