
// @ts-ignore to prevent runtime errors from confused libraries that think they are running inside node instead of browser: https://github.com/testing-library/dom-testing-library/issues/1180
window.process = {
    env: {
        NODE_ENV: 'production' // set mock process.env.NODE_ENV for test runtime in browser
    }
};

// this is WEBPACK SPECIFIC import for batch file importing (in this case all test files in src folder)
const testsContext2 = require.context('../src', true, /\.spec\.ts$/);
testsContext2.keys().forEach(testsContext2);

jasmine.DEFAULT_TIMEOUT_INTERVAL = 60000;
