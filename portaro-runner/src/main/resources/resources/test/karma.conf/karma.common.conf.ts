import * as path from 'path';
import * as ip from 'ip';

import webpackConfigFactory from '../../webpack.config';

import * as karmaWebpack from 'karma-webpack';
import * as karmaJasmine from 'karma-jasmine';
import * as karmaSpecReporter from 'karma-spec-reporter';
import * as karmaSourceMapSupport from 'karma-source-map-support';
import * as karmaSourcemapLoader from 'karma-sourcemap-loader';
import * as karmaCoverageIstanbulReporter from 'karma-coverage-istanbul-reporter';

const IP_ADDRESS = process.env.karmaServerHostIp || ip.address();

console.log('Host IP: ', IP_ADDRESS);

export default (root_dir) => ({
    basePath: path.resolve(root_dir),
    files: [
        path.resolve(root_dir, 'node_modules', 'angular', 'angular.js'),
        path.resolve(root_dir, 'node_modules', 'angular-mocks', 'angular-mocks.js'),
        path.resolve(root_dir, 'test', 'tests.ts')
    ],
    preprocessors: {
        'test/tests.ts': ['webpack']
    },
    frameworks: ['jasmine', 'webpack'],
    client: {
        // jasmine configuration
        jasmine: {
            random: false,
            timeoutInterval: 60000,
            DEFAULT_TIMEOUT_INTERVAL: 60000
        }
    },

    hostname: IP_ADDRESS,
    plugins: [
        karmaWebpack,
        karmaJasmine,
        karmaSpecReporter,
        karmaSourceMapSupport,
        karmaSourcemapLoader,
        karmaCoverageIstanbulReporter
    ],
    autoWatch: false,
    singleRun: true,
    reporters: ['spec', 'coverage-istanbul'],

    coverageIstanbulReporter: {
        reports: ['html', 'text-summary'],
        dir: path.resolve(root_dir, 'reports', 'coverage'),
        fixWebpackSourcePaths: true,
        combineBrowserReports: true,
        'es-modules': true
    },

    webpack: webpackConfigFactory('test'),

    webpackMiddleware: {
        noInfo: true
    }
});
