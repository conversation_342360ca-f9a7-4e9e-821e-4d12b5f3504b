@import "~bootstrap-less/bootstrap/variables";

/**
 Use with `ngf-drop`. It adding `.dragover` class by default, if file is dragged over its container.
 If `.dragover` class is present, it will display bouncing shadow around drop zone.
 Possible issue can be occurred if descending elements handling with their z-index.

 ... fucking english...
 */

@keyframes bouncing-shadow {
	from {
		box-shadow: @brand-primary 0 0 5px 5px inset;
	}

	to {
		box-shadow: @brand-primary 0 0 5px 7px inset;
	}
}

.dropzone {
	position: relative;

	> * {
		z-index: 10;
	}

	&::before {
		content: '';
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 0;
	}

	&.dragover::before {
		animation: bouncing-shadow 0.5s;
		animation-direction: alternate;
		animation-iteration-count: infinite;
		z-index: 20;
	}
}
