@import (reference) "bootstrap-less/bootstrap/variables";
@import (reference) "bootstrap-less/bootstrap/mixins/reset-filter";

.dropdown-menu-option {
    .active-state() {
        text-decoration: none;
        color: @dropdown-link-hover-color;
        background-color: @dropdown-link-hover-bg;
    }

    .selected-state() {
        color: @dropdown-link-active-color;
        text-decoration: none;
        outline: 0;
        background-color: @dropdown-link-active-bg;
    }
}

.dropdown-menu {
    margin-top: 1px;

    &.opened {
        display: block;
    }

    &.size-sm {
        font-size: 12px;
        .dropdown-menu-option {
            padding: 3px 16px;
        }
    }

    &.size-xs {
        font-size: 12px;
        .dropdown-menu-option {
            padding: 2px 12px;
        }
    }

    & > li > .dropdown-menu-option, & > .dropdown-menu-option {
        cursor: pointer;
        display: block;
        padding: 3px 20px;
        clear: both;
        font-weight: normal;
        line-height: @line-height-base;
        color: @dropdown-link-color;
        white-space: nowrap; // prevent links from randomly breaking onto new lines

        &.active {
            .dropdown-menu-option.active-state();
        }
    }
}

.dropdown-menu > li > .dropdown-menu-option.selected, .dropdown-menu .dropdown-menu-option.selected {
    &,
    &:hover,
    &:focus {
        .dropdown-menu-option.selected-state();
    }
}

.dropdown-menu > li > .dropdown-menu-option.disabled, .dropdown-menu .dropdown-menu-option.disabled {
    &,
    &:hover,
    &:focus {
        color: @dropdown-link-disabled-color;
    }

    // Nuke hover/focus effects
    &:hover,
    &:focus {
        text-decoration: none;
        background-color: transparent;
        background-image: none; // Remove CSS gradient
        .reset-filter();
        cursor: @cursor-disabled;
    }
}