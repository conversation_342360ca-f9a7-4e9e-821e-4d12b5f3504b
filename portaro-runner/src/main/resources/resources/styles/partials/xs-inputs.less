@import "bootstrap-less/bootstrap/variables";
@import "bootstrap-less/bootstrap/mixins/border-radius";

/* support for xs sized inputs */

@line-height-xs:    1.5;
@font-size-xs:      12px;
@input-height-xs:   22px;
@border-radius-xs:  3px;

.input-xs {
    height: @input-height-xs;
    padding: @padding-xs-vertical @padding-xs-horizontal;
    font-size: @font-size-xs;
    line-height: @line-height-xs;
    border-radius: @border-radius-xs;
}

.input-group-xs > .form-control,
.input-group-xs > .input-group-addon,
.input-group-xs > .input-group-btn > .btn {
    .input-xs();
}

.input-group-xs > .input-group-addon {
    &:first-child {
        .border-right-radius(0);
    }

    &:last-child {
        .border-left-radius(0);
    }
}

.input-group-addon.input-xs {
    padding:  @padding-xs-vertical @padding-xs-horizontal;
    font-size: @font-size-xs;
    border-radius: @border-radius-xs;

    &:first-child {
        .border-right-radius(0);
    }

    &:last-child {
        .border-left-radius(0);
    }
}

