// Custom variables
@spacing-xs: 3px;
@spacing-s: 6px;
@spacing-base: 8px;
@spacing-sm: 10px;
@spacing-m: 12px;
@spacing-ml: 16px;
@spacing-l: 20px;
@spacing-xl: 24px;
@spacing-xxl: 32px;

@border-radius-xs: 1px;
@border-radius-small: 2px;
@border-radius-sm: 3px;
@border-radius-default: 4px;
@border-radius-large: 6px;
@border-radius-xl: 8px;
@border-radius-xxl: 12px;

@font-size-xs: 10px;
@font-size-small: 12px;
@font-size-sm: 13px;
@font-size-default: 14px;
@font-size-large: 16px;
@font-size-xl: 18px;
@font-size-xxl: 24px;

@modal-z-index: 1050;
@toasts-z-index: 1049;
@popover-z-index: 1048;
@browser-progress-bar-z-index: 1049;
@under-modal-z-index: 1000;

.portaro-variables {
    --portaro-spacing-xs: @spacing-xs;
    --portaro-spacing-s: @spacing-s;
    --portaro-spacing-base: @spacing-base;
    --portaro-spacing-sm: @spacing-sm;
    --portaro-spacing-m: @spacing-m;
    --portaro-spacing-ml: @spacing-ml;
    --portaro-spacing-l: @spacing-l;
    --portaro-spacing-xl: @spacing-xl;
    --portaro-spacing-xxl: @spacing-xxl;

    --portaro-border-radius-xs: @border-radius-xs;
    --portaro-border-radius-small: @border-radius-small;
    --portaro-border-radius-sm: @border-radius-sm;
    --portaro-border-radius-default: @border-radius-default;
    --portaro-border-radius-large: @border-radius-large;
    --portaro-border-radius-xl: @border-radius-xl;
    --portaro-border-radius-xxl: @border-radius-xxl;

    --portaro-font-size-small: @font-size-small;
    --portaro-font-size-sm: @font-size-sm;
    --portaro-font-size-default: @font-size-default;
    --portaro-font-size-large: @font-size-large;
    --portaro-font-size-xl: @font-size-xl;
    --portaro-font-size-xxl: @font-size-xxl;
}