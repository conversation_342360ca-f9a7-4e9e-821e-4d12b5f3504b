.custom-scrollbars(@thumb-color, @thumb-color-hover, @bg: transparent, @border-color: transparent, @scrollbar-width: 12px, @scrollbar-border-width: 2px, @secondary-border-color: @border-color) {
    ::-webkit-scrollbar {
        width: @scrollbar-width;
        height: @scrollbar-width;
        background: @bg;

        &:vertical {
            border-left: 1px solid @border-color;
            border-bottom: 1px solid @secondary-border-color;
        }

        &:not(:vertical) {
            border-top: 1px solid @border-color;
            border-right: 1px solid @secondary-border-color;
        }
    }

    ::-webkit-scrollbar-thumb {
        cursor: pointer;
        border-radius: calc(@scrollbar-width / 2);
        border: @scrollbar-border-width solid transparent;
        background: @thumb-color;
        background-clip: content-box;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: @thumb-color-hover;
        background-clip: content-box;
    }

    ::-webkit-scrollbar-track-piece {
        margin: @scrollbar-border-width 0;
    }

    ::-webkit-scrollbar-corner {
        background: transparent;
    }
}

.custom-scrollbars-exact(@thumb-color, @thumb-color-hover, @bg: transparent, @border-color: transparent, @scrollbar-width: 12px, @scrollbar-border-width: 2px, @secondary-border-color: @border-color) {
    &::-webkit-scrollbar {
        width: @scrollbar-width;
        height: @scrollbar-width;
        background: @bg;

        &:vertical {
            border-left: 1px solid @border-color;
            border-bottom: 1px solid @secondary-border-color;
        }

        &:not(:vertical) {
            border-top: 1px solid @border-color;
            border-right: 1px solid @secondary-border-color;
        }
    }

    &::-webkit-scrollbar-thumb {
        cursor: pointer;
        border-radius: calc(@scrollbar-width / 2);
        border: @scrollbar-border-width solid transparent;
        background: @thumb-color;
        background-clip: content-box;
    }

    &::-webkit-scrollbar-thumb:hover {
        background: @thumb-color-hover;
        background-clip: content-box;
    }

    &::-webkit-scrollbar-track-piece {
        margin: @scrollbar-border-width 0;
    }

    &::-webkit-scrollbar-corner {
        background: transparent;
    }
}