@import (reference) "./portaro.themes.less";
@import (reference) "./portaro.variables.less";
@import "./portaro.mixins.less";

@main-padding-horizontal: 42px;
@main-padding-vertical: 32px;
@erp-topbar-height: 48px;
@sidebar-collapsed-width: 72px;

// This mixin takes list of classes as an argument
// and then sets 0-padding on a page containing page with one of classes
.no-padding-for-pages(@class-list) {
    .loop(@index) when (@index > 0) {
        @class: extract(@class-list, @index);
        main.portaro-erp-main:has(.router-outlet-container):has(kp-svelte-component-wrapper):has(@{class}) {
            padding: 0;
        }
        .loop(@index - 1);
    }

    .loop(length(@class-list)); // Start the loop
}

.flex-grow() {
    width: 100%;
    display: flex;
    flex-direction: column;
    flex: 1 1 0;
}

.kp-layout-flex-grow() {
    .flex-grow();

    .kp-layout {
        .flex-grow();

        .kp-column {
            .flex-grow();
        }
    }
}

.record-grid-display-flex-grow() {
    .kp-record-grid-display {
        .flex-grow();

        .kp-pageable-search-results {
            .flex-grow();
            gap: @spacing-m;

            .kp-search-results-loading-content,
            .kp-search-results-content {
                .flex-grow();

                .record-grid-container {
                    .flex-grow();

                    border-radius: 0 !important;
                    border-left: none !important;
                    border-right: none !important;

                    .kp-generic-grid {
                        .flex-grow();
                    }
                }
            }

            .load-previous-results-btn {
                flex-shrink: 0;
            }

            .load-next-results-btn {
                margin-bottom: @spacing-m;
                flex-shrink: 0;
            }
        }
    }
}

// Styles for Portaro ERP layout
#portaro-erp-layout {
    .custom-scrollbars(@themed-border-default, @themed-border-bold);
    --sutor-topbar-height: 48px;

    width: 100%;
    height: 100%;
    flex: 1 1 0;
    display: flex;
    background-color: @themed-body-bg;

    .erp-sidebar-nav-component-wrapper {
        display: flex;
        flex-direction: column;
        position: fixed;
        top: 0;
        left: 0;
        z-index: @under-modal-z-index;
        height: 100%;
    }

    .erp-topbar-component-wrapper {
        z-index: calc(@under-modal-z-index - 1);
        background-color: @themed-body-bg;
    }

    .erp-connection-bar-component-wrapper {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: @under-modal-z-index;
    }

    .portaro-erp-content {
        .flex-grow();
        --sidebar-opened-width: 400px;

        &.sidebar-opened {
            padding-left: var(--sidebar-opened-width);
        }

        &.sidebar-collapsed {
            padding-left: @sidebar-collapsed-width;
        }

        &.animate-padding-left {
            transition: padding-left 0.3s ease-in-out;
        }
    }

    .portaro-erp-main {
        .flex-grow();

        background-color: @themed-body-bg;
        border: 1px solid @themed-border-default;
        border-right: none;
        border-bottom: none;
        overflow-y: auto;
        border-top-left-radius: @border-radius-xxl;

        &:not(:has(.erp-page-layout)) {
            padding: @main-padding-vertical @main-padding-horizontal;
        }

        .router-outlet-container {
            .flex-grow();
            animation: 0.3s ease-in-out 0s 1 content-slide-from-top;

            // We want Bootstrap's containers to be full-screen width
            .container {
                width: 100%;
                padding: 0 !important;
                margin-top: 0 !important;
            }

            & > kp-svelte-component-wrapper {
                .flex-grow();
            }

            .kp-layout-grid-container,
            .kp-generic-page-container {
                margin-top: 0 !important; // We control padding though main container
            }

            .panel {
                background-color: @themed-body-bg;
                box-shadow: none;

                // We don't want differently colored panel headings
                .panel-heading {
                    font-weight: 500;
                    background-color: @themed-body-bg;
                }

                // We don't want striped tables
                .table-striped > tbody > tr {
                    background-color: @themed-body-bg !important;
                }
            }

            // We want our "built" layouts through KpLayout component to be full page width
            .kp-layout {
                width: 100%;

                .kp-row {
                    width: 100%;

                    &.row-width-fullscreen {
                        padding: 0 !important; // We control padding though main container
                    }
                }
            }

            // Record detail + user account detail - we want its height to fill screen by default
            kp-svelte-component-wrapper:has(.kp-document-detail-page),
            kp-svelte-component-wrapper:has(.kp-user-account-page),
            kp-svelte-component-wrapper:has(.sutor-reports-page) {
                .flex-grow();

                .kp-document-detail-page,
                .kp-user-account-page {
                    .kp-layout-flex-grow();
                }
            }

            // We don't want border and border radius on fullscreen table on record grid page
            kp-svelte-component-wrapper:has(.kp-record-grid-page) {
                .flex-grow();

                .kp-record-grid-page {
                    .flex-grow();
                    .record-grid-display-flex-grow();
                }
            }
        }
    }

    // We don't want padding on some pages
    .no-padding-for-pages(~".sutor-reports-processing-page, .sutor-reports-approving-page, .kp-generic-record-detail-page, .kp-user-account-page, .kp-record-grid-page, .kp-document-detail-page");
}

.erp-ai-rainbow-overlay-effect {
    .rainbow-effect();
}

.rainbow-effect(@after-opacity: 0.3) {
    position: relative;

    @rainbow-spacing: 2px;

    &::after {
        content: '';
        pointer-events: none;
        position: absolute;
        top: calc(@rainbow-spacing * -1);
        left: calc(@rainbow-spacing * -1);
        right: calc(@rainbow-spacing * -1);
        bottom: calc(@rainbow-spacing * -1);
        padding: 6px;
        background: #fff;
        z-index: 100;
        border-radius: 10px;

        background: linear-gradient(135deg,
        #2b86c5,
        #ff3cac,
        #b581e7,
        #00c9ff,
        #88f594,
        #eeb574,
        #ff3cac);

        background-size: 400% 400%;
        animation: rainbow 3s ease infinite;
        opacity: @after-opacity;
    }

    &::before {
        content: '';
        pointer-events: none;
        position: absolute;
        top: calc(@rainbow-spacing * -1);
        left: calc(@rainbow-spacing * -1);
        right: calc(@rainbow-spacing * -1);
        bottom: calc(@rainbow-spacing * -1);
        padding: 3px;
        border-radius: 10px;

        background: linear-gradient(135deg,
        #8102f1,
        #ff00b7,
        #0097ff,
        #14ff2c,
        #ffad00,
        #ff0030,
        #ff00b7,
        #0097ff,
        #14ff2c,
        #ffad00,
        #ff0030);

        background-size: 500% 500%;
        animation: rainbow 4.5s ease infinite;
        z-index: 101;
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: exclude;
        -webkit-mask-composite: destination-out;
    }
}


@keyframes rainbow {
    0% {
        background-position: 0 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0 50%;
    }
}

@keyframes content-slide-from-top {
    0% {
        transform: translateY(-0.75rem);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 100%;
    }
}