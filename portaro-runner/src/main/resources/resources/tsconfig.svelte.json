{
  "extends": "./tsconfig.frontend.json",
  "compilerOptions": {
    /**
      Svelte Preprocess cannot figure out whether you have a value or a type, so tell TypeScript
      to enforce using `import type` instead of `import` for Types.
     */
    "verbatimModuleSyntax": true,
    "isolatedModules": true,

    "strict": false, // todo enable after adding all missing typings
    "noImplicitReturns": true,
    "noUnusedLocals": true,

    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": [
    "src/**/*.d.ts",
    "src/**/*.svelte"
  ]
}