import type {StorybookConfig} from '@storybook/svelte-webpack5';
import * as path from 'path';
import * as sveltePreprocessConfig from '../svelte-preprocess.config';
import useLessLoader from 'storybook-less-loader';

const config: StorybookConfig = {
    stories: ['Introduction.mdx', '../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|ts|tsx|svelte)'],
    addons: [
        '@storybook/addon-links',
        '@storybook/addon-essentials',
        '@storybook/addon-interactions',
        '@storybook/addon-svelte-csf',
        '@storybook/addon-a11y',
    ],
    framework: {
        name: '@storybook/svelte-webpack5',
        options: {
            preprocess: sveltePreprocessConfig.preprocess
        },
    },
    core: {
        disableTelemetry: true
    },
    webpackFinal: (config) => {
        config.resolve.alias['src'] = path.resolve(__dirname, '..', 'src');
        config.resolve.alias['core'] = path.resolve(__dirname, '..', 'src', 'core');
        config.resolve.alias['shared'] = path.resolve(__dirname, '..', 'src', 'shared');
        config.resolve.alias['typings'] = path.resolve(__dirname, '..', 'src', 'typings');
        config.module.rules.push({
            test: /\.(ts)$/,
            use: {
                loader: 'ts-loader',
                options: {
                    configFile: path.resolve(__dirname, '..', 'tsconfig.frontend.json')
                }
            }
        });

        return useLessLoader(config);
    }
};

export default config;