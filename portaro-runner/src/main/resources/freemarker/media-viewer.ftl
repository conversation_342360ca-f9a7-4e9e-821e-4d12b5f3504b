<#ftl outputFormat="HTML">
<#import "_layouts.ftl" as layouts>
<#import "_localization.ftl" as loc>

<@layouts.headerOnly>
    <script type="text/javascript">
        window.model.rootDirectory = ${objectMapper.writeValueAsString(directoryTree)?noEsc};
    </script>

    <style>
        html,
        body {
            padding: 0;
            margin: 0;
        }

        body {
            display: flex;
            flex-direction: column;
            height: 100%;
            overflow: hidden;
            background-color: var(--viewer-content-bg);
        }
    </style>

    <!-- Browser progress bar -->
    <kp-svelte-component-wrapper class="kp-browser-progress-bar-component-wrapper" component="::$ctrl.kpBrowserProgressBarComponent"></kp-svelte-component-wrapper>
    <!-- Toast messages -->
    <kp-svelte-component-wrapper class="kp-toast-messages-component-wrapper" component="::$ctrl.kpToastMessagesComponent"></kp-svelte-component-wrapper>
    <!-- Connection bar -->
    <kp-svelte-component-wrapper class="erp-connection-bar-component-wrapper" component="::$ctrl.erpConnectionBarComponent"></kp-svelte-component-wrapper>
    <!-- Modal dialogs portal   -->
    <kp-svelte-component-wrapper component="::$ctrl.kpModalDialogsPortal"></kp-svelte-component-wrapper>

    <main ng-controller="MediaViewerCtrl as controller">
        <kp-svelte-component-wrapper ng-if="!controller.loadingData" component="::controller.kpMediaViewerComponent" props="::{
            rootDirectoryNode: controller.modelRootDirectory,
            editMode: controller.editMode,
            backToCatalogUrl: controller.backUrl,
            viewedFile: controller.openedFile
        }"></kp-svelte-component-wrapper>
    </main>
</@layouts.headerOnly>