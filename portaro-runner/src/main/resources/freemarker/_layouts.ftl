<#ftl outputFormat="HTML">
<#import "_localization.ftl" as loc>

<#macro headerTop>
    <!DOCTYPE html>
    <html xmlns="http://www.w3.org/1999/xhtml" lang="${currentPage.currentLanguage???then(currentPage.currentLanguage.id, '')}" id="ng-app" ng-app="portaro" ng-controller="PageCtrl as $ctrl" ng-strict-di>
    <head ng-non-bindable>
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        <meta name="description" content="${loc.loc("html.meta.description")?esc}"/>
        <meta name="author" content="Systemist a.s., KP-SYS spol. s r. o."/>
        <meta http-equiv="x-xrds-location" content="${currentPage.serverUrl!}/login/openid/xrds.xml"/>
        <meta http-equiv="content-language" content="${currentPage.locale.language}"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no"/>

        <#list currentPage.alternativePageLanguageLinks as languageLink>
            <link rel="alternate" href="${languageLink.url}" hreflang="${loc.locText(languageLink.text)}" />
        </#list>

        <link rel="search" type="application/opensearchdescription+xml" title="${loc.loc("commons.title")?esc}" href="${currentPage.contextPath}/opensearch.xml">

        <link rel="stylesheet" type="text/css" href="${currentPage.contextPath}/resources/dist/assets/styles/portaro.css?v=${currentPage.portaroVersion.value!}" media="all"/>
        <link rel="stylesheet" type="text/css" href="${currentPage.contextPath}/resources/dist/assets/styles/print.css?v=${currentPage.portaroVersion.value!}" media="print"/>
        <link rel="stylesheet" type="text/css" href="${currentPage.contextPath}/custom/design/style.css?v=${currentPage.portaroVersion.value!}" media="all"/>

        <style>
            :root {
                --header-bg-color: ${currentPage.headerBackgroundColor};
                --header-text-color: ${currentPage.headerTextColor};
                --header-link-color: ${currentPage.headerLinkColor};
                --main-menu-bg-color: ${currentPage.mainMenuBackgroundColor};
                --main-menu-color:${currentPage.mainMenuTextColor};
                --global-search-btn-color: ${currentPage.globalSearchButtonColor};
                --table-header-accent-color: ${currentPage.tableHeaderAccentColor};
                --selected-tab-highlight-color: ${currentPage.selectedTabHighlightColor};
            }
        </style>

        <link href="${currentPage.contextPath}/favicon.png?v=${currentPage.portaroVersion.value!}" rel="icon" sizes="256x256" type="image/png"/>

        <title>
            ${currentPage.titlePrefix!}
            <#if pageTitle??>
                ${loc.locText(pageTitle)?noEsc} -
            </#if>
            ${loc.loc("commons.title")?noEsc}
        </title>

        <script type="text/javascript">
            /* <![CDATA[ */
            window.serverUrl = '${currentPage.serverUrl!currentPage.contextPath}';
            window.currentAuth = ${currentPage.currentAuth???then(currentPage.escapeHtmlTags(objectMapper.writeValueAsString(currentPage.currentAuth))?noEsc, null)};
            window.currentDep = ${currentPage.currentDepartment???then(currentPage.escapeHtmlTags(objectMapper.writeValueAsString(currentPage.currentDepartment))?noEsc, null)};
            window.messages = {};
            window.portaroConfiguration = {};
            window.portaroConfiguration.loggingLevel = '${currentPage.frontendLoggerLevel.level}';
            window.portaroConfiguration.cameraScannerEnabled = ${objectMapper.writeValueAsString(currentPage.cameraScannerEnabled)};
            window.portaroConfiguration.globalSearchInputEnabled = ${objectMapper.writeValueAsString(currentPage.globalSearchInputEnabled)};
            window.portaroConfiguration.forAdministrationOnly = ${objectMapper.writeValueAsString(currentPage.forAdministrationOnly)};
            window.portaroConfiguration.isSutorSutinLayout = ${objectMapper.writeValueAsString(currentPage.isSutorSutinLayout)};
            window.customDataViewPagesConfiguration = ${currentPage.escapeHtmlTags(objectMapper.writeValueAsString(currentPage.customDataViewPages))?noEsc};
            window.dialogs = ${currentPage.escapeHtmlTags(objectMapper.writeValueAsString(currentPage.dialogs))?noEsc};
            window.portaroVersion = ${currentPage.escapeHtmlTags(objectMapper.writeValueAsString(currentPage.portaroVersion))?noEsc};
            window.globalSearchButtonColor = '${currentPage.globalSearchButtonColor}';
            window.mainMenu = {};
            window.mainMenu.items = ${currentPage.escapeHtmlTags(objectMapper.writeValueAsString(currentPage.mainMenu))?noEsc};
            window.mainMenu.backgroundColor = '${currentPage.mainMenuBackgroundColor}';
            window.mainMenu.textColor = '${currentPage.mainMenuTextColor}';
            window.mainMenu.highlightBackgroundColor =  '${currentPage.mainMenuHighlightBackgroundColor!""}' === '' ? null : '${currentPage.mainMenuHighlightBackgroundColor!""}';
            window.mainMenu.highlightTextColor = '${currentPage.mainMenuHighlightTextColor!""}' === '' ? null : '${currentPage.mainMenuHighlightTextColor!""}';
            window.switchableLanguages = ${currentPage.escapeHtmlTags(objectMapper.writeValueAsString(currentPage.switchableLanguages))?noEsc};
            <#if currentPage.currentLanguage??>window.currentLanguage = '${currentPage.currentLanguage.id!""}';</#if>
            window.model = {};
            /* ]]> */
        </script>

        <#include "./../resources/dist/localizations.ftl">

        <#-- pokud se nejedna o chybovou stranku, tzn. exception je null, zobraz all.html -->
        <#if !exception??>
            <#attempt>
                ${currentPage.renderCustomFile("html", "all", "html")?noEsc}
            <#recover>
                ${.error}
            </#attempt>
        </#if>

        <#-- outdated/unsupported browser alert -->
        <script type="text/javascript">
            if(/MSIE 9/i.test(window.navigator.userAgent)||/rv:11.0/i.test(window.navigator.userAgent)||/MSIE 10/i.test(window.navigator.userAgent)||/Edge\/\d./i.test(window.navigator.userAgent)) {
                window.alert("${loc.loc("commons.pouzivateZastaralyProhlizec")?noEsc}!\n${loc.loc("commons.vyuzijteNekteryZTechtoProhlizecu")?noEsc}\nMozilla Firefox, Google Chrome, Microsoft Edge");
            }
        </script>

        <script type="text/javascript" src="${currentPage.contextPath}/resources/scripts/jquery.js?v=${currentPage.portaroVersion.value!}"></script>
        <script type="text/javascript" src="${currentPage.contextPath}/resources/dist/scripts/vendors.js?v=${currentPage.portaroVersion.value!}"></script>
        <script type="text/javascript" src="${currentPage.contextPath}/resources/dist/scripts/portaro.js?v=${currentPage.portaroVersion.value!}"></script>
    </head>

    <body class="portaro-variables portaro-theme-${currentPage.themeName} ${currentPage.currentDepartment.root?then('building_all', '')} building_${currentPage.currentDepartment.id?c} language_${currentPage.locale.language} ${currentPage.currentAuth.evided?then('logged', 'not-logged')}">
        <button class="sr-only" ng-click="$ctrl.skipToContent()">
            <#-- Button visible only by screen readers. It allows to skip to main part of the page by moving focus. -->
            ${loc.loc('commons.SkipToContent')?noEsc}
        </button>

    <#if !currentPage.isSutorSutinLayout>
        <#list currentPage.alerts as alert>
            <div class="global-alert ${(alert.severity() == 'DANGER')?then('bg-danger', '')} ${alert?isLast?then('last-page-alert', '')}">
                ${loc.locText(alert.text())?noEsc}
            </div>
        </#list>
    </#if>
</#macro>


<#macro headerBottom>
    </body>
    </html>
</#macro>


<#macro full spa=false>
    <@headerTop></@headerTop>
    <@body spa>
        <#nested />
    </@body>
    <@headerBottom></@headerBottom>
</#macro>


<#macro spa>
    <#nested />
</#macro>


<#macro headerOnly>
    <@headerTop></@headerTop>
    <#nested />
    <@headerBottom></@headerBottom>
</#macro>


<#macro body spa=false>
    <!-- Browser progress bar -->
    <kp-svelte-component-wrapper class="kp-browser-progress-bar-component-wrapper" component="::$ctrl.kpBrowserProgressBarComponent"></kp-svelte-component-wrapper>
    <!-- Toast messages -->
    <kp-svelte-component-wrapper class="kp-toast-messages-component-wrapper" component="::$ctrl.kpToastMessagesComponent"></kp-svelte-component-wrapper>
    <!-- Barcode scanner viewer -->
    <kp-svelte-component-wrapper component="::$ctrl.kpBarcodeScannerViewerComponent"></kp-svelte-component-wrapper>
    <!-- Modal dialogs portal   -->
    <kp-svelte-component-wrapper component="::$ctrl.kpModalDialogsPortal"></kp-svelte-component-wrapper>

    <!-- Portaro layout -->
    <#if !currentPage.isSutorSutinLayout>
        <kp-svelte-component-wrapper class="kp-header-component-wrapper" component="::$ctrl.kpHeaderComponent" props="{searchAutofocusOrCompactOpened: $ctrl.autofocusGlobalSearch}">
            <div data-slot="custom-logo">
                <#attempt>
                    ${currentPage.renderCustomFile("html", "logo", "html")?noEsc}
                <#recover>
                    ${.error}
                </#attempt>
            </div>

            <div data-slot="custom-logo-footer">
                <#attempt>
                    ${currentPage.renderCustomFile("html", "logo-footer", "html")?noEsc}
                <#recover>
                    ${.error}
                </#attempt>
            </div>
        </kp-svelte-component-wrapper>

        <kp-svelte-component-wrapper class="kp-main-menu-svelte-component-wrapper" component="::$ctrl.kpMainMenuComponent"></kp-svelte-component-wrapper>

        <main class="main-content" tabindex="-1" ui-view>
            <#nested />
        </main>

        <div class="cleaner"></div>

        <kp-svelte-component-wrapper class="kp-footer-component-wrapper" component="::$ctrl.kpFooterComponent">
            <div data-slot="custom-footer">
                <#attempt>
                    ${currentPage.renderCustomFile("html", "footer", "html")?noEsc}
                <#recover>
                    ${.error}
                </#attempt>
            </div>
        </kp-svelte-component-wrapper>
    </#if>

    <!-- Portaro ERP layout -->
    <#if currentPage.isSutorSutinLayout>
        <div id="portaro-erp-layout">
            <!-- Sidebar -->
            <kp-svelte-component-wrapper class="erp-sidebar-nav-component-wrapper" component="::$ctrl.erpSidebarNavComponent"></kp-svelte-component-wrapper>

            <div class="portaro-erp-content">
                <!-- Topbar -->
                <kp-svelte-component-wrapper class="erp-topbar-component-wrapper" component="::$ctrl.erpTopbarComponent"></kp-svelte-component-wrapper>

                <main class="portaro-erp-main sidebar-opened">
                    <div class="router-outlet-container" ui-view>
                        <#nested />
                    </div>
                </main>
            </div>

            <!-- Connection bar -->
            <kp-svelte-component-wrapper class="erp-connection-bar-component-wrapper" component="::$ctrl.erpConnectionBarComponent"></kp-svelte-component-wrapper>
        </div>
    </#if>
</#macro>
