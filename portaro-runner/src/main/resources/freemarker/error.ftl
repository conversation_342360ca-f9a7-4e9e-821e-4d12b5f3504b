<#ftl outputFormat="HTML">
<#-- @ftlvariable name="message" type="java.lang.String" -->
<#-- @ftlvariable name="exception" type="java.lang.Throwable" -->
<#import "_layouts.ftl" as layouts>
<#import "_localization.ftl" as loc>


<@layouts.full>
    <div class="errorPage layout1c text-center container">

        <#assign severity=(warningOnly?? && warningOnly)?then('warning', 'danger')>

        <div class="alert alert-${severity}">
            <#if message?hasContent>
                ${message?esc}
            <#else>
                ${loc.loc("commons.NastalaChybaNaServeru")?noEsc}
            </#if>
        </div>

        <div>
            <a href="/" class="btn btn-primary">${loc.loc("commons.domu")?noEsc}</a>
        </div>

    </div>
</@layouts.full>