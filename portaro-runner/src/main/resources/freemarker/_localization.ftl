<#ftl outputFormat="HTML">


<#function locText text>
    <#-- @ftlvariable name="text" type="cz.kpsys.portaro.commons.localization.Text" -->
    <#return text.localize(currentPage.translator, currentPage.currentDepartment, currentPage.locale)>
</#function>

<#function loc messageCode="" args...>
    <#if messageCode?hasContent>
        <#return currentPage.translator.getMessage(messageCode, args, currentPage.currentDepartment, currentPage.locale).orElse(messageCode)>
    <#else>
        <#return "">
    </#if>
</#function>

<#function locDefault messageCode="" default="">
    <#if messageCode?hasContent>
        <#return currentPage.translator.getMessage(messageCode, [], currentPage.currentDepartment, currentPage.locale).orElse(default)>
    <#else>
        <#return "">
    </#if>
</#function>