<#ftl outputFormat="HTML">
<#-- @ftlvariable name="message" type="java.lang.String" -->
<#-- @ftlvariable name="exception" type="java.lang.Throwable" -->

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="utf-8">
    <title>
        <#if message?hasContent>
            ${message?esc}
        <#else>
            Server error
        </#if>
    </title>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;700&display=swap" rel="stylesheet">

    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            font-family: 'Ubuntu', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .error-container {
            text-align: center;
            background: #FFF;
            padding: 60px 80px;
            border-radius: 8px;
            border: 1px solid #CCC;
        }

        .verbis-logo {
            width: 64px;
            margin-bottom: 25px;
        }

        .error-title {
            font-size: 28px;
            color: #333;
            margin: 0;
            font-weight: 700;
        }

        .error-message {
            font-size: 14px;
            color: #777;
            margin: 12px 0 30px;
        }

        .back-button {
            display: inline-block;
            padding: 7px 12px;
            font-size: 14px;
            color: #FFF;
            background-color: #FF5712;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.25);
        }

        .back-button:hover {
            background-color: #e64d10;
        }
    </style>
</head>

<body>
<div class="error-container">
    <svg xmlns="http://www.w3.org/2000/svg" class="verbis-logo" viewBox="339.43 339.32 1232.58 339.43">
        <defs>
            <style>
                .cls-1 {
                    fill: #FF5712;
                }
            </style>
        </defs>
        <g>
            <path class="cls-1" d="m411.03,614.3c-11.99-23.42-24.17-50.83-36.51-82.23-12.34-31.39-24.05-65.59-35.09-102.61h55.52c2.35,9.14,5.12,19.04,8.3,29.69,3.17,10.66,6.47,21.44,9.88,32.33,3.41,10.89,6.82,21.5,10.24,31.8,3.42,10.31,6.77,19.68,10.06,28.11,3.05-8.43,6.35-17.8,9.87-28.11,3.52-10.31,6.99-20.91,10.4-31.8,3.41-10.89,6.71-21.67,9.88-32.33,3.17-10.66,5.94-20.56,8.29-29.69h54.15c-11.05,37.02-22.74,71.22-35.07,102.61-12.33,31.4-24.49,58.81-36.47,82.23h-43.46Z"/>
            <path class="cls-1" d="m540.08,523.28c0-16.4,2.51-30.75,7.55-43.05,5.04-12.3,11.65-22.54,19.86-30.75,8.2-8.2,17.63-14.41,28.29-18.62,10.66-4.22,21.61-6.33,32.86-6.33,26.24,0,46.97,8.03,62.2,24.07,15.23,16.05,22.84,39.66,22.84,70.81,0,3.05-.12,6.39-.35,10.02-.24,3.63-.47,6.85-.7,9.66h-118.78c1.17,10.78,6.21,19.33,15.11,25.65,8.9,6.32,20.85,9.49,35.84,9.49,9.61,0,19.03-.88,28.29-2.64,9.25-1.76,16.81-3.92,22.67-6.5l7.03,42.52c-2.81,1.41-6.56,2.81-11.24,4.22-4.69,1.4-9.9,2.64-15.64,3.69-5.74,1.05-11.89,1.93-18.45,2.64-6.56.7-13.12,1.05-19.68,1.05-16.64,0-31.1-2.46-43.4-7.38-12.3-4.92-22.49-11.65-30.57-20.21-8.08-8.55-14.06-18.68-17.92-30.4-3.86-11.71-5.8-24.36-5.8-37.95Zm122.99-20.03c-.24-4.45-1-8.79-2.28-13-1.29-4.22-3.28-7.96-5.97-11.25-2.7-3.28-6.09-5.97-10.19-8.08-4.1-2.11-9.2-3.16-15.28-3.16s-10.9,1-15.11,2.99c-4.22,1.99-7.73,4.63-10.54,7.91-2.81,3.28-4.98,7.09-6.5,11.42-1.53,4.34-2.64,8.73-3.34,13.18h69.23Z"/>
            <path class="cls-1" d="m865.49,475.84c-4.69-1.17-10.19-2.4-16.52-3.69-6.32-1.29-13.12-1.93-20.38-1.93-3.28,0-7.2.3-11.77.88-4.57.59-8.03,1.23-10.37,1.93v141.27h-52.36v-175.01c9.37-3.28,20.44-6.38,33.21-9.31,12.77-2.93,27-4.39,42.7-4.39,2.81,0,6.21.18,10.19.53,3.98.35,7.96.82,11.95,1.41,3.98.59,7.96,1.29,11.95,2.11,3.98.82,7.38,1.82,10.19,2.99l-8.79,43.22Z"/>
            <path class="cls-1" d="m1074.23,521.87c0,14.76-2.17,28.11-6.5,40.06-4.34,11.95-10.6,22.14-18.8,30.57-8.2,8.43-18.28,14.94-30.22,19.5-11.95,4.57-25.54,6.85-40.77,6.85-6.33,0-12.95-.3-19.85-.88-6.91-.59-13.71-1.35-20.38-2.28-6.68-.94-13.06-2.05-19.15-3.34-6.1-1.28-11.48-2.64-16.17-4.04v-227.26h52.36v52.61c5.85-2.58,11.95-4.57,18.27-5.97,6.33-1.41,13.12-2.11,20.38-2.11,13.12,0,24.71,2.28,34.79,6.85,10.07,4.57,18.5,11.07,25.3,19.5,6.79,8.43,11.95,18.57,15.46,30.4,3.51,11.83,5.27,25.01,5.27,39.53Zm-53.41-1.41c0-33.74-12.42-50.6-37.25-50.6-5.39,0-10.72.7-15.99,2.11s-9.55,3.16-12.83,5.27v95.23c2.57.47,5.85.88,9.84,1.23,3.98.35,8.32.53,13,.53,14.29,0,25.07-4.92,32.33-14.76,7.26-9.84,10.9-22.84,10.9-39.01Z"/>
            <path class="cls-1" d="m1167,614.3h-52.36v-184.84h52.36v184.84Z"/>
            <path class="cls-1" d="m1269.26,576.69c9.6,0,16.4-.93,20.38-2.81,3.98-1.87,5.97-5.5,5.97-10.9,0-4.22-2.58-7.91-7.73-11.07-5.16-3.16-13-6.73-23.55-10.72-8.2-3.04-15.64-6.21-22.31-9.49-6.68-3.28-12.36-7.2-17.04-11.77-4.69-4.57-8.32-10.01-10.9-16.34-2.58-6.32-3.86-13.94-3.86-22.84,0-17.33,6.44-31.04,19.33-41.11,12.88-10.07,30.57-15.11,53.06-15.11,11.24,0,22.02,1,32.33,2.99,10.31,1.99,18.5,4.16,24.6,6.5l-9.14,40.77c-6.09-2.11-12.71-3.98-19.85-5.62-7.15-1.64-15.17-2.46-24.07-2.46-16.4,0-24.6,4.57-24.6,13.71,0,2.11.35,3.99,1.05,5.62.7,1.64,2.11,3.22,4.22,4.74,2.11,1.53,4.97,3.17,8.61,4.94,3.63,1.77,8.26,3.72,13.88,5.84,11.47,4.25,20.96,8.45,28.46,12.58,7.5,4.13,13.41,8.6,17.75,13.39,4.33,4.79,7.38,10.11,9.14,15.96,1.76,5.84,2.64,12.62,2.64,20.33,0,18.24-6.85,32.03-20.56,41.38-13.7,9.35-33.09,14.02-58.16,14.02-16.4,0-30.05-1.41-40.94-4.22-10.9-2.81-18.45-5.15-22.67-7.03l8.79-42.52c8.9,3.51,18.04,6.27,27.41,8.26,9.37,1.99,18.63,2.99,27.76,2.99Z"/>
        </g>
        <path class="cls-1" d="m1572.01,529.73c-10.52,0-18.11,3.04-22.79,9.12-4.67,6.08-7.01,14.03-7.01,23.84v52.95c0,10.05-1.28,18.99-3.86,26.82-2.57,7.83-6.84,14.43-12.8,19.81-5.96,5.37-13.9,9.47-23.84,12.27-9.93,2.81-22.27,4.21-36.99,4.21h-7.01v-41.73h8.77c10.52,0,17.59-2.34,21.21-7.01,3.63-4.68,5.44-11.92,5.44-21.74v-46.64c0-12.15,1.64-22.44,4.91-30.86,3.28-8.42,9.82-15.66,19.64-21.74-9.82-6.08-16.36-13.32-19.64-21.74-3.27-8.41-4.91-18.7-4.91-30.86v-46.63c0-9.82-1.81-17.06-5.44-21.74-3.62-4.67-10.7-7.01-21.21-7.01h-8.77v-41.73h7.01c14.73,0,27.06,1.4,36.99,4.21,9.94,2.81,17.88,6.9,23.84,12.27,5.96,5.38,10.23,11.98,12.8,19.81,2.57,7.83,3.86,16.78,3.86,26.82v52.95c0,9.82,2.34,17.77,7.01,23.84,4.68,6.08,12.27,9.12,22.79,9.12v41.38Z"/>
        <circle class="cls-1" cx="1428.04" cy="451.22" r="21.68"/>
    </svg>

    <h1 class="error-title">Ajaj :-(</h1>
    <p class="error-message">
        <#if message?hasContent>
            ${message?esc}
        <#else>
            Server error
        </#if>
    </p>
    <a href="javascript:history.back()" class="back-button">Jít zpět</a>
</div>
</body>
</html>