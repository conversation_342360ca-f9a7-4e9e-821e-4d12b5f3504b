<#ftl outputFormat="XML">
<#-- @ftlvariable name="documents" type="java.util.List<cz.kpsys.portaro.record.Record>" -->
<#-- @ftlvariable name="library" type="cz.kpsys.portaro.department.SystemInstitution" -->
<?xml version="1.0" encoding="UTF-8" ?>
<rss version="2.0">
    <channel>
        <title>Nejnovější dokumenty</title>
        <description>Nejnovější dokumenty knihovny ${library.name()!}</description>
        <link>${serverUrl}</link>
        <#list documents as document>
            <item>
                <title>${document.name}</title>
                <description>
                    <#if document.subTitle?hasContent>
                        ${document.subTitle},
                    </#if>
                    <#if document.author?hasContent>
                        ${document.author},
                    </#if>
                    <#if document.publisher?hasContent>
                        Nakladatel: ${document.publisher},
                    </#if>
                    <#if document.isbn??>
                        ISBN: ${document.isbn}
                    </#if>
                </description>
                <link>${serverUrl}/#!/records/${document.id}</link>
            </item>
        </#list>
    </channel>
</rss>