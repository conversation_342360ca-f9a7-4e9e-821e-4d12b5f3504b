server:
  port: 80
  server-header: Portaro
  max-http-request-header-size: 16KB
  compression:
    enabled: true
    excluded-user-agents: gozilla,traviata
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json,application/x-javascript,image/svg
    min-response-size: 2048
  http2:
    enabled: true
  servlet:
    application-display-name: Portaro
    session:
      timeout: 30m
      persistent: false
      cookie:
        http-only: true
  tomcat:
    connection-timeout: 30s

jasypt:
  encryptor:
    # encode/decode passwords at cz.kpsys.portaro.tools.JasyptEncryptor
    password: ${ENCRYPTION_PASSWORD}
    key-obtention-iterations: 1000
    algorithm: PBEWITHMD5ANDTRIPLEDES

database:
  type:
  username: OPAC
  password: ENC(1GM/p6VH0b0olB9MRF9VUXCBT6kpSHJw)
  dbUpdateUsername: SQLKPWIN
  dbUpdatePassword: ENC(dRz37BRBmymUPvlM5NTJGM3PEQUMGZyiZa3rbYf73MI=)
  host:
  port: 0
  file:
  backup:
    target:
      host: mail.kpsys.cz
      user: zaloha
      key: ENC(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)
      knownHosts: |
        mail.kpsys.cz ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBFHEcyQxmb+OhxksdJ/xGu4lKxU2c4OLsVXZd/vtBMxS5PPWci0PSZktTX+4XUgYvW11cQMwtX8F+h+Pdg6TJSY=
        mail.kpsys.cz ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIBqd6SU6LmcNygCFfLzNVnFkxMcMqfOxN4fs3LMGDTBJ
        mail.kpsys.cz ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCx5GH72Y5Ea6jzXQgZ5eCQf8iPcyKMUgsS9vVXOtyL5PGPTDneVe44v+9Dglx1BF3I4T+0CreHtgex8moaMsONfEIIcst/VpUlCNtP2zWqsPUt7xxdedb9ZdBupCJJLbSbrJFT1YoR6Dc1w7G9Gn7o5umVBOZseQPr83hnWibYRNCqrrdbY0J0+JWH/iknOxXcU+qYXjxWDGqdgzIPB0FMuUNncMB6pusGRoNqvnH9zHv5iX7yak/79YRZkaFYj4hWnN0yx4utOr7aRiZqeY5bpz0UqMTCi1+4dd+POE27r/VadhToKYuKuv4Xuwl1ze/J9WBPbSJGEXyqqrHWzu6R

appserver:
  url: http://127.0.0.1:8182
  apiSecret: ENC(uBqY6wO+DSTNaDzPe0plBBtc8MyOSFUFLVLkkZ1kzpa6aWSxWzXAkGXOIPl4CZeR4GMd/hmrh1U=)

http:
  proxy:
    url: ''
    auth: false
    username:
    password:

portaro:
  instance:
    name: portaro-default
  startupAttemptsPeriod: 5s
  startupAttemptsCount: 20
  ncip:
    path: /api/ncip
  rootDepartment: -1
  resources-update:
    enabled: true
    strict: false

wallet:
  appleKeystorePassword: ENC(WGRtj4z0k9u2Mwo1T5bWa1c4FVa0C/+3kreaKjXSLYy0bifQqfDoujoFjLILMb45)
  googleServiceAccountEmail: ENC(IFff4NWouHgwqKbdCYkc64PtgCYIYQJX2CeaiYDehXre/GQ6mo8qd0S/AbTtpoMIrTHLbJ31M9EzP8+FZ+guMAbtSIOuYzDgm5XfZsOJO5k=)
  googleServiceAccountKey: ENC(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)

db-scheduler:
  delay-startup-until-context-ready: true

management:
  info:
    git:
      mode: full
    build:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true
  endpoint:
    configprops:
      show-values: when_authorized
      roles: ACTUATOR,ADMIN
    env:
      show-values: when_authorized
      roles: ACTUATOR,ADMIN
    health:
      show-details: when_authorized
      roles: ACTUATOR,ADMIN,SERVICEMAN
    shutdown:
      enabled: true
  endpoints:
    web:
      exposure:
        include: "*"
  health:
    ldap:
      enabled: false
    mail:
      enabled: false
    db:
      enabled: false
  httpexchanges:
    recording:
      include: request_headers,response_headers,time_taken,remote_address,principal

spring:
  application:
    name: portaro
  boot:
    admin:
      client:
        url: https://sba.kpsys.cz
        username: portaro
        password: ENC(X3kfeGlARmgqFcINiKS4MiQTgitHZ93wC1CuBV8nPvDQtRpMYXg27ThjBOcN56ke+xhPouYjinI=)
  jpa:
    open-in-view: false
  sql:
    init:
      mode: never
  servlet:
    multipart:
      max-file-size: 1GB
      max-request-size: 1GB
      file-size-threshold: 1MB
  cloud:
    openfeign:
      client:
        config:
          default:
            loggerLevel: basic
  output:
    ansi:
      enabled: always
  threads:
    virtual:
      enabled: false

springdoc:
  swagger-ui:
    docExpansion: none
    path: /swagger-ui
    tags-sorter: alpha
    operationsSorter: alpha
  packages-to-scan:
    - cz.kpsys.portaro.erp.workattendance.web
    - cz.kpsys.portaro.ext.ziskej.impl
    - cz.kpsys.portaro.inventory.api
    - cz.kpsys.portaro.loan.ill.api
    - cz.kpsys.portaro.verbisbox.station
    - cz.kpsys.portaro.verbisbox.shipment
    - cz.kpsys.portaro.record.grid
    - cz.kpsys.portaro.view.web.rest.record
    - cz.kpsys.portaro.verbisbox.info
    - cz.kpsys.portaro.view.web.rest.user
    - cz.kpsys.portaro.messages.thread
  writer-with-order-by-keys: false


logging:
  file:
    name: logs/log.txt
  level:
    org.springframework: warn
    org.springframework.web: warn
    org.springframework.web.method.support.InvocableHandlerMethod: warn
    org.apache.http: warn
    com.github.kagkarlsson.scheduler: info
    cz.kpsys.portaro.commons.crypto.KeyStoreAccessor: info
    org.springframework.web.servlet.PageNotFound: warn # nezobrazovani PageNotFound warningu
    cz.kpsys.portaro.acme: warn
    com.zaxxer.hikari.pool.HikariPool: warn
    com.zaxxer.hikari.HikariDataSource: warn
    org.springframework.web.client.RestTemplate: warn
    cz.kpsys.portaro.sql.generator.Query: warn
    cz.kpsys.portaro.appserver.AppserverAuthorizerByKey: warn
    cz.kpsys.portaro.appserver.AuthorizedAppserverService: warn
    cz.kpsys.portaro.appserver.BasicAppserverService: warn
    cz.kpsys.portaro.appserver.mapping.BasicMappingAppserverService: warn
    cz.kpsys.portaro.auth.anonym.AnonymCreatingAuthenticationFilter: warn
    cz.kpsys.portaro.auth.licence.SerialAndUserValidationCodeCheckingUserLicenceChecker: info
    cz.kpsys.portaro.commons.cache.CacheService: warn
    cz.kpsys.portaro.commons.cache.StaticCacheFullImpl: warn
    cz.kpsys.portaro.commons.cache.GuavaTimedDynamicCache: warn
    cz.kpsys.portaro.datacopy.AllColumnsJdbcBatchItemWriter: debug
    cz.kpsys.portaro.datacopy.NotSortingJdbcPagingItemReader: debug
    cz.kpsys.portaro.exemplar.AccessNumberSequenceItemLoaderDelegating: warn
    cz.kpsys.portaro.file.cover.CoverSearchManager: warn
    cz.kpsys.portaro.file.cover.BatchCoverImporter: warn
    cz.kpsys.portaro.file.cover.CoverDownloader: warn
    cz.kpsys.portaro.file.cover.SpringDbCoversToSearchLoader: warn
    cz.kpsys.portaro.file.index.DefaultFileIndexingService: info
    cz.kpsys.portaro.file.security.SecuredFileDataStreamer: warn
    cz.kpsys.portaro.licence.Licence: warn
    cz.kpsys.portaro.loan.renewal.AppserverRenewalService: debug
    cz.kpsys.portaro.logging.cz.kpsys.portaro.util.logging.CombinedClientSessionRepository: warn
    cz.kpsys.portaro.logging.ExecutionTimeLoggingAspect: info
    cz.kpsys.portaro.record.CachedInternalRecordLoader: warn
    cz.kpsys.portaro.record.DelegatingRichRecordsByIdsLoader: warn
    cz.kpsys.portaro.record.detail.AppserverRecordDetailLoader: warn
    cz.kpsys.portaro.record.detail.appservermarc.MarcXmlToDetailConverterImpl: info
    cz.kpsys.portaro.record.edit.RecordFieldEditor: warn
    cz.kpsys.portaro.record.print.RecordDetailAbstractPrinter: warn
    cz.kpsys.portaro.record.search.SearchServiceBackedSearch: warn
    cz.kpsys.portaro.record.search.AppserverSearchEngine: warn
    cz.kpsys.portaro.search.AbstractStandardSearch: warn
    cz.kpsys.portaro.user.edit.UserEditationRequestApplier: warn
    cz.kpsys.portaro.user.role.anonym.AnonymRole: warn
    cz.kpsys.portaro.user.role.reader.ReaderRoleImpl: warn
    cz.kpsys.portaro.user.registration.CredentialsRegistrationServiceImpl: warn
    cz.kpsys.portaro.user.auth: warn
    cz.kpsys.portaro.user.payment.provider.csobgw.CsobGwClient: info
    org.opensaml.saml.saml2.assertion.SAML20AssertionValidator: trace
    cz.kpsys.portaro.verbisboxer.manager.VerbisboxerManagerApiClient: debug
    org.openid4java: warn
    org.jasig: warn
    org.springframework.ldap: warn
    org.springframework.security: info
    org.springframework.security.web.DefaultSecurityFilterChain: warn
    org.springframework.security.oauth2: info
    org.springframework.security.openid: warn
    cz.kpsys.portaro.commons.io.DownloadFileStreamConsumer: warn
    cz.kpsys.portaro.commons.localization.HierarchyTraversingTranslator: warn
    org.apache.velocity: warn
    org.apache.velocity.app.VelocityEngine: warn
    cz.kpsys.portaro.web.log.ResponseTimeLoggingFilter: warn
    cz.kpsys.portaro.web.log.RequestStartLoggingFilter: warn
    cz.kpsys.portaro.web.exception.CompositeWebClientRequestExceptionResponseLogger: warn
    cz.kpsys.portaro.view.web.GenericController: warn
    cz.kpsys.portaro.view.web.page.GenericPageController: warn
    cz.kpsys.portaro.view.web.GenericTemplateController: warn
    cz.kpsys.portaro.view.web.GenericApiController: warn
    cz.kpsys.portaro.commons.web.CookieValueWebResolver: warn
    cz.kpsys.portaro.view.web.AppserverApiProxyController: warn
    cz.kpsys.portaro.view.web.ratelimit.RateLimitInterceptor: debug
    cz.kpsys.portaro.ncip: info
    cz.kpsys.portaro.oai.provider: info
    io.netty.handler.logging.LoggingHandler: debug
    cz.kpsys.portaro.frontend: warn
    cz.kpsys.portaro.ext.bakalari.BakalariClient: warn
    cz.kpsys.portaro.ext.alive.AliveClient: warn
    cz.kpsys.portaro.ext.cpk.CpkClient: debug
    cz.kpsys.portaro.ext.edookit.EdookitClient: warn
    cz.kpsys.portaro.ext.edupage.EdupageClient: warn
    cz.kpsys.portaro.ext.ifis.IfisClient: debug
    cz.kpsys.portaro.ext.report.server.ReportServerClient: warn
    cz.kpsys.portaro.ext.sol.SolClient: warn
    cz.kpsys.portaro.ext.unis.UnisClient: warn
    cz.kpsys.portaro.ext.ziskej.ZiskejClient: debug
    org.apache.pdfbox.pdmodel.font.PDTrueTypeFont: error
    cz.kpsys.portaro.logging.profiling.ProfilingMethodInterceptor: warn
    cz.kpsys.portaro.record.load.cz.kpsys.portaro.record.load.RecursiveRecordFieldsLoader: info
    org.apache.sshd: warn
