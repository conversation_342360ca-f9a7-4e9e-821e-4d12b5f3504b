package cz.kpsys.portaro.mail;

import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.id.IDdFile;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.messages.constants.MessageSeverity;
import cz.kpsys.portaro.messages.constants.MessageTopic;
import cz.kpsys.portaro.messages.dto.*;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.contact.ContactManager;
import cz.kpsys.portaro.user.contact.Email;
import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.validation.annotation.Validated;

import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Validated
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class MailMessageSendingSavingMailService implements MailService {

    @NonNull MailParticipantSpecifier mailParticipantSpecifier;
    @NonNull MailService nonInternalParticipantMailService;
    @NonNull Saver<Message, ?> messageSaver;
    @NonNull Saver<MessageSending, ?> messageSendingSaver;
    @NonNull Saver<MessageSendingEmailAddress, ?> messageSendingEmailAddressSaver;
    @NonNull ContactManager contactManager;
    @NonNull TransactionTemplate readwriteTransactionTemplate;

    @Override
    public void sendRawBodyMail(@NonNull @Valid NonspecificMailSendCommand command) {
        readwriteTransactionTemplate.executeWithoutResult(_ -> {
            SpecificMailSendCommand<Department> resolvedMail = mailParticipantSpecifier.resolve(command);

            if (!hasOnlyInternalParticipants(resolvedMail)) {
                nonInternalParticipantMailService.sendRawBodyMail(command);
                return;
            }

            for (SpecificEmailParticipant recipient : resolvedMail.recipients()) {
                UserToUserMessage message = Message.ofUserToUser(
                        UuidGenerator.forIdentifier(),
                        Optional.ofNullable(resolvedMail.body()).orElse(resolvedMail.subject()),
                        mapTopic(resolvedMail),
                        MessageSeverity.INFO,
                        resolvedMail.sender().existingInternalUser(),
                        recipient.existingInternalUser(),
                        resolvedMail.ctx(),
                        false,
                        null,
                        null,
                        Instant.now(),
                        Instant.now()
                );

                MessageSendingEmail messageSending = new MessageSendingEmail(
                        message.id(),
                        null,
                        null,
                        null,
                        resolvedMail.subject(),
                        StringUtil.notNullString(resolvedMail.body()),
                        getAttachmentDirectoryId(resolvedMail),
                        null
                );

                MessageSendingEmailAddress senderAddress = new MessageSendingEmailAddress(
                        UuidGenerator.forIdentifier(),
                        messageSending.getId(),
                        getUserEmail(recipient.existingInternalUser()),
                        EmailAddressType.RECIPIENT
                );

                MessageSendingEmailAddress recipientAddress = new MessageSendingEmailAddress(
                        UuidGenerator.forIdentifier(),
                        messageSending.getId(),
                        getUserEmail(resolvedMail.sender().existingInternalUser()),
                        EmailAddressType.SENDER
                );

                log.info("Sending mail (via saving to message tables) from {} to {} with subject {}:\n{}", senderAddress.emailAddress(), recipientAddress.emailAddress(), messageSending.getSubject(), command.body());

                messageSaver.save(message);
                messageSendingSaver.save(messageSending);
                messageSendingEmailAddressSaver.save(senderAddress);
                messageSendingEmailAddressSaver.save(recipientAddress);
            }
        });
    }

    private static boolean hasOnlyInternalParticipants(@NonNull SpecificMailSendCommand<Department> command) {
        return command.sender().type() == SpecificEmailUserType.INTERNAL && command.recipients().stream().allMatch(p -> p.type() == SpecificEmailUserType.INTERNAL);
    }

    @NonNull
    private static MessageTopic mapTopic(SpecificMailSendCommand<Department> resolvedMail) {
        return switch (resolvedMail.topic()) {
            case RawBodyMailTopics.COMMON,
                    RawBodyMailTopics.RECORD_SINGLE,
                    RawBodyMailTopics.RECORD_COLLECTION,
                    RawBodyMailTopics.RECORD_SDI,
                    RawBodyMailTopics.PAYMENT_RECEIPT -> MessageTopic.COMMON;
            case RawBodyMailTopics.LOAN_COMMON,
                    RawBodyMailTopics.LOAN_LOAN_READY_NOTICE,
                    RawBodyMailTopics.LOAN_EXTERNAL_LOAN_READY,
                    RawBodyMailTopics.LOAN_ILL_SEEKING_PROVISION_ORDER,
                    RawBodyMailTopics.LOAN_MAIL_LOAN_REQUEST -> MessageTopic.LOAN_COMMON;
            case RawBodyMailTopics.REMINDER -> MessageTopic.REMINDER;
            case RawBodyMailTopics.ORDER -> MessageTopic.ORDER;
            case RawBodyMailTopics.PRE_REMINDER -> MessageTopic.PRE_REMINDER;
            case RawBodyMailTopics.USER_COMMON,
                    RawBodyMailTopics.USER_PASSWORD_CHANGE,
                    RawBodyMailTopics.USER_MAIL_VERIFICATION -> MessageTopic.USER_COMMON;
            case RawBodyMailTopics.USER_REGISTRATION -> MessageTopic.USER_REGISTRATION;
            case RawBodyMailTopics.REGISTRATION_CREATION -> MessageTopic.REGISTRATION_CREATION;
            case RawBodyMailTopics.MESSAGE_FROM_READER -> MessageTopic.MESSAGE_FROM_READER;
            case RawBodyMailTopics.LIBRARY_NOTIFICATION -> MessageTopic.LIBRARY_NOTIFICATION;
            case RawBodyMailTopics.ADVERTISEMENT_COMMON -> MessageTopic.ADVERTISEMENT_COMMON;
            case RawBodyMailTopics.ERROR_COMMON -> MessageTopic.ERROR_COMMON;
            default -> {
                log.warn("Unresolved/unknown message topic {} -> returning {}", resolvedMail.topic(), MessageTopic.COMMON);
                yield MessageTopic.COMMON;
            }
        };
    }

    private String getUserEmail(BasicUser basicUser) {
        BasicUser user = Objects.requireNonNull(basicUser);
        return contactManager.getEmail(user).map(Email::value).orElseThrow(
                () -> new IllegalStateException(String.format("User %s has not any email, cannot send.", user)));
    }

    @Nullable
    private Integer getAttachmentDirectoryId(SpecificMailSendCommand<Department> resolvedMail) {
        List<IDdFile> attachments = ListUtil.notNullList(resolvedMail.attachments());
        if (attachments.isEmpty()) {
            return null;
        }

        throw new UnsupportedOperationException("Attachments in emails are not supported yet");
    }

}
