package cz.kpsys.portaro.record.fieldtype;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import cz.kpsys.portaro.form.valueeditor.date.DateValueEditor;
import cz.kpsys.portaro.form.valueeditor.inlinerecordsearch.InlineRecordSearchValueEditor;
import cz.kpsys.portaro.form.valueeditor.multipleacceptable.MultipleAcceptableValueEditor;
import cz.kpsys.portaro.form.valueeditor.record.RecordValueEditor;
import cz.kpsys.portaro.form.valueeditor.record.RecordValueEditorOptions;
import cz.kpsys.portaro.form.valueeditor.singleacceptable.SingleAcceptableValueEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.BasicValueEditorType;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorAliasType;
import cz.kpsys.portaro.hierarchy.InheritanceLoader;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.FieldDisplayType;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import cz.kpsys.portaro.record.edit.view.ValueEditorByFieldTypeLoader;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.search.restriction.FieldTypedSearchFieldParsing;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Stream;

import static cz.kpsys.portaro.record.detail.FieldTypes.FOND_FIELD_CODE;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EditableFieldTypesToFieldTypeDefinitionConverter {

    @NonNull ValueEditorByFieldTypeLoader valueEditorByFieldTypeLoader;
    @NonNull Function<Fond, List<Fond>> enabledLoadableFondsExpander;
    @NonNull InheritanceLoader<Fond> enabledFondInheritanceLoader;
    @NonNull Provider<@NonNull ZoneId> timeZoneProvider;
    @NonNull FieldTypesByFondLoader editableFieldTypesByFondLoader;

    public @NonNull List<FieldTypeDefinition> convert(@NonNull List<? extends EditableFieldType> fieldTypes, @NonNull Fond fond) {
        List<Fond> expandedFonds = enabledLoadableFondsExpander.apply(fond);
        return map(fieldTypes, expandedFonds, Optional.empty());
    }

    private @NonNull List<FieldTypeDefinition> map(List<? extends EditableFieldType> fieldTypes, List<Fond> expandedFonds, Optional<FieldTypeDefinitionAttributesContainer> parentFieldDefinitionAttributesContainer) {
        List<Fond> recordableExpandedFonds = Fond.filterRecordable(expandedFonds);
        return fieldTypes.stream()
                .filter(fieldType -> FieldDisplayType.USABLE_IN_GRID.contains(fieldType.getDisplayType()))
                .sorted(EditableFieldType.EDITATION_ORDER_COMPARATOR)
                .map(ft -> mapFieldTypeToGridFieldType(ft, expandedFonds, recordableExpandedFonds, parentFieldDefinitionAttributesContainer))
                .toList();
    }

    private @NonNull FieldTypeDefinition mapFieldTypeToGridFieldType(EditableFieldType fieldType, List<Fond> expandedFonds, List<Fond> recordableExpandedFonds, Optional<FieldTypeDefinitionAttributesContainer> parentFieldDefinitionAttributesContainer) {
        var subfondedEditableFieldTypes = new HashMap<Fond, Optional<EditableFieldType>>();
        for (var subfond : expandedFonds) {
            var subfondEditableType = editableFieldTypesByFondLoader.findByFondAndId(subfond, fieldType.getFieldTypeId(), FieldTypesByFondLoader.WhenMissing.RETURN_EMPTY);
            subfondedEditableFieldTypes.put(subfond, Optional.ofNullable(subfondEditableType));
        }

        var attributeFactory = new FieldTypeDefinitionAttributeFactory(fieldType, subfondedEditableFieldTypes);

        /*
         * Warning - all EditableFieldType (eft) value mapping functions should access only that type.
         * They are called also for EditableFieldTypes in subfonds!
         */
        var definitionAttributesContainer = new FieldTypeDefinitionAttributesContainer(
                attributeFactory.createForAllFields(EditableFieldTypesToFieldTypeDefinitionConverter::isEnabled, () -> false),
                attributeFactory.createForFieldsDefinedBySubfonds(eft -> isFieldEditableInGrid(eft, parentFieldDefinitionAttributesContainer)),
                attributeFactory.createForFieldsDefinedBySubfonds(eft -> isHierarchicalRecordLink(eft, recordableExpandedFonds)),
                attributeFactory.createForFieldsDefinedBySubfonds(EditableFieldType::isRequired),
                attributeFactory.createForFieldsDefinedBySubfonds(this::getEditor),
                attributeFactory.createForFieldsDefinedBySubfonds(this::getDependentFieldTypeIds));

        return new FieldTypeDefinition(
                fieldType.getId(),
                fieldType.getText(),
                fieldType.getEditationOrder(),
                fieldType.getDatatype().orElse(null),
                createValueFilterSearchParam(fieldType),
                createSortSearchParam(fieldType),
                definitionAttributesContainer.enabled(),
                definitionAttributesContainer.editable(),
                definitionAttributesContainer.hierarchicalRecordLink(),
                definitionAttributesContainer.required(),
                definitionAttributesContainer.editor(),
                definitionAttributesContainer.dependentFieldTypeIds(),
                map(fieldType.getSubfieldTypes(), recordableExpandedFonds, Optional.of(definitionAttributesContainer)),
                getDependentFieldTypeIds(fieldType)
        );
    }

    private static @Nullable String createValueFilterSearchParam(EditableFieldType fieldType) {
        if (fieldType.getDatatype().isEmpty()) {
            return null;
        }
        if (fieldType.getId().equals(FOND_FIELD_CODE)) {
            return null;
        }
        if (fieldType.getLinkRootFond().isPresent() && fieldType.getLinkRootFond().get().isOfDocument()) {
            return null;
        }
        return FieldTypedSearchFieldParsing.ofValue(fieldType.getFieldTypeId()).toSearchFieldName();
    }

    private static @Nullable String createSortSearchParam(EditableFieldType fieldType) {
        if (fieldType.getDatatype().isEmpty()) {
            return null;
        }
        if (fieldType.getId().equals(FOND_FIELD_CODE)) {
            return null;
        }
        if (fieldType.getLinkRootFond().isPresent()) {
            return null;
        }
        return FieldTypedSearchFieldParsing.ofSort(fieldType.getFieldTypeId()).toSearchFieldName();
    }

    private static boolean isEnabled(EditableFieldType fieldType) {
        return FieldDisplayType.USABLE_IN_GRID.contains(fieldType.getDisplayType());
    }

    private static boolean isFieldEditableInGrid(@NonNull EditableFieldType fieldType, Optional<FieldTypeDefinitionAttributesContainer> parentFieldDefinitionAttributesContainer) {
        var parentIsEditable = parentFieldDefinitionAttributesContainer
                .map(FieldTypeDefinitionAttributesContainer::editable)
                .map(isEditableAttribute -> resolveAttributeValue(isEditableAttribute, fieldType.getEffectiveFond()))
                .orElse(true);

        if (!parentIsEditable) {
            return false;
        }

        if (!FieldDisplayType.ALL_EDITABLE.contains(fieldType.getDisplayType())) {
            return false;
        }
        if (fieldType.getFormula().isEmpty()) {
            return true;
        }
        return fieldType.isLinkFieldType();
    }

    private boolean isHierarchicalRecordLink(@NonNull EditableFieldType fieldType, @NonNull Collection<Fond> recordableExpandedFonds) {
        Optional<Fond> linkedRecordFond = fieldType.getLinkRootFond();
        if (linkedRecordFond.isEmpty()) {
            return false;
        }
        List<Fond> possibleLinkedRecordFonds = Fond.filterRecordable(enabledFondInheritanceLoader.getThisAndChildren(linkedRecordFond.get()));
        return ListUtil.hasIntersection(recordableExpandedFonds, possibleLinkedRecordFonds);
    }

    private @NonNull List<String> getDependentFieldTypeIds(@NonNull EditableFieldType fieldType) {
        return fieldType.getLookups().stream()
                .flatMap(lookupDef -> {
                    if (lookupDef.isSelfLinkOfSelfRecord()) {
                        // return lookupDef.existingLinkedFieldTypeId(); // deprecated - nyni muze byt linkovany na dynamicky field (vstupni prvek)
                        return Stream.empty();
                    }
                    return Stream.of(lookupDef.linkFieldSpec().existingFieldTypeId());
                })
                .map(FieldTypeId::value)
                .toList();
    }

    private ValueEditor<?, ?, ?> getEditor(@NonNull EditableFieldType fieldType) {
        Optional<ValueEditor<?, ?, ?>> resolvedEditor = valueEditorByFieldTypeLoader.getEditorIfDatatyped(fieldType);

        if (resolvedEditor.isEmpty()) {
            return null;
        }

        var editor = resolvedEditor.get();

        if (fieldType.getDatatype().isPresent() && fieldType.getDatatype().get().equals(CoreConstants.Datatype.DATE) && fieldType.isLinkFieldType()) {
            return DateValueEditor
                    .getEmptyEditor()
                    .withEditorId(editor.getEditorId().orElse(null))
                    .withEditorName(editor.getEditorName().orElse(null))
                    .withPlaceholder(editor.getPlaceholder().orElse(null))
                    .withDisabled(editor.getDisabled().orElse(false))
                    .withVisible(editor.getVisible().orElse(null))
                    .withOnlyDate(true)
                    .withMinDateValidation(forceFourDigitsYearFormatWithMinYearValidation());
        }

        if (editor.getType().equals(BasicValueEditorType.DATE)) {
            return ((DateValueEditor) editor).withMinDateValidation(forceFourDigitsYearFormatWithMinYearValidation());
        }

        if (editor.getType().equals(BasicValueEditorType.SINGLE_ACCEPTABLE)) {
            return ((SingleAcceptableValueEditor<?>) editor).withForcedDropdownMenu();
        }

        if (editor.getType().equals(BasicValueEditorType.MULTIPLE_ACCEPTABLE)) {
            return ((MultipleAcceptableValueEditor<?>) editor).withForcedDropdownMenu();
        }

        if (editor.getType().equals(ValueEditorAliasType.RECORD_SEARCH_OR_EDIT)) {
            var originalEditor = (RecordValueEditor) editor;

            return InlineRecordSearchValueEditor
                    .getEmptyEditor()
                    .withEditorId(originalEditor.getEditorId().orElse(null))
                    .withEditorName(originalEditor.getEditorName().orElse(null))
                    .withPlaceholder(originalEditor.getPlaceholder().orElse(null))
                    .withDisabled(originalEditor.getDisabled().orElse(false))
                    .withVisible(originalEditor.getVisible().orElse(null))
                    .withValidations(originalEditor.getValidations().orElse(null))
                    .withStaticSearchParams(originalEditor.getOptions().map(RecordValueEditorOptions::searchParams).orElse(null));
        }

        return editor;
    }

    private Provider<Instant> forceFourDigitsYearFormatWithMinYearValidation() {
        var smallestFourDigitYear = LocalDate.of(1000, 1, 1)
                .atStartOfDay()
                .atZone(timeZoneProvider.get())
                .toInstant();

        return () -> smallestFourDigitYear;
    }

    private static boolean resolveAttributeValue(@NonNull FieldTypeDefinitionAttribute<Boolean> fieldTypeDefinitionAttribute, @NonNull Fond fond) {
        if (fieldTypeDefinitionAttribute.fondedValues().containsKey(fond.getId())) {
            return fieldTypeDefinitionAttribute.fondedValues().get(fond.getId());
        } else {
            return fieldTypeDefinitionAttribute.defaultValue();
        }
    }
}
