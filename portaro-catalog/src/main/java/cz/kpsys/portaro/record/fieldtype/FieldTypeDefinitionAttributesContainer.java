package cz.kpsys.portaro.record.fieldtype;

import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.Collection;


public record FieldTypeDefinitionAttributesContainer(
        @NonNull FieldTypeDefinitionAttribute<@NonNull Boolean> enabled,
        @NonNull FieldTypeDefinitionAttribute<@NonNull Boolean> editable,
        @NonNull FieldTypeDefinitionAttribute<@NonNull Boolean> hierarchicalRecordLink,
        @NonNull FieldTypeDefinitionAttribute<@NonNull Boolean> required,
        @NonNull FieldTypeDefinitionAttribute<? extends @Nullable ValueEditor<?, ?, ?>> editor,
        @NonNull FieldTypeDefinitionAttribute<@NonNull Collection<String>> dependentFieldTypeIds
) {}