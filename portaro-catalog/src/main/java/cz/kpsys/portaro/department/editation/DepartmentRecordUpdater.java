package cz.kpsys.portaro.department.editation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DepartmentRecordUpdater {

    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;
    @NonNull RecordEditationHelper recordEditationHelper;

    public void updatedDepartmentRecordIfExists(UserAuthentication currentAuth, Department ctx, Department updatedDepartment) {
        if (updatedDepartment.getRid() == null) {
            return;
        }
        Record departmentRecord = recordLoader.getById(updatedDepartment.getRid());
        RecordEditation recordEditation = recordEditationFactory
                .on(ctx)
                .ofExisting(departmentRecord)
                .build(currentAuth);

        fillDepartmentRecordEditation(recordEditation, updatedDepartment, ctx, currentAuth);

        recordEditation.saveIfModified(ctx, currentAuth);
    }

    private void fillDepartmentRecordEditation(RecordEditation recordEditation, Department updatedDepartment, Department ctx, UserAuthentication currentAuth) {
        String newDepartmentName = updatedDepartment.getName();

        FieldTypeId entryField = recordEntryFieldTypeIdResolver.getEntryNativeSubfield(recordEditation.getFond());
        recordEditationHelper.setStringSubfieldValue(newDepartmentName, true, entryField.existingParent(), true, entryField, recordEditation, ctx, currentAuth);
    }
}
