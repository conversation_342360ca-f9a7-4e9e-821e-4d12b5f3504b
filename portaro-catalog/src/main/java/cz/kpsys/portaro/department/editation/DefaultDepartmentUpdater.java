package cz.kpsys.portaro.department.editation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentEntity;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordStatusResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.util.Optional;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DefaultDepartmentUpdater implements DepartmentUpdater {

    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull Saver<Department, DepartmentEntity> departmentSaver;
    @NonNull DepartmentRecordUpdater departmentRecordUpdater;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull RecordStatusResolver recordStatusResolver;

    @Override
    public Department updateDepartment(UserAuthentication currentAuth, Department ctx, DepartmentEditationRequest departmentEditationRequest) {
        Department department = departmentLoader.getById(departmentEditationRequest.department().getId());
        Department updatedDepartment = update(department, departmentEditationRequest);
        throwIfDepartmentRecordIsLockedIfPresent(updatedDepartment, ctx);
        throwIfNonRootDepartmentIsMissingParent(departmentEditationRequest.department(), updatedDepartment);
        throwIfDepartmentHierarchyContainsCycle(updatedDepartment);
        departmentSaver.save(updatedDepartment);
        departmentRecordUpdater.updatedDepartmentRecordIfExists(currentAuth, ctx, updatedDepartment);
        return updatedDepartment;
    }

    private void throwIfDepartmentRecordIsLockedIfPresent(Department updatedDepartment, Department ctx) {
        if (updatedDepartment.getRid() != null) {
            Record record = recordLoader.getById(updatedDepartment.getRid());
            Assert.isTrue(!recordStatusResolver.isLockedState(record.getStatus(),  ctx), "Can not update department %s because department record is locked".formatted(updatedDepartment));
        }
    }

    private void throwIfNonRootDepartmentIsMissingParent(@NonNull Department department, Department updatedDepartment) {
        if (!department.isRoot() && updatedDepartment.getParentId() == null) {
            throw new IllegalArgumentException("Can not update department %s because department is missing parent department".formatted(updatedDepartment));
        }
    }

    private void throwIfDepartmentHierarchyContainsCycle(@NonNull Department department) {
        if (isRecursivelyDetectedCycle(department.getId(), Optional.ofNullable(department.getParentId()))) {
            throw new IllegalArgumentException("Can not update department %s because departments hierarchy can not contain cycles".formatted(department));
        }
    }

    private boolean isRecursivelyDetectedCycle(Integer startingId, Optional<Integer> parentId) {
        if (parentId.isEmpty()) {
            return false;
        }

        if (parentId.get().equals(startingId)) {
            return true;
        }

        return isRecursivelyDetectedCycle(startingId, Optional.ofNullable(departmentLoader.getById(parentId.get()).getParentId()));
    }

    private Department update(Department department, DepartmentEditationRequest departmentEditationRequest) {
        return new Department(
                department.getId(),
                departmentEditationRequest.name(),
                departmentEditationRequest.order(),
                department.getRid(),
                departmentEditationRequest.parentDepartment() != null ? departmentEditationRequest.parentDepartment().getId() : null,
                departmentEditationRequest.exemplarable(),
                departmentEditationRequest.sigla(),
                departmentEditationRequest.online(),
                departmentEditationRequest.central(),
                department.getAccountUnitId(),
                department.getInstitutionId(),
                department.getCreationEventId(),
                department.getActivationEventId(),
                department.getDeletionEventId(),
                department.getSyncId());
    }
}
