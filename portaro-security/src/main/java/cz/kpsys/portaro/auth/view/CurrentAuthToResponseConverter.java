package cz.kpsys.portaro.auth.view;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.current.AuthableUserResponse;
import cz.kpsys.portaro.auth.current.CurrentAuthResponse;
import cz.kpsys.portaro.auth.department.CurrentAuthDepartmentsLoader;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.HierarchicalDepartment;
import cz.kpsys.portaro.user.AuthableUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CurrentAuthToResponseConverter implements Converter<UserAuthentication, CurrentAuthResponse> {

    @NonNull CurrentAuthDepartmentsLoader currentAuthReadableDepartmentsLoader;
    @NonNull CurrentAuthDepartmentsLoader currentAuthEditableDepartmentsLoader;
    @NonNull Converter<AuthableUser, AuthableUserResponse> authableUserToResponseConverter;

    @Override
    public CurrentAuthResponse convert(@NonNull UserAuthentication currentAuth) {
        List<Department> editableDepartments = currentAuthEditableDepartmentsLoader.getSubtreesByAuth(currentAuth);
        List<Department> readableDepartments = currentAuthReadableDepartmentsLoader.getSubtreesByAuth(currentAuth);



        AuthableUserResponse activeUserResponse = Objects.requireNonNull(authableUserToResponseConverter.convert(currentAuth.getActiveUser()));
        return new CurrentAuthResponse(
                currentAuth.isEvided(),
                activeUserResponse,
                currentAuth.getRole(),
                readableDepartments,
                editableDepartments,
                convertToHierarchy(readableDepartments)
        );
    }

    private List<HierarchicalDepartment> convertToHierarchy(List<Department> readableDepartments) {

    }

}
